# Large directories to exclude (contains 89GB OSM file)
tile_processing/
venv_planet/

# Virtual environments
venv/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis/

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Documentation and guides
*.md
docs/

# Terraform files
terraform/
*.tf
*.tfvars
*.tfstate*

# Test and development files
test_*.py
*_test.*
tests/
test_db.sqlite3

# Logs and temporary files
logs/
*.log
planet_*.log
*.tmp

# Media and static files (will be served from B2)
media/
staticfiles/
staticfiles_test/

# Large data files
*.mbtiles
*.osm
*.pbf

# Build artifacts
dist/
build/
*.egg-info/

# Database files
*.sqlite3
*.db

# Shell scripts (except Docker entrypoint)
*.sh
!docker-entrypoint.sh

# Python files for planet processing
planet_*.py
download_planet_tiles.py
process_*.py
serve_tiles.py
simple_*.py
stream_*.py
monitor_*.py
setup_*.py

# Configuration files
rclone_*.sh
*_config.sh

# Dart/Flutter files (not needed in Django container)
*.dart

# Processing guides and documentation
*_GUIDE.md
*_IMPLEMENTATION.md
*_DOCUMENTATION.md
*_DEBUG_GUIDE.md 