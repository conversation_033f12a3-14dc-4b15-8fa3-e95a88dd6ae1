# BOPMaps Local Development Environment Configuration
# Copy this file to .env and edit with your local settings

# Debug and Environment settings
DEBUG=True
SECRET_KEY=django-insecure-replace-this-with-a-real-secret-key

# Database Configuration - PostgreSQL with PostGIS
DATABASE_URL=postgis://postgres:password@localhost:5432/bopmaps

# Redis Cache and Celery Configuration
REDIS_URL=redis://localhost:6379/0

# CORS Settings
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# JWT Settings
JWT_ACCESS_TOKEN_LIFETIME=1  # hours
JWT_REFRESH_TOKEN_LIFETIME=7  # days

# Email Configuration (Console backend for development)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
DEFAULT_FROM_EMAIL=<EMAIL>

# Static and Media Files
MEDIA_URL=/media/
STATIC_URL=/static/

# Optional Third-party API Keys (replace with your own keys for testing)
# SPOTIFY_CLIENT_ID=your_spotify_client_id
# SPOTIFY_CLIENT_SECRET=your_spotify_client_secret
# APPLE_MUSIC_KEY=your_apple_music_key
# SOUNDCLOUD_CLIENT_ID=your_soundcloud_client_id

# Music APIs
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret

APPLE_MUSIC_KEY_ID=your_apple_key_id
APPLE_MUSIC_TEAM_ID=your_apple_team_id
APPLE_MUSIC_PRIVATE_KEY=your_apple_private_key

SOUNDCLOUD_CLIENT_ID=your_soundcloud_client_id
SOUNDCLOUD_CLIENT_SECRET=your_soundcloud_client_secret

# Firebase (for notifications)
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
FIREBASE_APP_ID=your_firebase_app_id

# Media Storage
MEDIA_STORAGE=local  # or 's3' for production
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_STORAGE_BUCKET_NAME=your_aws_bucket_name

# Sentry (for error tracking)
SENTRY_DSN=your_sentry_dsn 