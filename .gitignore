# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# Environment variables
.env
.env.*
!.env.example
.venv
venv/
ENV/

# Credentials
*.pem
*.key
*.cert
*.crt
credentials.json
credential*
token*
secret*

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Project specific
logs/
temp/
uploaded_files/
*.dump
postgres-data/

# Jupyter Notebook
.ipynb_checkpoints

# Celery
celerybeat-schedule
celerybeat.pid

# Virtual Environment
venv/
venv_planet/
venv_py311/
ENV/
env.bak/
venv.bak/

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Coverage
.coverage
htmlcov/

# Docker
.docker/
docker-compose.override.yml

# Frontend
/node_modules
/build
.npm
.yarn
.eslintcache

# Terraform - IMPORTANT: These contain large binaries and sensitive state
terraform/.terraform/
terraform/.terraform.lock.hcl
terraform/terraform.tfstate
terraform/terraform.tfstate.backup
terraform/terraform.tfvars
!terraform/terraform.tfvars.example
terraform/crash.log
terraform/override.tf
terraform/override.tf.json
terraform/*_override.tf
terraform/*_override.tf.json
terraform/.terraformrc
terraform/terraform.rc

# Large binary files and data
*.mbtiles
*.pbf
*.osm
*.osm.gz
*.geojson
*.gpx
*.shapefile
*.shp
*.shx
*.dbf
*.prj

# OpenMapTiles data (can be very large)
docker/openmaptiles/data/
docker/openmaptiles/tiles/
docker/openmaptiles/cache/
docker/openmaptiles/*.mbtiles
docker/openmaptiles/*.pbf
tile_processing/

# Map tiles and fonts

*.jpg
*.jpeg
*.gif
*.svg
*.ttf
*.otf
*.woff
*.woff2
# But keep specific small images that might be part of the codebase
!**/static/**/*.png
!**/static/**/*.jpg
!**/static/**/*.jpeg
!**/static/**/*.gif
!**/static/**/*.svg
!test_tile*.png

# Large logs and temporary files
*.log.*
*.tmp
*.temp
*.cache
*.bak
*.backup

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
npm-debug.log*
yarn-debug.log*
yarn-error.log* 