# Design Document

## Overview

The Music Pin Seeding System is designed to solve the cold start problem for new BOPMaps users by automatically generating curated music pins in areas with sparse organic content. The system creates a compelling initial discovery experience by placing diverse, high-quality music tracks at realistic locations around users, making the app feel populated and engaging from day one.

The system operates as a background service that triggers when users request nearby pins, evaluating content density and generating appropriate seed content when needed. It leverages multiple music APIs (Spotify, Last.fm, SoundCharts) to create diverse, appealing content while maintaining the authenticity of the location-based music discovery experience.

## Architecture

### Exploration Pattern Strategy

Following the seeding.md strategy, the system implements a three-tier exploration pattern:

#### Immediate Discovery Zone (0-500m)
- **5-8 pins within walking distance**
- **Priority locations**: Cafes, small parks, local venues, bookstores
- **Content focus**: Diverse genres to provide immediate satisfaction
- **Purpose**: Instant gratification and app engagement

#### Nearby Exploration Zone (500m-2km)  
- **5-10 pins for short exploration trips**
- **Priority locations**: Libraries, college campuses, larger parks, cultural centers
- **Content focus**: Discovery-oriented tracks, local favorites
- **Purpose**: Encourage short exploration journeys

#### City Discovery Zone (2km+)
- **3-5 pins across broader city area**
- **Priority locations**: Major landmarks, popular districts, music venues
- **Content focus**: Popular hits, city-specific content
- **Purpose**: Long-term exploration motivation

This pattern ensures users always have immediate content to discover while providing incentives for broader city exploration, creating the "curated but believable" discovery experience outlined in the seeding strategy.

### High-Level Architecture

```mermaid
graph TB
    A[User Requests Nearby Pins] --> B[Pin Service]
    B --> C{Check Content Density}
    C -->|Sparse Content| D[Seeding Service]
    C -->|Sufficient Content| E[Return Organic Pins]
    
    D --> F[Seed Content Database]
    D --> G[Location Service]
    D --> H[Pin Generation Service]
    
    F --> I[Spotify API]
    F --> J[Last.fm API]
    F --> K[SoundCharts API]
    
    H --> L[Create Seed Pins]
    L --> M[Pin Database]
    M --> N[Return Mixed Content]
    
    O[Background Jobs] --> P[Seed Maintenance]
    P --> Q[Expire Old Seeds]
    P --> R[Update Content Database]
```

### System Components

1. **Seeding Trigger Service**: Evaluates when seeding is needed during pin requests
2. **Seed Content Generator**: Creates and maintains the JSON-based seed content database
3. **Pin Generation Service**: Creates actual Pin objects from seed content
4. **Location Service**: Handles geographic distribution and POI integration
5. **Maintenance Service**: Manages seed lifecycle and cleanup

### Data Flow

1. User requests nearby pins via `/pins/nearby/` endpoint
2. System checks organic pin density in the area (3km radius)
3. If density < 8 pins, seeding service is triggered
4. **Location Analysis**: Identify POIs within exploration zones:
   - Immediate zone (500m): cafes, parks, nearby venues
   - Nearby zone (2km): libraries, campuses, cultural spots
   - City zone (5km+): major landmarks, popular districts
5. **Content Selection**: Choose tracks based on city music profile + location contexts
   - Determine city (Nashville, NYC, Austin, etc.) from coordinates
   - Weight genre selection by local preferences (Nashville=country, NYC=hip-hop)
   - Include 60% locally popular genres, 40% diverse discovery content
6. **Contextual Placement**: Match track moods to location types
   - Chill tracks → parks, cafes
   - Energetic tracks → venues, transit hubs
   - Focus tracks → libraries, study spots
7. **Pin Generation**: Create pins with contextual descriptions
8. **Spatial Distribution**: Ensure realistic exploration pattern
9. Combined results returned with immediate discoveries + exploration incentives
10. Background jobs handle seed expiration and maintenance

## Location-Based Music Intelligence

### Data Sources for Regional Music Preferences

#### 1. Spotify Charts API
- **City-specific charts**: Top tracks by city (available for major cities)
- **Regional playlists**: "Nashville Hits", "NYC Hip-Hop", "Austin Indie" etc.
- **Usage**: Primary source for current popular tracks by location

#### 2. SoundCharts API  
- **Regional chart data**: Track performance by country/region
- **Genre popularity**: Genre distribution by geographic area
- **Usage**: Secondary validation and broader regional trends

#### 3. Last.fm API
- **City listening data**: Top artists/tracks by city from user scrobbles
- **Genre tagging**: Community-driven genre classification
- **Usage**: Historical data and genre insights

#### 4. Cultural Music Mapping
- **Manual curation**: Known music scenes (Nashville=Country, Seattle=Grunge)
- **Music venue data**: Venue types indicate local preferences
- **Festival data**: Local music festivals indicate genre preferences

### Location Intelligence Service

```python
class LocationMusicIntelligence:
    def get_city_music_profile(self, lat: float, lng: float) -> Dict:
        """Get music preferences for a specific city/region"""
        # Returns: {
        #   'city_name': 'Nashville',
        #   'popular_genres': ['country', 'americana', 'folk'],
        #   'genre_weights': {'country': 0.4, 'americana': 0.2, 'folk': 0.2},
        #   'local_artists': ['Kacey Musgraves', 'Chris Stapleton'],
        #   'cultural_context': 'music_city'
        # }
        
    def get_regional_popular_tracks(self, city_profile: Dict, genre: str, limit: int = 50) -> List[Dict]:
        """Get popular tracks for a genre in a specific region"""
        
    def determine_city_from_coordinates(self, lat: float, lng: float) -> str:
        """Reverse geocode to determine city for music profile lookup"""
        
    def get_fallback_regional_profile(self, country: str, region: str) -> Dict:
        """Get broader regional profile when city-specific data unavailable"""
```

### City Music Profile Examples

#### Nashville, TN
- **Genres**: Country (40%), Americana (20%), Folk (20%), Rock (20%)
- **Context**: Music City - heavy country influence, live music culture
- **Sources**: Country music venues, Grand Ole Opry, music row

#### New York, NY  
- **Genres**: Hip-Hop (30%), Indie (25%), Electronic (25%), Jazz (20%)
- **Context**: Urban diversity - multiple scenes coexisting
- **Sources**: Brooklyn indie scene, Harlem jazz history, hip-hop origins

#### Austin, TX
- **Genres**: Indie (30%), Country (25%), Rock (25%), Electronic (20%)  
- **Context**: "Live Music Capital" - eclectic mix, SXSW influence
- **Sources**: 6th Street venues, SXSW data, "Keep Austin Weird"

#### Seattle, WA
- **Genres**: Grunge (25%), Indie (30%), Alternative (25%), Electronic (20%)
- **Context**: Alternative rock heritage, coffee shop culture
- **Sources**: Grunge history, indie venues, tech culture influence

### Geographic Fallback Strategy

1. **City-specific**: Use exact city profile if available
2. **Metropolitan area**: Use metro area profile for suburbs  
3. **Regional**: Fall back to broader regional preferences (US South, West Coast, etc.)
4. **National**: Use country-level popular genres
5. **Global**: Use globally popular tracks as final fallback

## POI Data Sources and Strategy

### Primary Data Sources

#### 1. Google Places API
- **Coverage**: Comprehensive global POI database
- **Relevant Types**: `cafe`, `restaurant`, `park`, `library`, `university`, `tourist_attraction`, `transit_station`
- **Advantages**: High accuracy, rich metadata, business hours
- **Limitations**: API costs, rate limits
- **Usage**: Primary source for urban areas with good coverage

#### 2. OpenStreetMap Overpass API  
- **Coverage**: Community-driven, varies by region
- **Relevant Tags**: `amenity=cafe`, `amenity=library`, `leisure=park`, `amenity=university`
- **Advantages**: Free, open data, good coverage in many cities
- **Limitations**: Data quality varies, less commercial venue info
- **Usage**: Secondary source and primary for cost-sensitive deployments

#### 3. Foursquare Places API
- **Coverage**: Strong for entertainment venues and nightlife
- **Relevant Categories**: Music venues, bars, entertainment, cultural sites
- **Advantages**: Venue-focused, good for music-related locations
- **Limitations**: Limited free tier, less comprehensive than Google
- **Usage**: Tertiary source for music venue identification

### Fallback Strategy

When POI APIs are unavailable or return insufficient data:

1. **Geometric Distribution**: Create realistic scatter pattern using:
   - Avoid residential areas (detected via land use data if available)
   - Avoid highways and major roads
   - Prefer intersections and commercial zones
   - Use population density data to guide placement

2. **Cached POI Data**: Store successful POI queries for offline fallback

3. **User-Generated Hints**: Learn from organic pin locations over time

### POI Categorization Logic

```python
POI_CATEGORY_MAPPING = {
    # Google Places types -> Our categories
    'cafe': 'cafe',
    'restaurant': 'cafe',  # Many restaurants have cafe-like atmosphere
    'park': 'park',
    'library': 'campus',
    'university': 'campus',
    'school': 'campus',
    'tourist_attraction': 'landmark',
    'transit_station': 'transit',
    'subway_station': 'transit',
    'bus_station': 'transit',
    'night_club': 'venue',
    'bar': 'venue',
    'music_venue': 'venue',  # Custom category
    
    # OpenStreetMap amenity tags -> Our categories  
    'amenity=cafe': 'cafe',
    'amenity=restaurant': 'cafe',
    'leisure=park': 'park',
    'amenity=library': 'campus',
    'amenity=university': 'campus',
    'tourism=attraction': 'landmark',
    'public_transport=station': 'transit',
    'amenity=bar': 'venue',
    'amenity=nightclub': 'venue'
}
```

## Components and Interfaces

### 1. Seeding Service Interface

```python
class SeedingService:
    def should_seed_area(self, lat: float, lng: float, radius_km: float = 5.0) -> bool:
        """Check if an area needs seeding based on organic pin density"""
        
    def generate_exploration_seed_pins(self, lat: float, lng: float, user: User = None) -> List[Pin]:
        """Generate seed pins following exploration pattern from seeding.md"""
        # Creates 5-8 pins within walking distance (immediate discovery)
        # Plus 5-10 pins for nearby exploration (short trips)
        # Plus 3-5 pins across broader city (longer exploration)
        
    def get_diverse_seed_content(self, lat: float, lng: float) -> Dict[str, List[Dict]]:
        """Get genre-diverse content appropriate for city's music culture"""
        # 1. Determine city music profile (Nashville=country, NYC=hip-hop/indie)
        # 2. Weight content selection by local preferences  
        # 3. Include 60% local popular genres, 40% diverse discovery content
        # Returns content categorized by mood/genre for location matching
        
    def create_realistic_discovery_experience(self, center_point: Point) -> List[Pin]:
        """Create the full seeding experience as described in seeding.md"""
        # Implements the "curated but believable" discovery experience
        # with realistic spatial distribution and contextual content
```

### 2. Seed Content Database Structure

```json
{
  "version": "1.0",
  "generated_at": "2025-01-20T10:00:00Z",
  "content": {
    "global": [
      {
        "track_id": "spotify:track:4iV5W9uYEdYUVa79Axb7Rh",
        "title": "Flowers",
        "artist": "Miley Cyrus",
        "album": "Endless Summer Vacation",
        "genre": "pop",
        "mood": "energetic",
        "popularity_score": 95,
        "regional_popularity": {
          "US": 98,
          "UK": 85,
          "CA": 92
        },
        "release_year": 2023,
        "duration_ms": 200000,
        "artwork_url": "https://...",
        "preview_url": "https://...",
        "track_url": "https://open.spotify.com/track/...",
        "service": "spotify",
        "tags": ["trending", "popular", "upbeat"],
        "description_templates": { /* ... */ }
      }
    ],
    "city_profiles": {
      "nashville": {
        "popular_genres": ["country", "americana", "folk", "rock"],
        "genre_weights": {"country": 0.4, "americana": 0.2, "folk": 0.2, "rock": 0.2},
        "local_artists": ["Kacey Musgraves", "Chris Stapleton", "Maren Morris"],
        "cultural_context": "music_city",
        "tracks": [...]
      },
      "new_york": {
        "popular_genres": ["hip-hop", "indie", "electronic", "jazz"],
        "genre_weights": {"hip-hop": 0.3, "indie": 0.25, "electronic": 0.25, "jazz": 0.2},
        "local_artists": ["Jay-Z", "The Strokes", "Vampire Weekend"],
        "cultural_context": "urban_diverse",
        "tracks": [...]
      },
      "austin": {
        "popular_genres": ["indie", "country", "rock", "electronic"],
        "genre_weights": {"indie": 0.3, "country": 0.25, "rock": 0.25, "electronic": 0.2},
        "local_artists": ["Spoon", "Gary Clark Jr.", "Black Angels"],
        "cultural_context": "live_music_capital",
        "tracks": [...]
      },
      "seattle": {
        "popular_genres": ["grunge", "indie", "alternative", "electronic"],
        "genre_weights": {"grunge": 0.25, "indie": 0.3, "alternative": 0.25, "electronic": 0.2},
        "local_artists": ["Nirvana", "Pearl Jam", "Macklemore"],
        "cultural_context": "alternative_scene",
        "tracks": [...]
      }
    },
    "regional": {
      "US_south": {
        "popular_genres": ["country", "hip-hop", "blues", "rock"],
        "tracks": [...]
      },
      "US_west_coast": {
        "popular_genres": ["hip-hop", "indie", "electronic", "pop"],
        "tracks": [...]
      },
      "US_northeast": {
        "popular_genres": ["indie", "hip-hop", "rock", "jazz"],
        "tracks": [...]
      }
    },
    "genres": {
      "hip-hop": [...],
      "rock": [...],
      "electronic": [...],
      "indie": [...],
      "pop": [...],
      "country": [...],
      "jazz": [...],
      "grunge": [...]
    }
  }
}
```

### 3. Pin Generation Service

```python
class PinGenerationService:
    def create_seed_pins(self, seed_content: List[Dict], location_zones: Dict[str, List[Point]]) -> List[Pin]:
        """Create Pin objects distributed across exploration zones"""
        
    def get_curator_account(self) -> User:
        """Get or create the curator account for seed pins"""
        
    def generate_contextual_pin_description(self, track: Dict, location_type: str, location_name: str) -> str:
        """Generate location-specific descriptions for authentic discovery feel"""
        # Examples:
        # Cafe: "Perfect coffee shop vibes for this indie track"
        # Park: "Chill beats for a peaceful moment in the park"
        # Campus: "Study session anthem discovered at the library"
        # Venue: "This track was made for live music venues like this"
        
    def match_content_to_location(self, tracks: List[Dict], location_type: str) -> List[Dict]:
        """Match track mood/genre to location context for realistic placement"""
        # Cafe -> chill, indie, acoustic
        # Park -> chill, ambient, folk
        # Venue -> energetic, rock, electronic
        # Campus -> focus, indie, alternative
        # Transit -> upbeat, pop, energetic
```

### 4. Location Service

```python
class LocationService:
    def get_seed_locations(self, center_lat: float, center_lng: float) -> Dict[str, List[Point]]:
        """Generate realistic locations for seed pins following exploration pattern"""
        # Returns: {
        #   'immediate': [5-8 locations within 500m walking distance],
        #   'nearby': [5-10 locations within 2km for short exploration],
        #   'city': [3-5 locations across broader city area]
        # }
        
    def get_poi_locations(self, lat: float, lng: float, radius_km: float) -> List[Dict]:
        """Get points of interest using multiple data sources with fallbacks"""
        # Primary: Google Places API (cafes, parks, libraries, venues)
        # Secondary: OpenStreetMap Overpass API (free alternative)
        # Tertiary: Foursquare Places API (venue-focused)
        # Fallback: Geometric distribution if all APIs fail
        
    def query_google_places(self, lat: float, lng: float, place_types: List[str], radius: int) -> List[Dict]:
        """Query Google Places API for POIs"""
        # place_types: ['cafe', 'park', 'library', 'university', 'tourist_attraction']
        
    def query_openstreetmap(self, lat: float, lng: float, amenity_types: List[str], radius: int) -> List[Dict]:
        """Query OpenStreetMap Overpass API as free fallback"""
        # amenity_types: ['cafe', 'restaurant', 'library', 'university', 'park']
        
    def generate_fallback_locations(self, center: Point, count: int, radius_km: float) -> List[Point]:
        """Generate geometric distribution when POI APIs fail"""
        # Creates realistic scatter pattern avoiding residential/highway areas
        
    def categorize_location_by_context(self, poi_data: Dict) -> str:
        """Categorize location for mood/genre matching"""
        # Returns: 'cafe', 'park', 'venue', 'campus', 'landmark', 'transit'
        
    def distribute_pins_by_exploration_zones(self, center: Point, pins: List[Dict]) -> List[Tuple[Dict, Point, str]]:
        """Distribute pins across exploration zones with context matching"""
        # Returns list of (pin_data, location, zone_type) tuples
```

### 5. Maintenance Service

```python
class SeedMaintenanceService:
    def expire_old_seeds(self) -> int:
        """Remove expired seed pins"""
        
    def retire_seeds_in_active_areas(self) -> int:
        """Remove seeds from areas with sufficient organic content"""
        
    def update_seed_content_database(self) -> bool:
        """Refresh the seed content database from music APIs"""
```

## Data Models

### Extended Pin Model

The existing Pin model already supports the seeding system with these key fields:

- `tags`: ArrayField to include "seed" tag for identification
- `expiration_date`: For automatic seed cleanup
- `owner`: References curator account
- `location`: Geographic placement
- `genre`, `mood`: For content categorization

### New Models

```python
class SeedContentDatabase(models.Model):
    """Stores the JSON seed content database"""
    version = models.CharField(max_length=20)
    content_data = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    
class SeedingArea(models.Model):
    """Tracks areas that have been seeded"""
    center_point = gis_models.PointField(geography=True)
    radius_km = models.FloatField(default=5.0)
    seed_count = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)
    last_organic_check = models.DateTimeField(auto_now=True)
    
class SeedingMetrics(models.Model):
    """Analytics for seeding system performance"""
    date = models.DateField(auto_now_add=True)
    areas_seeded = models.IntegerField(default=0)
    pins_generated = models.IntegerField(default=0)
    seeds_expired = models.IntegerField(default=0)
    seeds_retired = models.IntegerField(default=0)
    avg_user_engagement = models.FloatField(default=0.0)
```

## Error Handling

### Graceful Degradation

1. **API Failures**: If music APIs are unavailable, fall back to cached content
2. **Location Errors**: Use default distribution patterns if POI data is unavailable
3. **Database Issues**: Return organic pins only if seeding fails
4. **Rate Limiting**: Implement exponential backoff for API calls

### Error Recovery

```python
class SeedingErrorHandler:
    def handle_api_failure(self, api_name: str, fallback_content: List[Dict]) -> List[Dict]:
        """Handle music API failures with cached fallbacks"""
        
    def handle_location_failure(self, center: Point) -> List[Point]:
        """Generate locations using geometric distribution if POI fails"""
        
    def handle_generation_failure(self, error: Exception) -> List[Pin]:
        """Return empty list and log error if pin generation fails"""
```

### Monitoring and Alerting

- Track seeding success/failure rates
- Monitor API usage and rate limits
- Alert on high failure rates or system degradation
- Log user engagement with seed pins for optimization

## Testing Strategy

### Unit Tests

1. **Content Selection Logic**: Test genre diversity and quality filtering
2. **Location Distribution**: Verify realistic geographic spread
3. **Pin Generation**: Ensure proper Pin object creation
4. **Expiration Logic**: Test seed lifecycle management

### Integration Tests

1. **API Integration**: Test with actual music service APIs
2. **Database Operations**: Verify seed pin CRUD operations
3. **Geographic Queries**: Test location-based pin retrieval
4. **User Flow**: End-to-end seeding trigger and response

### Performance Tests

1. **Response Time**: Ensure seeding doesn't slow pin requests
2. **Database Load**: Test with large numbers of seed pins
3. **API Rate Limits**: Verify proper rate limiting handling
4. **Memory Usage**: Monitor memory consumption during generation

### A/B Testing Framework

```python
class SeedingExperiment:
    def get_seed_strategy(self, user: User) -> str:
        """Return seeding strategy for A/B testing"""
        # Strategies: diverse_genres, popular_only, mood_based, location_specific
        
    def track_engagement(self, user: User, pin: Pin, interaction_type: str):
        """Track user engagement for experiment analysis"""
```

## Security Considerations

### Data Privacy

- Hash user preferences before using in seed selection
- Don't store personal music listening data
- Ensure seed pins don't reveal individual user patterns

### API Security

- Secure storage of API keys and tokens
- Rate limiting to prevent abuse
- Input validation for all geographic coordinates
- Sanitization of user-provided location data

### Content Moderation

- Pre-filter seed content for explicit material
- Implement reporting system for inappropriate seeds
- Regular review of curator account activity
- Automated detection of spam or low-quality content

## Performance Optimization

### Caching Strategy

1. **Seed Content Cache**: Redis cache for frequently accessed seed data
2. **POI Location Cache**: Cache Google Places/OSM results by geographic area (24-hour TTL)
3. **Generated Pins Cache**: Cache recent seed pin sets per area
4. **API Response Cache**: Cache music API responses per SoundCharts/Spotify guidelines
5. **Fallback Location Cache**: Store geometric distributions for areas with poor POI coverage

### Database Optimization

1. **Indexes**: Optimize queries on location, tags, and expiration_date
2. **Partitioning**: Consider partitioning pins by geographic region
3. **Archival**: Archive expired seeds instead of deletion for analytics

### Async Processing

```python
@shared_task
def generate_seed_pins_async(lat: float, lng: float, user_id: int = None):
    """Asynchronously generate seed pins for an area"""
    
@shared_task
def update_seed_content_database():
    """Background task to refresh seed content from APIs"""
    
@shared_task
def cleanup_expired_seeds():
    """Daily cleanup of expired seed pins"""
```

## Scalability Considerations

### Horizontal Scaling

- Stateless seeding service design
- Database read replicas for pin queries
- CDN for seed content database distribution
- Load balancing for API requests

### Geographic Distribution

- Regional seed content databases
- Localized curator accounts
- Time zone aware expiration handling
- Regional API endpoints where available

### Growth Planning

- Monitor seed-to-organic pin ratios by region
- Scale curator accounts based on user growth
- Implement seed pin quotas per area
- Plan for international expansion with localized content

This design provides a robust, scalable foundation for the music pin seeding system that will effectively solve the cold start problem while maintaining the authentic discovery experience that makes BOPMaps engaging.