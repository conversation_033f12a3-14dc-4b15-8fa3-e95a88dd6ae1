FROM python:3.10-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Set work directory
WORKDIR /code

# Install system dependencies in stages to avoid conflicts
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    postgresql-client \
    netcat-openbsd \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install GDAL separately to handle dependencies better
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    gdal-bin \
    libgdal-dev \
    python3-gdal \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* || echo "GDAL installation had issues but continuing..."

# Set GDAL environment variables
ENV CPLUS_INCLUDE_PATH=/usr/include/gdal
ENV C_INCLUDE_PATH=/usr/include/gdal

# Install Python dependencies
COPY requirements.txt /code/
RUN pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY . /code/

# Create required directories
RUN mkdir -p /code/logs /code/media /code/staticfiles

# Collect static files
RUN python manage.py collectstatic --noinput --clear || echo "Static files collection failed, continuing..."

# Expose port
EXPOSE 8000

# Run daphne directly
CMD ["daphne", "-b", "0.0.0.0", "-p", "8000", "--access-log", "-", "bopmaps.asgi:application"] 