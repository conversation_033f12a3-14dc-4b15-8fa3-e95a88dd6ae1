[{"model": "gamification.achievement", "pk": 1, "fields": {"name": "Local Explorer", "description": "Place 5 pins within 1km radius", "icon": "", "icon_name": null, "criteria": {"radius_km": 1, "pins_in_radius": 5}, "type": "location", "challenge_id": "local_explorer_t1", "tier": "T1", "xp_reward": 100, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 2, "fields": {"name": "City Hopper", "description": "Place pins in 3 different cities", "icon": "", "icon_name": null, "criteria": {"different_cities": 3}, "type": "location", "challenge_id": "city_hopper_t2", "tier": "T2", "xp_reward": 200, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 3, "fields": {"name": "Music Explorer", "description": "Create pins for 10 different artists", "icon": "", "icon_name": null, "criteria": {"unique_artists": 10}, "type": "artist", "challenge_id": "music_explorer_t1", "tier": "T1", "xp_reward": 100, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 4, "fields": {"name": "Super Fan", "description": "Create 5 pins for your favorite artist", "icon": "", "icon_name": null, "criteria": {"target_artist": null, "same_artist_pins": 5}, "type": "artist", "challenge_id": "super_fan_t2", "tier": "T2", "xp_reward": 200, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 5, "fields": {"name": "Genre Explorer", "description": "Create pins for 5 different genres", "icon": "", "icon_name": null, "criteria": {"unique_genres": 5}, "type": "genre", "challenge_id": "genre_explorer_t1", "tier": "T1", "xp_reward": 100, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 6, "fields": {"name": "Genre Master", "description": "Create 10 pins in your favorite genre", "icon": "", "icon_name": null, "criteria": {"target_genre": null, "same_genre_pins": 10}, "type": "genre", "challenge_id": "genre_master_t2", "tier": "T2", "xp_reward": 200, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 7, "fields": {"name": "Rising Star", "description": "Get 10 reactions on your pins", "icon": "", "icon_name": null, "criteria": {"reactions_received": 10}, "type": "social", "challenge_id": "rising_star_t1", "tier": "T1", "xp_reward": 100, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 8, "fields": {"name": "Community Favorite", "description": "Get reactions from 5 different users", "icon": "", "icon_name": null, "criteria": {"unique_reactors": 5}, "type": "social", "challenge_id": "community_favorite_t2", "tier": "T2", "xp_reward": 200, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 9, "fields": {"name": "Night Owl", "description": "Create 3 pins between midnight and 4 AM", "icon": "", "icon_name": null, "criteria": {"night_pins": 3}, "type": "achievement", "challenge_id": "night_owl_secret", "tier": "T0", "xp_reward": 500, "is_secret": true, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 10, "fields": {"name": "Global Citizen", "description": "Create pins in 3 different countries", "icon": "", "icon_name": null, "criteria": {"different_countries": 3}, "type": "location", "challenge_id": "global_citizen_secret", "tier": "T0", "xp_reward": 1000, "is_secret": true, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 11, "fields": {"name": "First Drop", "description": "1 pin anywhere", "icon": "", "icon_name": null, "criteria": {"total_pins": 1, "public_only": true}, "type": "location", "challenge_id": "first_drop", "tier": "T0", "xp_reward": 25, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 12, "fields": {"name": "Neighborhood Navigator", "description": "5 pins within 1 km of each other (any time span)", "icon": "", "icon_name": null, "criteria": {"radius_km": 1, "public_only": true, "pins_in_radius": 5}, "type": "location", "challenge_id": "close_quarters", "tier": "T0", "xp_reward": 50, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 13, "fields": {"name": "City Sampler", "description": "3 pins on 3 different days in the same city", "icon": "", "icon_name": null, "criteria": {"public_only": true, "pins_in_city": 3, "different_days": 3}, "type": "location", "challenge_id": "city_sampler", "tier": "T1", "xp_reward": 75, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 14, "fields": {"name": "Midnight Bop", "description": "1 pin between 00:00–03:00 local", "icon": "", "icon_name": null, "criteria": {"pins_count": 1, "time_range": {"end": "03:00", "start": "00:00"}, "public_only": true}, "type": "location", "challenge_id": "after_hours", "tier": "T1", "xp_reward": 75, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 15, "fields": {"name": "Metro Mover", "description": "5 districts/neighborhoods in one city (≥ 1 km apart)", "icon": "", "icon_name": null, "criteria": {"districts": 5, "same_city": true, "public_only": true, "min_distance_km": 1}, "type": "location", "challenge_id": "metro_mover", "tier": "T2", "xp_reward": 125, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 16, "fields": {"name": "Cross-Town Sound", "description": "2 pins 10 km+ apart in < 24 h", "icon": "", "icon_name": null, "criteria": {"pins_count": 2, "public_only": true, "min_distance_km": 10, "time_window_hours": 24}, "type": "location", "challenge_id": "cross_town_sound", "tier": "T2", "xp_reward": 150, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 17, "fields": {"name": "City Collector I", "description": "3 different cities (same state/region)", "icon": "", "icon_name": null, "criteria": {"public_only": true, "same_region": true, "different_cities": 3}, "type": "location", "challenge_id": "city_collector_I", "tier": "T3", "xp_reward": 175, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 18, "fields": {"name": "Backroads Bopper", "description": "3 pins in towns < 100k population", "icon": "", "icon_name": null, "criteria": {"pins_count": 3, "public_only": true, "max_population": 100000}, "type": "location", "challenge_id": "backroads_bopper", "tier": "T3", "xp_reward": 200, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 19, "fields": {"name": "State-Line Dropper", "description": "2 states/administrative regions", "icon": "", "icon_name": null, "criteria": {"public_only": true, "different_states": 2}, "type": "location", "challenge_id": "state_line", "tier": "T4", "xp_reward": 250, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 20, "fields": {"name": "City Collector II", "description": "6 cities total (country-wide)", "icon": "", "icon_name": null, "criteria": {"public_only": true, "same_country": true, "different_cities": 6}, "type": "location", "challenge_id": "city_collector_II", "tier": "T4", "xp_reward": 300, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 21, "fields": {"name": "<PERSON> Crosser", "description": "3 countries, same continent", "icon": "", "icon_name": null, "criteria": {"public_only": true, "same_continent": true, "different_countries": 3}, "type": "location", "challenge_id": "border_crosser", "tier": "T5", "xp_reward": 350, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 22, "fields": {"name": "Jet-Set Sound", "description": "2 countries within 7 days", "icon": "", "icon_name": null, "criteria": {"public_only": true, "time_window_days": 7, "different_countries": 2}, "type": "location", "challenge_id": "jet_set", "tier": "T5", "xp_reward": 400, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 23, "fields": {"name": "Continent Toucher", "description": "3 different continents", "icon": "", "icon_name": null, "criteria": {"public_only": true, "different_continents": 3}, "type": "location", "challenge_id": "continent_toucher", "tier": "T6", "xp_reward": 500, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 24, "fields": {"name": "Globe Trotter", "description": "10 cities across ≥ 5 countries", "icon": "", "icon_name": null, "criteria": {"public_only": true, "min_countries": 5, "different_cities": 10}, "type": "location", "challenge_id": "globe_trotter", "tier": "T6", "xp_reward": 600, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 25, "fields": {"name": "Atlas Mode", "description": "10 cities in ≥ 5 countries plus 25 total pins", "icon": "", "icon_name": null, "criteria": {"total_pins": 25, "public_only": true, "min_countries": 5, "different_cities": 10}, "type": "location", "challenge_id": "atlas_mode", "tier": "T7", "xp_reward": 800, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 26, "fields": {"name": "World Tour", "description": "≥ 1 city on every continent except Antarctica", "icon": "", "icon_name": null, "criteria": {"public_only": true, "continents_required": ["NA", "SA", "EU", "AF", "AS", "OC"]}, "type": "location", "challenge_id": "world_tour", "tier": "T7", "xp_reward": 1000, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 27, "fields": {"name": "Pin Pioneer", "description": "Drop in a location with no prior pins", "icon": "", "icon_name": null, "criteria": {"auto_reveal": true, "public_only": true, "no_prior_pins": true}, "type": "location", "challenge_id": "pin_pioneer", "tier": "SECRET", "xp_reward": 800, "is_secret": true, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 28, "fields": {"name": "First Fan", "description": "1 pin by any artist", "icon": "", "icon_name": null, "criteria": {"total_pins": 1}, "type": "artist", "challenge_id": "first_fan", "tier": "T0", "xp_reward": 25, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 29, "fields": {"name": "Triple Taste", "description": "3 unique artists", "icon": "", "icon_name": null, "criteria": {"unique_artists": 3}, "type": "artist", "challenge_id": "triple_taste", "tier": "T0", "xp_reward": 50, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 30, "fields": {"name": "Loyal Listener", "description": "3 pins, same artist", "icon": "", "icon_name": null, "criteria": {"same_artist_pins": 3}, "type": "artist", "challenge_id": "loyal_listener", "tier": "T1", "xp_reward": 75, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 31, "fields": {"name": "Ten-Artist Sampler", "description": "10 artists total", "icon": "", "icon_name": null, "criteria": {"unique_artists": 10}, "type": "artist", "challenge_id": "ten_artist_sampler", "tier": "T1", "xp_reward": 100, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 32, "fields": {"name": "Superfan-in-Training", "description": "7 pins, same artist (≥ 3 days)", "icon": "", "icon_name": null, "criteria": {"different_days": 3, "same_artist_pins": 7}, "type": "artist", "challenge_id": "superfan_in_training", "tier": "T2", "xp_reward": 150, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 33, "fields": {"name": "Twenty-Artist Tour", "description": "20 artists", "icon": "", "icon_name": null, "criteria": {"unique_artists": 20}, "type": "artist", "challenge_id": "twenty_artist_tour", "tier": "T2", "xp_reward": 175, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 34, "fields": {"name": "Album Run", "description": "Tracks from 3 albums, same artist", "icon": "", "icon_name": null, "criteria": {"same_artist": true, "unique_albums": 3}, "type": "artist", "challenge_id": "album_run", "tier": "T3", "xp_reward": 200, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 35, "fields": {"name": "Thirty-Artist Explorer", "description": "30 artists", "icon": "", "icon_name": null, "criteria": {"unique_artists": 30}, "type": "artist", "challenge_id": "thirty_artist_explorer", "tier": "T3", "xp_reward": 225, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 36, "fields": {"name": "Discography Diver", "description": "6 albums, same artist", "icon": "", "icon_name": null, "criteria": {"same_artist": true, "unique_albums": 6}, "type": "artist", "challenge_id": "discography_diver", "tier": "T4", "xp_reward": 300, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 37, "fields": {"name": "<PERSON><PERSON>", "description": "5 artists in 5 genres", "icon": "", "icon_name": null, "criteria": {"unique_genres": 5, "artists_per_genre": 5}, "type": "artist", "challenge_id": "genre_hopper", "tier": "T4", "xp_reward": 325, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 38, "fields": {"name": "Chart Tracker", "description": "Pin 3 chart-topping artists (API driven)", "icon": "", "icon_name": null, "criteria": {"api_verified": true, "chart_topping_artists": 3}, "type": "artist", "challenge_id": "chart_tracker", "tier": "T5", "xp_reward": 400, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 39, "fields": {"name": "Fifty-Artist Collection", "description": "50 artists", "icon": "", "icon_name": null, "criteria": {"unique_artists": 50}, "type": "artist", "challenge_id": "fifty_artist_collection", "tier": "T5", "xp_reward": 450, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 40, "fields": {"name": "Artist Evangelist", "description": "15 pins, same artist across ≥ 3 countries", "icon": "", "icon_name": null, "criteria": {"same_artist_pins": 15, "different_countries": 3}, "type": "artist", "challenge_id": "artist_evangelist", "tier": "T6", "xp_reward": 600, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 41, "fields": {"name": "Hundred-Artist Explorer", "description": "100 artists", "icon": "", "icon_name": null, "criteria": {"unique_artists": 100}, "type": "artist", "challenge_id": "hundred_artist_explorer", "tier": "T6", "xp_reward": 650, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 42, "fields": {"name": "Discography Master", "description": "10 albums, same artist", "icon": "", "icon_name": null, "criteria": {"same_artist": true, "unique_albums": 10}, "type": "artist", "challenge_id": "discography_master", "tier": "T7", "xp_reward": 800, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 43, "fields": {"name": "Worldwide Tastemaker", "description": "150 artists", "icon": "", "icon_name": null, "criteria": {"unique_artists": 150}, "type": "artist", "challenge_id": "worldwide_tastemaker", "tier": "T7", "xp_reward": 1000, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 44, "fields": {"name": "Early Adopter", "description": "Pin within 7 days of release", "icon": "", "icon_name": null, "criteria": {"days_since_release": 7}, "type": "artist", "challenge_id": "early_adopter", "tier": "SECRET", "xp_reward": 350, "is_secret": true, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 45, "fields": {"name": "First Flavor", "description": "Pin a track in any genre", "icon": "", "icon_name": null, "criteria": {"total_pins": 1}, "type": "genre", "challenge_id": "first_genre_pin", "tier": "T0", "xp_reward": 25, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 46, "fields": {"name": "Triple Dip", "description": "Pin tracks from 3 different genres", "icon": "", "icon_name": null, "criteria": {"unique_genres": 3}, "type": "genre", "challenge_id": "triple_genre", "tier": "T0", "xp_reward": 50, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 47, "fields": {"name": "Genre Devotee I", "description": "3 pins in the same genre", "icon": "", "icon_name": null, "criteria": {"same_genre_pins": 3}, "type": "genre", "challenge_id": "genre_devotee_I", "tier": "T1", "xp_reward": 75, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 48, "fields": {"name": "Five-Flavor Flight", "description": "5 unique genres total", "icon": "", "icon_name": null, "criteria": {"unique_genres": 5}, "type": "genre", "challenge_id": "five_flavors", "tier": "T1", "xp_reward": 100, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 49, "fields": {"name": "Genre Devotee II", "description": "7 pins, same genre (≥ 3 days)", "icon": "", "icon_name": null, "criteria": {"different_days": 3, "same_genre_pins": 7}, "type": "genre", "challenge_id": "genre_devotee_II", "tier": "T2", "xp_reward": 150, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 50, "fields": {"name": "Ten-<PERSON><PERSON><PERSON>", "description": "10 unique genres", "icon": "", "icon_name": null, "criteria": {"unique_genres": 10}, "type": "genre", "challenge_id": "ten_flavors", "tier": "T2", "xp_reward": 175, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 51, "fields": {"name": "Sub-Scene Explorer", "description": "Pin tracks from 3 subgenres inside one parent genre (e.g. \"Lo-fi House, Tech House, Deep House\")", "icon": "", "icon_name": null, "criteria": {"subgenres_in_parent": 3, "parent_genre_required": true}, "type": "genre", "challenge_id": "subgenre_explorer", "tier": "T3", "xp_reward": 200, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 52, "fields": {"name": "Fifteen-Flavor Tour", "description": "15 unique genres", "icon": "", "icon_name": null, "criteria": {"unique_genres": 15}, "type": "genre", "challenge_id": "fifteen_flavors", "tier": "T3", "xp_reward": 225, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 53, "fields": {"name": "Genre Devotee III", "description": "12 pins, same genre", "icon": "", "icon_name": null, "criteria": {"same_genre_pins": 12}, "type": "genre", "challenge_id": "genre_devotee_III", "tier": "T4", "xp_reward": 300, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 54, "fields": {"name": "Genre Mixer", "description": "Pin 2 genres in the same day (3 separate days)", "icon": "", "icon_name": null, "criteria": {"different_days": 3, "genres_per_day": 2}, "type": "genre", "challenge_id": "genre_mixer", "tier": "T4", "xp_reward": 325, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 55, "fields": {"name": "Regional Flavor", "description": "Pin 3 genres that originate from 3 different continents", "icon": "", "icon_name": null, "criteria": {"different_continents": true, "genres_from_continents": 3}, "type": "genre", "challenge_id": "regional_flavor", "tier": "T5", "xp_reward": 400, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 56, "fields": {"name": "Twenty-Flavor Collection", "description": "20 unique genres", "icon": "", "icon_name": null, "criteria": {"unique_genres": 20}, "type": "genre", "challenge_id": "twenty_flavors", "tier": "T5", "xp_reward": 450, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 57, "fields": {"name": "Era Explorer", "description": "Pin a track from 3 different decades inside the same genre", "icon": "", "icon_name": null, "criteria": {"same_genre": true, "decades_in_genre": 3}, "type": "genre", "challenge_id": "era_explorer", "tier": "T6", "xp_reward": 600, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 58, "fields": {"name": "Thirty-<PERSON><PERSON><PERSON>", "description": "30 unique genres", "icon": "", "icon_name": null, "criteria": {"unique_genres": 30}, "type": "genre", "challenge_id": "thirty_flavors", "tier": "T6", "xp_reward": 650, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 59, "fields": {"name": "Genre Master", "description": "20 pins in one genre plus 5 subgenres & 3 decades", "icon": "", "icon_name": null, "criteria": {"same_genre_pins": 20, "decades_in_genre": 3, "subgenres_in_parent": 5}, "type": "genre", "challenge_id": "genre_master", "tier": "T7", "xp_reward": 800, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 60, "fields": {"name": "Worldly Tastemaker", "description": "40 unique genres", "icon": "", "icon_name": null, "criteria": {"unique_genres": 40}, "type": "genre", "challenge_id": "worldly_tastemaker", "tier": "T7", "xp_reward": 1000, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 61, "fields": {"name": "Micro-Genre Maverick", "description": "Pin a track whose Spotify/Apple genre tag contains \"–wave\" or \"-core\"", "icon": "", "icon_name": null, "criteria": {"auto_reveal": true, "microgenre_pattern": ["-wave", "-core"]}, "type": "genre", "challenge_id": "microgenre_maverick", "tier": "SECRET", "xp_reward": 350, "is_secret": true, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 62, "fields": {"name": "First React", "description": "Give 1 reaction", "icon": "", "icon_name": null, "criteria": {"reactions_given": 1}, "type": "social", "challenge_id": "first_react", "tier": "T0", "xp_reward": 25, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 63, "fields": {"name": "Noticed", "description": "Get 1 reaction on one of your pins", "icon": "", "icon_name": null, "criteria": {"reactions_received": 1}, "type": "social", "challenge_id": "first_like_received", "tier": "T0", "xp_reward": 25, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 64, "fields": {"name": "Reaction Rookie", "description": "Give 25 reactions", "icon": "", "icon_name": null, "criteria": {"reactions_given": 25}, "type": "social", "challenge_id": "react_25_given", "tier": "T1", "xp_reward": 100, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 65, "fields": {"name": "Local Buzz", "description": "Get 5 reactions from 3 unique users", "icon": "", "icon_name": null, "criteria": {"unique_reactors": 3, "reactions_received": 5}, "type": "social", "challenge_id": "local_buzz", "tier": "T1", "xp_reward": 100, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 66, "fields": {"name": "Week-Long Hype", "description": "Give ≥ 1 reaction 7 days in a row", "icon": "", "icon_name": null, "criteria": {"daily_reactions": 1, "consecutive_days": 7}, "type": "social", "challenge_id": "react_streak_7", "tier": "T2", "xp_reward": 150, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 67, "fields": {"name": "Pass the Aux", "description": "Share 5 different pins", "icon": "", "icon_name": null, "criteria": {"unique_shares": 5}, "type": "social", "challenge_id": "shares_5", "tier": "T2", "xp_reward": 150, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 68, "fields": {"name": "<PERSON><PERSON>", "description": "Your pins replayed 25× total", "icon": "", "icon_name": null, "criteria": {"total_replays": 25}, "type": "social", "challenge_id": "replays_25", "tier": "T2", "xp_reward": 150, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 69, "fields": {"name": "Hype Machine", "description": "Give 100 reactions (lifetime)", "icon": "", "icon_name": null, "criteria": {"reactions_given": 100}, "type": "social", "challenge_id": "react_100_given", "tier": "T3", "xp_reward": 200, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 70, "fields": {"name": "Mini Hit", "description": "Any one pin earns 10 reactions + 5 replays", "icon": "", "icon_name": null, "criteria": {"single_pin_replays": 5, "single_pin_reactions": 10}, "type": "social", "challenge_id": "mini_hit", "tier": "T3", "xp_reward": 225, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 71, "fields": {"name": "Rising Star", "description": "15 unique users reacted to your pins", "icon": "", "icon_name": null, "criteria": {"unique_reactors": 15}, "type": "social", "challenge_id": "unique_supporters_15", "tier": "T3", "xp_reward": 225, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 72, "fields": {"name": "Crowd Favorite", "description": "Collect 100 reactions (lifetime)", "icon": "", "icon_name": null, "criteria": {"reactions_received": 100}, "type": "social", "challenge_id": "react_100_received", "tier": "T4", "xp_reward": 300, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 73, "fields": {"name": "Consistency King", "description": "React ≥ 1/day 15 consecutive days", "icon": "", "icon_name": null, "criteria": {"daily_reactions": 1, "consecutive_days": 15}, "type": "social", "challenge_id": "react_streak_15", "tier": "T4", "xp_reward": 300, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 74, "fields": {"name": "<PERSON> Booster", "description": "Share 20 different pins", "icon": "", "icon_name": null, "criteria": {"unique_shares": 20}, "type": "social", "challenge_id": "shares_20", "tier": "T4", "xp_reward": 300, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 75, "fields": {"name": "Bring the Crew", "description": "Refer 5 friends who each drop ≥ 3 pins", "icon": "", "icon_name": null, "criteria": {"pins_per_referral": 3, "successful_referrals": 5}, "type": "social", "challenge_id": "referrals_5", "tier": "T5", "xp_reward": 400, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 76, "fields": {"name": "Ripple Effect", "description": "40 unique users reacted to your pins", "icon": "", "icon_name": null, "criteria": {"unique_reactors": 40}, "type": "social", "challenge_id": "unique_supporters_40", "tier": "T5", "xp_reward": 450, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 77, "fields": {"name": "Super Hype", "description": "Give 500 reactions total", "icon": "", "icon_name": null, "criteria": {"reactions_given": 500}, "type": "social", "challenge_id": "react_500_given", "tier": "T6", "xp_reward": 600, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 78, "fields": {"name": "Multi-Hit Maker", "description": "3 pins with ≥ 25 reactions each", "icon": "", "icon_name": null, "criteria": {"reactions_per_pin": 25, "pins_with_reactions": 3}, "type": "social", "challenge_id": "multi_hit", "tier": "T6", "xp_reward": 650, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 79, "fields": {"name": "Repeat Spin", "description": "Your pins replayed 150× total", "icon": "", "icon_name": null, "criteria": {"total_replays": 150}, "type": "social", "challenge_id": "replays_150", "tier": "T6", "xp_reward": 650, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 80, "fields": {"name": "Social Icon", "description": "350 reactions received + 75 unique supporters", "icon": "", "icon_name": null, "criteria": {"unique_reactors": 75, "reactions_received": 350}, "type": "social", "challenge_id": "react_350_received", "tier": "T7", "xp_reward": 800, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 81, "fields": {"name": "Pinfluence Peak", "description": "One pin hits 25 reactions & 50 replays within 30 days", "icon": "", "icon_name": null, "criteria": {"time_window_days": 30, "single_pin_replays": 50, "single_pin_reactions": 25}, "type": "social", "challenge_id": "viral_pin", "tier": "T7", "xp_reward": 1000, "is_secret": false, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 82, "fields": {"name": "Silent Impact", "description": "A Legend-rank user reacts to your pin", "icon": "", "icon_name": null, "criteria": {"auto_reveal": true, "legend_rank_reaction": true}, "type": "social", "challenge_id": "legend_like", "tier": "SECRET", "xp_reward": 350, "is_secret": true, "created_at": "2025-06-17T03:16:16.095Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.achievement", "pk": 83, "fields": {"name": "Weekly Challenger", "description": "Participate in weekly challenges", "icon": "", "icon_name": null, "criteria": {"weekly_participations": 1}, "type": "social", "challenge_id": "weekly_participation", "tier": "T1", "xp_reward": 50, "is_secret": false, "created_at": "2025-06-20T20:08:46.635Z", "background_color": "#FFFFFF", "primary_color": "#000000", "reward_skin": null}}, {"model": "gamification.userachievement", "pk": 1, "fields": {"user": 2, "achievement": 83, "progress": {"weekly_participations": 19}, "completed_at": "2025-06-17T22:29:37.808Z", "last_updated": "2025-06-22T00:41:59.975Z"}}, {"model": "gamification.userachievement", "pk": 2, "fields": {"user": 1, "achievement": 1, "progress": {"total_pins": 1, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "pins_in_radius": 1, "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.602Z"}}, {"model": "gamification.userachievement", "pk": 3, "fields": {"user": 1, "achievement": 2, "progress": {"total_pins": 1, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.609Z"}}, {"model": "gamification.userachievement", "pk": 4, "fields": {"user": 1, "achievement": 3, "progress": {"artist_albums": {"Taylor Swift": ["1989"]}, "unique_albums": 1, "artists_played": ["<PERSON>"], "unique_artists": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.615Z"}}, {"model": "gamification.userachievement", "pk": 5, "fields": {"user": 1, "achievement": 4, "progress": {"artist_albums": {"Taylor Swift": ["1989"]}, "unique_albums": 1, "artists_played": ["<PERSON>"], "unique_artists": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.621Z"}}, {"model": "gamification.userachievement", "pk": 6, "fields": {"user": 1, "achievement": 5, "progress": {"genres_played": ["pop"], "unique_genres": 1, "same_genre_pins": 1, "genre_pin_counts": {"pop": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.626Z"}}, {"model": "gamification.userachievement", "pk": 7, "fields": {"user": 1, "achievement": 6, "progress": {"genres_played": ["pop"], "unique_genres": 1, "same_genre_pins": 1, "genre_pin_counts": {"pop": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.631Z"}}, {"model": "gamification.userachievement", "pk": 8, "fields": {"user": 1, "achievement": 7, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.637Z"}}, {"model": "gamification.userachievement", "pk": 9, "fields": {"user": 1, "achievement": 8, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.642Z"}}, {"model": "gamification.userachievement", "pk": 10, "fields": {"user": 1, "achievement": 9, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.648Z"}}, {"model": "gamification.userachievement", "pk": 11, "fields": {"user": 1, "achievement": 10, "progress": {"total_pins": 1, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.653Z"}}, {"model": "gamification.userachievement", "pk": 12, "fields": {"user": 1, "achievement": 11, "progress": {"total_pins": 1}, "completed_at": "2025-07-03T23:38:18.661Z", "last_updated": "2025-07-03T23:38:18.661Z"}}, {"model": "gamification.userachievement", "pk": 13, "fields": {"user": 1, "achievement": 12, "progress": {"total_pins": 1, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "pins_in_radius": 1, "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.666Z"}}, {"model": "gamification.userachievement", "pk": 14, "fields": {"user": 1, "achievement": 13, "progress": {"pin_dates": ["2025-07-03"], "different_days": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.671Z"}}, {"model": "gamification.userachievement", "pk": 15, "fields": {"user": 1, "achievement": 14, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.677Z"}}, {"model": "gamification.userachievement", "pk": 16, "fields": {"user": 1, "achievement": 15, "progress": {"total_pins": 1, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.682Z"}}, {"model": "gamification.userachievement", "pk": 17, "fields": {"user": 1, "achievement": 16, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.687Z"}}, {"model": "gamification.userachievement", "pk": 18, "fields": {"user": 1, "achievement": 17, "progress": {"total_pins": 1, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.692Z"}}, {"model": "gamification.userachievement", "pk": 19, "fields": {"user": 1, "achievement": 18, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.697Z"}}, {"model": "gamification.userachievement", "pk": 20, "fields": {"user": 1, "achievement": 19, "progress": {"total_pins": 1, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.702Z"}}, {"model": "gamification.userachievement", "pk": 21, "fields": {"user": 1, "achievement": 20, "progress": {"total_pins": 1, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.707Z"}}, {"model": "gamification.userachievement", "pk": 22, "fields": {"user": 1, "achievement": 21, "progress": {"total_pins": 1, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.711Z"}}, {"model": "gamification.userachievement", "pk": 23, "fields": {"user": 1, "achievement": 22, "progress": {"total_pins": 1, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.716Z"}}, {"model": "gamification.userachievement", "pk": 24, "fields": {"user": 1, "achievement": 23, "progress": {"total_pins": 1, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.721Z"}}, {"model": "gamification.userachievement", "pk": 25, "fields": {"user": 1, "achievement": 24, "progress": {"total_pins": 1, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.727Z"}}, {"model": "gamification.userachievement", "pk": 26, "fields": {"user": 1, "achievement": 25, "progress": {"total_pins": 2, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.732Z"}}, {"model": "gamification.userachievement", "pk": 27, "fields": {"user": 1, "achievement": 26, "progress": {"total_pins": 1, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.737Z"}}, {"model": "gamification.userachievement", "pk": 28, "fields": {"user": 1, "achievement": 27, "progress": {"no_prior_pins_verified": false}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.741Z"}}, {"model": "gamification.userachievement", "pk": 29, "fields": {"user": 1, "achievement": 28, "progress": {"total_pins": 1}, "completed_at": "2025-07-03T23:38:18.748Z", "last_updated": "2025-07-03T23:38:18.748Z"}}, {"model": "gamification.userachievement", "pk": 30, "fields": {"user": 1, "achievement": 29, "progress": {"artist_albums": {"Taylor Swift": ["1989"]}, "unique_albums": 1, "artists_played": ["<PERSON>"], "unique_artists": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.753Z"}}, {"model": "gamification.userachievement", "pk": 31, "fields": {"user": 1, "achievement": 30, "progress": {"artist_albums": {"Taylor Swift": ["1989"]}, "unique_albums": 1, "artists_played": ["<PERSON>"], "unique_artists": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.759Z"}}, {"model": "gamification.userachievement", "pk": 32, "fields": {"user": 1, "achievement": 31, "progress": {"artist_albums": {"Taylor Swift": ["1989"]}, "unique_albums": 1, "artists_played": ["<PERSON>"], "unique_artists": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.763Z"}}, {"model": "gamification.userachievement", "pk": 33, "fields": {"user": 1, "achievement": 32, "progress": {"pin_dates": ["2025-07-03"], "artist_albums": {"Taylor Swift": ["1989"]}, "unique_albums": 1, "artists_played": ["<PERSON>"], "different_days": 1, "unique_artists": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.769Z"}}, {"model": "gamification.userachievement", "pk": 34, "fields": {"user": 1, "achievement": 33, "progress": {"artist_albums": {"Taylor Swift": ["1989"]}, "unique_albums": 1, "artists_played": ["<PERSON>"], "unique_artists": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.774Z"}}, {"model": "gamification.userachievement", "pk": 35, "fields": {"user": 1, "achievement": 34, "progress": {"artist_albums": {"Taylor Swift": ["1989"]}, "unique_albums": 1, "artists_played": ["<PERSON>"], "unique_artists": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.778Z"}}, {"model": "gamification.userachievement", "pk": 36, "fields": {"user": 1, "achievement": 35, "progress": {"artist_albums": {"Taylor Swift": ["1989"]}, "unique_albums": 1, "artists_played": ["<PERSON>"], "unique_artists": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.784Z"}}, {"model": "gamification.userachievement", "pk": 37, "fields": {"user": 1, "achievement": 36, "progress": {"artist_albums": {"Taylor Swift": ["1989"]}, "unique_albums": 1, "artists_played": ["<PERSON>"], "unique_artists": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.789Z"}}, {"model": "gamification.userachievement", "pk": 38, "fields": {"user": 1, "achievement": 37, "progress": {"genre_artists": {"pop": ["<PERSON>"]}, "genres_played": ["pop"], "unique_genres": 1, "same_genre_pins": 1, "genre_pin_counts": {"pop": 1}, "genres_with_enough_artists": 0}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.795Z"}}, {"model": "gamification.userachievement", "pk": 39, "fields": {"user": 1, "achievement": 38, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.800Z"}}, {"model": "gamification.userachievement", "pk": 40, "fields": {"user": 1, "achievement": 39, "progress": {"artist_albums": {"Taylor Swift": ["1989"]}, "unique_albums": 1, "artists_played": ["<PERSON>"], "unique_artists": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.804Z"}}, {"model": "gamification.userachievement", "pk": 41, "fields": {"user": 1, "achievement": 40, "progress": {"total_pins": 1, "artist_albums": {"Taylor Swift": ["1989"]}, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "unique_albums": 1, "artists_played": ["<PERSON>"], "cities_visited": ["New York"], "states_visited": ["New York"], "unique_artists": 1, "different_cities": 1, "different_states": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.809Z"}}, {"model": "gamification.userachievement", "pk": 42, "fields": {"user": 1, "achievement": 41, "progress": {"artist_albums": {"Taylor Swift": ["1989"]}, "unique_albums": 1, "artists_played": ["<PERSON>"], "unique_artists": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.813Z"}}, {"model": "gamification.userachievement", "pk": 43, "fields": {"user": 1, "achievement": 42, "progress": {"artist_albums": {"Taylor Swift": ["1989"]}, "unique_albums": 1, "artists_played": ["<PERSON>"], "unique_artists": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.817Z"}}, {"model": "gamification.userachievement", "pk": 44, "fields": {"user": 1, "achievement": 43, "progress": {"artist_albums": {"Taylor Swift": ["1989"]}, "unique_albums": 1, "artists_played": ["<PERSON>"], "unique_artists": 1, "same_artist_pins": 1, "artist_pin_counts": {"Taylor Swift": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.821Z"}}, {"model": "gamification.userachievement", "pk": 45, "fields": {"user": 1, "achievement": 44, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.825Z"}}, {"model": "gamification.userachievement", "pk": 46, "fields": {"user": 1, "achievement": 45, "progress": {"total_pins": 1}, "completed_at": "2025-07-03T23:38:18.832Z", "last_updated": "2025-07-03T23:38:18.833Z"}}, {"model": "gamification.userachievement", "pk": 47, "fields": {"user": 1, "achievement": 46, "progress": {"genres_played": ["pop"], "unique_genres": 1, "same_genre_pins": 1, "genre_pin_counts": {"pop": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.837Z"}}, {"model": "gamification.userachievement", "pk": 48, "fields": {"user": 1, "achievement": 47, "progress": {"genres_played": ["pop"], "unique_genres": 1, "same_genre_pins": 1, "genre_pin_counts": {"pop": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.840Z"}}, {"model": "gamification.userachievement", "pk": 49, "fields": {"user": 1, "achievement": 48, "progress": {"genres_played": ["pop"], "unique_genres": 1, "same_genre_pins": 1, "genre_pin_counts": {"pop": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.844Z"}}, {"model": "gamification.userachievement", "pk": 50, "fields": {"user": 1, "achievement": 49, "progress": {"pin_dates": ["2025-07-03"], "genres_played": ["pop"], "unique_genres": 1, "different_days": 1, "same_genre_pins": 1, "genre_pin_counts": {"pop": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.848Z"}}, {"model": "gamification.userachievement", "pk": 51, "fields": {"user": 1, "achievement": 50, "progress": {"genres_played": ["pop"], "unique_genres": 1, "same_genre_pins": 1, "genre_pin_counts": {"pop": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.852Z"}}, {"model": "gamification.userachievement", "pk": 52, "fields": {"user": 1, "achievement": 51, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.856Z"}}, {"model": "gamification.userachievement", "pk": 53, "fields": {"user": 1, "achievement": 52, "progress": {"genres_played": ["pop"], "unique_genres": 1, "same_genre_pins": 1, "genre_pin_counts": {"pop": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.860Z"}}, {"model": "gamification.userachievement", "pk": 54, "fields": {"user": 1, "achievement": 53, "progress": {"genres_played": ["pop"], "unique_genres": 1, "same_genre_pins": 1, "genre_pin_counts": {"pop": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.864Z"}}, {"model": "gamification.userachievement", "pk": 55, "fields": {"user": 1, "achievement": 54, "progress": {"pin_dates": ["2025-07-03"], "different_days": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.868Z"}}, {"model": "gamification.userachievement", "pk": 56, "fields": {"user": 1, "achievement": 55, "progress": {"total_pins": 1, "big_city_pins": 1, "pin_locations": [[40.7128, -74.006]], "cities_visited": ["New York"], "states_visited": ["New York"], "different_cities": 1, "different_states": 1, "countries_visited": ["United States"], "districts_visited": ["Manhattan"], "visited_continents": ["NA"], "different_countries": 1, "different_districts": 1, "different_continents": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.871Z"}}, {"model": "gamification.userachievement", "pk": 57, "fields": {"user": 1, "achievement": 56, "progress": {"genres_played": ["pop"], "unique_genres": 1, "same_genre_pins": 1, "genre_pin_counts": {"pop": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.875Z"}}, {"model": "gamification.userachievement", "pk": 58, "fields": {"user": 1, "achievement": 57, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.879Z"}}, {"model": "gamification.userachievement", "pk": 59, "fields": {"user": 1, "achievement": 58, "progress": {"genres_played": ["pop"], "unique_genres": 1, "same_genre_pins": 1, "genre_pin_counts": {"pop": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.884Z"}}, {"model": "gamification.userachievement", "pk": 60, "fields": {"user": 1, "achievement": 59, "progress": {"genres_played": ["pop"], "unique_genres": 1, "same_genre_pins": 1, "genre_pin_counts": {"pop": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.887Z"}}, {"model": "gamification.userachievement", "pk": 61, "fields": {"user": 1, "achievement": 60, "progress": {"genres_played": ["pop"], "unique_genres": 1, "same_genre_pins": 1, "genre_pin_counts": {"pop": 1}}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.891Z"}}, {"model": "gamification.userachievement", "pk": 62, "fields": {"user": 1, "achievement": 61, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.895Z"}}, {"model": "gamification.userachievement", "pk": 63, "fields": {"user": 1, "achievement": 62, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.899Z"}}, {"model": "gamification.userachievement", "pk": 64, "fields": {"user": 1, "achievement": 63, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.903Z"}}, {"model": "gamification.userachievement", "pk": 65, "fields": {"user": 1, "achievement": 64, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.907Z"}}, {"model": "gamification.userachievement", "pk": 66, "fields": {"user": 1, "achievement": 65, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.911Z"}}, {"model": "gamification.userachievement", "pk": 67, "fields": {"user": 1, "achievement": 66, "progress": {"max_streak": 1, "last_pin_date": "2025-07-03", "current_streak": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.915Z"}}, {"model": "gamification.userachievement", "pk": 68, "fields": {"user": 1, "achievement": 67, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.919Z"}}, {"model": "gamification.userachievement", "pk": 69, "fields": {"user": 1, "achievement": 68, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.923Z"}}, {"model": "gamification.userachievement", "pk": 70, "fields": {"user": 1, "achievement": 69, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.927Z"}}, {"model": "gamification.userachievement", "pk": 71, "fields": {"user": 1, "achievement": 70, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.930Z"}}, {"model": "gamification.userachievement", "pk": 72, "fields": {"user": 1, "achievement": 71, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.934Z"}}, {"model": "gamification.userachievement", "pk": 73, "fields": {"user": 1, "achievement": 72, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.938Z"}}, {"model": "gamification.userachievement", "pk": 74, "fields": {"user": 1, "achievement": 73, "progress": {"max_streak": 1, "last_pin_date": "2025-07-03", "current_streak": 1}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.942Z"}}, {"model": "gamification.userachievement", "pk": 75, "fields": {"user": 1, "achievement": 74, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.946Z"}}, {"model": "gamification.userachievement", "pk": 76, "fields": {"user": 1, "achievement": 75, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.950Z"}}, {"model": "gamification.userachievement", "pk": 77, "fields": {"user": 1, "achievement": 76, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.953Z"}}, {"model": "gamification.userachievement", "pk": 78, "fields": {"user": 1, "achievement": 77, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.957Z"}}, {"model": "gamification.userachievement", "pk": 79, "fields": {"user": 1, "achievement": 78, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.961Z"}}, {"model": "gamification.userachievement", "pk": 80, "fields": {"user": 1, "achievement": 79, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.965Z"}}, {"model": "gamification.userachievement", "pk": 81, "fields": {"user": 1, "achievement": 80, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.969Z"}}, {"model": "gamification.userachievement", "pk": 82, "fields": {"user": 1, "achievement": 81, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.973Z"}}, {"model": "gamification.userachievement", "pk": 83, "fields": {"user": 1, "achievement": 82, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.977Z"}}, {"model": "gamification.userachievement", "pk": 84, "fields": {"user": 1, "achievement": 83, "progress": {}, "completed_at": null, "last_updated": "2025-07-03T23:38:18.981Z"}}]