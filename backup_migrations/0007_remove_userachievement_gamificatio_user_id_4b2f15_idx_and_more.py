# Generated by Django 4.2.7 on 2025-06-20 20:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("pins", "0014_alter_pinskin_image"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("gamification", "0006_add_ui_fields"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="userachievement",
            name="gamificatio_user_id_4b2f15_idx",
        ),
        migrations.RemoveIndex(
            model_name="userachievement",
            name="gamificatio_complet_82f63a_idx",
        ),
        migrations.RenameIndex(
            model_name="achievement",
            new_name="gamificatio_created_12555f_idx",
            old_name="gamificatio_created_123abc_idx",
        ),
        migrations.AlterField(
            model_name="achievement",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="achievement",
            name="icon",
            field=models.ImageField(blank=True, null=True, upload_to="achievements/"),
        ),
        migrations.AlterField(
            model_name="achievement",
            name="reward_skin",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="achievement_rewards",
                to="pins.pinskin",
            ),
        ),
        migrations.AlterField(
            model_name="userachievement",
            name="achievement",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="gamification.achievement",
            ),
        ),
        migrations.AlterField(
            model_name="userachievement",
            name="progress",
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AlterField(
            model_name="userachievement",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddIndex(
            model_name="userachievement",
            index=models.Index(
                fields=["user", "completed_at"], name="gamificatio_user_id_3288d8_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="userachievement",
            index=models.Index(
                fields=["achievement"], name="gamificatio_achieve_e7ca42_idx"
            ),
        ),
        migrations.DeleteModel(
            name="PinSkin",
        ),
    ]
