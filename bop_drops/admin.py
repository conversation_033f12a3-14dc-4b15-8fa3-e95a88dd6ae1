from django.contrib import admin
from .models import BopDrop, BopDropLike, BopDropView


@admin.register(BopDrop)
class BopDropAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'user', 'track_title', 'track_artist', 'mood', 
        'is_active', 'like_count', 'view_count', 'created_at'
    ]
    list_filter = [
        'is_active', 'mood', 'music_service', 'friends_only', 
        'is_currently_playing', 'created_at'
    ]
    search_fields = [
        'track_title', 'track_artist', 'track_album', 'caption',
        'user__username', 'user__first_name', 'user__last_name'
    ]
    readonly_fields = [
        'created_at', 'updated_at', 'like_count', 'view_count', 'share_count'
    ]
    ordering = ['-created_at']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('User & Track Info', {
            'fields': ('user', 'track_id', 'track_title', 'track_artist', 'track_album')
        }),
        ('Track Details', {
            'fields': ('track_duration_ms', 'album_art_url', 'preview_url', 'music_service')
        }),
        ('Status Details', {
            'fields': (
                'caption', 'mood', 'is_currently_playing', 
                'friends_only', 'is_active', 'expires_at'
            )
        }),
        ('Analytics', {
            'fields': ('like_count', 'view_count', 'share_count'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(BopDropLike)
class BopDropLikeAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'bop_drop_title', 'bop_drop_artist', 'created_at']
    list_filter = ['created_at']
    search_fields = [
        'user__username', 'bop_drop__track_title', 'bop_drop__track_artist'
    ]
    readonly_fields = ['created_at']
    ordering = ['-created_at']
    date_hierarchy = 'created_at'
    
    def bop_drop_title(self, obj):
        return obj.bop_drop.track_title
    bop_drop_title.short_description = 'Track Title'
    
    def bop_drop_artist(self, obj):
        return obj.bop_drop.track_artist
    bop_drop_artist.short_description = 'Artist'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'bop_drop')


@admin.register(BopDropView)
class BopDropViewAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'bop_drop_title', 'bop_drop_artist', 'viewed_at']
    list_filter = ['viewed_at']
    search_fields = [
        'user__username', 'bop_drop__track_title', 'bop_drop__track_artist'
    ]
    readonly_fields = ['viewed_at']
    ordering = ['-viewed_at']
    date_hierarchy = 'viewed_at'
    
    def bop_drop_title(self, obj):
        return obj.bop_drop.track_title
    bop_drop_title.short_description = 'Track Title'
    
    def bop_drop_artist(self, obj):
        return obj.bop_drop.track_artist
    bop_drop_artist.short_description = 'Artist'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'bop_drop')
