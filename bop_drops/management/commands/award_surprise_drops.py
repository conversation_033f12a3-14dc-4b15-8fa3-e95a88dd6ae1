#!/usr/bin/env python
"""
Django management command to award surprise drop skins to random active users
Can be run periodically (e.g., via cron) to create surprise moments
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Q, Count
from datetime import timed<PERSON>ta
import random
from users.models import User
from pins.models import Pin<PERSON><PERSON>, UserSkin, Pin
from notifications.utils import notify_skin_unlocked


class Command(BaseCommand):
    help = 'Award surprise drop skins to random active users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=5,
            help='Number of users to award surprise drops to',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would happen without actually awarding',
        )
        parser.add_argument(
            '--min-activity-days',
            type=int,
            default=7,
            help='Consider users active in the last N days',
        )

    def handle(self, *args, **options):
        count = options['count']
        dry_run = options['dry_run']
        activity_days = options['min_activity_days']
        
        if dry_run:
            self.stdout.write('🔍 DRY RUN MODE - No changes will be made')
        
        self.stdout.write(f'🎁 Awarding {count} surprise drops to active users...')
        
        # Get the surprise drop skin
        surprise_skin = PinSkin.objects.filter(
            metadata__unlock_type='BOP_DROP'
        ).first()
        
        if not surprise_skin:
            self.stdout.write(self.style.ERROR('❌ No surprise drop skin found!'))
            return
        
        self.stdout.write(f'📌 Using skin: {surprise_skin.name}')
        
        # Find eligible users (active in last N days, don't already have the skin)
        cutoff_date = timezone.now() - timedelta(days=activity_days)
        
        # Users who have the skin
        users_with_skin = UserSkin.objects.filter(
            skin=surprise_skin
        ).values_list('user_id', flat=True)
        
        # Active users who don't have the skin
        eligible_users = User.objects.filter(
            # Active in last N days (created pins)
            Q(pins__created_at__gte=cutoff_date) |
            # Or interacted with pins
            Q(pin_interactions__created_at__gte=cutoff_date) |
            # Or commented
            Q(comments__created_at__gte=cutoff_date)
        ).exclude(
            id__in=users_with_skin
        ).annotate(
            recent_activity_count=Count('pins', filter=Q(pins__created_at__gte=cutoff_date))
        ).distinct()
        
        self.stdout.write(f'📊 Found {eligible_users.count()} eligible users')
        
        if eligible_users.count() == 0:
            self.stdout.write('⚠️  No eligible users found')
            return
        
        # Randomly select users
        selected_count = min(count, eligible_users.count())
        selected_users = random.sample(list(eligible_users), selected_count)
        
        awarded_count = 0
        
        for user in selected_users:
            if dry_run:
                self.stdout.write(
                    f'   🎯 Would award to: {user.username} '
                    f'(recent pins: {user.recent_activity_count})'
                )
            else:
                try:
                    # Create UserSkin entry
                    UserSkin.objects.create(
                        user=user,
                        skin=surprise_skin
                    )
                    
                    # Send notification
                    notify_skin_unlocked(
                        user=user,
                        skin_name=surprise_skin.name,
                        unlock_reason="🎉 Surprise BOP Drop! You've been randomly selected!"
                    )
                    
                    awarded_count += 1
                    self.stdout.write(
                        f'   ✅ Awarded to: {user.username} '
                        f'(recent pins: {user.recent_activity_count})'
                    )
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(
                            f'   ❌ Failed to award to {user.username}: {str(e)}'
                        )
                    )
        
        if not dry_run:
            self.stdout.write(f'\n🎉 Successfully awarded {awarded_count} surprise drops!')
        else:
            self.stdout.write(f'\n🔍 Would have awarded {selected_count} surprise drops') 