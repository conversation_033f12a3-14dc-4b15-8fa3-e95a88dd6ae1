import random
from datetime import timed<PERSON><PERSON>
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction
from django.db import models

from bop_drops.models import BopDrop, BopDropLike, BopDropView
from friends.models import Friend

User = get_user_model()


class Command(BaseCommand):
    help = 'Generate sample bop drops for testing and demonstration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--users',
            type=int,
            default=10,
            help='Number of test users to create (default: 10)'
        )
        parser.add_argument(
            '--drops',
            type=int,
            default=50,
            help='Number of bop drops to create (default: 50)'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing bop drops before generating new ones'
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write(self.style.WARNING('Clearing existing bop drops...'))
            BopDrop.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Cleared existing bop drops'))

        with transaction.atomic():
            users = self.create_test_users(options['users'])
            self.create_friendships(users)
            bop_drops = self.create_bop_drops(users, options['drops'])
            self.create_engagement(users, bop_drops)

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully generated {len(bop_drops)} bop drops for {len(users)} users'
            )
        )

    def create_test_users(self, count):
        """Create test users if they don't exist"""
        users = []
        usernames = [
            'musiclover', 'beatdrop', 'melodymaven', 'rhythmrider', 'soundseeker',
            'vibecheck', 'tunechaser', 'basshead', 'synthwave', 'jazzcat',
            'rockstar', 'popqueen', 'hiphophead', 'indiekid', 'classicfan',
            'electrobeats', 'folkwise', 'metalcore', 'soulseeker', 'funkmaster'
        ]
        
        for i in range(min(count, len(usernames))):
            username = usernames[i]
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': f'{username}@example.com',
                    'first_name': username.capitalize(),
                    'last_name': 'User',
                }
            )
            if created:
                user.set_password('testpass123')
                user.save()
                self.stdout.write(f'Created user: {username}')
            else:
                self.stdout.write(f'Using existing user: {username}')
            users.append(user)
        
        return users

    def create_friendships(self, users):
        """Create friendships between users"""
        friendships_created = 0
        
        for i, user1 in enumerate(users):
            # Each user becomes friends with 3-7 other users
            num_friends = random.randint(3, min(7, len(users) - 1))
            potential_friends = [u for u in users if u != user1]
            friends = random.sample(potential_friends, num_friends)
            
            for user2 in friends:
                # Check if friendship already exists
                existing = Friend.objects.filter(
                    (models.Q(requester=user1, recipient=user2) | 
                     models.Q(requester=user2, recipient=user1))
                ).first()
                
                if not existing:
                    Friend.objects.create(
                        requester=user1,
                        recipient=user2,
                        status='accepted'
                    )
                    friendships_created += 1

        self.stdout.write(f'Created {friendships_created} friendships')

    def create_bop_drops(self, users, count):
        """Create diverse bop drops"""
        # Sample tracks with realistic data
        sample_tracks = [
            {
                'track_id': 'spotify:track:4iV5W9uYEdYUVa79Axb7Rh',
                'track_title': 'Never Gonna Give You Up',
                'track_artist': 'Rick Astley',
                'track_album': 'Whenever You Need Somebody',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273a2f3a75d5c8c9a8b97ad3c8c',
                'mood': 'nostalgic'
            },
            {
                'track_id': 'spotify:track:7ouMYWpwJ422jRcDASZB7P',
                'track_title': 'Bohemian Rhapsody',
                'track_artist': 'Queen',
                'track_album': 'A Night at the Opera',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273e319baafd16e84f0408af2a0',
                'mood': 'energetic'
            },
            {
                'track_id': 'spotify:track:5ChkMS8OtdzJeqyybCc9R5',
                'track_title': 'Watermelon Sugar',
                'track_artist': 'Harry Styles',
                'track_album': 'Fine Line',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273adce0bc2b69a6c5d97d6756a',
                'mood': 'happy'
            },
            {
                'track_id': 'spotify:track:1Je1IMUlBXcx1Fz0WE7oPT',
                'track_title': 'Bad Guy',
                'track_artist': 'Billie Eilish',
                'track_album': 'When We All Fall Asleep, Where Do We Go?',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273e7ad110d43c6d6ac8f8ead4d',
                'mood': 'chill'
            },
            {
                'track_id': 'spotify:track:4LRPiXqCikLlN15c3yImP7',
                'track_title': 'As It Was',
                'track_artist': 'Harry Styles',
                'track_album': 'Harry\'s House',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273b46f74097655d7f353caab14',
                'mood': 'romantic'
            },
            {
                'track_id': 'spotify:track:6dOtVTDdiauQNBQEDOtlAB',
                'track_title': 'Good 4 U',
                'track_artist': 'Olivia Rodrigo',
                'track_album': 'SOUR',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273a91c10fe9472d9bd89802e5a',
                'mood': 'angry'
            },
            {
                'track_id': 'spotify:track:0VjIjW4GlULA8N0EV8Fl1P',
                'track_title': 'Blinding Lights',
                'track_artist': 'The Weeknd',
                'track_album': 'After Hours',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b2738863bc11d2aa12b54f5aeb36',
                'mood': 'party'
            },
            {
                'track_id': 'spotify:track:2plbrEY59IikOBgBGLjaoe',
                'track_title': 'Drivers License',
                'track_artist': 'Olivia Rodrigo',
                'track_album': 'SOUR',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273a91c10fe9472d9bd89802e5a',
                'mood': 'sad'
            },
            {
                'track_id': 'spotify:track:4u7EnebtmKWzUH433cf5Qv',
                'track_title': 'Industry Baby',
                'track_artist': 'Lil Nas X',
                'track_album': 'MONTERO',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273be82673b5f79d9658ec0a9fd',
                'mood': 'energetic'
            },
            {
                'track_id': 'spotify:track:0tgVpDi06FyKpA1z0VMD4v',
                'track_title': 'Peaches',
                'track_artist': 'Justin Bieber',
                'track_album': 'Justice',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273564b3c4f67ea500ab4dd9a85',
                'mood': 'chill'
            },
            {
                'track_id': 'spotify:track:1BxfuPKGuaTgP7aM0Bbdwr',
                'track_title': 'Circles',
                'track_artist': 'Post Malone',
                'track_album': 'Hollywood\'s Bleeding',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273b1c4b76e23414c9f20242268',
                'mood': 'focus'
            },
            {
                'track_id': 'spotify:track:7I7JbDfeZRDkzE2b7iGVdM',
                'track_title': 'Say So',
                'track_artist': 'Doja Cat',
                'track_album': 'Hot Pink',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273f096ff29dc7e8b14476c7ee3',
                'mood': 'party'
            },
        ]

        # Sample captions
        sample_captions = [
            "This song hits different 🔥",
            "Can't stop listening to this!",
            "Perfect vibes for today ✨",
            "This is my new obsession",
            "Y'all need to hear this 🎵",
            "Soundtrack to my day",
            "This song speaks to my soul",
            "Dancing to this RN 💃",
            "Such a mood",
            "This beat is everything",
            "Throwback but make it relevant",
            "On repeat all day",
            "Pure musical gold",
            "This artist never misses",
            "Feeling this energy",
            "",  # Some without captions
            "",
            ""
        ]

        bop_drops = []
        
        for i in range(count):
            user = random.choice(users)
            track = random.choice(sample_tracks)
            caption = random.choice(sample_captions)
            
            # Create bop drop at various times in the past (last 7 days)
            hours_ago = random.randint(0, 168)  # Last 7 days
            created_time = timezone.now() - timedelta(hours=hours_ago)
            
            bop_drop = BopDrop.objects.create(
                user=user,
                track_id=track['track_id'],
                track_title=track['track_title'],
                track_artist=track['track_artist'],
                track_album=track['track_album'],
                album_art_url=track['album_art_url'],
                music_service='spotify',
                caption=caption if caption else None,
                mood=track['mood'],
                is_currently_playing=random.choice([True, False, False, False]),  # 25% chance
                friends_only=random.choice([True, True, True, False]),  # 75% friends only
                created_at=created_time,
                expires_at=created_time + timedelta(hours=24)
            )
            bop_drops.append(bop_drop)

        self.stdout.write(f'Created {len(bop_drops)} bop drops')
        return bop_drops

    def create_engagement(self, users, bop_drops):
        """Create likes and views to simulate engagement"""
        likes_created = 0
        views_created = 0
        
        for bop_drop in bop_drops:
            # Get user's friends for realistic engagement
            friends = []
            friendships = Friend.objects.filter(
                (models.Q(requester=bop_drop.user) | models.Q(recipient=bop_drop.user)),
                status='accepted'
            )
            
            for friendship in friendships:
                friend = friendship.recipient if friendship.requester == bop_drop.user else friendship.requester
                friends.append(friend)
            
            if not friends:
                continue
                
            # Random engagement from friends
            num_viewers = random.randint(0, min(len(friends), 8))
            viewers = random.sample(friends, num_viewers)
            
            for viewer in viewers:
                # Create view
                view, created = BopDropView.objects.get_or_create(
                    user=viewer,
                    bop_drop=bop_drop
                )
                if created:
                    views_created += 1
                    bop_drop.view_count += 1
                
                # 30% chance to like if they viewed
                if random.random() < 0.3:
                    like, created = BopDropLike.objects.get_or_create(
                        user=viewer,
                        bop_drop=bop_drop
                    )
                    if created:
                        likes_created += 1
                        bop_drop.like_count += 1
            
            bop_drop.save()

        self.stdout.write(f'Created {likes_created} likes and {views_created} views')

    def success_message(self):
        return """
🎵 Bop Drops Generator Complete! 🎵

Your database now has sample data including:
- Test users with realistic usernames
- Friendships between users  
- Diverse bop drops with real song data
- Engagement (likes and views) from friends

Try these API endpoints to see the data:
- GET /api/bop-drops/feed/ - See the friends' feed
- GET /api/bop-drops/shuffle/ - Random shuffle
- GET /api/bop-drops/trending/ - Trending drops
- GET /api/bop-drops/my_stats/ - Your statistics

Have fun testing the bop drops feature! 🚀
        """ 