import random
from datetime import timed<PERSON><PERSON>
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction
from django.db import models

from bop_drops.models import BopDrop, BopDropLike, BopDropView
from friends.models import Friend

User = get_user_model()


class Command(BaseCommand):
    help = 'Generate bop drops for all existing users and their real friendships'

    def add_arguments(self, parser):
        parser.add_argument(
            '--drops-per-user',
            type=int,
            default=3,
            help='Average number of bop drops per user (default: 3)'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing bop drops before generating new ones'
        )
        parser.add_argument(
            '--days-back',
            type=int,
            default=7,
            help='Generate drops from this many days back (default: 7)'
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write(self.style.WARNING('Clearing existing bop drops...'))
            BopDrop.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Cleared existing bop drops'))

        # Get all existing users
        users = list(User.objects.all())
        
        if not users:
            self.stdout.write(self.style.ERROR('No users found in database!'))
            return

        self.stdout.write(f'Found {len(users)} existing users')

        # Check friendships
        friendships = Friend.objects.filter(status='accepted')
        self.stdout.write(f'Found {friendships.count()} existing friendships')

        if friendships.count() == 0:
            self.stdout.write(self.style.WARNING('No friendships found! Creating some test friendships...'))
            self.create_test_friendships(users)

        with transaction.atomic():
            bop_drops = self.create_bop_drops_for_users(users, options)
            self.create_realistic_engagement(bop_drops)

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully generated {len(bop_drops)} bop drops for {len(users)} existing users!'
            )
        )
        self.stdout.write(self.style.SUCCESS(self.get_success_message()))

    def create_test_friendships(self, users):
        """Create some test friendships if none exist"""
        friendships_created = 0
        
        if len(users) < 2:
            self.stdout.write(self.style.WARNING('Need at least 2 users to create friendships'))
            return
        
        # Create friendships between random users
        for i in range(min(len(users) * 2, 20)):  # Create some friendships
            user1, user2 = random.sample(users, 2)
            
            # Check if friendship already exists
            existing = Friend.objects.filter(
                (models.Q(requester=user1, recipient=user2) | 
                 models.Q(requester=user2, recipient=user1))
            ).first()
            
            if not existing:
                Friend.objects.create(
                    requester=user1,
                    recipient=user2,
                    status='accepted'
                )
                friendships_created += 1

        self.stdout.write(f'Created {friendships_created} test friendships')

    def create_bop_drops_for_users(self, users, options):
        """Create bop drops for existing users"""
        # Sample tracks with realistic data
        sample_tracks = [
            {
                'track_id': 'spotify:track:4iV5W9uYEdYUVa79Axb7Rh',
                'track_title': 'Never Gonna Give You Up',
                'track_artist': 'Rick Astley',
                'track_album': 'Whenever You Need Somebody',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273a2f3a75d5c8c9a8b97ad3c8c',
                'mood': 'nostalgic'
            },
            {
                'track_id': 'spotify:track:7ouMYWpwJ422jRcDASZB7P',
                'track_title': 'Bohemian Rhapsody',
                'track_artist': 'Queen',
                'track_album': 'A Night at the Opera',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273e319baafd16e84f0408af2a0',
                'mood': 'energetic'
            },
            {
                'track_id': 'spotify:track:5ChkMS8OtdzJeqyybCc9R5',
                'track_title': 'Watermelon Sugar',
                'track_artist': 'Harry Styles',
                'track_album': 'Fine Line',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273adce0bc2b69a6c5d97d6756a',
                'mood': 'happy'
            },
            {
                'track_id': 'spotify:track:1Je1IMUlBXcx1Fz0WE7oPT',
                'track_title': 'Bad Guy',
                'track_artist': 'Billie Eilish',
                'track_album': 'When We All Fall Asleep, Where Do We Go?',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273e7ad110d43c6d6ac8f8ead4d',
                'mood': 'chill'
            },
            {
                'track_id': 'spotify:track:4LRPiXqCikLlN15c3yImP7',
                'track_title': 'As It Was',
                'track_artist': 'Harry Styles',
                'track_album': 'Harry\'s House',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273b46f74097655d7f353caab14',
                'mood': 'romantic'
            },
            {
                'track_id': 'spotify:track:6dOtVTDdiauQNBQEDOtlAB',
                'track_title': 'Good 4 U',
                'track_artist': 'Olivia Rodrigo',
                'track_album': 'SOUR',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273a91c10fe9472d9bd89802e5a',
                'mood': 'angry'
            },
            {
                'track_id': 'spotify:track:0VjIjW4GlULA8N0EV8Fl1P',
                'track_title': 'Blinding Lights',
                'track_artist': 'The Weeknd',
                'track_album': 'After Hours',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b2738863bc11d2aa12b54f5aeb36',
                'mood': 'party'
            },
            {
                'track_id': 'spotify:track:2plbrEY59IikOBgBGLjaoe',
                'track_title': 'Drivers License',
                'track_artist': 'Olivia Rodrigo',
                'track_album': 'SOUR',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273a91c10fe9472d9bd89802e5a',
                'mood': 'sad'
            },
            {
                'track_id': 'spotify:track:4u7EnebtmKWzUH433cf5Qv',
                'track_title': 'Industry Baby',
                'track_artist': 'Lil Nas X',
                'track_album': 'MONTERO',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273be82673b5f79d9658ec0a9fd',
                'mood': 'energetic'
            },
            {
                'track_id': 'spotify:track:0tgVpDi06FyKpA1z0VMD4v',
                'track_title': 'Peaches',
                'track_artist': 'Justin Bieber',
                'track_album': 'Justice',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273564b3c4f67ea500ab4dd9a85',
                'mood': 'chill'
            },
            {
                'track_id': 'spotify:track:1BxfuPKGuaTgP7aM0Bbdwr',
                'track_title': 'Circles',
                'track_artist': 'Post Malone',
                'track_album': 'Hollywood\'s Bleeding',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273b1c4b76e23414c9f20242268',
                'mood': 'focus'
            },
            {
                'track_id': 'spotify:track:7I7JbDfeZRDkzE2b7iGVdM',
                'track_title': 'Say So',
                'track_artist': 'Doja Cat',
                'track_album': 'Hot Pink',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273f096ff29dc7e8b14476c7ee3',
                'mood': 'party'
            },
            {
                'track_id': 'spotify:track:3n3Ppam7vgaVa1iaRUc9Lp',
                'track_title': 'Mr. Brightside',
                'track_artist': 'The Killers',
                'track_album': 'Hot Fuss',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273ccdddd46119a4ff53eaf1f5d',
                'mood': 'energetic'
            },
            {
                'track_id': 'spotify:track:2takcwOaAZWiXQijPHIx7B',
                'track_title': 'Time After Time',
                'track_artist': 'Cyndi Lauper',
                'track_album': 'She\'s So Unusual',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273c3c2ba832c5c16ee2ad3e2b9',
                'mood': 'nostalgic'
            },
            {
                'track_id': 'spotify:track:1uNFoZAHBGtllmzznpCI3s',
                'track_title': 'Shape of You',
                'track_artist': 'Ed Sheeran',
                'track_album': '÷ (Deluxe)',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273ba5db46f4b838ef6027e6f96',
                'mood': 'happy'
            },
        ]

        # Sample captions
        sample_captions = [
            "This song hits different 🔥",
            "Can't stop listening to this!",
            "Perfect vibes for today ✨",
            "This is my new obsession",
            "Y'all need to hear this 🎵",
            "Soundtrack to my day",
            "This song speaks to my soul",
            "Dancing to this RN 💃",
            "Such a mood",
            "This beat is everything",
            "Throwback but make it relevant",
            "On repeat all day",
            "Pure musical gold",
            "This artist never misses",
            "Feeling this energy",
            "Vibes are immaculate ✨",
            "Current state of mind",
            "This one's for the playlist",
            "Chef's kiss 👌",
            "Absolutely iconic",
            "",  # Some without captions
            "",
            "",
            ""
        ]

        bop_drops = []
        drops_per_user = options['drops_per_user']
        days_back = options['days_back']
        
        for user in users:
            # Each user gets 1-5 bop drops (average based on drops_per_user)
            num_drops = random.randint(max(1, drops_per_user - 2), drops_per_user + 2)
            
            for _ in range(num_drops):
                track = random.choice(sample_tracks)
                caption = random.choice(sample_captions)
                
                # Create bop drop at various times in the past
                hours_ago = random.randint(1, days_back * 24)
                created_time = timezone.now() - timedelta(hours=hours_ago)
                
                bop_drop = BopDrop.objects.create(
                    user=user,
                    track_id=track['track_id'],
                    track_title=track['track_title'],
                    track_artist=track['track_artist'],
                    track_album=track['track_album'],
                    album_art_url=track['album_art_url'],
                    music_service='spotify',
                    caption=caption if caption else None,
                    mood=track['mood'],
                    is_currently_playing=random.choice([True, False, False, False]),  # 25% chance
                    friends_only=random.choice([True, True, True, False]),  # 75% friends only
                    created_at=created_time,
                    expires_at=created_time + timedelta(hours=24)
                )
                bop_drops.append(bop_drop)

        self.stdout.write(f'Created {len(bop_drops)} bop drops for {len(users)} users')
        return bop_drops

    def create_realistic_engagement(self, bop_drops):
        """Create realistic likes and views based on actual friendships"""
        likes_created = 0
        views_created = 0
        
        for bop_drop in bop_drops:
            # Get user's actual friends
            friends = []
            friendships = Friend.objects.filter(
                (models.Q(requester=bop_drop.user) | models.Q(recipient=bop_drop.user)),
                status='accepted'
            )
            
            for friendship in friendships:
                friend = friendship.recipient if friendship.requester == bop_drop.user else friendship.requester
                friends.append(friend)
            
            if not friends:
                continue
                
            # Realistic engagement: 30-80% of friends might see it
            engagement_rate = random.uniform(0.3, 0.8)
            num_viewers = int(len(friends) * engagement_rate)
            viewers = random.sample(friends, min(num_viewers, len(friends)))
            
            for viewer in viewers:
                # Create view
                view, created = BopDropView.objects.get_or_create(
                    user=viewer,
                    bop_drop=bop_drop
                )
                if created:
                    views_created += 1
                    bop_drop.view_count += 1
                
                # 20-40% chance to like if they viewed (realistic social media engagement)
                like_chance = random.uniform(0.2, 0.4)
                if random.random() < like_chance:
                    like, created = BopDropLike.objects.get_or_create(
                        user=viewer,
                        bop_drop=bop_drop
                    )
                    if created:
                        likes_created += 1
                        bop_drop.like_count += 1
            
            bop_drop.save()

        self.stdout.write(f'Created {likes_created} likes and {views_created} views')

    def get_success_message(self):
        return """
🎵 Bop Drops for Existing Users Complete! 🎵

Your database now has realistic bop drops for all existing users:
✅ Uses your actual user accounts
✅ Respects existing friendships
✅ Creates realistic engagement patterns
✅ Distributes drops across the last week

Ready to test! Try these endpoints:
- GET /api/bop-drops/feed/ - See your friends' bop drops
- GET /api/bop-drops/shuffle/ - Random shuffle through friends' music
- GET /api/bop-drops/trending/ - What's trending among friends
- GET /api/bop-drops/my_stats/ - Your bop drops statistics

Pro tip: Log in as any existing user to see their personalized feed! 🚀
        """ 