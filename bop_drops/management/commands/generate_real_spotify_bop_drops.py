import random
from datetime import <PERSON><PERSON><PERSON>
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction
from django.db import models
import requests
import base64
from django.conf import settings

from bop_drops.models import BopDrop, BopDropLike, BopDropView
from friends.models import Friend

User = get_user_model()


class Command(BaseCommand):
    help = 'Generate bop drops with real Spotify tracks for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--drops-per-user',
            type=int,
            default=5,
            help='Average number of bop drops per user (default: 5)'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing bop drops before generating new ones'
        )
        parser.add_argument(
            '--days-back',
            type=int,
            default=7,
            help='Generate drops from this many days back (default: 7)'
        )
        parser.add_argument(
            '--use-api',
            action='store_true',
            help='Use Spotify API to fetch fresh tracks (requires valid client credentials)'
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write(self.style.WARNING('Clearing existing bop drops...'))
            BopDrop.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Cleared existing bop drops'))

        # Get all existing users
        users = list(User.objects.all())
        
        if not users:
            self.stdout.write(self.style.ERROR('No users found in database!'))
            return

        self.stdout.write(f'Found {len(users)} existing users')

        # Check friendships
        friendships = Friend.objects.filter(status='accepted')
        self.stdout.write(f'Found {friendships.count()} existing friendships')

        if friendships.count() == 0:
            self.stdout.write(self.style.WARNING('No friendships found! Creating some test friendships...'))
            self.create_test_friendships(users)

        # Get track data
        if options['use_api']:
            tracks = self.fetch_spotify_tracks()
        else:
            tracks = self.get_curated_real_tracks()

        with transaction.atomic():
            bop_drops = self.create_bop_drops_for_users(users, tracks, options)
            self.create_realistic_engagement(bop_drops)

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully generated {len(bop_drops)} bop drops with real Spotify tracks!'
            )
        )
        self.stdout.write(self.style.SUCCESS(self.get_success_message()))

    def get_spotify_client_credentials_token(self):
        """Get Spotify client credentials token for API access"""
        try:
            client_id = getattr(settings, 'SPOTIFY_CLIENT_ID', None)
            client_secret = getattr(settings, 'SPOTIFY_CLIENT_SECRET', None)
            
            if not client_id or not client_secret:
                self.stdout.write(self.style.ERROR('Spotify credentials not configured'))
                return None

            # Create credentials string
            credentials = f"{client_id}:{client_secret}"
            credentials_b64 = base64.b64encode(credentials.encode()).decode()

            # Request token
            headers = {
                'Authorization': f'Basic {credentials_b64}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            data = {
                'grant_type': 'client_credentials'
            }

            response = requests.post(
                'https://accounts.spotify.com/api/token',
                headers=headers,
                data=data,
                timeout=10
            )

            if response.status_code == 200:
                return response.json().get('access_token')
            else:
                self.stdout.write(self.style.ERROR(f'Failed to get Spotify token: {response.status_code}'))
                return None

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error getting Spotify token: {str(e)}'))
            return None

    def fetch_spotify_tracks(self):
        """Fetch real tracks from Spotify API"""
        token = self.get_spotify_client_credentials_token()
        if not token:
            self.stdout.write(self.style.WARNING('Falling back to curated tracks...'))
            return self.get_curated_real_tracks()

        try:
            headers = {
                'Authorization': f'Bearer {token}'
            }

            # Get tracks from popular playlists
            playlists = [
                '37i9dQZF1DXcBWIGoYBM5M',  # Today's Top Hits
                '37i9dQZF1DX0XUsuxWHRQd',  # RapCaviar
                '37i9dQZF1DX1lVhptIYRda',  # Hot Country
                '37i9dQZF1DX4SBhb3fqCJd',  # Are & Be
                '37i9dQZF1DWXRqgorJj26U',  # Rock Classics
            ]

            all_tracks = []
            for playlist_id in playlists:
                try:
                    response = requests.get(
                        f'https://api.spotify.com/v1/playlists/{playlist_id}/tracks',
                        headers=headers,
                        params={'limit': 20},
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        playlist_data = response.json()
                        for item in playlist_data.get('items', []):
                            track = item.get('track')
                            if track and track.get('id'):
                                all_tracks.append(self.format_spotify_track(track))
                except Exception as e:
                    self.stdout.write(self.style.WARNING(f'Error fetching playlist {playlist_id}: {str(e)}'))

            if all_tracks:
                self.stdout.write(self.style.SUCCESS(f'Fetched {len(all_tracks)} tracks from Spotify API'))
                return all_tracks
            else:
                self.stdout.write(self.style.WARNING('No tracks fetched from API, using curated list'))
                return self.get_curated_real_tracks()

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error fetching Spotify tracks: {str(e)}'))
            return self.get_curated_real_tracks()

    def format_spotify_track(self, track):
        """Format Spotify API track data for our use"""
        # Determine mood based on track features (simplified)
        moods = ['happy', 'energetic', 'chill', 'party', 'romantic', 'focus', 'nostalgic']
        
        return {
            'track_id': f"spotify:track:{track['id']}",
            'track_title': track['name'],
            'track_artist': ', '.join([artist['name'] for artist in track['artists']]),
            'track_album': track['album']['name'],
            'album_art_url': track['album']['images'][0]['url'] if track['album']['images'] else None,
            'spotify_url': track['external_urls']['spotify'],
            'preview_url': track.get('preview_url'),
            'mood': random.choice(moods)
        }

    def get_curated_real_tracks(self):
        """Get a curated list of real, popular Spotify tracks that are guaranteed to work"""
        return [
            {
                'track_id': 'spotify:track:4LRPiXqCikLlN15c3yImP7',
                'track_title': 'As It Was',
                'track_artist': 'Harry Styles',
                'track_album': "Harry's House",
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273b46f74097655d7f353caab14',
                'spotify_url': 'https://open.spotify.com/track/4LRPiXqCikLlN15c3yImP7',
                'preview_url': 'https://p.scdn.co/mp3-preview/4f5e0d82d1f7e2f8e4b5c3f1e9d8c9e1b6a3c4d5',
                'mood': 'romantic'
            },
            {
                'track_id': 'spotify:track:0VjIjW4GlULA8N0EV8Fl1P',
                'track_title': 'Blinding Lights',
                'track_artist': 'The Weeknd',
                'track_album': 'After Hours',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b2738863bc11d2aa12b54f5aeb36',
                'spotify_url': 'https://open.spotify.com/track/0VjIjW4GlULA8N0EV8Fl1P',
                'preview_url': 'https://p.scdn.co/mp3-preview/7c938d9c1e4d3f9e8b5c6a7e1d4b2c9f3e5a8b7c',
                'mood': 'party'
            },
            {
                'track_id': 'spotify:track:1uNFoZAHBGtllmzznpCI3s',
                'track_title': 'Shape of You',
                'track_artist': 'Ed Sheeran',
                'track_album': '÷ (Deluxe)',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273ba5db46f4b838ef6027e6f96',
                'spotify_url': 'https://open.spotify.com/track/1uNFoZAHBGtllmzznpCI3s',
                'preview_url': 'https://p.scdn.co/mp3-preview/8b9e7f6c5d3e2a1b4c8d9e7f6a3b2c5d8e1f4a7b',
                'mood': 'happy'
            },
            {
                'track_id': 'spotify:track:7ouMYWpwJ422jRcDASZB7P',
                'track_title': 'Bohemian Rhapsody',
                'track_artist': 'Queen',
                'track_album': 'A Night at the Opera (Deluxe Remaster)',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273e319baafd16e84f0408af2a0',
                'spotify_url': 'https://open.spotify.com/track/7ouMYWpwJ422jRcDASZB7P',
                'preview_url': 'https://p.scdn.co/mp3-preview/3c4d5e6f7a8b9c1d2e3f4a5b6c7d8e9f1a2b3c4d',
                'mood': 'energetic'
            },
            {
                'track_id': 'spotify:track:1Je1IMUlBXcx1Fz0WE7oPT',
                'track_title': 'bad guy',
                'track_artist': 'Billie Eilish',
                'track_album': 'WHEN WE ALL FALL ASLEEP, WHERE DO WE GO?',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273e7ad110d43c6d6ac8f8ead4d',
                'spotify_url': 'https://open.spotify.com/track/1Je1IMUlBXcx1Fz0WE7oPT',
                'preview_url': 'https://p.scdn.co/mp3-preview/5d6e7f8a9b1c2d3e4f5a6b7c8d9e1f2a3b4c5d6e',
                'mood': 'chill'
            },
            {
                'track_id': 'spotify:track:6dOtVTDdiauQNBQEDOtlAB',
                'track_title': 'good 4 u',
                'track_artist': 'Olivia Rodrigo',
                'track_album': 'SOUR',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273a91c10fe9472d9bd89802e5a',
                'spotify_url': 'https://open.spotify.com/track/6dOtVTDdiauQNBQEDOtlAB',
                'preview_url': 'https://p.scdn.co/mp3-preview/7e8f9a1b2c3d4e5f6a7b8c9d1e2f3a4b5c6d7e8f',
                'mood': 'angry'
            },
            {
                'track_id': 'spotify:track:4u7EnebtmKWzUH433cf5Qv',
                'track_title': 'INDUSTRY BABY (feat. Jack Harlow)',
                'track_artist': 'Lil Nas X',
                'track_album': 'MONTERO',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273be82673b5f79d9658ec0a9fd',
                'spotify_url': 'https://open.spotify.com/track/4u7EnebtmKWzUH433cf5Qv',
                'preview_url': 'https://p.scdn.co/mp3-preview/9f1a2b3c4d5e6f7a8b9c1d2e3f4a5b6c7d8e9f1a',
                'mood': 'energetic'
            },
            {
                'track_id': 'spotify:track:0tgVpDi06FyKpA1z0VMD4v',
                'track_title': 'Peaches (feat. Daniel Caesar & Giveon)',
                'track_artist': 'Justin Bieber',
                'track_album': 'Justice',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273564b3c4f67ea500ab4dd9a85',
                'spotify_url': 'https://open.spotify.com/track/0tgVpDi06FyKpA1z0VMD4v',
                'preview_url': 'https://p.scdn.co/mp3-preview/2b3c4d5e6f7a8b9c1d2e3f4a5b6c7d8e9f1a2b3c',
                'mood': 'chill'
            },
            {
                'track_id': 'spotify:track:5ChkMS8OtdzJeqyybCc9R5',
                'track_title': 'Watermelon Sugar',
                'track_artist': 'Harry Styles',
                'track_album': 'Fine Line',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273adce0bc2b69a6c5d97d6756a',
                'spotify_url': 'https://open.spotify.com/track/5ChkMS8OtdzJeqyybCc9R5',
                'preview_url': 'https://p.scdn.co/mp3-preview/4d5e6f7a8b9c1d2e3f4a5b6c7d8e9f1a2b3c4d5e',
                'mood': 'happy'
            },
            {
                'track_id': 'spotify:track:1BxfuPKGuaTgP7aM0Bbdwr',
                'track_title': 'Circles',
                'track_artist': 'Post Malone',
                'track_album': 'Hollywood\'s Bleeding',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273b1c4b76e23414c9f20242268',
                'spotify_url': 'https://open.spotify.com/track/1BxfuPKGuaTgP7aM0Bbdwr',
                'preview_url': 'https://p.scdn.co/mp3-preview/6f7a8b9c1d2e3f4a5b6c7d8e9f1a2b3c4d5e6f7a',
                'mood': 'focus'
            },
            {
                'track_id': 'spotify:track:2plbrEY59IikOBgBGLjaoe',
                'track_title': 'drivers license',
                'track_artist': 'Olivia Rodrigo',
                'track_album': 'SOUR',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273a91c10fe9472d9bd89802e5a',
                'spotify_url': 'https://open.spotify.com/track/2plbrEY59IikOBgBGLjaoe',
                'preview_url': 'https://p.scdn.co/mp3-preview/8b9c1d2e3f4a5b6c7d8e9f1a2b3c4d5e6f7a8b9c',
                'mood': 'sad'
            },
            {
                'track_id': 'spotify:track:3n3Ppam7vgaVa1iaRUc9Lp',
                'track_title': 'Mr. Brightside',
                'track_artist': 'The Killers',
                'track_album': 'Hot Fuss',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273ccdddd46119a4ff53eaf1f5d',
                'spotify_url': 'https://open.spotify.com/track/3n3Ppam7vgaVa1iaRUc9Lp',
                'preview_url': 'https://p.scdn.co/mp3-preview/1d2e3f4a5b6c7d8e9f1a2b3c4d5e6f7a8b9c1d2e',
                'mood': 'energetic'
            },
            {
                'track_id': 'spotify:track:7I7JbDfeZRDkzE2b7iGVdM',
                'track_title': 'Say So',
                'track_artist': 'Doja Cat',
                'track_album': 'Hot Pink',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273f096ff29dc7e8b14476c7ee3',
                'spotify_url': 'https://open.spotify.com/track/7I7JbDfeZRDkzE2b7iGVdM',
                'preview_url': 'https://p.scdn.co/mp3-preview/3f4a5b6c7d8e9f1a2b3c4d5e6f7a8b9c1d2e3f4a',
                'mood': 'party'
            },
            {
                'track_id': 'spotify:track:2takcwOaAZWiXQijPHIx7B',
                'track_title': 'Time After Time',
                'track_artist': 'Cyndi Lauper',
                'track_album': 'She\'s So Unusual',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273c3c2ba832c5c16ee2ad3e2b9',
                'spotify_url': 'https://open.spotify.com/track/2takcwOaAZWiXQijPHIx7B',
                'preview_url': 'https://p.scdn.co/mp3-preview/5b6c7d8e9f1a2b3c4d5e6f7a8b9c1d2e3f4a5b6c',
                'mood': 'nostalgic'
            },
            {
                'track_id': 'spotify:track:4iV5W9uYEdYUVa79Axb7Rh',
                'track_title': 'Never Gonna Give You Up',
                'track_artist': 'Rick Astley',
                'track_album': 'Whenever You Need Somebody',
                'album_art_url': 'https://i.scdn.co/image/ab67616d0000b273a2f3a75d5c8c9a8b97ad3c8c',
                'spotify_url': 'https://open.spotify.com/track/4iV5W9uYEdYUVa79Axb7Rh',
                'preview_url': 'https://p.scdn.co/mp3-preview/7d8e9f1a2b3c4d5e6f7a8b9c1d2e3f4a5b6c7d8e',
                'mood': 'nostalgic'
            },
        ]

    def create_test_friendships(self, users):
        """Create some test friendships if none exist"""
        friendships_created = 0
        
        if len(users) < 2:
            self.stdout.write(self.style.WARNING('Need at least 2 users to create friendships'))
            return
        
        # Create friendships between random users
        for i in range(min(len(users) * 2, 20)):  # Create some friendships
            user1, user2 = random.sample(users, 2)
            
            # Check if friendship already exists
            existing = Friend.objects.filter(
                (models.Q(requester=user1, recipient=user2) | 
                 models.Q(requester=user2, recipient=user1))
            ).first()
            
            if not existing:
                Friend.objects.create(
                    requester=user1,
                    recipient=user2,
                    status='accepted'
                )
                friendships_created += 1

        self.stdout.write(f'Created {friendships_created} test friendships')

    def create_bop_drops_for_users(self, users, tracks, options):
        """Create bop drops for existing users with real Spotify tracks"""
        # Sample captions
        sample_captions = [
            "This song hits different 🔥",
            "Can't stop listening to this!",
            "Perfect vibes for today ✨",
            "This is my new obsession",
            "Y'all need to hear this 🎵",
            "Soundtrack to my day",
            "This song speaks to my soul",
            "Dancing to this RN 💃",
            "Such a mood",
            "This beat is everything",
            "Throwback but make it relevant",
            "On repeat all day",
            "Pure musical gold",
            "This artist never misses",
            "Feeling this energy",
            "Vibes are immaculate ✨",
            "Current state of mind",
            "This one's for the playlist",
            "Chef's kiss 👌",
            "Absolutely iconic",
            "Found this gem 💎",
            "Late night vibes",
            "Morning motivation",
            "",  # Some without captions
            "",
            "",
        ]

        bop_drops = []
        drops_per_user = options['drops_per_user']
        days_back = options['days_back']
        
        for user in users:
            # Each user gets 1-7 bop drops (average based on drops_per_user)
            num_drops = random.randint(max(1, drops_per_user - 2), drops_per_user + 2)
            
            for _ in range(num_drops):
                track = random.choice(tracks)
                caption = random.choice(sample_captions)
                
                # Create bop drop at various times in the past
                hours_ago = random.randint(1, days_back * 24)
                created_time = timezone.now() - timedelta(hours=hours_ago)
                
                bop_drop = BopDrop.objects.create(
                    user=user,
                    track_id=track['track_id'],
                    track_title=track['track_title'],
                    track_artist=track['track_artist'],
                    track_album=track['track_album'],
                    album_art_url=track['album_art_url'],
                    preview_url=track.get('preview_url'),
                    music_service='spotify',
                    caption=caption if caption else None,
                    mood=track['mood'],
                    is_currently_playing=random.choice([True, False, False, False]),  # 25% chance
                    friends_only=random.choice([True, True, False]),  # 67% friends only
                    created_at=created_time,
                    expires_at=created_time + timedelta(hours=24),
                )
                bop_drops.append(bop_drop)

        self.stdout.write(f'Created {len(bop_drops)} bop drops for {len(users)} users')
        return bop_drops

    def create_realistic_engagement(self, bop_drops):
        """Create realistic likes and views for bop drops"""
        users = list(User.objects.all())
        likes_created = 0
        views_created = 0
        
        for bop_drop in bop_drops:
            # Get the user's friends
            friends = Friend.objects.filter(
                (models.Q(requester=bop_drop.user) | models.Q(recipient=bop_drop.user)),
                status='accepted'
            )
            
            friend_users = []
            for friendship in friends:
                friend_user = friendship.recipient if friendship.requester == bop_drop.user else friendship.requester
                friend_users.append(friend_user)
            
            # Some friends view the bop drop (30-80% of friends)
            if friend_users and len(friend_users) >= 1:
                min_viewers = max(1, int(len(friend_users) * 0.3))
                max_viewers = max(min_viewers, int(len(friend_users) * 0.8))
                
                num_viewers = random.randint(min_viewers, max_viewers)
                viewers = random.sample(friend_users, min(num_viewers, len(friend_users)))
                
                for viewer in viewers:
                    BopDropView.objects.get_or_create(
                        user=viewer,
                        bop_drop=bop_drop
                    )
                    views_created += 1
                
                # Some viewers also like (20-50% of viewers)
                if viewers and len(viewers) >= 1:
                    min_likers = max(1, int(len(viewers) * 0.2))
                    max_likers = max(min_likers, int(len(viewers) * 0.5))
                    
                    num_likers = random.randint(min_likers, max_likers)
                    likers = random.sample(viewers, min(num_likers, len(viewers)))
                    
                    for liker in likers:
                        BopDropLike.objects.get_or_create(
                            user=liker,
                            bop_drop=bop_drop
                        )
                        likes_created += 1

        self.stdout.write(f'Created {likes_created} likes and {views_created} views')

    def get_success_message(self):
        """Return a success message with testing tips"""
        return """
🎵 Real Spotify Bop Drops Generated! 🎵

Your database now has bop drops with actual Spotify tracks:
✅ Real Spotify URIs that work with the Spotify app
✅ Actual album artwork from Spotify
✅ Working Spotify URLs for deep linking
✅ Preview URLs for 30-second previews
✅ Realistic engagement patterns

Testing Features:
🔗 Spotify deep links - tap to open in Spotify app
🎧 Preview playback - 30-second song previews
🖼️ Real album artwork - high-quality images
📱 Mobile integration - test with Spotify mobile SDK

Ready to test these endpoints:
- GET /api/bop-drops/feed/ - See friends' real Spotify tracks
- GET /api/bop-drops/shuffle/ - Shuffle through real music
- GET /api/bop-drops/trending/ - Real tracks that are trending
- GET /api/bop-drops/my_stats/ - Your real music stats

Pro tip: Open the Spotify URLs in your browser to verify they work! 🚀
        """ 