# Generated by Django 4.2.7 on 2025-06-18 00:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BopDrop",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("track_id", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("track_title", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("track_artist", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                (
                    "track_album",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("track_duration_ms", models.IntegerField(blank=True, null=True)),
                ("album_art_url", models.URLField(blank=True, null=True)),
                ("preview_url", models.URLField(blank=True, null=True)),
                ("music_service", models.CharField(default="spotify", max_length=20)),
                (
                    "caption",
                    models.TextField(
                        blank=True,
                        help_text="Optional message about the track",
                        max_length=500,
                        null=True,
                    ),
                ),
                (
                    "mood",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("happy", "Happy"),
                            ("sad", "Sad"),
                            ("energetic", "Energetic"),
                            ("chill", "Chill"),
                            ("nostalgic", "Nostalgic"),
                            ("party", "Party"),
                            ("focus", "Focus"),
                            ("romantic", "Romantic"),
                            ("angry", "Angry"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "is_currently_playing",
                    models.BooleanField(
                        default=False,
                        help_text="If user is currently listening to this track",
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("friends_only", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                ("like_count", models.PositiveIntegerField(default=0)),
                ("view_count", models.PositiveIntegerField(default=0)),
                ("share_count", models.PositiveIntegerField(default=0)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bop_drops",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="BopDropView",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("viewed_at", models.DateTimeField(auto_now_add=True)),
                (
                    "bop_drop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="views",
                        to="bop_drops.bopdrop",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bop_drop_views",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["bop_drop", "viewed_at"],
                        name="bop_drops_b_bop_dro_1e9ad1_idx",
                    ),
                    models.Index(
                        fields=["user", "viewed_at"],
                        name="bop_drops_b_user_id_0996e5_idx",
                    ),
                ],
                "unique_together": {("user", "bop_drop")},
            },
        ),
        migrations.CreateModel(
            name="BopDropLike",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "bop_drop",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="likes",
                        to="bop_drops.bopdrop",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bop_drop_likes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["bop_drop", "created_at"],
                        name="bop_drops_b_bop_dro_ce5d61_idx",
                    ),
                    models.Index(
                        fields=["user", "created_at"],
                        name="bop_drops_b_user_id_26780d_idx",
                    ),
                ],
                "unique_together": {("user", "bop_drop")},
            },
        ),
        migrations.AddIndex(
            model_name="bopdrop",
            index=models.Index(
                fields=["user", "created_at"], name="bop_drops_b_user_id_2bb11b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bopdrop",
            index=models.Index(
                fields=["is_active", "created_at"],
                name="bop_drops_b_is_acti_36f7e2_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="bopdrop",
            index=models.Index(
                fields=["music_service", "track_id"],
                name="bop_drops_b_music_s_ead81d_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="bopdrop",
            index=models.Index(
                fields=["mood", "created_at"], name="bop_drops_b_mood_f7c73a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bopdrop",
            index=models.Index(
                fields=["expires_at"], name="bop_drops_b_expires_56d6da_idx"
            ),
        ),
    ]
