from django.db import models
from django.conf import settings
from django.utils import timezone
from datetime import timedelta


class BopDrop(models.Model):
    """
    Model representing a music recommendation/status that users can share with friends
    """
    MOOD_CHOICES = [
        ('happy', 'Happy'),
        ('sad', 'Sad'),
        ('energetic', 'Energetic'),
        ('chill', 'Chill'),
        ('nostalgic', 'Nostalgic'),
        ('party', 'Party'),
        ('focus', 'Focus'),
        ('romantic', 'Romantic'),
        ('angry', 'Angry'),
        ('other', 'Other'),
    ]
    
    # User who dropped the bop
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='bop_drops'
    )
    
    # Music details
    track_id = models.CharField(max_length=255)  # Spotify/Apple Music track ID
    track_title = models.CharField(max_length=255)
    track_artist = models.CharField(max_length=255)
    track_album = models.CharField(max_length=255, blank=True, null=True)
    track_duration_ms = models.IntegerField(null=True, blank=True)
    album_art_url = models.URLField(blank=True, null=True)
    preview_url = models.URLField(blank=True, null=True)
    music_service = models.CharField(max_length=20, default='spotify')  # spotify, apple, etc.
    
    # Status/recommendation details
    caption = models.TextField(max_length=500, blank=True, null=True, help_text="Optional message about the track")
    mood = models.CharField(max_length=20, choices=MOOD_CHOICES, blank=True, null=True)
    is_currently_playing = models.BooleanField(default=False, help_text="If user is currently listening to this track")
    
    # Visibility and interaction
    is_active = models.BooleanField(default=True)  # Users can deactivate old bop drops
    friends_only = models.BooleanField(default=True)  # Visible to friends only vs public
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(null=True, blank=True)  # Optional expiration
    
    # Interaction counts (for performance)
    like_count = models.PositiveIntegerField(default=0)
    view_count = models.PositiveIntegerField(default=0)
    share_count = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['is_active', 'created_at']),
            models.Index(fields=['music_service', 'track_id']),
            models.Index(fields=['mood', 'created_at']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"{self.user.username}: {self.track_title} - {self.track_artist}"

    def save(self, *args, **kwargs):
        # Set default expiration to 24 hours if not specified
        if not self.expires_at and self.is_active:
            self.expires_at = timezone.now() + timedelta(hours=24)
        super().save(*args, **kwargs)

    def is_expired(self):
        """Check if the bop drop has expired"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    def increment_view_count(self):
        """Increment view count"""
        self.view_count += 1
        self.save(update_fields=['view_count'])

    def increment_share_count(self):
        """Increment share count"""
        self.share_count += 1
        self.save(update_fields=['share_count'])

    @property
    def is_visible(self):
        """Check if bop drop is currently visible"""
        return self.is_active and not self.is_expired()


class BopDropLike(models.Model):
    """
    Model for tracking likes on bop drops
    """
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='bop_drop_likes'
    )
    bop_drop = models.ForeignKey(
        BopDrop,
        on_delete=models.CASCADE,
        related_name='likes'
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'bop_drop')
        indexes = [
            models.Index(fields=['bop_drop', 'created_at']),
            models.Index(fields=['user', 'created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} likes {self.bop_drop.track_title}"

    def save(self, *args, **kwargs):
        # Update the like count on the bop drop
        is_new = self.pk is None
        super().save(*args, **kwargs)
        if is_new:
            self.bop_drop.like_count += 1
            self.bop_drop.save(update_fields=['like_count'])

    def delete(self, *args, **kwargs):
        # Update the like count on the bop drop
        bop_drop = self.bop_drop
        super().delete(*args, **kwargs)
        bop_drop.like_count -= 1
        bop_drop.save(update_fields=['like_count'])


class BopDropView(models.Model):
    """
    Model for tracking views on bop drops (for analytics)
    """
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='bop_drop_views'
    )
    bop_drop = models.ForeignKey(
        BopDrop,
        on_delete=models.CASCADE,
        related_name='views'
    )
    viewed_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'bop_drop')  # One view per user per bop drop
        indexes = [
            models.Index(fields=['bop_drop', 'viewed_at']),
            models.Index(fields=['user', 'viewed_at']),
        ]

    def __str__(self):
        return f"{self.user.username} viewed {self.bop_drop.track_title}"
