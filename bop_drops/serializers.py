from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import BopDrop, BopDropLike, BopDropView
from django.utils import timezone

User = get_user_model()


class UserMiniSerializer(serializers.ModelSerializer):
    """Minimal user serializer for bop drops with profile picture"""
    profile_pic = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'profile_pic']
    
    def get_profile_pic(self, obj):
        """Get the absolute URL for the user's profile picture"""
        if obj.profile_pic:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.profile_pic)
            return obj.profile_pic
        return None


class BopDropCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating new bop drops"""
    
    class Meta:
        model = BopDrop
        fields = [
            'track_id', 'track_title', 'track_artist', 'track_album',
            'track_duration_ms', 'album_art_url', 'preview_url', 'music_service',
            'caption', 'mood', 'is_currently_playing', 'friends_only', 'expires_at'
        ]
        extra_kwargs = {
            'track_id': {'required': True},
            'track_title': {'required': True},
            'track_artist': {'required': True},
        }

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class BopDropSerializer(serializers.ModelSerializer):
    """Full serializer for bop drops with user info and interaction data"""
    user = UserMiniSerializer(read_only=True)
    is_liked_by_user = serializers.SerializerMethodField()
    is_viewed_by_user = serializers.SerializerMethodField()
    is_expired = serializers.SerializerMethodField()
    is_visible = serializers.SerializerMethodField()
    time_since_created = serializers.SerializerMethodField()
    
    class Meta:
        model = BopDrop
        fields = [
            'id', 'user', 'track_id', 'track_title', 'track_artist', 'track_album',
            'track_duration_ms', 'album_art_url', 'preview_url', 'music_service',
            'caption', 'mood', 'is_currently_playing', 'friends_only',
            'is_active', 'created_at', 'updated_at', 'expires_at',
            'like_count', 'view_count', 'share_count',
            'is_liked_by_user', 'is_viewed_by_user', 'is_expired', 'is_visible',
            'time_since_created'
        ]
        read_only_fields = [
            'id', 'user', 'created_at', 'updated_at', 'like_count', 'view_count', 'share_count'
        ]

    def get_is_liked_by_user(self, obj):
        """Check if current user has liked this bop drop"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return BopDropLike.objects.filter(user=request.user, bop_drop=obj).exists()
        return False

    def get_is_viewed_by_user(self, obj):
        """Check if current user has viewed this bop drop"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return BopDropView.objects.filter(user=request.user, bop_drop=obj).exists()
        return False

    def get_is_expired(self, obj):
        """Check if bop drop is expired"""
        return obj.is_expired()

    def get_is_visible(self, obj):
        """Check if bop drop is visible"""
        return obj.is_visible

    def get_time_since_created(self, obj):
        """Get human-readable time since creation"""
        from datetime import timedelta
        
        now = timezone.now()
        diff = now - obj.created_at
        
        if diff < timedelta(minutes=1):
            return "just now"
        elif diff < timedelta(hours=1):
            minutes = int(diff.total_seconds() / 60)
            return f"{minutes}m ago"
        elif diff < timedelta(days=1):
            hours = int(diff.total_seconds() / 3600)
            return f"{hours}h ago"
        elif diff < timedelta(days=7):
            days = diff.days
            return f"{days}d ago"
        else:
            return obj.created_at.strftime("%b %d")


class BopDropLikeSerializer(serializers.ModelSerializer):
    """Serializer for bop drop likes"""
    user = UserMiniSerializer(read_only=True)
    
    class Meta:
        model = BopDropLike
        fields = ['id', 'user', 'bop_drop', 'created_at']
        read_only_fields = ['id', 'user', 'created_at']

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class BopDropFeedSerializer(serializers.ModelSerializer):
    """Optimized serializer for the bop drops feed"""
    user = UserMiniSerializer(read_only=True)
    is_liked_by_user = serializers.SerializerMethodField()
    time_since_created = serializers.SerializerMethodField()
    
    class Meta:
        model = BopDrop
        fields = [
            'id', 'user', 'track_id', 'track_title', 'track_artist', 'track_album',
            'album_art_url', 'preview_url', 'music_service', 'caption', 'mood',
            'is_currently_playing', 'created_at', 'like_count', 'view_count',
            'is_liked_by_user', 'time_since_created'
        ]

    def get_is_liked_by_user(self, obj):
        """Check if current user has liked this bop drop"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            # Use prefetch_related data if available to avoid N+1 queries
            if hasattr(obj, 'user_likes'):
                return bool(obj.user_likes)
            return BopDropLike.objects.filter(user=request.user, bop_drop=obj).exists()
        return False

    def get_time_since_created(self, obj):
        """Get human-readable time since creation"""
        from datetime import timedelta
        
        now = timezone.now()
        diff = now - obj.created_at
        
        if diff < timedelta(minutes=1):
            return "just now"
        elif diff < timedelta(hours=1):
            minutes = int(diff.total_seconds() / 60)
            return f"{minutes}m ago"
        elif diff < timedelta(days=1):
            hours = int(diff.total_seconds() / 3600)
            return f"{hours}h ago"
        else:
            days = diff.days
            return f"{days}d ago"


class BopDropStatsSerializer(serializers.Serializer):
    """Serializer for bop drop statistics"""
    total_drops = serializers.IntegerField()
    active_drops = serializers.IntegerField()
    total_likes_received = serializers.IntegerField()
    total_views_received = serializers.IntegerField()
    most_liked_track = serializers.CharField()
    favorite_mood = serializers.CharField()
    drops_this_week = serializers.IntegerField()
    average_likes_per_drop = serializers.FloatField() 


class MyBopDropDetailSerializer(serializers.ModelSerializer):
    """Comprehensive serializer for detailed bop drop information"""
    user = UserMiniSerializer(read_only=True)
    likes = serializers.SerializerMethodField()
    views = serializers.SerializerMethodField()
    engagement_stats = serializers.SerializerMethodField()
    time_since_created = serializers.SerializerMethodField()
    
    class Meta:
        model = BopDrop
        fields = [
            'id', 'user', 'track_id', 'track_title', 'track_artist', 'track_album',
            'track_duration_ms', 'album_art_url', 'preview_url', 'music_service',
            'caption', 'mood', 'is_currently_playing', 'friends_only',
            'is_active', 'created_at', 'updated_at', 'expires_at',
            'likes', 'views', 'engagement_stats', 'time_since_created'
        ]
    
    def get_likes(self, obj):
        """Get detailed information about users who liked the bop drop"""
        likes = BopDropLike.objects.filter(bop_drop=obj).select_related('user')
        result = []
        for like in likes:
            profile_pic_url = None
            if like.user.profile_pic:
                request = self.context.get('request')
                if request:
                    profile_pic_url = request.build_absolute_uri(like.user.profile_pic)
                else:
                    profile_pic_url = like.user.profile_pic
            
            result.append({
                'user_id': like.user.id,
                'username': like.user.username,
                'first_name': like.user.first_name,
                'last_name': like.user.last_name,
                'profile_pic': profile_pic_url,
                'liked_at': like.created_at
            })
        return result
    
    def get_views(self, obj):
        """Get detailed information about users who viewed the bop drop"""
        views = BopDropView.objects.filter(bop_drop=obj).select_related('user')
        result = []
        for view in views:
            profile_pic_url = None
            if view.user.profile_pic:
                request = self.context.get('request')
                if request:
                    profile_pic_url = request.build_absolute_uri(view.user.profile_pic)
                else:
                    profile_pic_url = view.user.profile_pic
            
            result.append({
                'user_id': view.user.id,
                'username': view.user.username,
                'first_name': view.user.first_name,
                'last_name': view.user.last_name,
                'profile_pic': profile_pic_url,
                'viewed_at': view.viewed_at
            })
        return result
    
    def get_engagement_stats(self, obj):
        """Get comprehensive engagement statistics using real-time counts"""
        # Use real-time counts from the database, not cached counts
        actual_likes_count = obj.likes.count()
        actual_views_count = obj.views.count()
        actual_shares_count = obj.share_count  # This is only updated by increment_share_count()
        
        return {
            'total_likes': actual_likes_count,
            'total_views': actual_views_count,
            'total_shares': actual_shares_count,
            'total_comments': 0,  # No comment system implemented yet
            'engagement_rate': round((actual_likes_count + actual_views_count) / max(actual_views_count, 1) * 100, 2) if actual_views_count > 0 else 0,
            'is_trending': actual_likes_count >= 5 and actual_views_count >= 10,
            # Additional useful stats
            'cached_counts': {
                'likes': obj.like_count,
                'views': obj.view_count, 
                'shares': obj.share_count
            }
        }
    
    def get_time_since_created(self, obj):
        """Get human-readable time since creation"""
        now = timezone.now()
        diff = now - obj.created_at
        
        if diff.days > 0:
            return f"{diff.days}d ago"
        elif diff.seconds >= 3600:
            return f"{diff.seconds // 3600}h ago"
        elif diff.seconds >= 60:
            return f"{diff.seconds // 60}m ago"
        else:
            return "just now" 