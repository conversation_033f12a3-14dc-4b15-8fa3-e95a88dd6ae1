from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from friends.models import Friend
from .models import BopDrop, BopDropLike, BopDropView

User = get_user_model()


class BopDropModelTests(TestCase):
    """Test cases for BopDrop model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_bop_drop_creation(self):
        """Test creating a bop drop"""
        bop_drop = BopDrop.objects.create(
            user=self.user,
            track_id='spotify:track:123',
            track_title='Test Song',
            track_artist='Test Artist',
            track_album='Test Album',
            caption='Great song!',
            mood='happy'
        )
        
        self.assertEqual(bop_drop.user, self.user)
        self.assertEqual(bop_drop.track_title, 'Test Song')
        self.assertEqual(bop_drop.mood, 'happy')
        self.assertTrue(bop_drop.is_active)
        self.assertTrue(bop_drop.friends_only)
        self.assertIsNotNone(bop_drop.expires_at)
    
    def test_bop_drop_expiration(self):
        """Test bop drop expiration logic"""
        # Create expired bop drop
        bop_drop = BopDrop.objects.create(
            user=self.user,
            track_id='spotify:track:123',
            track_title='Test Song',
            track_artist='Test Artist',
            expires_at=timezone.now() - timedelta(hours=1)
        )
        
        self.assertTrue(bop_drop.is_expired())
        self.assertFalse(bop_drop.is_visible)
    
    def test_bop_drop_str_representation(self):
        """Test string representation of bop drop"""
        bop_drop = BopDrop.objects.create(
            user=self.user,
            track_id='spotify:track:123',
            track_title='Test Song',
            track_artist='Test Artist'
        )
        
        expected = f"{self.user.username}: Test Song - Test Artist"
        self.assertEqual(str(bop_drop), expected)


class BopDropLikeModelTests(TestCase):
    """Test cases for BopDropLike model"""
    
    def setUp(self):
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        self.bop_drop = BopDrop.objects.create(
            user=self.user1,
            track_id='spotify:track:123',
            track_title='Test Song',
            track_artist='Test Artist'
        )
    
    def test_like_creation_updates_count(self):
        """Test that creating a like updates the bop drop's like count"""
        initial_count = self.bop_drop.like_count
        
        BopDropLike.objects.create(
            user=self.user2,
            bop_drop=self.bop_drop
        )
        
        self.bop_drop.refresh_from_db()
        self.assertEqual(self.bop_drop.like_count, initial_count + 1)
    
    def test_like_deletion_updates_count(self):
        """Test that deleting a like updates the bop drop's like count"""
        like = BopDropLike.objects.create(
            user=self.user2,
            bop_drop=self.bop_drop
        )
        
        self.bop_drop.refresh_from_db()
        initial_count = self.bop_drop.like_count
        
        like.delete()
        
        self.bop_drop.refresh_from_db()
        self.assertEqual(self.bop_drop.like_count, initial_count - 1)


class BopDropAPITests(APITestCase):
    """Test cases for BopDrop API endpoints"""
    
    def setUp(self):
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create friendship
        Friend.objects.create(
            requester=self.user1,
            recipient=self.user2,
            status='accepted'
        )
        
        self.client = APIClient()
    
    def test_create_bop_drop(self):
        """Test creating a bop drop via API"""
        self.client.force_authenticate(user=self.user1)
        
        data = {
            'track_id': 'spotify:track:123',
            'track_title': 'Test Song',
            'track_artist': 'Test Artist',
            'track_album': 'Test Album',
            'caption': 'Love this song!',
            'mood': 'happy'
        }
        
        response = self.client.post('/api/bop-drops/', data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(BopDrop.objects.count(), 1)
        
        bop_drop = BopDrop.objects.first()
        self.assertEqual(bop_drop.user, self.user1)
        self.assertEqual(bop_drop.track_title, 'Test Song')
    
    def test_list_own_bop_drops(self):
        """Test listing user's own bop drops"""
        self.client.force_authenticate(user=self.user1)
        
        BopDrop.objects.create(
            user=self.user1,
            track_id='spotify:track:123',
            track_title='My Song',
            track_artist='My Artist'
        )
        
        response = self.client.get('/api/bop-drops/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['track_title'], 'My Song')
    
    def test_friends_feed(self):
        """Test getting friends' bop drops feed"""
        self.client.force_authenticate(user=self.user1)
        
        # Create bop drop from friend
        BopDrop.objects.create(
            user=self.user2,
            track_id='spotify:track:456',
            track_title='Friend Song',
            track_artist='Friend Artist'
        )
        
        response = self.client.get('/api/bop-drops/feed/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['track_title'], 'Friend Song')
    
    def test_shuffle_friends_bop_drops(self):
        """Test shuffling friends' bop drops"""
        self.client.force_authenticate(user=self.user1)
        
        # Create multiple bop drops from friend
        for i in range(5):
            BopDrop.objects.create(
                user=self.user2,
                track_id=f'spotify:track:{i}',
                track_title=f'Song {i}',
                track_artist='Test Artist'
            )
        
        response = self.client.get('/api/bop-drops/shuffle/?limit=3')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)
    
    def test_like_bop_drop(self):
        """Test liking a bop drop"""
        self.client.force_authenticate(user=self.user1)
        
        bop_drop = BopDrop.objects.create(
            user=self.user2,
            track_id='spotify:track:123',
            track_title='Test Song',
            track_artist='Test Artist'
        )
        
        response = self.client.post(f'/api/bop-drops/{bop_drop.id}/like/')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(BopDropLike.objects.filter(user=self.user1, bop_drop=bop_drop).exists())
    
    def test_unlike_bop_drop(self):
        """Test unliking a bop drop"""
        self.client.force_authenticate(user=self.user1)
        
        bop_drop = BopDrop.objects.create(
            user=self.user2,
            track_id='spotify:track:123',
            track_title='Test Song',
            track_artist='Test Artist'
        )
        
        # First like it
        BopDropLike.objects.create(user=self.user1, bop_drop=bop_drop)
        
        response = self.client.post(f'/api/bop-drops/{bop_drop.id}/unlike/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(BopDropLike.objects.filter(user=self.user1, bop_drop=bop_drop).exists())
    
    def test_view_bop_drop(self):
        """Test recording a view on a bop drop"""
        self.client.force_authenticate(user=self.user1)
        
        bop_drop = BopDrop.objects.create(
            user=self.user2,
            track_id='spotify:track:123',
            track_title='Test Song',
            track_artist='Test Artist'
        )
        
        response = self.client.post(f'/api/bop-drops/{bop_drop.id}/view/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(BopDropView.objects.filter(user=self.user1, bop_drop=bop_drop).exists())
    
    def test_my_stats(self):
        """Test getting user's bop drop statistics"""
        self.client.force_authenticate(user=self.user1)
        
        # Create some bop drops
        for i in range(3):
            BopDrop.objects.create(
                user=self.user1,
                track_id=f'spotify:track:{i}',
                track_title=f'Song {i}',
                track_artist='Test Artist'
            )
        
        response = self.client.get('/api/bop-drops/my_stats/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_drops'], 3)
        self.assertEqual(response.data['active_drops'], 3)
    
    def test_trending_bop_drops(self):
        """Test getting trending bop drops"""
        self.client.force_authenticate(user=self.user1)
        
        # Create recent bop drop from friend
        bop_drop = BopDrop.objects.create(
            user=self.user2,
            track_id='spotify:track:123',
            track_title='Trending Song',
            track_artist='Test Artist',
            created_at=timezone.now() - timedelta(hours=2)
        )
        
        # Add some engagement
        bop_drop.like_count = 5
        bop_drop.view_count = 10
        bop_drop.save()
        
        response = self.client.get('/api/bop-drops/trending/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['track_title'], 'Trending Song')
    
    def test_filter_by_mood(self):
        """Test filtering bop drops by mood"""
        self.client.force_authenticate(user=self.user1)
        
        BopDrop.objects.create(
            user=self.user2,
            track_id='spotify:track:123',
            track_title='Happy Song',
            track_artist='Test Artist',
            mood='happy'
        )
        
        BopDrop.objects.create(
            user=self.user2,
            track_id='spotify:track:456',
            track_title='Sad Song',
            track_artist='Test Artist',
            mood='sad'
        )
        
        response = self.client.get('/api/bop-drops/by_mood/?mood=happy')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['track_title'], 'Happy Song')
    
    def test_privacy_friends_only(self):
        """Test that friends_only bop drops are only visible to friends"""
        # Create non-friend user
        user3 = User.objects.create_user(
            username='user3',
            email='<EMAIL>',
            password='testpass123'
        )
        
        bop_drop = BopDrop.objects.create(
            user=self.user1,
            track_id='spotify:track:123',
            track_title='Private Song',
            track_artist='Test Artist',
            friends_only=True
        )
        
        # Test that non-friend cannot like
        self.client.force_authenticate(user=user3)
        response = self.client.post(f'/api/bop-drops/{bop_drop.id}/like/')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class BopDropIntegrationTests(APITestCase):
    """Integration tests for bop drops with other app features"""
    
    def setUp(self):
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create friendship
        Friend.objects.create(
            requester=self.user1,
            recipient=self.user2,
            status='accepted'
        )
        
        self.client = APIClient()
    
    def test_bop_drop_feed_integration(self):
        """Test complete bop drop feed workflow"""
        self.client.force_authenticate(user=self.user1)
        
        # User2 creates bop drop
        bop_drop = BopDrop.objects.create(
            user=self.user2,
            track_id='spotify:track:123',
            track_title='Amazing Song',
            track_artist='Great Artist',
            caption='You have to hear this!',
            mood='energetic'
        )
        
        # User1 views feed
        response = self.client.get('/api/bop-drops/feed/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        
        # User1 likes the bop drop
        like_response = self.client.post(f'/api/bop-drops/{bop_drop.id}/like/')
        self.assertEqual(like_response.status_code, status.HTTP_201_CREATED)
        
        # User1 views the bop drop
        view_response = self.client.post(f'/api/bop-drops/{bop_drop.id}/view/')
        self.assertEqual(view_response.status_code, status.HTTP_200_OK)
        
        # Check that engagement is tracked
        bop_drop.refresh_from_db()
        self.assertEqual(bop_drop.like_count, 1)
        self.assertEqual(bop_drop.view_count, 1)
