from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import BopDropViewSet

router = DefaultRouter()
router.register(r'bop-drops', BopDropViewSet, basename='bop-drops')

# Custom URL patterns for specific endpoints
urlpatterns = [
    path('api/', include(router.urls)),
    # Custom endpoint for engagement with the URL structure you expect
    path('api/bop-drops/mybopdrop/engagement/', 
         BopDropViewSet.as_view({'get': 'mybopdrop_engagement'}), 
         name='mybopdrop-engagement'),
]

# Available endpoints:
# GET /api/bop-drops/ - List user's own bop drops
# POST /api/bop-drops/ - Create a new bop drop
# GET /api/bop-drops/{id}/ - Get specific bop drop
# PUT/PATCH /api/bop-drops/{id}/ - Update bop drop
# DELETE /api/bop-drops/{id}/ - Soft delete bop drop
# 
# GET /api/bop-drops/feed/ - Get friends' bop drops feed
# GET /api/bop-drops/shuffle/ - Get shuffled friends' bop drops
# GET /api/bop-drops/trending/ - Get trending bop drops from friends
# GET /api/bop-drops/by_mood/ - Get bop drops filtered by mood
# GET /api/bop-drops/mybopdrop/ - Get user's own bop drops with stats
# GET /api/bop-drops/mybopdrop/engagement/ - Get detailed engagement info (CUSTOM URL)
# GET /api/bop-drops/mybopdrop-engagement/ - Same endpoint via router (ALTERNATIVE URL)
# 
# POST /api/bop-drops/{id}/like/ - Like a bop drop
# POST /api/bop-drops/{id}/unlike/ - Unlike a bop drop
# POST /api/bop-drops/{id}/view/ - Record a view
# POST /api/bop-drops/{id}/share/ - Record a share 