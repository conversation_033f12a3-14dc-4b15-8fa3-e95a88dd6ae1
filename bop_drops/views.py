from django.shortcuts import render
from django.db import transaction
from django.db.models import Q, Count, Avg, Case, When, IntegerField, Prefetch, F, Sum
from django.utils import timezone
from datetime import timedel<PERSON>
import random

from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.core.cache import cache

from .models import BopDrop, BopDropLike, BopDropView
from .serializers import (
    BopDropSerializer, 
    BopDropCreateSerializer,
    BopDropFeedSerializer,
    BopDropLikeSerializer,
    BopDropStatsSerializer,
    MyBopDropDetailSerializer
)
from friends.models import Friend
import logging

logger = logging.getLogger('bopmaps')


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


class BopDropViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing bop drops with comprehensive social features
    """
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['track_title', 'track_artist', 'track_album', 'caption']
    ordering_fields = ['created_at', 'like_count', 'view_count']
    
    def get_serializer_class(self):
        if self.action == 'create':
            return BopDropCreateSerializer
        elif self.action == 'feed':
            return BopDropFeedSerializer
        return BopDropSerializer
    
    def get_queryset(self):
        """Return user's own bop drops"""
        return BopDrop.objects.filter(
            user=self.request.user,
            is_active=True
        ).select_related('user').order_by('-created_at')

    def get_object(self):
        """
        Override to allow access to friends' bop drops for certain actions
        """
        # For like, unlike, view, share actions, allow access to friends' bop drops
        if self.action in ['like', 'unlike', 'view', 'share']:
            try:
                obj = BopDrop.objects.select_related('user').get(pk=self.kwargs['pk'])
                # Check if it's user's own bop drop or from a friend
                if obj.user == self.request.user:
                    return obj
                
                # If it's friends_only, check if they're friends
                if obj.friends_only:
                    is_friend = Friend.objects.filter(
                        (Q(requester=self.request.user, recipient=obj.user) |
                         Q(requester=obj.user, recipient=self.request.user)),
                        status='accepted'
                    ).exists()
                    
                    if not is_friend:
                        from rest_framework.exceptions import PermissionDenied
                        raise PermissionDenied("You can only interact with bop drops from friends")
                
                return obj
            except BopDrop.DoesNotExist:
                from django.http import Http404
                raise Http404("BopDrop not found")
        
        # For other actions (list, retrieve, update, destroy), use default behavior
        return super().get_object()

    def create(self, request, *args, **kwargs):
        """Create a new bop drop and return complete object"""
        # Use create serializer for validation
        create_serializer = BopDropCreateSerializer(data=request.data, context={'request': request})
        create_serializer.is_valid(raise_exception=True)
        
        # Create the bop drop
        bop_drop = create_serializer.save()
        
        # Return the complete object using the full serializer
        response_serializer = BopDropSerializer(bop_drop, context={'request': request})
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    def perform_create(self, serializer):
        """Create a new bop drop"""
        serializer.save(user=self.request.user)

    def perform_destroy(self, instance):
        """Soft delete by setting is_active to False"""
        instance.is_active = False
        instance.save()

    @action(detail=False, methods=['GET'])
    def feed(self, request):
        """
        Get bop drops from friends - the main social feed
        IMPROVED: Added bulletproof user-specific filtering
        """
        user = request.user
        
        # 🎯 NEW: Check for specific user_id filter
        target_user_id = request.query_params.get('user_id')
        
        if target_user_id:
            # ✅ BULLETPROOF: Get drops from ONLY the specified user
            return self._get_user_specific_drops(request, target_user_id)
        
        # Original feed logic for mixed friends feed
        # Get user's friends
        friends_query = Friend.objects.filter(
            (Q(requester=user) | Q(recipient=user)),
            status='accepted'
        ).select_related('requester', 'recipient')
        
        # Extract friend user IDs
        friend_ids = set()
        for friendship in friends_query:
            if friendship.requester == user:
                friend_ids.add(friendship.recipient.id)
            else:
                friend_ids.add(friendship.requester.id)
        
        if not friend_ids:
            return Response([])
        
        # Get bop drops from friends - show ALL posts from friends
        queryset = BopDrop.objects.filter(
            user_id__in=friend_ids,
            is_active=True
        ).filter(
            Q(expires_at__isnull=True) | Q(expires_at__gt=timezone.now())
        ).select_related('user').prefetch_related(
            Prefetch(
                'likes',
                queryset=BopDropLike.objects.filter(user=user),
                to_attr='user_likes'
            )
        ).order_by('-created_at')
        
        # Apply filters
        mood_filter = request.query_params.get('mood')
        if mood_filter:
            queryset = queryset.filter(mood=mood_filter)
        
        currently_playing = request.query_params.get('currently_playing')
        if currently_playing == 'true':
            queryset = queryset.filter(is_currently_playing=True)
        
        # Paginate
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def _get_user_specific_drops(self, request, target_user_id):
        """
        🎯 BULLETPROOF: Get drops from ONLY the specified user
        This fixes the "same song for different friends" bug
        """
        import logging
        logger = logging.getLogger(__name__)
        
        # ✅ CRITICAL: Validate user_id parameter
        try:
            target_user_id = int(target_user_id)
        except (ValueError, TypeError):
            logger.error(f"🚨 Invalid user_id format: {target_user_id}")
            return Response({
                'error': 'Invalid user_id format. Must be an integer.',
                'provided_user_id': target_user_id
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # ✅ CRITICAL: Validate user exists
        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            target_user = User.objects.get(id=target_user_id)
        except User.DoesNotExist:
            logger.error(f"🚨 User not found: {target_user_id}")
            return Response({
                'error': 'User not found',
                'user_id': target_user_id
            }, status=status.HTTP_404_NOT_FOUND)
        
        # ✅ CRITICAL: Check friendship (privacy protection)
        if target_user != request.user:  # Allow users to see their own drops
            is_friend = Friend.objects.filter(
                (Q(requester=request.user, recipient=target_user) |
                 Q(requester=target_user, recipient=request.user)),
                status='accepted'
            ).exists()
            
            if not is_friend:
                logger.warning(f"🚨 User {request.user.id} tried to access drops from non-friend {target_user_id}")
                return Response({
                    'error': 'You can only view bop drops from friends',
                    'user_id': target_user_id
                }, status=status.HTTP_403_FORBIDDEN)
        
        # ✅ CRITICAL: Filter ONLY by the specific user - BULLETPROOF
        logger.info(f"🎯 Getting drops for user_id={target_user_id}, requested by user_id={request.user.id}")
        
        queryset = BopDrop.objects.filter(
            user_id=target_user_id,  # ✅ EXPLICIT: Only this user's drops
            is_active=True
        ).filter(
            Q(expires_at__isnull=True) | Q(expires_at__gt=timezone.now())
        ).select_related('user').prefetch_related(
            Prefetch(
                'likes',
                queryset=BopDropLike.objects.filter(user=request.user),
                to_attr='user_likes'
            )
        ).order_by('-created_at')
        
        # ✅ CRITICAL: Apply limit (for "latest track" requests)
        limit = request.query_params.get('limit')
        if limit:
            try:
                limit = int(limit)
                queryset = queryset[:limit]
            except (ValueError, TypeError):
                pass  # Ignore invalid limit values
        
        # ✅ CRITICAL: Validate every result belongs to the requested user
        drops_list = list(queryset)
        validated_drops = []
        
        for drop in drops_list:
            if drop.user.id != target_user_id:
                logger.error(f"🚨 CRITICAL ERROR: Drop {drop.id} belongs to user {drop.user.id}, not {target_user_id}")
                continue  # Skip this drop - should never happen but safety check
            validated_drops.append(drop)
            logger.debug(f"✅ Validated drop {drop.id} from user {drop.user.id} - track: '{drop.track_title}'")
        
        # Serialize and return
        serializer = BopDropFeedSerializer(validated_drops, many=True, context={'request': request})
        
        response_data = {
            'user_info': {
                'id': target_user.id,
                'username': target_user.username,
                'display_name': getattr(target_user, 'display_name', target_user.username) or target_user.username
            },
            'count': len(validated_drops),
            'results': serializer.data
        }
        
        logger.info(f"🎯 Returning {len(validated_drops)} drops for user {target_user_id}")
        return Response(response_data)

    @action(detail=False, methods=['GET'])
    def shuffle(self, request):
        """
        Get a random shuffled selection of friends' bop drops
        """
        user = request.user
        limit = int(request.query_params.get('limit', 10))
        
        # Get friends
        friends_query = Friend.objects.filter(
            (Q(requester=user) | Q(recipient=user)),
            status='accepted'
        ).select_related('requester', 'recipient')
        
        friend_ids = set()
        for friendship in friends_query:
            if friendship.requester == user:
                friend_ids.add(friendship.recipient.id)
            else:
                friend_ids.add(friendship.requester.id)
        
        if not friend_ids:
            return Response([])
        
        # Get all active bop drops from friends - show ALL posts from friends
        all_drops = list(BopDrop.objects.filter(
            user_id__in=friend_ids,
            is_active=True
        ).filter(
            Q(expires_at__isnull=True) | Q(expires_at__gt=timezone.now())
        ).select_related('user').prefetch_related(
            Prefetch(
                'likes',
                queryset=BopDropLike.objects.filter(user=user),
                to_attr='user_likes'
            )
        ))
        
        # Shuffle and limit
        random.shuffle(all_drops)
        shuffled_drops = all_drops[:limit]
        
        serializer = BopDropFeedSerializer(shuffled_drops, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['POST'])
    def like(self, request, pk=None):
        """Like a bop drop"""
        bop_drop = self.get_object()
        
        like, created = BopDropLike.objects.get_or_create(
            user=request.user,
            bop_drop=bop_drop
        )
        
        if created:
            return Response({"message": "Bop drop liked"}, status=status.HTTP_201_CREATED)
        else:
            return Response({"message": "Already liked"}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['POST'])
    def unlike(self, request, pk=None):
        """Unlike a bop drop"""
        bop_drop = self.get_object()
        
        try:
            like = BopDropLike.objects.get(user=request.user, bop_drop=bop_drop)
            like.delete()
            return Response({"message": "Bop drop unliked"})
        except BopDropLike.DoesNotExist:
            return Response(
                {"error": "You haven't liked this bop drop"},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['POST'])
    def view(self, request, pk=None):
        """Record a view for analytics"""
        bop_drop = self.get_object()
        
        # Record view (only once per user per bop drop)
        view, created = BopDropView.objects.get_or_create(
            user=request.user,
            bop_drop=bop_drop
        )
        
        if created:
            bop_drop.increment_view_count()
        
        return Response({"message": "View recorded"})

    @action(detail=True, methods=['POST'])
    def share(self, request, pk=None):
        """Record a share for analytics"""
        bop_drop = self.get_object()
        bop_drop.increment_share_count()
        return Response({"message": "Share recorded"})

    @action(detail=False, methods=['GET'])
    def my_stats(self, request):
        """Get user's bop drop statistics"""
        user = request.user
        
        # Calculate stats
        user_drops = BopDrop.objects.filter(user=user)
        
        total_drops = user_drops.count()
        active_drops = user_drops.filter(is_active=True).count()
        
        total_likes = user_drops.aggregate(
            total=Count('likes')
        )['total'] or 0
        
        total_views = user_drops.aggregate(
            total=Count('views')
        )['total'] or 0
        
        # Most liked track
        most_liked = user_drops.annotate(
            like_count_calc=Count('likes')
        ).order_by('-like_count_calc').first()
        
        most_liked_track = f"{most_liked.track_title} - {most_liked.track_artist}" if most_liked else "None"
        
        # Favorite mood
        favorite_mood_data = user_drops.values('mood').annotate(
            count=Count('mood')
        ).order_by('-count').first()
        
        favorite_mood = favorite_mood_data['mood'] if favorite_mood_data else "None"
        
        # Drops this week
        week_ago = timezone.now() - timedelta(days=7)
        drops_this_week = user_drops.filter(created_at__gte=week_ago).count()
        
        # Average likes per drop
        avg_likes = user_drops.aggregate(
            avg=Avg('like_count')
        )['avg'] or 0
        
        stats_data = {
            'total_drops': total_drops,
            'active_drops': active_drops,
            'total_likes_received': total_likes,
            'total_views_received': total_views,
            'most_liked_track': most_liked_track,
            'favorite_mood': favorite_mood,
            'drops_this_week': drops_this_week,
            'average_likes_per_drop': round(avg_likes, 2)
        }
        
        serializer = BopDropStatsSerializer(stats_data)
        return Response(serializer.data)

    @action(detail=False, methods=['GET'])
    def trending(self, request):
        """Get trending bop drops from friends"""
        user = request.user
        
        # Get friends
        friends_query = Friend.objects.filter(
            (Q(requester=user) | Q(recipient=user)),
            status='accepted'
        ).select_related('requester', 'recipient')
        
        friend_ids = set()
        for friendship in friends_query:
            if friendship.requester == user:
                friend_ids.add(friendship.recipient.id)
            else:
                friend_ids.add(friendship.requester.id)
        
        if not friend_ids:
            return Response([])
        
        # Get trending bop drops (high engagement in last 24 hours)
        last_24h = timezone.now() - timedelta(hours=24)
        
        queryset = BopDrop.objects.filter(
            user_id__in=friend_ids,
            is_active=True,
            friends_only=True,
            created_at__gte=last_24h
        ).filter(
            Q(expires_at__isnull=True) | Q(expires_at__gt=timezone.now())
        ).select_related('user').annotate(
            engagement_score=F('like_count') * 2 + F('view_count')
        ).order_by('-engagement_score')[:20]
        
        serializer = BopDropFeedSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['GET'])
    def by_mood(self, request):
        """Get bop drops filtered by mood"""
        mood = request.query_params.get('mood')
        if not mood:
            return Response({"error": "Mood parameter required"}, status=status.HTTP_400_BAD_REQUEST)
        
        user = request.user
        
        # Get friends
        friends_query = Friend.objects.filter(
            (Q(requester=user) | Q(recipient=user)),
            status='accepted'
        ).select_related('requester', 'recipient')
        
        friend_ids = set()
        for friendship in friends_query:
            if friendship.requester == user:
                friend_ids.add(friendship.recipient.id)
            else:
                friend_ids.add(friendship.requester.id)
        
        queryset = BopDrop.objects.filter(
            user_id__in=friend_ids,
            is_active=True,
            friends_only=True,
            mood=mood
        ).filter(
            Q(expires_at__isnull=True) | Q(expires_at__gt=timezone.now())
        ).select_related('user').order_by('-created_at')
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = BopDropFeedSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)
        
        serializer = BopDropFeedSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['GET'])
    def mybopdrop(self, request):
        """
        Get comprehensive information about user's BOP drops including all engagement details
        """
        try:
            user = request.user
            logger.info(f"Fetching bop drops for user {user.id}")
            
            # Get base queryset with minimal joins first
            queryset = BopDrop.objects.filter(
                user=user,
                is_active=True
            ).select_related('user')
            
            # Add counts using annotation instead of prefetch
            queryset = queryset.annotate(
                total_likes=Count('likes'),
                total_views=Count('views')
            ).order_by('-created_at')
            
            # Optional filtering
            time_filter = request.query_params.get('time')
            if time_filter == 'today':
                queryset = queryset.filter(created_at__date=timezone.now().date())
            elif time_filter == 'week':
                week_ago = timezone.now() - timedelta(days=7)
                queryset = queryset.filter(created_at__gte=week_ago)
            elif time_filter == 'month':
                month_ago = timezone.now() - timedelta(days=30)
                queryset = queryset.filter(created_at__gte=month_ago)
            
            # Get total engagement metrics
            total_engagement = queryset.aggregate(
                total_likes=Sum('like_count'),
                total_views=Sum('view_count')
            )
            
            total_likes = total_engagement.get('total_likes') or 0
            total_views = total_engagement.get('total_views') or 0
            
            # Get profile picture URL
            profile_pic_url = None
            if user.profile_pic:
                profile_pic_url = request.build_absolute_uri(user.profile_pic)
            
            response_data = {
                'user_info': {
                    'id': user.id,
                    'username': user.username,
                    'profile_pic': profile_pic_url,
                    'total_drops': queryset.count(),
                    'total_engagement': {
                        'likes': total_likes,
                        'views': total_views,
                        'engagement_rate': round((total_likes + total_views) / max(total_views, 1) * 100, 2) if total_views > 0 else 0
                    }
                },
                'bop_drops': BopDropSerializer(queryset, many=True, context={'request': request}).data
            }
            
            logger.info(f"Successfully fetched {len(response_data['bop_drops'])} bop drops")
            return Response(response_data)
            
        except Exception as e:
            logger.error(f"Error in mybopdrop endpoint: {str(e)}", exc_info=True)
            return Response(
                {"error": "An error occurred while fetching your bop drops", "detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['GET'])
    def mybopdrop_engagement(self, request):
        """
        Get detailed engagement information for user's BOP drops including user lists
        
        NOTE: There is NO comment system for bop drops - only likes, views, and shares.
        This endpoint provides accurate real-time counts directly from the database.
        """
        try:
            user = request.user
            logger.info(f"Fetching detailed engagement for user {user.id}")
            
            # Get base queryset with prefetched data for efficiency
            queryset = BopDrop.objects.filter(
                user=user,
                is_active=True
            ).select_related('user').prefetch_related(
                'likes__user',
                'views__user'
            ).order_by('-created_at')
            
            # Optional filtering by specific bop drop ID
            bop_drop_id = request.query_params.get('bop_drop_id')
            if bop_drop_id:
                try:
                    bop_drop_id = int(bop_drop_id)
                    queryset = queryset.filter(id=bop_drop_id)
                except (ValueError, TypeError):
                    return Response(
                        {"error": "Invalid bop_drop_id format"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Get profile picture URL
            profile_pic_url = None
            if user.profile_pic:
                profile_pic_url = request.build_absolute_uri(user.profile_pic)
            
            # Serialize with detailed engagement
            serializer = MyBopDropDetailSerializer(queryset, many=True, context={'request': request})
            
            response_data = {
                'user_info': {
                    'id': user.id,
                    'username': user.username,
                    'profile_pic': profile_pic_url,
                    'total_drops': queryset.count(),
                },
                'bop_drops': serializer.data,
                'notes': {
                    'comments_system': 'NOT_IMPLEMENTED',
                    'engagement_types': ['likes', 'views', 'shares'],
                    'counts_source': 'real_time_database_queries',
                    'upvotes_explanation': 'Upvotes = Likes (same thing)',
                    'cached_vs_realtime': 'This endpoint shows both cached and real-time counts for comparison'
                }
            }
            
            logger.info(f"Successfully fetched detailed engagement for {len(response_data['bop_drops'])} bop drops")
            return Response(response_data)
            
        except Exception as e:
            logger.error(f"Error in mybopdrop_engagement endpoint: {str(e)}", exc_info=True)
            return Response(
                {"error": "An error occurred while fetching engagement details", "detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
