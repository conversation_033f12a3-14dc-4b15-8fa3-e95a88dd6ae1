from django.contrib.admin import AdminSite
from django.shortcuts import redirect

class BopMapsAdminSite(AdminSite):
    site_header = 'BOPMaps Administration'
    site_title = 'BOPMaps Admin'
    index_title = 'BOPMaps Dashboard'
    
    def index(self, request, extra_context=None):
        """Use Django's default admin index page.

        The earlier redirect to a changelist caused a 500 error under the ASGI
        handling path. Falling back to the standard implementation restores
        compatibility.
        """
        return super().index(request, extra_context=extra_context)

# Create an instance of the custom admin site
bopmaps_admin_site = BopMapsAdminSite(name='bopmaps_admin') 