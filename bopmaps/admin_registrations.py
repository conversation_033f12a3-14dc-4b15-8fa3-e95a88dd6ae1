from django.contrib import admin
from django.utils import timezone
from .admin import bopmaps_admin_site
from users.models import User
from pins.models import (
    Pin, PinInteraction, VirtualPin, VirtualPinInteraction,
    Pin<PERSON>kin, UserSkin, Collection, CollectionPin
)
from friends.models import Friend
from music.models import MusicService, Genre, RecentTrack, Artist, Track
from gamification.models import Achievement, UserAchievement
from geo.models import TrendingArea, UserLocation
from bop_drops.models import BopDrop, BopDropLike, BopDropView
from notifications.models import (
    Notification, NotificationTemplate, UserNotificationSettings,
    OneSignalPlayerID, NotificationBatch
)
from seeding.models import (
    SeedContentDatabase, SeedingArea, SeedingMetrics, CuratorAccount, UserSeedingState
)
from django.contrib.admin.sites import NotRegistered

# Unregister User from custom admin site if already registered
try:
    bopmaps_admin_site.unregister(User)
except NotRegistered:
    pass

# Register Users models
@admin.register(User, site=bopmaps_admin_site)
class UserAdmin(admin.ModelAdmin):
    list_display = ('username', 'email', 'date_joined', 'is_staff')
    search_fields = ('username', 'email')
    list_filter = ('is_active', 'is_staff', 'date_joined')

# Register Pins models
@admin.register(Pin, site=bopmaps_admin_site)
class PinAdmin(admin.ModelAdmin):
    list_display = ('title', 'owner', 'created_at', 'is_private')
    search_fields = ('title', 'description')
    list_filter = ('is_private', 'created_at')

@admin.register(PinInteraction, site=bopmaps_admin_site)
class PinInteractionAdmin(admin.ModelAdmin):
    list_display = ('user', 'pin', 'interaction_type', 'created_at')
    list_filter = ('interaction_type', 'created_at')

@admin.register(VirtualPin, site=bopmaps_admin_site)
class VirtualPinAdmin(admin.ModelAdmin):
    list_display = ('title', 'owner', 'track_title', 'track_artist', 'service', 'created_at', 'is_private')
    search_fields = ('title', 'description', 'track_title', 'track_artist')
    list_filter = ('is_private', 'service', 'created_at')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(VirtualPinInteraction, site=bopmaps_admin_site)
class VirtualPinInteractionAdmin(admin.ModelAdmin):
    list_display = ('user', 'virtual_pin', 'interaction_type', 'created_at')
    list_filter = ('interaction_type', 'created_at')
    readonly_fields = ('created_at',)

# Register Collection models
@admin.register(Collection, site=bopmaps_admin_site)
class CollectionAdmin(admin.ModelAdmin):
    list_display = ('name', 'owner', 'is_public', 'created_at')
    search_fields = ('name', 'description', 'owner__username')
    list_filter = ('is_public', 'created_at')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('owner', 'name', 'description', 'is_public')
        }),
        ('Customization', {
            'fields': ('primary_color', 'cover_image_urls'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(CollectionPin, site=bopmaps_admin_site)
class CollectionPinAdmin(admin.ModelAdmin):
    list_display = ('collection', 'get_item_title', 'is_virtual', 'added_at')
    list_filter = ('added_at',)
    search_fields = ('collection__name', 'pin__title', 'virtual_pin__title')
    readonly_fields = ('added_at',)
    
    def get_item_title(self, obj):
        return obj.item.title if obj.item else 'No item'
    get_item_title.short_description = 'Item Title'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('collection', 'pin', 'virtual_pin')

# Register Skin System models
@admin.register(PinSkin, site=bopmaps_admin_site)
class PinSkinAdmin(admin.ModelAdmin):
    list_display = ('name', 'skin_type', 'artist', 'is_premium', 'challenge')
    list_filter = ('skin_type', 'is_premium', 'challenge')
    search_fields = ('name', 'description')
    readonly_fields = ('slug', 'created_at')

@admin.register(UserSkin, site=bopmaps_admin_site)
class UserSkinAdmin(admin.ModelAdmin):
    list_display = ('user', 'skin', 'unlocked_at')
    list_filter = ('unlocked_at', 'skin__skin_type')
    search_fields = ('user__username', 'skin__name')

# Register Friends models
@admin.register(Friend, site=bopmaps_admin_site)
class FriendAdmin(admin.ModelAdmin):
    list_display = ('requester', 'recipient', 'status', 'created_at')
    list_filter = ('status', 'created_at')

# Register Music models
@admin.register(Artist, site=bopmaps_admin_site)
class ArtistAdmin(admin.ModelAdmin):
    list_display = ('name', 'spotify_id', 'created_at')
    search_fields = ('name', 'spotify_id')

@admin.register(MusicService, site=bopmaps_admin_site)
class MusicServiceAdmin(admin.ModelAdmin):
    list_display = ('user', 'service_type', 'is_connected')
    list_filter = ('service_type',)
    
    def is_connected(self, obj):
        return obj.expires_at is not None and obj.expires_at > timezone.now()
    is_connected.boolean = True

@admin.register(Genre, site=bopmaps_admin_site)
class GenreAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(RecentTrack, site=bopmaps_admin_site)
class RecentTrackAdmin(admin.ModelAdmin):
    list_display = ('user', 'title', 'artist', 'played_at')
    search_fields = ('title', 'artist')
    list_filter = ('played_at',)

@admin.register(Track, site=bopmaps_admin_site)
class TrackAdmin(admin.ModelAdmin):
    list_display = ('title', 'artist', 'album', 'created_at')
    search_fields = ('title', 'artist', 'album')
    list_filter = ('created_at',)

# Register Gamification models
@admin.register(Achievement, site=bopmaps_admin_site)
class AchievementAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    search_fields = ('name', 'description')

@admin.register(UserAchievement, site=bopmaps_admin_site)
class UserAchievementAdmin(admin.ModelAdmin):
    list_display = ('user', 'achievement', 'completed_at')
    list_filter = ('completed_at',)

# Register Geo models
@admin.register(TrendingArea, site=bopmaps_admin_site)
class TrendingAreaAdmin(admin.ModelAdmin):
    list_display = ('name', 'last_updated')
    search_fields = ('name',)

@admin.register(UserLocation, site=bopmaps_admin_site)
class UserLocationAdmin(admin.ModelAdmin):
    list_display = ('user', 'timestamp')
    list_filter = ('timestamp',)

# Register Bop Drops models
@admin.register(BopDrop, site=bopmaps_admin_site)
class BopDropAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'user', 'track_title', 'track_artist', 'mood', 
        'is_active', 'like_count', 'view_count', 'created_at'
    ]
    list_filter = [
        'is_active', 'mood', 'music_service', 'friends_only', 
        'is_currently_playing', 'created_at'
    ]
    search_fields = [
        'track_title', 'track_artist', 'track_album', 'caption',
        'user__username', 'user__first_name', 'user__last_name'
    ]
    readonly_fields = [
        'created_at', 'updated_at', 'like_count', 'view_count', 'share_count'
    ]
    ordering = ['-created_at']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('User & Track Info', {
            'fields': ('user', 'track_id', 'track_title', 'track_artist', 'track_album')
        }),
        ('Track Details', {
            'fields': ('track_duration_ms', 'album_art_url', 'preview_url', 'music_service')
        }),
        ('Status Details', {
            'fields': (
                'caption', 'mood', 'is_currently_playing', 
                'friends_only', 'is_active', 'expires_at'
            )
        }),
        ('Analytics', {
            'fields': ('like_count', 'view_count', 'share_count'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')

@admin.register(BopDropLike, site=bopmaps_admin_site)
class BopDropLikeAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'bop_drop_title', 'bop_drop_artist', 'created_at']
    list_filter = ['created_at']
    search_fields = [
        'user__username', 'bop_drop__track_title', 'bop_drop__track_artist'
    ]
    readonly_fields = ['created_at']
    ordering = ['-created_at']
    date_hierarchy = 'created_at'
    
    def bop_drop_title(self, obj):
        return obj.bop_drop.track_title
    bop_drop_title.short_description = 'Track Title'
    
    def bop_drop_artist(self, obj):
        return obj.bop_drop.track_artist
    bop_drop_artist.short_description = 'Artist'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'bop_drop')

@admin.register(BopDropView, site=bopmaps_admin_site)
class BopDropViewAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'bop_drop_title', 'bop_drop_artist', 'viewed_at']
    list_filter = ['viewed_at']
    search_fields = [
        'user__username', 'bop_drop__track_title', 'bop_drop__track_artist'
    ]
    readonly_fields = ['viewed_at']
    ordering = ['-viewed_at']
    date_hierarchy = 'viewed_at'
    
    def bop_drop_title(self, obj):
        return obj.bop_drop.track_title
    bop_drop_title.short_description = 'Track Title'
    
    def bop_drop_artist(self, obj):
        return obj.bop_drop.track_artist
    bop_drop_artist.short_description = 'Artist'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'bop_drop')

# Register Notifications models
@admin.register(Notification, site=bopmaps_admin_site)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('title', 'recipient', 'notification_type', 'category', 'priority', 'is_read', 'is_sent', 'created_at')
    search_fields = ('title', 'message', 'recipient__username', 'recipient__email')
    list_filter = ('notification_type', 'category', 'priority', 'is_read', 'is_sent', 'delivery_status', 'created_at')
    readonly_fields = ('onesignal_id', 'onesignal_response', 'created_at', 'updated_at', 'sent_at', 'read_at')
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('recipient', 'notification_type', 'category', 'priority', 'title', 'message')
        }),
        ('Media & Actions', {
            'fields': ('image_url', 'action_data'),
            'classes': ('collapse',)
        }),
        ('Status', {
            'fields': ('is_read', 'read_at', 'is_sent', 'sent_at', 'delivery_status')
        }),
        ('OneSignal Integration', {
            'fields': ('onesignal_id', 'onesignal_response'),
            'classes': ('collapse',)
        }),
        ('Related Object', {
            'fields': ('content_type', 'object_id'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(NotificationTemplate, site=bopmaps_admin_site)
class NotificationTemplateAdmin(admin.ModelAdmin):
    list_display = ('notification_type', 'category', 'default_priority', 'is_active', 'created_at')
    search_fields = ('title_template', 'message_template')
    list_filter = ('notification_type', 'category', 'default_priority', 'is_active', 'created_at')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Template Information', {
            'fields': ('notification_type', 'category', 'default_priority', 'is_active')
        }),
        ('Template Content', {
            'fields': ('title_template', 'message_template')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(UserNotificationSettings, site=bopmaps_admin_site)
class UserNotificationSettingsAdmin(admin.ModelAdmin):
    list_display = ('user', 'push_notifications_enabled', 'email_notifications_enabled', 'quiet_hours_enabled', 'max_daily_notifications')
    search_fields = ('user__username', 'user__email')
    list_filter = ('push_notifications_enabled', 'email_notifications_enabled', 'quiet_hours_enabled', 'created_at')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Category Preferences', {
            'fields': ('map_enabled', 'social_enabled', 'music_enabled', 'gamification_enabled', 
                      'collection_enabled', 'exploration_enabled', 'customization_enabled', 'general_enabled')
        }),
        ('Delivery Preferences', {
            'fields': ('push_notifications_enabled', 'email_notifications_enabled')
        }),
        ('Quiet Hours', {
            'fields': ('quiet_hours_enabled', 'quiet_start_time', 'quiet_end_time'),
            'classes': ('collapse',)
        }),
        ('Limits', {
            'fields': ('max_daily_notifications',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(OneSignalPlayerID, site=bopmaps_admin_site)
class OneSignalPlayerIDAdmin(admin.ModelAdmin):
    list_display = ('user', 'platform', 'player_id', 'is_active', 'created_at')
    search_fields = ('user__username', 'user__email', 'player_id')
    list_filter = ('platform', 'is_active', 'created_at')
    readonly_fields = ('created_at', 'updated_at')
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('user')

@admin.register(NotificationBatch, site=bopmaps_admin_site)
class NotificationBatchAdmin(admin.ModelAdmin):
    list_display = ('batch_key', 'recipient', 'notification_type', 'event_count', 'is_sent', 'first_event_at', 'last_event_at')
    search_fields = ('batch_key', 'recipient__username', 'recipient__email')
    list_filter = ('notification_type', 'is_sent', 'first_event_at')
    readonly_fields = ('first_event_at', 'last_event_at', 'sent_at', 'aggregated_data')
    date_hierarchy = 'first_event_at'
    
    fieldsets = (
        ('Batch Information', {
            'fields': ('batch_key', 'recipient', 'notification_type')
        }),
        ('Configuration', {
            'fields': ('batch_size', 'batch_window_minutes')
        }),
        ('Status', {
            'fields': ('event_count', 'is_sent', 'sent_at')
        }),
        ('Timeline', {
            'fields': ('first_event_at', 'last_event_at')
        }),
        ('Data', {
            'fields': ('aggregated_data',),
            'classes': ('collapse',)
        })
    )

# Register Seeding models
@admin.register(SeedContentDatabase, site=bopmaps_admin_site)
class SeedContentDatabaseAdmin(admin.ModelAdmin):
    list_display = ['version', 'is_active', 'total_tracks', 'cities_covered', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['version']
    readonly_fields = ['created_at', 'content_preview']

    fieldsets = (
        ('Basic Information', {
            'fields': ('version', 'is_active', 'created_at')
        }),
        ('Content Statistics', {
            'fields': ('total_tracks', 'cities_covered', 'last_api_update')
        }),
        ('Content Data', {
            'fields': ('content_data', 'content_preview'),
            'classes': ('collapse',)
        }),
    )

    def content_preview(self, obj):
        """Show a preview of the content structure"""
        if obj.content_data:
            import json
            try:
                preview = json.dumps(obj.content_data, indent=2)[:500]
                if len(preview) >= 500:
                    preview += "..."
                return f"<pre>{preview}</pre>"
            except:
                return "Invalid JSON"
        return "No content"

    content_preview.allow_tags = True
    content_preview.short_description = "Content Preview"

@admin.register(SeedingArea, site=bopmaps_admin_site)
class SeedingAreaAdmin(admin.ModelAdmin):
    list_display = ['city_name', 'seed_count', 'organic_pin_count', 'strategy_used', 'created_at']
    list_filter = ['strategy_used', 'country_code', 'created_at']
    search_fields = ['city_name', 'country_code']
    readonly_fields = ['created_at', 'last_organic_check', 'location_link']

    fieldsets = (
        ('Location Information', {
            'fields': ('center_point', 'location_link', 'radius_km', 'city_name', 'country_code')
        }),
        ('Seeding Details', {
            'fields': ('seed_count', 'organic_pin_count', 'strategy_used', 'content_version')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'last_organic_check')
        }),
    )

    def location_link(self, obj):
        """Generate a Google Maps link for the seeding area"""
        if obj.center_point:
            lat, lng = obj.center_point.y, obj.center_point.x
            url = f"https://maps.google.com/?q={lat},{lng}"
            return f'<a href="{url}" target="_blank">View on Google Maps</a>'
        return "No location"

    location_link.allow_tags = True
    location_link.short_description = "Location"

@admin.register(SeedingMetrics, site=bopmaps_admin_site)
class SeedingMetricsAdmin(admin.ModelAdmin):
    list_display = ['date', 'areas_seeded', 'pins_generated', 'seed_pin_views', 'avg_user_engagement']
    list_filter = ['date']
    readonly_fields = ['date']

    fieldsets = (
        ('Date', {
            'fields': ('date',)
        }),
        ('Seeding Activity', {
            'fields': ('areas_seeded', 'pins_generated', 'seeds_expired', 'seeds_retired')
        }),
        ('User Engagement', {
            'fields': ('seed_pin_views', 'seed_pin_collects', 'seed_pin_likes', 'avg_user_engagement')
        }),
        ('System Performance', {
            'fields': ('avg_seeding_response_time', 'api_failures', 'cache_hit_rate')
        }),
    )

    def has_add_permission(self, request):
        """Prevent manual creation of metrics (should be auto-generated)"""
        return False

@admin.register(CuratorAccount, site=bopmaps_admin_site)
class CuratorAccountAdmin(admin.ModelAdmin):
    list_display = ['user', 'persona_type', 'pins_created', 'last_used', 'is_active']
    list_filter = ['persona_type', 'is_active', 'created_at']
    search_fields = ['user__username', 'user__email', 'user__first_name', 'user__last_name']
    readonly_fields = ['pins_created', 'last_used', 'created_at', 'user_link', 'profile_picture_preview']

    fieldsets = (
        ('User Information', {
            'fields': ('user', 'user_link', 'profile_picture_preview', 'persona_type', 'is_active')
        }),
        ('Preferences', {
            'fields': ('preferred_genres', 'preferred_locations', 'assigned_cities')
        }),
        ('Activity', {
            'fields': ('pins_created', 'last_used', 'created_at')
        }),
    )

    def user_link(self, obj):
        """Link to the user admin page"""
        if obj.user:
            from django.urls import reverse
            from django.utils.html import format_html
            url = reverse('admin:users_user_change', args=[obj.user.pk])
            return format_html('<a href="{}">View User Profile</a>', url)
        return "No user"

    user_link.short_description = "User Profile"

    def profile_picture_preview(self, obj):
        """Show profile picture preview"""
        if obj.user and obj.user.profile_pic:
            from django.utils.html import format_html
            return format_html(
                '<img src="{}" style="width: 50px; height: 50px; border-radius: 50%;" />',
                obj.user.profile_pic
            )
        return "No profile picture"

    profile_picture_preview.short_description = "Profile Picture"

    actions = ['activate_curators', 'deactivate_curators']

    def activate_curators(self, request, queryset):
        queryset.update(is_active=True)
        self.message_user(request, f"Activated {queryset.count()} curator(s)")

    activate_curators.short_description = "Activate selected curators"

    def deactivate_curators(self, request, queryset):
        queryset.update(is_active=False)
        self.message_user(request, f"Deactivated {queryset.count()} curator(s)")

    deactivate_curators.short_description = "Deactivate selected curators"

@admin.register(UserSeedingState, site=bopmaps_admin_site)
class UserSeedingStateAdmin(admin.ModelAdmin):
    list_display = ['user', 'current_state', 'first_seeding_at', 'personalized_seeding_at', 'full_seeding_at']
    list_filter = ['current_state', 'first_seeding_at', 'personalized_seeding_at', 'full_seeding_at']
    search_fields = ['user__username', 'user__email', 'user__first_name', 'user__last_name']
    readonly_fields = ['first_seeding_at', 'personalized_seeding_at', 'full_seeding_at']

    fieldsets = (
        ('User Information', {
            'fields': ('user', 'current_state')
        }),
        ('Seeding History', {
            'fields': ('first_seeding_at', 'personalized_seeding_at', 'full_seeding_at')
        }),
        ('Geographic Tracking', {
            'fields': ('personalized_seeding_areas', 'full_seeding_areas'),
            'classes': ('collapse',)
        }),
    )