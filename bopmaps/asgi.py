"""
ASGI config for bopmaps project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/asgi/
"""

import os
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from django.core.asgi import get_asgi_application
import geo.routing
import friends.routing
import gamification.routing

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "bopmaps.settings")

# Remove the 'music.routing' import if it's not being used
# If you need it, make sure the file exists and is correctly structured.

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter(
            geo.routing.websocket_urlpatterns +
            friends.routing.websocket_urlpatterns +
            gamification.routing.websocket_urlpatterns
        )
    ),
})
