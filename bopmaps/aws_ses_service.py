"""
AWS SES Email Service for BOPMaps
Handles sending emails via Amazon Simple Email Service with template support
"""
import boto3
import logging
from django.conf import settings
from typing import Dict, Any, Optional
from botocore.exceptions import ClientError, BotoCoreError

logger = logging.getLogger('bopmaps')


class SESEmailService:
    """
    Service class for sending emails via AWS SES
    """
    
    def __init__(self):
        """Initialize SES client"""
        # Use IAM role credentials for ECS tasks instead of explicit credentials
        self.ses_client = boto3.client(
            'ses',
            region_name=getattr(settings, 'AWS_REGION', 'us-east-1')
        )
        self.configuration_set = getattr(settings, 'SES_CONFIGURATION_SET', 'bopmaps-emails')
        self.from_email = getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')
        
    def send_templated_email(
        self,
        template_name: str,
        recipient_email: str,
        template_data: Dict[str, Any],
        source_email: Optional[str] = None,
        subject: Optional[str] = None
    ) -> bool:
        """
        Send a templated email via SES
        
        Args:
            template_name: Name of the SES template to use
            recipient_email: Email address of the recipient
            template_data: Dictionary of template variables
            source_email: Optional sender email (defaults to configured from_email)
            subject: Optional subject for the email
            
        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        try:
            sender_email = source_email or self.from_email
            
            # Prepare template data as JSON string
            import json
            template_data_json = json.dumps(template_data)
            
            # Prepare subject if provided, otherwise use a default
            email_subject = subject if subject else f"🎵 BOP Maps - {template_name.replace('-', ' ').title()}"
            
            # Send the email
            response = self.ses_client.send_templated_email(
                Source=sender_email,
                Destination={
                    'ToAddresses': [recipient_email]
                },
                Template=template_name,
                TemplateData=template_data_json,
                ConfigurationSetName=self.configuration_set,
                Subject={'Data': email_subject}, # Add subject to the email
                Tags=[
                    {
                        'Name': 'EmailType',
                        'Value': template_name
                    },
                    {
                        'Name': 'Application',
                        'Value': 'BOPMaps'
                    }
                ]
            )
            
            message_id = response.get('MessageId')
            logger.info(f"Email sent successfully via SES. Template: {template_name}, "
                       f"Recipient: {recipient_email}, MessageId: {message_id}")
            
            return True
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"SES ClientError sending email. Template: {template_name}, "
                        f"Recipient: {recipient_email}, Error: {error_code} - {error_message}")
            return False
            
        except BotoCoreError as e:
            logger.error(f"SES BotoCoreError sending email. Template: {template_name}, "
                        f"Recipient: {recipient_email}, Error: {str(e)}")
            return False
            
        except Exception as e:
            logger.error(f"Unexpected error sending email via SES. Template: {template_name}, "
                        f"Recipient: {recipient_email}, Error: {str(e)}")
            return False
    
    def send_user_verification_email(
        self,
        recipient_email: str,
        username: str,
        verification_code: str
    ) -> bool:
        """
        Send user email verification code
        
        Args:
            recipient_email: Email address to send verification to
            username: User's username
            verification_code: 6-digit verification code
            
        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        template_data = {
            'username': username,
            'code': verification_code
        }
        
        # Include verification code in subject line
        subject = f'🔐 {verification_code} - Your BOPMaps Code'
        
        return self.send_templated_email(
            template_name='user-verification',
            recipient_email=recipient_email,
            template_data=template_data,
            subject=subject  # Add subject parameter
        )
    
    def send_school_verification_email(
        self,
        recipient_email: str,
        username: str,
        school_name: str,
        verification_code: str
    ) -> bool:
        """
        Send school email verification code
        
        Args:
            recipient_email: School email address to send verification to
            username: User's username
            school_name: Name of the school
            verification_code: 6-digit verification code
            
        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        template_data = {
            'username': username,
            'school_name': school_name,
            'code': verification_code
        }
        
        # Include verification code in subject line
        subject = f'🎓 {school_name} Verification Code: {verification_code}'
        
        return self.send_templated_email(
            template_name='school-verification',
            recipient_email=recipient_email,
            template_data=template_data,
            subject=subject  # Add subject parameter
        )
    
    def send_school_verification_success_email(
        self,
        recipient_email: str,
        username: str,
        school_name: str
    ) -> bool:
        """
        Send school verification success email
        
        Args:
            recipient_email: School email address to send success notification to
            username: User's username
            school_name: Name of the school
            
        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        template_data = {
            'username': username,
            'school_name': school_name
        }
        
        return self.send_templated_email(
            template_name='school-verification-success',
            recipient_email=recipient_email,
            template_data=template_data
        )
    
    def send_raw_email(
        self,
        recipient_email: str,
        subject: str,
        html_body: str,
        text_body: str,
        source_email: Optional[str] = None
    ) -> bool:
        """
        Send a raw email (non-templated) via SES
        
        Args:
            recipient_email: Email address of the recipient
            subject: Email subject
            html_body: HTML email content
            text_body: Plain text email content
            source_email: Optional sender email (defaults to configured from_email)
            
        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        try:
            sender_email = source_email or self.from_email
            
            # Prepare the email message
            message = {
                'Subject': {'Data': subject},
                'Body': {
                    'Text': {'Data': text_body},
                    'Html': {'Data': html_body}
                }
            }
            
            # Send the email
            response = self.ses_client.send_email(
                Source=sender_email,
                Destination={'ToAddresses': [recipient_email]},
                Message=message,
                ConfigurationSetName=self.configuration_set,
                Tags=[
                    {
                        'Name': 'EmailType',
                        'Value': 'raw-email'
                    },
                    {
                        'Name': 'Application',
                        'Value': 'BOPMaps'
                    }
                ]
            )
            
            message_id = response.get('MessageId')
            logger.info(f"Raw email sent successfully via SES. Subject: {subject}, "
                       f"Recipient: {recipient_email}, MessageId: {message_id}")
            
            return True
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"SES ClientError sending raw email. Subject: {subject}, "
                        f"Recipient: {recipient_email}, Error: {error_code} - {error_message}")
            return False
            
        except BotoCoreError as e:
            logger.error(f"SES BotoCoreError sending raw email. Subject: {subject}, "
                        f"Recipient: {recipient_email}, Error: {str(e)}")
            return False
            
        except Exception as e:
            logger.error(f"Unexpected error sending raw email via SES. Subject: {subject}, "
                        f"Recipient: {recipient_email}, Error: {str(e)}")
            return False
    
    def send_test_email(self, recipient_email: str) -> bool:
        """
        Send a test email to verify SES configuration
        
        Args:
            recipient_email: Email address to send test email to
            
        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        subject = "🎵 BOP Maps - SES Test Email"
        html_body = """
        <html>
        <body style="font-family: Arial, sans-serif; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2196F3;">🎵 BOP Maps SES Test</h2>
                <p>This is a test email to verify that AWS SES is working correctly.</p>
                <p>If you received this email, your SES configuration is working! 🎉</p>
                <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                <p style="color: #999; font-size: 14px;">
                    Best regards,<br>
                    The BOP Maps Team
                </p>
            </div>
        </body>
        </html>
        """
        text_body = """
        BOP Maps SES Test
        
        This is a test email to verify that AWS SES is working correctly.
        If you received this email, your SES configuration is working! 🎉
        
        Best regards,
        The BOP Maps Team
        """
        
        return self.send_raw_email(
            recipient_email=recipient_email,
            subject=subject,
            html_body=html_body,
            text_body=text_body
        )


# Global instance
ses_service = SESEmailService() 