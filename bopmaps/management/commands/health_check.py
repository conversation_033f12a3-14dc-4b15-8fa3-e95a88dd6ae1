from django.core.management.base import BaseCommand
from django.db import connection
from django.core.cache import cache
import sys


class Command(BaseCommand):
    help = 'Check the health of the Django application'

    def handle(self, *args, **options):
        errors = []
        
        # Check database connection
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            self.stdout.write(self.style.SUCCESS('✓ Database connection: OK'))
        except Exception as e:
            error_msg = f'✗ Database connection failed: {e}'
            self.stdout.write(self.style.ERROR(error_msg))
            errors.append(error_msg)
        
        # Check cache connection
        try:
            cache.set('health_check', 'test', 10)
            if cache.get('health_check') == 'test':
                self.stdout.write(self.style.SUCCESS('✓ Cache connection: OK'))
                cache.delete('health_check')
            else:
                raise Exception('Cache test failed')
        except Exception as e:
            error_msg = f'✗ Cache connection failed: {e}'
            self.stdout.write(self.style.WARNING(error_msg))
            # Cache failures are not critical
        
        # Check Django settings
        try:
            from django.conf import settings
            if settings.DEBUG:
                self.stdout.write(self.style.WARNING('⚠ DEBUG mode is enabled'))
            else:
                self.stdout.write(self.style.SUCCESS('✓ Production mode'))
        except Exception as e:
            error_msg = f'✗ Settings check failed: {e}'
            self.stdout.write(self.style.ERROR(error_msg))
            errors.append(error_msg)
        
        if errors:
            self.stdout.write(self.style.ERROR(f'\nHealth check failed with {len(errors)} errors'))
            sys.exit(1)
        else:
            self.stdout.write(self.style.SUCCESS('\n✓ All health checks passed'))
            sys.exit(0) 