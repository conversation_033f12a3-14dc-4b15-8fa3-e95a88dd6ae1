from django.core.management.base import BaseCommand
from django.db import connection
from django.core.management import call_command
import sys


class Command(BaseCommand):
    help = 'Setup database with required extensions and run migrations'

    def handle(self, *args, **options):
        try:
            # Create required extensions
            self.stdout.write('🔧 Setting up database extensions...')
            
            with connection.cursor() as cursor:
                # Create PostGIS extension
                cursor.execute("CREATE EXTENSION IF NOT EXISTS postgis;")
                self.stdout.write(self.style.SUCCESS('✓ PostGIS extension created'))
                
                # Create hstore extension
                cursor.execute("CREATE EXTENSION IF NOT EXISTS hstore;")
                self.stdout.write(self.style.SUCCESS('✓ hstore extension created'))
                
                # Create uuid extension
                cursor.execute("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";")
                self.stdout.write(self.style.SUCCESS('✓ uuid-ossp extension created'))
                
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Extension creation had issues: {e}')
            )
            self.stdout.write('🔄 Continuing with migrations...')
        
        # Run migrations
        try:
            self.stdout.write('📊 Running database migrations...')
            call_command('migrate', verbosity=2)
            self.stdout.write(self.style.SUCCESS('✅ Database migrations completed'))
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Migration failed: {e}')
            )
            sys.exit(1)
        
        # Create superuser if it doesn't exist
        try:
            self.stdout.write('👤 Setting up admin user...')
            call_command('shell', '-c', """
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(is_superuser=True).exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('✅ Admin user created')
else:
    print('✅ Admin user already exists')
""")
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Admin user setup had issues: {e}')
            )
        
        self.stdout.write(self.style.SUCCESS('🎉 Database setup completed!')) 