"""
Django management command to test AWS SES email sending
"""
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from bopmaps.aws_ses_service import ses_service
import logging

logger = logging.getLogger('bopmaps')


class Command(BaseCommand):
    help = 'Test AWS SES email sending functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            'recipient_email',
            type=str,
            help='Email address to send test email to'
        )
        parser.add_argument(
            '--template',
            type=str,
            choices=['user', 'school', 'school-success', 'raw'],
            default='raw',
            help='Type of email template to test (default: raw)'
        )
        parser.add_argument(
            '--username',
            type=str,
            default='TestUser',
            help='Username for template testing (default: TestUser)'
        )
        parser.add_argument(
            '--school',
            type=str,
            default='Test University',
            help='School name for school verification templates (default: Test University)'
        )
        parser.add_argument(
            '--code',
            type=str,
            default='123456',
            help='Verification code for templates (default: 123456)'
        )

    def handle(self, *args, **options):
        recipient_email = options['recipient_email']
        template_type = options['template']
        username = options['username']
        school_name = options['school']
        verification_code = options['code']

        self.stdout.write(f"Testing SES email sending to: {recipient_email}")
        self.stdout.write(f"Template type: {template_type}")
        self.stdout.write(f"AWS Region: {getattr(settings, 'AWS_REGION', 'us-east-1')}")
        self.stdout.write(f"SES Configuration Set: {getattr(settings, 'SES_CONFIGURATION_SET', 'bopmaps-emails')}")
        self.stdout.write(f"From Email: {getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')}")
        self.stdout.write("-" * 50)

        try:
            success = False
            
            if template_type == 'user':
                self.stdout.write("Testing user verification email template...")
                success = ses_service.send_user_verification_email(
                    recipient_email=recipient_email,
                    username=username,
                    verification_code=verification_code
                )
                
            elif template_type == 'school':
                self.stdout.write("Testing school verification email template...")
                success = ses_service.send_school_verification_email(
                    recipient_email=recipient_email,
                    username=username,
                    school_name=school_name,
                    verification_code=verification_code
                )
                
            elif template_type == 'school-success':
                self.stdout.write("Testing school verification success email template...")
                success = ses_service.send_school_verification_success_email(
                    recipient_email=recipient_email,
                    username=username,
                    school_name=school_name
                )
                
            elif template_type == 'raw':
                self.stdout.write("Testing raw email sending...")
                success = ses_service.send_test_email(recipient_email)
                
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f"✅ Email sent successfully to {recipient_email}!")
                )
                self.stdout.write("Check your email inbox (and spam folder) for the test email.")
                
                if template_type != 'raw':
                    self.stdout.write("\nTemplate variables used:")
                    self.stdout.write(f"  - Username: {username}")
                    if template_type in ['school', 'school-success']:
                        self.stdout.write(f"  - School: {school_name}")
                    if template_type in ['user', 'school']:
                        self.stdout.write(f"  - Code: {verification_code}")
                        
            else:
                self.stdout.write(
                    self.style.ERROR(f"❌ Failed to send email to {recipient_email}")
                )
                self.stdout.write("Check the logs for more details.")
                
        except Exception as e:
            logger.error(f"Error in SES test command: {str(e)}")
            raise CommandError(f"SES test failed: {str(e)}")

        self.stdout.write("-" * 50)
        self.stdout.write("SES test completed.")
        
        # Additional configuration info
        self.stdout.write("\n📋 Configuration Info:")
        self.stdout.write(f"  Environment: {getattr(settings, 'ENVIRONMENT', 'development')}")
        self.stdout.write(f"  Debug: {getattr(settings, 'DEBUG', False)}")
        self.stdout.write(f"  Email Backend: {getattr(settings, 'EMAIL_BACKEND', 'Not set')}")
        
        # Check for AWS credentials
        aws_access_key = getattr(settings, 'AWS_ACCESS_KEY_ID', None)
        if aws_access_key:
            self.stdout.write(f"  AWS Access Key: {'*' * (len(aws_access_key) - 4) + aws_access_key[-4:]}")
        else:
            self.stdout.write("  AWS Access Key: Using IAM role or environment variables")
            
        self.stdout.write("\n💡 Tips:")
        self.stdout.write("  - Make sure your email domain is verified in SES")
        self.stdout.write("  - Check SES sending statistics in AWS Console")
        self.stdout.write("  - Verify the recipient email is not on the SES suppression list")
        self.stdout.write("  - For production: move out of SES sandbox mode") 