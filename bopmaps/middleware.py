import time
import logging
import json
from django.utils import timezone
from django.conf import settings
from asgiref.sync import sync_to_async, iscoroutinefunction
from django.utils.decorators import sync_and_async_middleware

logger = logging.getLogger('bopmaps')

@sync_and_async_middleware
def RequestLogMiddleware(get_response):
    """
    Middleware that logs all requests including timing information.
    """
    if iscoroutinefunction(get_response):
        async def middleware(request):
            start_time = time.time()
            
            response = await get_response(request)
            
            duration = time.time() - start_time
            
            # Get user info
            user = None
            user_id = None
            try:
                if hasattr(request, 'user') and request.user.is_authenticated:
                    user = request.user.username
                    user_id = request.user.id
            except Exception as e:
                logger.debug(f"Could not access user info in sync context: {str(e)}")
                
            # Log request details
            log_data = {
                'method': request.method,
                'path': request.path,
                'user': user,
                'user_id': user_id,
                'status_code': response.status_code,
                'duration': round(duration * 1000, 2),  # Convert to ms
                'content_length': len(response.content) if hasattr(response, 'content') else 0,
            }
            
            # Only include query params in debug mode
            if settings.DEBUG:
                log_data['query_params'] = dict(request.GET.items())
                
            # Log the request
            if response.status_code >= 500:
                logger.error(f"Request: {json.dumps(log_data)}")
            elif response.status_code >= 400:
                logger.warning(f"Request: {json.dumps(log_data)}")
            else:
                logger.info(f"Request: {json.dumps(log_data)}")
                
            return response
    else:
        def middleware(request):
            start_time = time.time()
            
            response = get_response(request)
            
            duration = time.time() - start_time
            
            # Get user info
            user = None
            user_id = None
            if hasattr(request, 'user') and request.user.is_authenticated:
                user = request.user.username
                user_id = request.user.id
                
            # Log request details
            log_data = {
                'method': request.method,
                'path': request.path,
                'user': user,
                'user_id': user_id,
                'status_code': response.status_code,
                'duration': round(duration * 1000, 2),  # Convert to ms
                'content_length': len(response.content) if hasattr(response, 'content') else 0,
            }
            
            # Only include query params in debug mode
            if settings.DEBUG:
                log_data['query_params'] = dict(request.GET.items())
                
            # Log the request
            if response.status_code >= 500:
                logger.error(f"Request: {json.dumps(log_data)}")
            elif response.status_code >= 400:
                logger.warning(f"Request: {json.dumps(log_data)}")
            else:
                logger.info(f"Request: {json.dumps(log_data)}")
                
            return response
    
    return middleware


@sync_and_async_middleware
def UpdateLastActivityMiddleware(get_response):
    """
    Middleware that updates a user's last_active timestamp.
    Enhanced with error handling to prevent authentication issues.
    """
    if iscoroutinefunction(get_response):
        async def middleware(request):
            response = await get_response(request)

            # Enhanced user authentication check
            try:
                if (hasattr(request, 'user') and 
                    request.user.is_authenticated and 
                    hasattr(request.user, 'id') and 
                    request.user.id is not None):
                    
                    # Limit the frequency of updates to avoid excessive database writes
                    # Only update if the user doesn't have a last_active timestamp
                    # or if it's been more than 15 minutes since the last update
                    update_interval = getattr(settings, 'LAST_ACTIVE_UPDATE_INTERVAL', 15 * 60)  # 15 minutes in seconds
                    
                    user = request.user
                    now = timezone.now()
                    
                    if not user.last_active or (now - user.last_active).total_seconds() > update_interval:
                        try:
                            # Using update_fields to minimize the database operation
                            user.last_active = now
                            save_user = sync_to_async(user.save)
                            await save_user(update_fields=['last_active'])
                        except Exception as e:
                            logger.warning(f"Error updating last_active for user {user.username}: {str(e)}")
            except Exception as e:
                # Log authentication-related errors but don't break the request
                logger.warning(f"Error in UpdateLastActivityMiddleware: {str(e)}")
                        
            return response
    else:
        def middleware(request):
            response = get_response(request)

            # Enhanced user authentication check
            try:
                if (hasattr(request, 'user') and 
                    request.user.is_authenticated and 
                    hasattr(request.user, 'id') and 
                    request.user.id is not None):
                    
                    # Limit the frequency of updates to avoid excessive database writes
                    # Only update if the user doesn't have a last_active timestamp
                    # or if it's been more than 15 minutes since the last update
                    update_interval = getattr(settings, 'LAST_ACTIVE_UPDATE_INTERVAL', 15 * 60)  # 15 minutes in seconds
                    
                    user = request.user
                    now = timezone.now()
                    
                    if not user.last_active or (now - user.last_active).total_seconds() > update_interval:
                        try:
                            # Using update_fields to minimize the database operation
                            user.last_active = now
                            user.save(update_fields=['last_active'])
                        except Exception as e:
                            logger.warning(f"Error updating last_active for user {user.username}: {str(e)}")
            except Exception as e:
                # Log authentication-related errors but don't break the request
                logger.warning(f"Error in UpdateLastActivityMiddleware: {str(e)}")
                        
            return response
    
    return middleware 