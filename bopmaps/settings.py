"""
Django settings for bopmaps project.

Generated by 'django-admin startproject' using Django 4.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from decouple import config
import dj_database_url
import sys # Ensure sys is imported at the top

# Import Redis exceptions for cache configuration
try:
    from redis.exceptions import ConnectionError as RedisConnectionError, TimeoutError as RedisTimeoutError
except ImportError:
    # Fallback for when redis is not available
    RedisConnectionError = Exception
    RedisTimeoutError = Exception

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# GDAL Configuration for GeoDjango
# Use environment-specific paths - Docker containers have different paths than macOS
if os.path.exists('/opt/homebrew/lib/libgdal.dylib'):
    # macOS with Homebrew
    GDAL_LIBRARY_PATH = '/opt/homebrew/lib/libgdal.dylib'
    GEOS_LIBRARY_PATH = '/opt/homebrew/lib/libgeos_c.dylib'
elif os.path.exists('/usr/lib/x86_64-linux-gnu/libgdal.so'):
    # Ubuntu/Debian Linux (Docker containers)
    GDAL_LIBRARY_PATH = '/usr/lib/x86_64-linux-gnu/libgdal.so'
    GEOS_LIBRARY_PATH = '/usr/lib/x86_64-linux-gnu/libgeos_c.so'
else:
    # Let Django auto-detect (fallback)
    pass

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config('SECRET_KEY', default='django-insecure-replace-this-in-production')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config('DEBUG', default=True, cast=bool)

# Determine if running tests
IS_TESTING = 'test' in sys.argv

ALLOWED_HOSTS = ['*']  # Allow all hosts for demo purposes

# Ensure proper error handling for admin and APIs
ADMINS = [('BOPMaps Admin', '<EMAIL>')]
MANAGERS = ADMINS

# Append slash setting (might help with 301 redirects)
APPEND_SLASH = True

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'bopmaps': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.gis',
    
    # Third-party apps
    'rest_framework',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    'corsheaders',
    'django_filters',
    'leaflet',
    'drf_spectacular',
    'drf_yasg',
    'django_redis',
    'django_celery_beat',
    'storages',
    
    # Project apps
    'users',
    'pins',
    'friends',
    'music',
    'gamification',
    'geo',
    'votes',
    'comments',
    'challenges',  # Weekly music challenges
    'rankings',  # Global user rankings
    'verification',  # School verification system
    'bop_drops',  # Music status sharing
    'notifications',  # Push notifications
    'lastfm',  # Last.fm caching service
    'spotify',  # Spotify caching service
    'youtube',  # YouTube caching service
    'youtube_music',  # YouTube Music caching service
    'moderation',  # User blocking and reporting
    'tiles',  # OpenMapTiles planet data processing
    'seeding',  # Music pin seeding system
]

if DEBUG and not IS_TESTING:
    INSTALLED_APPS.append('debug_toolbar')

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'spotify.middleware.ConnectionPoolingMiddleware',  # Early connection management
    'whitenoise.middleware.WhiteNoiseMiddleware',  # Moved up for better static file handling
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',  # Critical: Auth before custom middleware
    'spotify.middleware.SpotifyCacheMiddleware',  # After auth for better caching
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'bopmaps.middleware.RequestLogMiddleware',  # After auth so we can log authenticated users
    'bopmaps.middleware.UpdateLastActivityMiddleware',  # After auth to update user activity
    'spotify.middleware.DatabaseConnectionMiddleware',  # Late cleanup
]

if DEBUG and not IS_TESTING:
    MIDDLEWARE.append('debug_toolbar.middleware.DebugToolbarMiddleware')

ROOT_URLCONF = 'bopmaps.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'bopmaps.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': dj_database_url.config(
        default=config('DATABASE_URL', default='postgis://postgres:password@localhost:5432/bopmaps'),
        conn_max_age=300,  # Use connection pooling with 5-minute timeout
        conn_health_checks=True,
    )
}

# Ensure we use PostGIS backend for geographic features
DATABASES['default']['ENGINE'] = 'django.contrib.gis.db.backends.postgis'

# Add database connection management settings
# Use connection pooling to prevent connection exhaustion
if not DEBUG:
    # In production, use connection pooling with reasonable timeouts
    DATABASES['default']['conn_max_age'] = 300  # 5 minutes
    DATABASES['default']['OPTIONS'] = {
        'connect_timeout': 10,
        'options': '-c statement_timeout=60000 -c idle_in_transaction_session_timeout=300000',  # 5 minutes idle timeout
    }
else:
    # In development, use shorter connection pooling but still pool connections
    DATABASES['default']['conn_max_age'] = 60  # 1 minute
    DATABASES['default']['OPTIONS'] = {
        'connect_timeout': 10,
        'options': '-c idle_in_transaction_session_timeout=60000',  # 1 minute idle timeout
    }

# Enable atomic requests for better transaction management
DATABASES['default']['ATOMIC_REQUESTS'] = True


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# Static files storage configuration
if DEBUG:
    STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'
else:
    # Use WhiteNoise with simpler storage to avoid admin CSS issues
    STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'

# Additional static files directories
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# Static files finders - make sure admin static files are found
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# WhiteNoise configuration for better static file serving
WHITENOISE_USE_FINDERS = True
WHITENOISE_AUTOREFRESH = True if DEBUG else False

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# File Upload Security Settings
FILE_UPLOAD_MAX_MEMORY_SIZE = 5 * 1024 * 1024  # 5MB max file size in memory
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB max total upload size
FILE_UPLOAD_PERMISSIONS = 0o644  # Secure file permissions
FILE_UPLOAD_DIRECTORY_PERMISSIONS = 0o755  # Secure directory permissions

# Maximum number of files that can be uploaded at once
DATA_UPLOAD_MAX_NUMBER_FILES = 5

# File upload handlers - prioritize memory for small files, temp files for large ones
FILE_UPLOAD_HANDLERS = [
    'django.core.files.uploadhandler.MemoryFileUploadHandler',
    'django.core.files.uploadhandler.TemporaryFileUploadHandler',
]

# AWS S3 configuration for production file storage
# Disabled in development (DEBUG=True)
if not DEBUG:
    # Try Backblaze B2 first, fallback to AWS S3
    B2_APPLICATION_KEY_ID = config('B2_APPLICATION_KEY_ID', default='')
    B2_APPLICATION_KEY = config('B2_APPLICATION_KEY', default='')
    B2_BUCKET_NAME = config('B2_BUCKET_NAME', default='')
    B2_ENDPOINT_URL = config('B2_ENDPOINT_URL', default='')
    B2_REGION = config('B2_REGION', default='us-west-004')
    MEDIA_BUCKET_NAME = config('MEDIA_BUCKET_NAME', default='')
    B2_TILES_BUCKET_NAME = config('B2_TILES_BUCKET_NAME', default='')
    
    # Also make these available globally for the management commands
    globals().update({
        'B2_APPLICATION_KEY_ID': B2_APPLICATION_KEY_ID,
        'B2_APPLICATION_KEY': B2_APPLICATION_KEY,
        'B2_TILES_BUCKET_NAME': B2_TILES_BUCKET_NAME,
        'B2_ENDPOINT_URL': B2_ENDPOINT_URL,
        'B2_REGION': B2_REGION,
    })
    
    # Use Backblaze B2 if configured
    if B2_APPLICATION_KEY_ID and B2_APPLICATION_KEY and B2_BUCKET_NAME:
        # Configure django-storages for B2 S3-compatible API
        AWS_ACCESS_KEY_ID = B2_APPLICATION_KEY_ID
        AWS_SECRET_ACCESS_KEY = B2_APPLICATION_KEY
        AWS_STORAGE_BUCKET_NAME = B2_BUCKET_NAME
        AWS_S3_ENDPOINT_URL = B2_ENDPOINT_URL
        AWS_S3_REGION_NAME = B2_REGION
        AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.s3.{B2_REGION}.backblazeb2.com'
        AWS_S3_OBJECT_PARAMETERS = {
            'CacheControl': 'max-age=86400',
        }
        AWS_DEFAULT_ACL = 'public-read'
        AWS_S3_SIGNATURE_VERSION = 's3v4'
        
        # If using B2 for media
        if config('USE_S3_FOR_MEDIA', default=False, cast=bool):
            DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
            MEDIA_URL = f'https://{MEDIA_BUCKET_NAME}.s3.{B2_REGION}.backblazeb2.com/'
            
            # Configure media bucket separately
            MEDIA_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
            AWS_MEDIA_BUCKET_NAME = MEDIA_BUCKET_NAME
            
        print("INFO: Using Backblaze B2 for storage")
    else:
        # Fallback to AWS S3
        AWS_ACCESS_KEY_ID = config('AWS_ACCESS_KEY_ID', default='')
        AWS_SECRET_ACCESS_KEY = config('AWS_SECRET_ACCESS_KEY', default='')
        AWS_STORAGE_BUCKET_NAME = config('AWS_STORAGE_BUCKET_NAME', default='')
        
        # Only use S3 if all required settings are provided
        if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY and AWS_STORAGE_BUCKET_NAME:
            AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com'
            AWS_S3_OBJECT_PARAMETERS = {
                'CacheControl': 'max-age=86400',
            }
            AWS_DEFAULT_ACL = 'public-read'
            
            # If using S3 for media
            if config('USE_S3_FOR_MEDIA', default=False, cast=bool):
                DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
                MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/'
        else:
            print("WARNING: Storage credentials not fully configured. Using local storage instead.")

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Rest Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
    ],
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '200/hour',  # Increased from 100/hour
        'user': '2000/hour',  # Increased from 1000/hour
        'auth': '30/min',  # Increased from 10/min to prevent auth 401s
        'upload': '50/hour',  # Increased from 20/hour
        'lastfm': '100/min',  # Increased from 60/min
        'spotify': '200/min',  # Increased from 100/min
        'youtube': '200/min'  # YouTube API rate limiting
    },
    'EXCEPTION_HANDLER': 'bopmaps.utils.custom_exception_handler',
}

# JWT Settings
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=config('JWT_ACCESS_TOKEN_LIFETIME', default=36500, cast=int)),  # 100 years - effectively never expires
    'REFRESH_TOKEN_LIFETIME': timedelta(days=config('JWT_REFRESH_TOKEN_LIFETIME', default=36500, cast=int)),  # 100 years - effectively never expires
    'ROTATE_REFRESH_TOKENS': False,  # Disabled to prevent token rotation issues
    'BLACKLIST_AFTER_ROTATION': False,  # Disabled since we're not rotating
    'UPDATE_LAST_LOGIN': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',
    'TOKEN_TYPE_CLAIM': 'token_type',
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'TOKEN_USER_CLASS': 'rest_framework_simplejwt.models.TokenUser',
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
    'SLIDING_TOKEN_LIFETIME': timedelta(days=36500),
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=36500),
}

# CORS Settings - Enhanced for better authentication reliability
CORS_ALLOW_ALL_ORIGINS = True if DEBUG else False
CORS_ALLOWED_ORIGINS = config('CORS_ALLOWED_ORIGINS', default='http://localhost:3000', cast=lambda v: [s.strip() for s in v.split(',')])
CORS_ALLOW_CREDENTIALS = True
CORS_PREFLIGHT_MAX_AGE = 86400  # 24 hours
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'cache-control',
    'pragma',
    'if-modified-since',
    'if-none-match',
]

# Celery Settings
CELERY_BROKER_URL = config('REDIS_URL', default='redis://localhost:6379/0')
CELERY_RESULT_BACKEND = config('REDIS_URL', default='redis://localhost:6379/0')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'

# Channels Settings
ASGI_APPLICATION = 'bopmaps.asgi.application'

# Channel Layers - Use Redis in production, InMemory for development/testing
if DEBUG or IS_TESTING:
    CHANNEL_LAYERS = {
        'default': {
            'BACKEND': 'channels.layers.InMemoryChannelLayer',
        },
    }
else:
    # Production/staging - use Redis for WebSocket channel layer
    CHANNEL_LAYERS = {
        'default': {
            'BACKEND': 'channels_redis.core.RedisChannelLayer',
            'CONFIG': {
                'hosts': [config('REDIS_URL', default='redis://localhost:6379/0')],
                'capacity': 1500,  # Maximum number of messages to hold in memory
                'expiry': 60,      # How long to hold messages (seconds)
                'group_expiry': 86400,  # How long to hold group membership (seconds)
                'symmetric_encryption_keys': [SECRET_KEY],
            },
        },
    }

# Leaflet Configuration
LEAFLET_CONFIG = {
    'DEFAULT_CENTER': (40.7128, -74.0060),  # New York
    'DEFAULT_ZOOM': 13,
    'MIN_ZOOM': 3,
    'MAX_ZOOM': 18,
    'SCALE': 'both',  # Display both metric and imperial scale
    'ATTRIBUTION_PREFIX': 'BOPMaps',
    'RESET_VIEW': False,  # Don't reset view when adding layers
    'NO_GLOBALS': False,  # Make global Leaflet variables available
    'TILES': [
        ('OSM', 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            'attribution': '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            'maxZoom': 19
        }),
        ('Carto Light', 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}.png', {
            'attribution': '&copy; <a href="https://carto.com/attribution">CARTO</a>',
            'maxZoom': 19
        })
    ],
    'PLUGINS': {
        'MarkerCluster': {
            'js': ['https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js'],
            'css': ['https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css', 
                    'https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css'],
            'auto-include': True
        },
        'HeatMap': {
            'js': ['https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js'],
            'auto-include': True
        },
        'Locate': {
            'js': ['https://unpkg.com/leaflet.locatecontrol@0.74.0/dist/L.Control.Locate.min.js'],
            'css': ['https://unpkg.com/leaflet.locatecontrol@0.74.0/dist/L.Control.Locate.min.css'],
            'auto-include': True
        }
    }
}

# Cache configuration with database fallback for reliability
CACHES = {
    'default': {
        # Use database cache as default for reliability
        'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
        'LOCATION': 'cache_table',
        'TIMEOUT': 60 * 60 * 24,  # 1 day
        'OPTIONS': {
            'MAX_ENTRIES': 10000,
            'CULL_FREQUENCY': 3,
        }
    },
    'redis': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': config('REDIS_URL', default='redis://127.0.0.1:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'SOCKET_CONNECT_TIMEOUT': 5,   # Reduced for faster failure
            'SOCKET_TIMEOUT': 5,           # Reduced for faster failure
            'RETRY_ON_TIMEOUT': True,
            'MAX_CONNECTIONS': 25,         # Reduced to prevent overload
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
            'COMPRESS_MIN_LEN': 10,
            'KEY_PREFIX': 'bopmaps',
            'IGNORE_EXCEPTIONS': True,     # Don't fail if Redis is unavailable
            'CONNECTION_POOL_KWARGS': {
                'retry_on_timeout': True,
                'retry_on_error': [RedisConnectionError, RedisTimeoutError],
                'health_check_interval': 30,
            },
        },
        'TIMEOUT': 60 * 60 * 24 * 7,  # 7 days
    },
    'tiles': {
        # Use Redis for tiles cache (can handle failures gracefully)
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': config('REDIS_URL', default='redis://127.0.0.1:6379/2'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'SOCKET_CONNECT_TIMEOUT': 3,   # Faster failure for tiles
            'SOCKET_TIMEOUT': 3,           # Faster failure for tiles
            'RETRY_ON_TIMEOUT': False,     # Don't retry for tiles
            'MAX_CONNECTIONS': 10,         # Minimal connections
            'KEY_PREFIX': 'tiles',
            'IGNORE_EXCEPTIONS': True,     # Gracefully handle failures
        },
        'TIMEOUT': 60 * 60 * 24 * 30,  # 30 days for tiles
    }
}

# Authentication Settings - Enhanced for reliability
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
]

# Session configuration for better authentication reliability
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_COOKIE_AGE = 86400 * 365  # 1 year for long sessions
SESSION_SAVE_EVERY_REQUEST = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = False
SESSION_COOKIE_SECURE = not DEBUG
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'

# Additional authentication settings
LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = '/'
LOGIN_URL = '/api/users/auth/login/'

# Password validation - keep secure but not too strict for API usage
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Cache cleanup settings
TILE_CACHE_DAYS = 30  # How long to keep tiles
REGION_CACHE_DAYS = 60  # How long to keep region bundles
MAX_CACHE_SIZE_BYTES = 10 * 1024 * 1024 * 1024  # 10GB max cache size

# Celery Beat schedule for cleanup tasks
CELERY_BEAT_SCHEDULE = {
    'cleanup-expired-data': {
        'task': 'geo.tasks.cleanup_expired_data',
        'schedule': timedelta(days=1),  # Run daily
    },
    'monitor-storage': {
        'task': 'geo.tasks.monitor_storage_usage',
        'schedule': timedelta(hours=1),  # Run hourly
    },
    'daily-seed-cleanup': {
        'task': 'seeding.tasks.daily_seed_cleanup',
        'schedule': timedelta(days=1),  # Run daily at midnight
    },
    'update-seeding-metrics': {
        'task': 'seeding.tasks.update_seeding_metrics',
        'schedule': timedelta(hours=6),  # Run every 6 hours
    },
    'refresh-seed-content': {
        'task': 'seeding.tasks.refresh_seed_content_database',
        'schedule': timedelta(days=7),  # Run weekly
    },
    'close-completed-challenges': {
        'task': 'pins.tasks.close_completed_challenges',
        'schedule': timedelta(days=1),  # Run daily
    },
}

# Cache Settings for static files using CDN
if not DEBUG:
    AWS_S3_OBJECT_PARAMETERS = {
        'CacheControl': 'max-age=86400',  # 1 day in seconds
    }

# Cache Settings
CACHE_MIDDLEWARE_SECONDS = 60 * 15  # 15 minutes
CACHE_MIDDLEWARE_KEY_PREFIX = 'bopmaps_middleware'

# Custom User Model
AUTH_USER_MODEL = 'users.User'

# Email Configuration
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'  # For development
# For production, use SMTP:
# EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
# EMAIL_HOST = 'smtp.gmail.com'
# EMAIL_PORT = 587
# EMAIL_USE_TLS = True
# EMAIL_HOST_USER = config('EMAIL_HOST_USER', default='')
# EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD', default='')
DEFAULT_FROM_EMAIL = 'BOPMaps <<EMAIL>>'

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s'
        },
        'simple': {
            'format': '%(levelname)s %(message)s'
        },
        'cache': {
            'format': '%(levelname)s [%(asctime)s] Cache: %(message)s'
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'logs/bopmaps.log',
            'formatter': 'verbose',
        },
        'cache_file': {
            'class': 'logging.FileHandler',
            'filename': 'logs/cache.log',
            'formatter': 'cache',
        },
    },
    'loggers': {
        'bopmaps': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': True,
        },
        'bopmaps.cache': {
            'handlers': ['console', 'cache_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'bopmaps.geo': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': True,
        },
        'spotify': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'spotify.services': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'lastfm': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'youtube': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'youtube.services': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Cache timeouts
TILE_CACHE_TIMEOUT = 60 * 60 * 24 * 30  # 30 days
BUILDING_CACHE_TIMEOUT = 60 * 60 * 24 * 7  # 7 days
VECTOR_CACHE_TIMEOUT = 60 * 60 * 24 * 7  # 7 days

# Cache size limits (in bytes)
MAX_CACHE_SIZE_BYTES = 10 * 1024 * 1024 * 1024  # 10GB
MAX_TILE_CACHE_SIZE = 4 * 1024 * 1024 * 1024    # 4GB
MAX_VECTOR_CACHE_SIZE = 2 * 1024 * 1024 * 1024  # 2GB

# API Documentation Settings
SPECTACULAR_SETTINGS = {
    'TITLE': 'BOPMaps API',
    'DESCRIPTION': 'API documentation for BOPMaps - a musical geocaching app',
    'VERSION': '1.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
    'SWAGGER_UI_SETTINGS': {
        'deepLinking': True,
        'persistAuthorization': True,
        'displayOperationId': True,
    },
    'COMPONENT_SPLIT_REQUEST': True,
    'SORT_OPERATIONS': False,
}

# Third-party API Settings
# Spotify API
SPOTIFY_CLIENT_ID = config('SPOTIFY_CLIENT_ID', default='')
SPOTIFY_CLIENT_SECRET = config('SPOTIFY_CLIENT_SECRET', default='')
# Web-based redirect URI
SPOTIFY_REDIRECT_URI = config('SPOTIFY_REDIRECT_URI', default='http://localhost:8888/callback')
# Mobile app-specific redirect URI
SPOTIFY_MOBILE_REDIRECT_URI = config('SPOTIFY_MOBILE_REDIRECT_URI', default='bopmaps://callback')

# PKCE-specific settings
SPOTIFY_TOKEN_URL = 'https://accounts.spotify.com/api/token'
ALLOWED_REDIRECT_URIS = config('ALLOWED_REDIRECT_URIS', default='bopmaps://callback', cast=lambda v: [s.strip() for s in v.split(',')])

# Apple Music API
APPLE_MUSIC_KEY_ID = config('APPLE_MUSIC_KEY_ID', default='')
APPLE_MUSIC_TEAM_ID = config('APPLE_MUSIC_TEAM_ID', default='')
APPLE_MUSIC_PRIVATE_KEY = config('APPLE_MUSIC_PRIVATE_KEY', default='')

# SoundCloud API
SOUNDCLOUD_CLIENT_ID = config('SOUNDCLOUD_CLIENT_ID', default='')
SOUNDCLOUD_CLIENT_SECRET = config('SOUNDCLOUD_CLIENT_SECRET', default='')

# Last.fm API
LASTFM_API_KEY = config('LASTFM_API_KEY', default='')

# Spotify API
SPOTIFY_CLIENT_ID = config('SPOTIFY_CLIENT_ID', default='')
SPOTIFY_CLIENT_SECRET = config('SPOTIFY_CLIENT_SECRET', default='')

# SoundCharts API
SOUNDCHARTS_API_KEY = config('SOUNDCHARTS_API_KEY', default='')

# Google Places API (for POI data)
GOOGLE_PLACES_API_KEY = config('GOOGLE_PLACES_API_KEY', default='')
GROQ_API_KEY = config('GROQ_API_KEY', default='')

# YouTube API
YOUTUBE_API_KEY = config('YOUTUBE_API_KEY', default='')

# Backblaze B2 Configuration
B2_APPLICATION_KEY_ID = config('B2_APPLICATION_KEY_ID', default='')
B2_APPLICATION_KEY = config('B2_APPLICATION_KEY', default='')
B2_BUCKET_NAME = config('B2_BUCKET_NAME', default='')
B2_ENDPOINT_URL = config('B2_ENDPOINT_URL', default='')
B2_REGION = config('B2_REGION', default='')
MEDIA_BUCKET_NAME = config('MEDIA_BUCKET_NAME', default='')


# OneSignal API Configuration
ONESIGNAL_APP_ID = config('ONESIGNAL_APP_ID', default='')
ONESIGNAL_API_KEY = config('ONESIGNAL_API_KEY', default='')
ONESIGNAL_API_URL = config('ONESIGNAL_API_URL', default='https://onesignal.com/api/v1/notifications')

# Security settings for production
if not DEBUG:
    SECURE_SSL_REDIRECT = config('SECURE_SSL_REDIRECT', default=True, cast=bool)
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
    SECURE_HSTS_SECONDS = 31536000  # 1 year
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_BROWSER_XSS_FILTER = True
    X_FRAME_OPTIONS = 'DENY'

# Email settings
EMAIL_BACKEND = config('EMAIL_BACKEND', default='django.core.mail.backends.console.EmailBackend')
DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL', default='<EMAIL>')
SERVER_EMAIL = config('SERVER_EMAIL', default='<EMAIL>')
EMAIL_HOST = config('EMAIL_HOST', default='')
EMAIL_PORT = config('EMAIL_PORT', default=587, cast=int)
EMAIL_HOST_USER = config('EMAIL_HOST_USER', default='')
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD', default='')
EMAIL_USE_TLS = config('EMAIL_USE_TLS', default=True, cast=bool)

# Debug Toolbar Settings
if DEBUG:
    INTERNAL_IPS = [
        '127.0.0.1',
    ]
    
    DEBUG_TOOLBAR_CONFIG = {
        'SHOW_TOOLBAR_CALLBACK': lambda request: DEBUG,
    }

# CSRF Settings
CSRF_TRUSTED_ORIGINS = config('CSRF_TRUSTED_ORIGINS', default='http://localhost:3000', cast=lambda v: [s.strip() for s in v.split(',')])
CSRF_USE_SESSIONS = True
CSRF_COOKIE_SECURE = not DEBUG
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'

# Exempt certain URLs from CSRF protection
CSRF_EXEMPT_URLS = [
    r'^api/geo/tiles/.*$',  # Tile requests
]

# A configuration variable to detect if we're in test mode
TESTING = 'test' in sys.argv
