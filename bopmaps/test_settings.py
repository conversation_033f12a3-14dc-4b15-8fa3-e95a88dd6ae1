from .settings import *
import os

# Simplified database for tile processing
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': 'local_tiles.db',
    }
}

# Minimal installed apps for tile processing
INSTALLED_APPS = [
    'django.contrib.contenttypes',
    'django.contrib.auth',
    'tiles',
]

# Add tiles to sys.path so Django can find it
import sys
import os
sys.path.append(os.path.join(BASE_DIR, 'tiles'))

# Disable problematic middleware
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.middleware.common.CommonMiddleware',
]

# Disable caching for simplicity
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# Simple logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
    },
}

# Disable GDAL paths that cause issues
GDAL_LIBRARY_PATH = None
GEOS_LIBRARY_PATH = None
