"""
URL configuration for bopmaps project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView, SpectacularRedocView
from rest_framework_simplejwt.views import TokenVerifyView
from django.views.generic.base import RedirectView
from .admin import bopmaps_admin_site
from .views import IndexView, health_check
from music.views import spotify_callback

# Import admin registrations to ensure they're loaded
from . import admin_registrations
from users.views import (
    AuthTokenObtainPairView, RegistrationView, logout_view, logout_all_devices,
    CheckEmailView, LoginView, RegisterView, SendVerificationCodeView, VerifyEmailCodeView
)

urlpatterns = [
    # Landing page
    path('', IndexView.as_view(), name='index'),
    
    # Health check endpoint for load balancer (handle both with/without trailing slash)
    path('health/', health_check, name='health_check'),
    path('health', health_check, name='health_check_no_slash'),
    
    # Spotify Callback URL - to match what's likely in Spotify Developer Dashboard
    path('callback/', spotify_callback, name='spotify_callback_root'),
    
    # Admin - with custom admin site
    path('admin/', bopmaps_admin_site.urls),
    
    # API Documentation
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/schema/swagger-ui/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/schema/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    
    # Email verification auth endpoints
    path('api/auth/check-email/', CheckEmailView.as_view(), name='check_email'),
    path('api/auth/send-verification-code/', SendVerificationCodeView.as_view(), name='send_verification_code'),
    path('api/auth/verify-email-code/', VerifyEmailCodeView.as_view(), name='verify_email_code'),
    path('api/auth/login/', LoginView.as_view(), name='auth_login'),
    path('api/auth/register/', RegisterView.as_view(), name='auth_register'),
    
    # Existing auth endpoints (for backward compatibility)
    path('api/auth/token/', AuthTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/auth/token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    path('api/auth/logout/', logout_view, name='logout'),
    path('api/auth/logout/all-devices/', logout_all_devices, name='logout_all_devices'),
    
    # App URLs
    path('api/users/', include('users.urls')),
    path('api/pins/', include('pins.urls')),
    path('api/friends/', include('friends.urls')),
    path('api/music/', include('music.urls')),
    path('api/gamification/', include('gamification.urls')),
    path('api/geo/', include('geo.urls')),
    path('api/votes/', include('votes.urls')),
    path('api/comments/', include('comments.urls')),
    path('api/challenges/', include('challenges.urls')),  # Weekly music challenges
    path('api/rankings/', include('rankings.urls')),  # User rankings system
    path('api/verification/', include('verification.urls')),  # School verification system
    path('', include('bop_drops.urls')),  # Bop drops - music status sharing
    path('api/notifications/', include('notifications.urls')),  # Push notifications
    path('api/lastfm/', include('lastfm.urls')),  # Last.fm caching service
    path('api/spotify/', include('spotify.urls')),  # Spotify caching service
    path('api/youtube/', include('youtube.urls')),  # YouTube caching service
    path('api/youtube_music/', include('youtube_music.urls')),  # YouTube Music caching service
    path('api/moderation/', include('moderation.urls')),  # User blocking and reporting
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    
    # Enable the debug toolbar in development, but not during tests
    import sys
    if 'test' not in sys.argv: # Check if 'test' command is being run
        try:
            import debug_toolbar
            urlpatterns.append(path('__debug__/', include(debug_toolbar.urls)))
        except ImportError:
            pass
