from django.contrib import admin
from django.utils.html import format_html
from .models import WeeklyChallenge, ChallengeParticipation, ChallengeEntryVote

@admin.register(WeeklyChallenge)
class WeeklyChallengeAdmin(admin.ModelAdmin):
    list_display = ['title', 'start_date', 'end_date', 'entry_count', 'is_active', 'time_left_display']
    list_filter = ['is_active']
    search_fields = ['title', 'description']
    readonly_fields = ['entry_count', 'created_at', 'updated_at']
    
    fieldsets = [
        ('Basic Info', {
            'fields': ['title', 'description']
        }),
        ('Visual Style', {
            'fields': ['icon_name', 'gradient_start_color', 'gradient_end_color'],
            'description': 'Choose from available Material icons and color codes'
        }),
        ('Timing', {
            'fields': ['start_date', 'end_date', 'is_active']
        }),
        ('Statistics', {
            'fields': ['entry_count', 'created_at', 'updated_at'],
            'classes': ['collapse']
        })
    ]
    
    def time_left_display(self, obj):
        """Display time left in a human-readable format"""
        seconds = obj.time_left
        if seconds <= 0:
            return format_html(
                '<span style="color: red;">Ended</span>'
            )
        
        days = seconds // 86400
        hours = (seconds % 86400) // 3600
        
        return format_html(
            '<span style="color: green;">{} days, {} hours</span>',
            days, hours
        )
    time_left_display.short_description = 'Time Left'

@admin.register(ChallengeParticipation)
class ChallengeParticipationAdmin(admin.ModelAdmin):
    list_display = ['user', 'challenge', 'song', 'vote_score', 'upvote_count', 'downvote_count', 'created_at']
    list_filter = ['challenge', 'created_at']
    search_fields = ['user__username', 'challenge__title', 'song__title']
    readonly_fields = ['created_at', 'upvote_count', 'downvote_count', 'vote_score']
    
    def get_readonly_fields(self, request, obj=None):
        """Make all fields readonly if entry exists"""
        if obj:  # Editing an existing object
            return self.readonly_fields + ['user', 'challenge', 'song']
        return self.readonly_fields

@admin.register(ChallengeEntryVote)
class ChallengeEntryVoteAdmin(admin.ModelAdmin):
    list_display = ['user', 'entry', 'value', 'created_at']
    list_filter = ['value', 'created_at']
    search_fields = ['user__username', 'entry__user__username', 'entry__challenge__title']
    readonly_fields = ['created_at', 'updated_at']
    
    def get_readonly_fields(self, request, obj=None):
        """Make all fields readonly if vote exists"""
        if obj:  # Editing an existing object
            return self.readonly_fields + ['user', 'entry']
        return self.readonly_fields
