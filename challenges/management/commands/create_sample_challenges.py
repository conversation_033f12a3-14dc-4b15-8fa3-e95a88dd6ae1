from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from challenges.models import WeeklyChallenge
from django.db import transaction

class Command(BaseCommand):
    help = 'Create sample weekly challenges'

    def handle(self, *args, **options):
        now = timezone.now()
        
        challenges_data = [
            # Exact matches from Flutter UI
            {
                'title': "Songs You'd Play After Getting Left on Read",
                'description': "Share your heartbreak anthems",
                'start_date': now,
                'end_date': now + timedelta(days=2),
                'icon_name': 'heart_broken_rounded',
                'gradient_start_color': '#9C27B0',  # purple
                'gradient_end_color': '#2196F3',  # blue
            },
            {
                'title': "Your Favorite Summer Hits",
                'description': "Best tracks for sunny days",
                'start_date': now,
                'end_date': now + timedelta(days=4),
                'icon_name': 'wb_sunny_rounded',
                'gradient_start_color': '#FF9800',  # orange
                'gradient_end_color': '#E91E63',  # pink
            },
            {
                'title': "Hidden Gems of 2024",
                'description': "Underrated tracks you love",
                'start_date': now,
                'end_date': now + timedelta(days=5),
                'icon_name': 'diamond_rounded',
                'gradient_start_color': '#009688',  # teal
                'gradient_end_color': '#3F51B5',  # indigo
            },
            # Additional engaging challenges
            {
                'title': "Throwback Thursday Classics",
                'description': "Share the songs that defined your childhood",
                'start_date': now,
                'end_date': now + timedelta(days=3),
                'icon_name': 'replay_rounded',
                'gradient_start_color': '#FF4081',  # pink
                'gradient_end_color': '#7C4DFF',  # deep purple
            },
            {
                'title': "Workout Motivation Mix",
                'description': "Songs that get you pumped at the gym",
                'start_date': now + timedelta(days=1),
                'end_date': now + timedelta(days=6),
                'icon_name': 'fitness_center',
                'gradient_start_color': '#FF5722',  # deep orange
                'gradient_end_color': '#F44336',  # red
            },
            {
                'title': "Late Night Drive Playlist",
                'description': "Perfect tracks for cruising under the stars",
                'start_date': now + timedelta(days=2),
                'end_date': now + timedelta(days=7),
                'icon_name': 'nightlight_rounded',
                'gradient_start_color': '#1A237E',  # dark blue
                'gradient_end_color': '#311B92',  # dark purple
            },
            {
                'title': "International Music Discovery",
                'description': "Share songs in different languages",
                'start_date': now + timedelta(days=3),
                'end_date': now + timedelta(days=8),
                'icon_name': 'language',
                'gradient_start_color': '#00BCD4',  # cyan
                'gradient_end_color': '#2196F3',  # blue
            },
            {
                'title': "Indie Artist Spotlight",
                'description': "Hidden talents with under 100k streams",
                'start_date': now + timedelta(days=4),
                'end_date': now + timedelta(days=9),
                'icon_name': 'stars_rounded',
                'gradient_start_color': '#8BC34A',  # light green
                'gradient_end_color': '#4CAF50',  # green
            },
            {
                'title': "Rainy Day Melodies",
                'description': "Cozy songs for staying in",
                'start_date': now + timedelta(days=5),
                'end_date': now + timedelta(days=10),
                'icon_name': 'water_drop',
                'gradient_start_color': '#78909C',  # blue grey
                'gradient_end_color': '#607D8B',  # grey
            },
            {
                'title': "Festival Favorites",
                'description': "Songs that rock the crowd",
                'start_date': now + timedelta(days=6),
                'end_date': now + timedelta(days=11),
                'icon_name': 'festival',
                'gradient_start_color': '#FFC107',  # amber
                'gradient_end_color': '#FF9800',  # orange
            }
        ]

        with transaction.atomic():
            # Clear existing challenges
            WeeklyChallenge.objects.all().delete()
            
            # Create new challenges
            for data in challenges_data:
                challenge = WeeklyChallenge.objects.create(**data)
                self.stdout.write(self.style.SUCCESS(f'Created challenge: {challenge.title}')) 