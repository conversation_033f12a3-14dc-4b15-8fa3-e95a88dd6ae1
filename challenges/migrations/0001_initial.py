# Generated by Django 4.2.7 on 2025-06-13 23:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("music", "0003_track"),
    ]

    operations = [
        migrations.CreateModel(
            name="WeeklyChallenge",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField()),
                (
                    "icon_name",
                    models.CharField(
                        help_text="Material icon name (e.g., 'heart_broken_rounded')",
                        max_length=50,
                    ),
                ),
                (
                    "gradient_start_color",
                    models.Char<PERSON>ield(
                        help_text="Hex color code (e.g., '#6200EE')", max_length=7
                    ),
                ),
                (
                    "gradient_end_color",
                    models.CharField(
                        help_text="Hex color code (e.g., '#3700B3')", max_length=7
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("entry_count", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["-start_date"],
                "indexes": [
                    models.Index(
                        fields=["start_date", "end_date"],
                        name="challenges__start_d_d27b3d_idx",
                    ),
                    models.Index(
                        fields=["is_active"], name="challenges__is_acti_642c05_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="ChallengeParticipation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "challenge",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="participations",
                        to="challenges.weeklychallenge",
                    ),
                ),
                (
                    "song",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="challenge_entries",
                        to="music.track",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="challenge_participations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["created_at"], name="challenges__created_e37933_idx"
                    ),
                    models.Index(
                        fields=["user", "challenge"],
                        name="challenges__user_id_342380_idx",
                    ),
                ],
                "unique_together": {("user", "challenge")},
            },
        ),
    ]
