# Generated by Django 4.2.7 on 2025-06-13 23:58

from django.conf import settings
import django.contrib.gis.db.models.fields
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("challenges", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ChallengeEntryVote",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "value",
                    models.SmallIntegerField(
                        choices=[(1, "Upvote"), (-1, "Downvote")],
                        validators=[
                            django.core.validators.MinValueValidator(-1),
                            django.core.validators.MaxValueValidator(1),
                        ],
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AddField(
            model_name="challengeparticipation",
            name="downvote_count",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="challengeparticipation",
            name="location",
            field=django.contrib.gis.db.models.fields.PointField(
                blank=True,
                geography=True,
                help_text="Location where the song was submitted",
                null=True,
                srid=4326,
            ),
        ),
        migrations.AddField(
            model_name="challengeparticipation",
            name="upvote_count",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="challengeparticipation",
            name="vote_score",
            field=models.IntegerField(default=0),
        ),
        migrations.AddIndex(
            model_name="challengeparticipation",
            index=models.Index(
                fields=["vote_score"], name="challenges__vote_sc_e3c3df_idx"
            ),
        ),
        migrations.AddField(
            model_name="challengeentryvote",
            name="entry",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="votes",
                to="challenges.challengeparticipation",
            ),
        ),
        migrations.AddField(
            model_name="challengeentryvote",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="challenge_entry_votes",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddIndex(
            model_name="challengeentryvote",
            index=models.Index(
                fields=["created_at"], name="challenges__created_d897ef_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="challengeentryvote",
            index=models.Index(fields=["value"], name="challenges__value_f8482d_idx"),
        ),
        migrations.AlterUniqueTogether(
            name="challengeentryvote",
            unique_together={("user", "entry")},
        ),
    ]
