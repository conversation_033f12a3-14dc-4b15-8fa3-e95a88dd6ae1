from django.db import models
from django.conf import settings
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.contrib.gis.db import models as gis_models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db.models import Count, Q

class WeeklyChallenge(models.Model):
    """Weekly music challenge that users can participate in"""
    title = models.CharField(max_length=200)
    description = models.TextField()
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    
    # Visual customization (matching Flutter UI)
    icon_name = models.CharField(
        max_length=50,
        help_text="Material icon name (e.g., 'heart_broken_rounded')"
    )
    gradient_start_color = models.CharField(
        max_length=7,
        help_text="Hex color code (e.g., '#6200EE')"
    )
    gradient_end_color = models.CharField(
        max_length=7,
        help_text="Hex color code (e.g., '#3700B3')"
    )
    
    # Challenge metadata
    is_active = models.BooleanField(default=True)
    entry_count = models.PositiveIntegerField(default=0)
    max_participants = models.PositiveIntegerField(null=True, blank=True, help_text="Maximum number of participants (null for unlimited)")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-start_date']
        indexes = [
            models.Index(fields=['start_date', 'end_date']),
            models.Index(fields=['is_active'])
        ]
        
    def __str__(self):
        return f"{self.title} ({self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')})"
    
    def clean(self):
        """Validate challenge dates"""
        if self.end_date <= self.start_date:
            raise ValidationError("End date must be after start date")
        
        # Check for overlapping challenges
        overlapping = WeeklyChallenge.objects.exclude(pk=self.pk).filter(
            start_date__lte=self.end_date,
            end_date__gte=self.start_date
        )
        if overlapping.exists():
            raise ValidationError("Challenge dates overlap with existing challenge")
    
    @property
    def time_left(self):
        """Calculate time left in seconds"""
        now = timezone.now()
        if now > self.end_date:
            return 0
        return int((self.end_date - now).total_seconds())
    
    @property
    def is_ongoing(self):
        """Check if challenge is currently active"""
        now = timezone.now()
        return self.start_date <= now <= self.end_date

    # Compatibility property for serializers (already has is_ongoing)
    @property
    def has_ended(self):
        return timezone.now() > self.end_date

class ChallengeParticipation(models.Model):
    """Track user participation in challenges"""
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='challenge_participations'
    )
    challenge = models.ForeignKey(
        WeeklyChallenge,
        on_delete=models.CASCADE,
        related_name='participations'
    )
    song = models.ForeignKey(
        'music.Track',
        on_delete=models.CASCADE,
        related_name='challenge_entries'
    )
    # New fields for location and vote tracking
    location = gis_models.PointField(
        geography=True,
        null=True,
        blank=True,
        help_text="Location where the song was submitted"
    )
    upvote_count = models.PositiveIntegerField(default=0)
    downvote_count = models.PositiveIntegerField(default=0)
    vote_score = models.IntegerField(default=0)  # upvotes - downvotes
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['user', 'challenge']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['created_at']),
            models.Index(fields=['user', 'challenge']),
            models.Index(fields=['vote_score']),  # For sorting by votes
        ]
    
    def __str__(self):
        return f"{self.user.username}'s entry for {self.challenge.title}"
    
    def save(self, *args, **kwargs):
        """Update challenge entry count on save"""
        is_new = self.pk is None
        super().save(*args, **kwargs)
        
        if is_new:
            self.challenge.entry_count = models.F('entry_count') + 1
            self.challenge.save()
            
    def update_vote_counts(self):
        """Update vote counts from actual votes"""
        vote_counts = ChallengeEntryVote.objects.filter(entry=self).aggregate(
            upvotes=Count('id', filter=Q(value=1)),
            downvotes=Count('id', filter=Q(value=-1))
        )
        
        self.upvote_count = vote_counts['upvotes']
        self.downvote_count = vote_counts['downvotes']
        self.vote_score = self.upvote_count - self.downvote_count
        self.save(update_fields=['upvote_count', 'downvote_count', 'vote_score'])


class ChallengeEntryVote(models.Model):
    """Model for handling challenge entry votes (upvotes/downvotes)"""
    VOTE_CHOICES = [
        (1, 'Upvote'),
        (-1, 'Downvote')
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='challenge_entry_votes'
    )
    entry = models.ForeignKey(
        ChallengeParticipation,
        on_delete=models.CASCADE,
        related_name='votes'
    )
    value = models.SmallIntegerField(
        choices=VOTE_CHOICES,
        validators=[MinValueValidator(-1), MaxValueValidator(1)]
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('user', 'entry')
        indexes = [
            models.Index(fields=['created_at']),
            models.Index(fields=['value']),
        ]
        
    def __str__(self):
        return f"{self.user.username} {'upvoted' if self.value == 1 else 'downvoted'} {self.entry.user.username}'s entry"
    
    def save(self, *args, **kwargs):
        """Override save to update entry's vote counts"""
        is_new = self._state.adding
        
        if not is_new:
            # If this is an update, get the old value
            old_vote = ChallengeEntryVote.objects.get(pk=self.pk)
            if old_vote.value == self.value:
                # No change in vote value, just return
                return super().save(*args, **kwargs)
        
        # Save the vote
        super().save(*args, **kwargs)
        
        try:
            # Update entry's vote counts
            self.entry.update_vote_counts()
        except Exception as e:
            logger.error(f"Error updating entry vote counts: {str(e)}")
            
    @classmethod
    def get_entry_vote_stats(cls, entry):
        """Get voting statistics for an entry"""
        stats = cls.objects.filter(entry=entry).aggregate(
            total_votes=Count('id'),
            upvotes=Count('id', filter=Q(value=1)),
            downvotes=Count('id', filter=Q(value=-1))
        )
        
        stats['score'] = stats['upvotes'] - stats['downvotes']
        if stats['total_votes'] > 0:
            stats['upvote_ratio'] = stats['upvotes'] / stats['total_votes']
        else:
            stats['upvote_ratio'] = 0
            
        return stats
