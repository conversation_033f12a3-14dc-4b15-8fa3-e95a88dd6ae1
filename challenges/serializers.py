from rest_framework import serializers
from rest_framework_gis.serializers import GeoFeatureModelSerializer
from .models import WeeklyChallenge, ChallengeParticipation, ChallengeEntryVote
from music.models import Track
from django.contrib.auth import get_user_model

User = get_user_model()

class TrackSerializer(serializers.ModelSerializer):
    """Simple serializer for music tracks"""
    album_art = serializers.SerializerMethodField()
    
    class Meta:
        model = Track
        fields = [
            'id',
            'spotify_id',
            'title',
            'artist',
            'album',
            'duration_ms',
            'album_art',
            'preview_url',
            'created_at'
        ]
        read_only_fields = ['created_at']
        
    def get_album_art(self, obj):
        """Return album art URL with fallback"""
        if obj.album_art and obj.album_art.strip():
            return obj.album_art
        # Return a default album art URL
        return "https://via.placeholder.com/800x800.png?text=No+Album+Art"

class WeeklyChallengeSerializer(serializers.ModelSerializer):
    """Serializer exactly matching Flutter Challenge model"""
    time_left = serializers.SerializerMethodField()
    gradient_colors = serializers.SerializerMethodField()
    entries = serializers.IntegerField(source='entry_count')
    icon = serializers.CharField(source='icon_name')
    has_participated = serializers.SerializerMethodField()
    
    class Meta:
        model = WeeklyChallenge
        fields = [
            'id',
            'title',
            'description',
            'entries',
            'time_left',
            'gradient_colors',
            'icon',
            'is_active',
            'created_at',
            'has_participated'
        ]
    
    def get_time_left(self, obj):
        """Return time left in a format Flutter can parse"""
        seconds = obj.time_left
        return {
            'inDays': seconds // 86400,
            'inHours': seconds // 3600,
            'inMinutes': seconds // 60,
            'inSeconds': seconds
        }
    
    def get_gradient_colors(self, obj):
        """Return colors in format Flutter expects"""
        return [obj.gradient_start_color, obj.gradient_end_color]
        
    def get_has_participated(self, obj):
        """Check if current user has participated in this challenge"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.participations.filter(user=request.user).exists()
        return False

class UserMiniSerializer(serializers.ModelSerializer):
    """Minimal user serializer for challenge entries"""
    profile_pic = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'username', 'profile_pic']
    
    def get_profile_pic(self, obj):
        """Get profile picture URL with absolute path"""
        request = self.context.get('request')
        if obj.profile_pic and request:
            return request.build_absolute_uri(obj.profile_pic)
        return None

class ChallengeParticipationSerializer(GeoFeatureModelSerializer):
    """Serializer for challenge entries with location support"""
    user = UserMiniSerializer(read_only=True)
    song = TrackSerializer()
    challenge = WeeklyChallengeSerializer(read_only=True)
    upvotes = serializers.IntegerField(source='upvote_count', read_only=True)
    downvotes = serializers.IntegerField(source='downvote_count', read_only=True)
    vote_score = serializers.IntegerField(read_only=True)
    user_vote = serializers.SerializerMethodField()
    entry_id = serializers.IntegerField(source='id', read_only=True)
    
    class Meta:
        model = ChallengeParticipation
        geo_field = 'location'
        fields = [
            'id',
            'entry_id',
            'user',
            'challenge',
            'song',
            'location',
            'upvotes',
            'downvotes',
            'vote_score',
            'user_vote',
            'created_at'
        ]
        read_only_fields = ['user', 'created_at', 'upvotes', 'downvotes', 'vote_score']
        
    def get_user_vote(self, obj):
        """Get the current user's vote on this entry"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                vote = ChallengeEntryVote.objects.get(
                    user=request.user,
                    entry=obj
                )
                return vote.value
            except ChallengeEntryVote.DoesNotExist:
                pass
        return None

class ChallengeEntryVoteSerializer(serializers.ModelSerializer):
    """Serializer for challenge entry votes"""
    user = serializers.PrimaryKeyRelatedField(read_only=True)
    
    class Meta:
        model = ChallengeEntryVote
        fields = ['id', 'user', 'entry', 'value', 'created_at', 'updated_at']
        read_only_fields = ['id', 'user', 'created_at', 'updated_at']
        
    def create(self, validated_data):
        """Create or update vote"""
        user = self.context['request'].user
        entry = validated_data['entry']
        value = validated_data['value']
        
        # Try to get existing vote
        vote, created = ChallengeEntryVote.objects.get_or_create(
            user=user,
            entry=entry,
            defaults={'value': value}
        )
        
        if not created and vote.value != value:
            # Update existing vote if value changed
            vote.value = value
            vote.save()
            
        return vote 