from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import ChallengeParticipation

@receiver(post_save, sender=ChallengeParticipation)
def handle_new_participation(sender, instance, created, **kwargs):
    """Handle new challenge participation"""
    if created:
        # TODO: Add notification logic here
        # For example:
        # - Notify user's friends
        # - Update activity feed
        # - Send push notification
        pass 