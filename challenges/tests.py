from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.contrib.gis.geos import Point
from rest_framework.test import APITestCase
from rest_framework import status
from datetime import timedelta
from .models import WeeklyChallenge, ChallengeParticipation, ChallengeEntryVote
from music.models import Track, MusicService
from friends.models import Friend

User = get_user_model()

class WeeklyChallengeModelTests(TestCase):
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        # Create a test challenge
        self.now = timezone.now()
        self.challenge = WeeklyChallenge.objects.create(
            title="Test Challenge",
            description="Test Description",
            start_date=self.now,
            end_date=self.now + timedelta(days=7),
            icon_name="test_icon",
            gradient_start_color="#000000",
            gradient_end_color="#FFFFFF",
            is_active=True
        )
        
        # Create a test track
        self.track = Track.objects.create(
            spotify_id="test123",
            title="Test Track",
            artist="Test Artist",
            album="Test Album",
            duration_ms=300000,
            album_art="https://example.com/art.jpg",
            preview_url="https://example.com/preview.mp3"
        )
        
    def tearDown(self):
        """Clean up test data"""
        WeeklyChallenge.objects.all().delete()
        Track.objects.all().delete()
        
    def test_challenge_creation(self):
        """Test that a challenge can be created with all fields"""
        self.assertEqual(self.challenge.title, "Test Challenge")
        self.assertEqual(self.challenge.description, "Test Description")
        self.assertTrue(self.challenge.is_active)
        
    def test_time_left_calculation(self):
        """Test the time_left property calculation"""
        # Should be around 7 days (in seconds)
        self.assertGreater(self.challenge.time_left, 7 * 24 * 3600 - 10)
        self.assertLess(self.challenge.time_left, 7 * 24 * 3600 + 10)
        
    def test_is_ongoing_property(self):
        """Test the is_ongoing property"""
        # Current challenge should be ongoing
        self.assertTrue(self.challenge.is_ongoing)
        
        # Past challenge should not be ongoing
        past_challenge = WeeklyChallenge.objects.create(
            title="Past Challenge",
            description="Past Description",
            start_date=self.now - timedelta(days=14),
            end_date=self.now - timedelta(days=7),
            icon_name="past_icon",
            gradient_start_color="#000000",
            gradient_end_color="#FFFFFF"
        )
        self.assertFalse(past_challenge.is_ongoing)
        
        # Future challenge should not be ongoing
        future_challenge = WeeklyChallenge.objects.create(
            title="Future Challenge",
            description="Future Description",
            start_date=self.now + timedelta(days=7),
            end_date=self.now + timedelta(days=14),
            icon_name="future_icon",
            gradient_start_color="#000000",
            gradient_end_color="#FFFFFF"
        )
        self.assertFalse(future_challenge.is_ongoing)
        
        # Clean up test challenges
        past_challenge.delete()
        future_challenge.delete()

class WeeklyChallengeAPITests(APITestCase):
    def setUp(self):
        # Delete all existing challenges first
        WeeklyChallenge.objects.all().delete()
        ChallengeParticipation.objects.all().delete()
        ChallengeEntryVote.objects.all().delete()
        
        # Create test user
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        self.client.force_authenticate(user=self.user)
        
        # Create a test challenge
        self.now = timezone.now()
        self.challenge = WeeklyChallenge.objects.create(
            title="Test Challenge",
            description="Test Description",
            start_date=self.now,
            end_date=self.now + timedelta(days=7),
            icon_name="test_icon",
            gradient_start_color="#000000",
            gradient_end_color="#FFFFFF",
            is_active=True
        )
        
        # Create a test track
        self.track = Track.objects.create(
            spotify_id="test123",
            title="Test Track",
            artist="Test Artist",
            album="Test Album",
            duration_ms=300000,
            album_art="https://example.com/art.jpg",
            preview_url="https://example.com/preview.mp3"
        )
        
        # Verify no other challenges exist
        self.assertEqual(WeeklyChallenge.objects.count(), 1, "Expected only one challenge to exist")
        
    def tearDown(self):
        """Clean up test data"""
        WeeklyChallenge.objects.all().delete()
        Track.objects.all().delete()
        User.objects.all().delete()
        ChallengeParticipation.objects.all().delete()
        ChallengeEntryVote.objects.all().delete()
        
    def test_list_challenges(self):
        """Test retrieving list of active challenges"""
        # Delete all existing challenges first
        WeeklyChallenge.objects.all().delete()
        
        # Create test challenge
        challenge = WeeklyChallenge.objects.create(
            title="Test Challenge",
            description="Test Description",
            start_date=self.now,
            end_date=self.now + timedelta(days=7),
            icon_name="test_icon",
            gradient_start_color="#000000",
            gradient_end_color="#FFFFFF",
            is_active=True
        )
        
        # Verify only one challenge exists
        self.assertEqual(WeeklyChallenge.objects.count(), 1, "Expected only one challenge to exist")
        
        # Get challenges
        response = self.client.get('/api/challenges/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Log the response data to understand what we're getting
        print("Response data:", response.data)
        print("All challenges:", WeeklyChallenge.objects.all().values('title', 'start_date', 'end_date', 'is_active'))
        
        # Response is paginated; ensure exactly one challenge is returned
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(len(response.data['results']), 1)
        
    def test_challenge_participation(self):
        """Test participating in a challenge"""
        # Create participation data
        data = {
            'song': {
                'spotify_id': 'test123',
                'title': 'Test Track',
                'artist': 'Test Artist',
                'album': 'Test Album',
                'duration_ms': 300000,
                'album_art': 'https://example.com/art.jpg',
                'preview_url': 'https://example.com/preview.mp3'
            },
            'latitude': 40.7128,
            'longitude': -74.0060
        }
        
        response = self.client.post(
            f'/api/challenges/{self.challenge.id}/participate/',
            data,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify participation was created
        self.assertTrue(
            ChallengeParticipation.objects.filter(
                user=self.user,
                challenge=self.challenge
            ).exists()
        )
        
    def test_duplicate_participation_prevention(self):
        """Test that users cannot participate twice in the same challenge"""
        # Create initial participation
        ChallengeParticipation.objects.create(
            user=self.user,
            challenge=self.challenge,
            song=self.track
        )
        
        # Try to participate again
        data = {
            'song': {
                'spotify_id': 'another123',
                'title': 'Another Track',
                'artist': 'Another Artist',
                'album': 'Another Album',
                'duration_ms': 300000,
                'album_art': 'https://example.com/art.jpg',
                'preview_url': 'https://example.com/preview.mp3'
            }
        }
        
        response = self.client.post(
            f'/api/challenges/{self.challenge.id}/participate/',
            data,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
    def test_challenge_entries_filtering(self):
        """Test filtering challenge entries by different scopes"""
        # Delete any existing entries first
        ChallengeParticipation.objects.all().delete()
        Friend.objects.all().delete()
        
        # Create some test users and entries
        friend = User.objects.create_user(
            username="friend",
            email="<EMAIL>",
            password="friend123"
        )
        
        # Create friendship both ways to ensure it's found
        Friend.objects.create(
            requester=self.user,
            recipient=friend,
            status='accepted'
        )
        Friend.objects.create(
            requester=friend,
            recipient=self.user,
            status='accepted'
        )
        
        # Verify friendship exists
        self.assertTrue(
            Friend.objects.filter(
                requester=self.user,
                recipient=friend,
                status='accepted'
            ).exists(),
            "Friendship should exist"
        )
        
        # Create friend's entry
        friend_entry = ChallengeParticipation.objects.create(
            user=friend,
            challenge=self.challenge,
            song=self.track,
            location=Point(-74.0060, 40.7128)  # NYC coordinates
        )
        
        # Verify only one entry exists
        self.assertEqual(ChallengeParticipation.objects.count(), 1, "Expected only one entry to exist")
        
        # Test friends filter
        response = self.client.get(
            f'/api/challenges/{self.challenge.id}/entries/',
            {'scope': 'friends'}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Log the response data to understand what we're getting
        print("Response data:", response.data)
        print("Friend IDs:", Friend.objects.values_list('requester_id', 'recipient_id'))
        print("Entry user ID:", friend_entry.user_id)
        print("Current user ID:", self.user.id)
        
        # Check GeoJSON response format
        self.assertEqual(response.data['type'], 'FeatureCollection')
        self.assertEqual(len(response.data['features']), 1)  # Expect one feature
        
        # Test local filter
        response = self.client.get(
            f'/api/challenges/{self.challenge.id}/entries/',
            {
                'scope': 'local',
                'lat': 40.7128,
                'lng': -74.0060,
                'radius_km': 5
            }
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['features']), 1)  # Expect one feature
        
    def test_challenge_leaderboard(self):
        """Test the challenge leaderboard functionality"""
        # Create some participations and votes
        participation = ChallengeParticipation.objects.create(
            user=self.user,
            challenge=self.challenge,
            song=self.track
        )
        
        # Create another user to vote
        voter = User.objects.create_user(
            username="voter",
            email="<EMAIL>",
            password="voter123"
        )
        
        # Create a vote
        ChallengeEntryVote.objects.create(
            user=voter,
            entry=participation,
            value=1  # Upvote
        )
        
        # Test leaderboard
        response = self.client.get('/api/challenges/leaderboard/', {'challenge_id': self.challenge.id})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # User should have points from participation (10) and upvote (1)
        user_entry = next(
            entry for entry in response.data
            if entry['id'] == str(self.user.id)
        )
        self.assertEqual(int(user_entry['score']), 11)

class ChallengeVotingTests(APITestCase):
    def setUp(self):
        # Create test users
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        self.other_user = User.objects.create_user(
            username="otheruser",
            email="<EMAIL>",
            password="other123"
        )
        self.client.force_authenticate(user=self.user)
        
        # Create challenge and participation
        self.challenge = WeeklyChallenge.objects.create(
            title="Test Challenge",
            description="Test Description",
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=7),
            icon_name="test_icon",
            gradient_start_color="#000000",
            gradient_end_color="#FFFFFF"
        )
        
        self.track = Track.objects.create(
            spotify_id="test123",
            title="Test Track",
            artist="Test Artist",
            album="Test Album",
            duration_ms=300000,
            album_art="https://example.com/art.jpg",
            preview_url="https://example.com/preview.mp3"
        )
        
        self.participation = ChallengeParticipation.objects.create(
            user=self.other_user,
            challenge=self.challenge,
            song=self.track
        )
        
    def test_voting_on_entry(self):
        """Test voting on a challenge entry"""
        # Create upvote
        response = self.client.post(
            '/api/challenges/votes/',
            {
                'entry': self.participation.id,
                'value': 1
            },
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify vote was counted
        self.participation.refresh_from_db()
        self.assertEqual(self.participation.upvote_count, 1)
        self.assertEqual(self.participation.vote_score, 1)
        
        # Change vote to downvote
        response = self.client.post(
            '/api/challenges/votes/',
            {
                'entry': self.participation.id,
                'value': -1
            },
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify vote was updated
        self.participation.refresh_from_db()
        self.assertEqual(self.participation.upvote_count, 0)
        self.assertEqual(self.participation.downvote_count, 1)
        self.assertEqual(self.participation.vote_score, -1)

