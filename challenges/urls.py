from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from .views import (
    WeeklyChallengeViewSet,
    ChallengeParticipationViewSet,
    ChallengeEntryVoteViewSet
)

router = DefaultRouter()
router.register(r'votes', ChallengeEntryVoteViewSet, basename='challenge-vote')
router.register(r'participations', ChallengeParticipationViewSet, basename='participation')
router.register(r'', WeeklyChallengeViewSet, basename='challenge')

app_name = 'challenges'

urlpatterns = [
    path('', include(router.urls)),
] 