from django.shortcuts import render
from rest_framework import viewsets, permissions, status, mixins
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db import transaction
from django.db.models import F, Q, Sum, Count, Case, When, IntegerField
from django.contrib.gis.geos import Point
from django.contrib.gis.measure import D
import logging

from .models import WeeklyChallenge, ChallengeParticipation, ChallengeEntryVote
from .serializers import (
    WeeklyChallengeSerializer,
    ChallengeParticipationSerializer,
    ChallengeEntryVoteSerializer
)
from music.models import Track
from friends.models import Friend
from django.contrib.auth import get_user_model

User = get_user_model()
logger = logging.getLogger('bopmaps')

class WeeklyChallengeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for weekly challenges
    """
    serializer_class = WeeklyChallengeSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get active challenges ordered by start date"""
        now = timezone.now()
        return WeeklyChallenge.objects.filter(
            end_date__gt=now,
            is_active=True
        ).order_by('start_date')
    
    @action(detail=True, methods=['POST'])
    def participate(self, request, pk=None):
        """Handle song submission for a challenge"""
        challenge = self.get_object()
        
        # Validate challenge is still active
        if not challenge.is_ongoing:
            return Response(
                {"error": "Challenge is not active"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if user already participated
        if ChallengeParticipation.objects.filter(
            user=request.user,
            challenge=challenge
        ).exists():
            return Response(
                {"error": "You have already participated in this challenge"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get or create track
        track_data = request.data.get('song')
        if not track_data:
            return Response(
                {"error": "Song data is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Get location data
        lat = request.data.get('latitude')
        lng = request.data.get('longitude')
        location = None
        if lat is not None and lng is not None:
            try:
                location = Point(float(lng), float(lat))
            except (ValueError, TypeError):
                return Response(
                    {"error": "Invalid coordinates"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
        try:
            with transaction.atomic():
                # Create or get track
                track, _ = Track.objects.get_or_create(
                    spotify_id=track_data.get('spotify_id', ''),
                    defaults={
                        'title': track_data.get('title', ''),
                        'artist': track_data.get('artist', ''),
                        'album': track_data.get('album', ''),
                        'duration_ms': track_data.get('duration_ms', 0),
                        'album_art': track_data.get('album_art', ''),
                        'preview_url': track_data.get('preview_url', '')
                    }
                )
                
                # Create participation entry
                participation = ChallengeParticipation.objects.create(
                    user=request.user,
                    challenge=challenge,
                    song=track,
                    location=location
                )
                
                # Update challenge entry count
                challenge.refresh_from_db()
                challenge.entry_count += 1
                challenge.save()
                
                return Response(
                    ChallengeParticipationSerializer(participation).data,
                    status=status.HTTP_201_CREATED
                )
                
        except Exception as e:
            logger.error(f"Error creating challenge participation: {str(e)}")
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True)
    def entries(self, request, pk=None):
        """Get entries for a challenge with filtering options"""
        challenge = self.get_object()
        
        # Get filter parameters
        scope = request.query_params.get('scope', 'all')
        lat = request.query_params.get('lat')
        lng = request.query_params.get('lng')
        radius_km = request.query_params.get('radius_km', 5)
        
        # Start with base queryset
        entries = challenge.participations.all()
        
        # Apply scope filters
        if scope == 'friends':
            # Get IDs of user's friends
            friend_rows = Friend.objects.filter(
                (Q(requester=request.user) | Q(recipient=request.user)),
                status='accepted'
            ).values_list('requester_id', 'recipient_id')
            
            # Flatten and remove the current user's ID
            friend_ids = set()
            for req_id, rec_id in friend_rows:
                if req_id != request.user.id:
                    friend_ids.add(req_id)
                if rec_id != request.user.id:
                    friend_ids.add(rec_id)
            
            entries = entries.filter(user_id__in=friend_ids)
            
        elif scope == 'local' and lat and lng:
            try:
                user_location = Point(float(lng), float(lat))
                entries = entries.filter(
                    location__isnull=False,
                    location__distance_lte=(user_location, D(km=float(radius_km)))
                )
            except (ValueError, TypeError):
                return Response(
                    {"error": "Invalid coordinates or radius"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        elif scope == 'school':
            # Filter by school if user has one
            if hasattr(request.user, 'school') and request.user.school:
                entries = entries.filter(user__school=request.user.school)
            else:
                return Response(
                    {"error": "You must be associated with a school to use this filter"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # Order by votes and creation date
        entries = entries.order_by('-vote_score', '-created_at')
        
        serializer = ChallengeParticipationSerializer(
            entries,
            many=True,
            context={'request': request}
        )
        return Response(serializer.data)

    @action(detail=False)
    def leaderboard(self, request):
        """Get leaderboard rankings based on challenge participation and votes"""
        # Get filter parameters
        scope = request.query_params.get('scope', 'all')
        lat = request.query_params.get('lat')
        lng = request.query_params.get('lng')
        radius_km = request.query_params.get('radius_km', 5)
        limit = int(request.query_params.get('limit', 20))
        
        # Start with all users who have participated in challenges
        users_with_entries = User.objects.filter(
            challenge_participations__isnull=False
        ).distinct()
        
        # Apply scope filters
        if scope == 'friends':
            # Get IDs of user's friends
            friend_rows = Friend.objects.filter(
                (Q(requester=request.user) | Q(recipient=request.user)),
                status='accepted'
            ).values_list('requester_id', 'recipient_id')
            
            # Flatten and remove the current user's ID
            friend_ids = set()
            for req_id, rec_id in friend_rows:
                if req_id != request.user.id:
                    friend_ids.add(req_id)
                if rec_id != request.user.id:
                    friend_ids.add(rec_id)
            
            users_with_entries = users_with_entries.filter(id__in=friend_ids)
            
        elif scope == 'local' and lat and lng:
            try:
                user_location = Point(float(lng), float(lat))
                # Get users who have submitted entries near this location
                local_entry_users = ChallengeParticipation.objects.filter(
                    location__isnull=False,
                    location__distance_lte=(user_location, D(km=float(radius_km)))
                ).values_list('user_id', flat=True).distinct()
                
                users_with_entries = users_with_entries.filter(id__in=local_entry_users)
            except (ValueError, TypeError):
                return Response(
                    {"error": "Invalid coordinates or radius"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        elif scope == 'school':
            # Filter by school if user has one
            if hasattr(request.user, 'school') and request.user.school:
                users_with_entries = users_with_entries.filter(school=request.user.school)
            else:
                return Response(
                    {"error": "You must be associated with a school to use this filter"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # Calculate scores for each user based on their challenge entries
        users_with_scores = users_with_entries.annotate(
            # Base participation score (10 points per entry)
            participation_score=Count('challenge_participations', distinct=True) * 10,
            
            # Upvotes received (1 point per upvote)
            upvotes_received=Sum(
                Case(
                    When(challenge_participations__votes__value=1, then=1),
                    default=0,
                    output_field=IntegerField()
                )
            ),
            
            # Downvotes received (-1 point per downvote)
            downvotes_received=Sum(
                Case(
                    When(challenge_participations__votes__value=-1, then=1),
                    default=0,
                    output_field=IntegerField()
                )
            ),
            
            # Total score
            total_score=F('participation_score') + 
                        F('upvotes_received') - 
                        F('downvotes_received')
        ).order_by('-total_score')
        
        # Limit results and prepare response
        users_with_scores = users_with_scores[:limit]
        
        # Get school info if this is a school-scoped leaderboard
        school_info = None
        if scope == 'school' and request.user.school:
            school_info = {
                'id': request.user.school.id,
                'name': request.user.school.name,
                'total_participants': users_with_entries.count(),
                'verified': request.user.school.verified
            }
        
        # Format the response to match the Flutter UI
        leaderboard_data = []
        for i, user in enumerate(users_with_scores):
            # Get the user's previous rank if available (for change indicator)
            # This would require storing historical rankings, for now we'll simulate
            previous_rank = i + 1 + ((-1)**i if i % 3 != 0 else 0)  # Simulate rank changes
            rank_change = 'same' if previous_rank == i + 1 else ('up' if previous_rank > i + 1 else 'down')
            rank_change_amount = abs(previous_rank - (i + 1)) if previous_rank != i + 1 else 0
            
            entry = {
                'id': str(user.id),
                'name': user.get_full_name() or user.username,
                'username': user.username,
                'avatar': request.build_absolute_uri(user.profile_pic) if user.profile_pic else None,
                'score': str(user.total_score or 0),  # Handle None values
                'change': rank_change,
                'changeAmount': str(rank_change_amount),
                'isCurrentUser': user.id == request.user.id,
            }
            
            # Add school info if school scope and user has school
            if scope == 'school' and hasattr(user, 'school') and user.school:
                entry['school'] = {
                    'id': user.school.id,
                    'name': user.school.name
                }
            
            leaderboard_data.append(entry)
        
        # Find current user's rank if they're not in the top results
        current_user_in_results = any(entry['isCurrentUser'] for entry in leaderboard_data)
        
        if not current_user_in_results and request.user.is_authenticated:
            # Get current user's rank
            current_user_rank = users_with_entries.annotate(
                participation_score=Count('challenge_participations', distinct=True) * 10,
                upvotes_received=Sum(
                    Case(
                        When(challenge_participations__votes__value=1, then=1),
                        default=0,
                        output_field=IntegerField()
                    )
                ),
                downvotes_received=Sum(
                    Case(
                        When(challenge_participations__votes__value=-1, then=1),
                        default=0,
                        output_field=IntegerField()
                    )
                ),
                total_score=F('participation_score') + 
                            F('upvotes_received') - 
                            F('downvotes_received')
            ).filter(total_score__gt=F('total_score')).count() + 1
            
            # Add current user to results
            if current_user_rank > 0:
                current_user = request.user
                current_user_entry = {
                    'id': str(current_user.id),
                    'name': current_user.get_full_name() or current_user.username,
                    'username': current_user.username,
                    'avatar': request.build_absolute_uri(current_user.profile_pic) if current_user.profile_pic else None,
                    'score': str(getattr(current_user, 'total_score', 0)),
                    'change': 'same',  # We don't track historical data yet
                    'changeAmount': '0',
                    'isCurrentUser': True,
                    'rank': current_user_rank,
                }
                
                # Add school info if school scope
                if scope == 'school' and current_user.school:
                    current_user_entry['school'] = {
                        'id': current_user.school.id,
                        'name': current_user.school.name
                    }
                
                leaderboard_data.append(current_user_entry)
        
        return Response({
            'leaderboard': leaderboard_data,
            'schoolInfo': school_info
        })

class ChallengeParticipationViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing challenge participations
    """
    serializer_class = ChallengeParticipationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get all participations, optionally filtered by user"""
        queryset = ChallengeParticipation.objects.all()
        
        # Filter by user if requested
        user_id = self.request.query_params.get('user', None)
        if user_id:
            queryset = queryset.filter(user_id=user_id)
            
        return queryset.order_by('-created_at')

class ChallengeEntryVoteViewSet(mixins.CreateModelMixin,
                               mixins.ListModelMixin,
                               mixins.RetrieveModelMixin,
                               viewsets.GenericViewSet):
    """API viewset for vote operations"""
    serializer_class = ChallengeEntryVoteSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter queryset to only show user's votes"""
        return ChallengeEntryVote.objects.filter(
            user=self.request.user
        ).order_by('-created_at')
    
    def perform_create(self, serializer):
        """Create or update vote"""
        try:
            with transaction.atomic():
                vote = serializer.save(user=self.request.user)
                logger.info(f"Vote created/updated: {vote}")
        except Exception as e:
            logger.error(f"Error creating/updating vote: {str(e)}")
            raise
            
    @action(detail=False, methods=['post'])
    def upvote(self, request):
        """Upvote an entry"""
        entry_id = request.data.get('entry')
        try:
            entry = ChallengeParticipation.objects.get(id=entry_id)
            vote, created = ChallengeEntryVote.objects.get_or_create(
                user=request.user,
                entry=entry,
                defaults={'value': 1}
            )
            
            if not created:
                if vote.value == 1:
                    # Remove vote if already upvoted
                    vote.delete()
                else:
                    # Change downvote to upvote
                    vote.value = 1
                    vote.save()
            
            entry.refresh_from_db()
            return Response({
                'upvotes': entry.upvote_count,
                'downvotes': entry.downvote_count,
                'score': entry.vote_score,
                'user_vote': 1 if vote.pk else None
            })
            
        except ChallengeParticipation.DoesNotExist:
            return Response(
                {"error": "Entry not found"},
                status=status.HTTP_404_NOT_FOUND
            )
            
    @action(detail=False, methods=['post'])
    def downvote(self, request):
        """Downvote an entry"""
        entry_id = request.data.get('entry')
        try:
            entry = ChallengeParticipation.objects.get(id=entry_id)
            vote, created = ChallengeEntryVote.objects.get_or_create(
                user=request.user,
                entry=entry,
                defaults={'value': -1}
            )
            
            if not created:
                if vote.value == -1:
                    # Remove vote if already downvoted
                    vote.delete()
                else:
                    # Change upvote to downvote
                    vote.value = -1
                    vote.save()
            
            entry.refresh_from_db()
            return Response({
                'upvotes': entry.upvote_count,
                'downvotes': entry.downvote_count,
                'score': entry.vote_score,
                'user_vote': -1 if vote.pk else None
            })
            
        except ChallengeParticipation.DoesNotExist:
            return Response(
                {"error": "Entry not found"},
                status=status.HTTP_404_NOT_FOUND
            )
            
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get voting statistics for an entry"""
        entry_id = request.query_params.get('entry_id')
        if not entry_id:
            return Response(
                {"error": "entry_id parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            entry = ChallengeParticipation.objects.get(id=entry_id)
            stats = ChallengeEntryVote.get_entry_vote_stats(entry)
            
            # Add user's vote if exists
            user_vote = ChallengeEntryVote.objects.filter(
                user=request.user,
                entry=entry
            ).first()
            stats['user_vote'] = user_vote.value if user_vote else None
            
            return Response(stats)
        except ChallengeParticipation.DoesNotExist:
            return Response(
                {"error": "Entry not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error getting vote stats: {str(e)}")
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
