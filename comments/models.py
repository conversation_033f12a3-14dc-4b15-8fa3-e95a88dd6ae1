from django.db import models
from django.conf import settings
from django.utils import timezone
import logging

logger = logging.getLogger('bopmaps')

class Comment(models.Model):
    """
    Model for pin comments with emoji support
    """
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='pin_comments'
    )
    pin = models.ForeignKey(
        'pins.Pin',
        on_delete=models.CASCADE,
        related_name='comments'
    )
    text = models.TextField()  # Will store both text and emojis
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_edited = models.BooleanField(default=False)
    is_hidden = models.BooleanField(default=False)  # For soft deletion/moderation
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['created_at']),
            models.Index(fields=['is_hidden']),
        ]
        
    def __str__(self):
        return f"Comment by {self.user.username} on {self.pin.title}"
        
    def save(self, *args, **kwargs):
        """Override save to track edits"""
        if not self._state.adding and self.has_changed:
            self.is_edited = True
        super().save(*args, **kwargs)
        
    @property
    def has_changed(self):
        """Check if the comment text has been modified"""
        if not self.pk:
            return False
        old_comment = Comment.objects.get(pk=self.pk)
        return old_comment.text != self.text
