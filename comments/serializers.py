from rest_framework import serializers
from .models import Comment
from django.contrib.auth import get_user_model

User = get_user_model()

class CommentSerializer(serializers.ModelSerializer):
    """
    Serializer for Comment model
    """
    user = serializers.PrimaryKeyRelatedField(read_only=True)
    
    class Meta:
        model = Comment
        fields = ['id', 'user', 'pin', 'text', 'created_at', 'updated_at', 'is_hidden', 'is_edited']
        read_only_fields = ['id', 'user', 'created_at', 'updated_at', 'is_hidden', 'is_edited']
        
    def validate_text(self, value):
        """
        Validate comment text:
        - Not empty
        - Not too long
        """
        if not value.strip():
            raise serializers.ValidationError("Comment text cannot be empty.")
        if len(value) > 1000:  # Reasonable limit for comments
            raise serializers.ValidationError("Comment text is too long (max 1000 characters).")
        return value
        
    def create(self, validated_data):
        """Create comment with current user"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data) 