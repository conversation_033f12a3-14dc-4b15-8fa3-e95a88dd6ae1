from django.test import TestCase, TransactionTestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, APIClient
from django.contrib.auth import get_user_model
from django.contrib.gis.geos import Point
from django.db import transaction, connection
from django.test.utils import CaptureQueriesContext
from .models import Comment
from pins.models import Pin
from gamification.models import PinSkin

User = get_user_model()

class CommentTests(TransactionTestCase):
    reset_sequences = True  # This ensures primary keys start at 1 in each test
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Any class-level setup if needed
    
    @classmethod
    def tearDownClass(cls):
        super().tearDownClass()
        # Any class-level cleanup if needed
    
    def setUp(self):
        # Clean up any existing data first
        with transaction.atomic():
            Comment.objects.all().delete()
            Pin.objects.all().delete()
            PinSkin.objects.all().delete()
            User.objects.all().delete()
        
        # Create test users
        self.user1 = User.objects.create_user(username='user1', email='<EMAIL>', password='password123')
        self.user2 = User.objects.create_user(username='user2', email='<EMAIL>', password='password123')
        
        # Create a default PinSkin
        self.default_skin = PinSkin.objects.create(
            id=1, 
            name='Default Skin',
            image='skins/default.png'
        )
        
        # Create a test pin
        self.test_pin = Pin.objects.create(
            owner=self.user1,
            location=Point(-73.985130, 40.758896),
            title='Test Pin',
            track_title='Test Track',
            track_artist='Test Artist',
            track_url='http://example.com/track',
            service='spotify',
            skin=self.default_skin
        )
        
        # Set up client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user1)
        
        # URLs
        self.list_create_url = reverse('comment-list')

    def tearDown(self):
        # Clean up all test data
        with transaction.atomic():
            Comment.objects.all().delete()
            Pin.objects.all().delete()
            PinSkin.objects.all().delete()
            User.objects.all().delete()
        
        # Reset sequences for PostgreSQL
        with connection.cursor() as cursor:
            try:
                cursor.execute("ALTER SEQUENCE comments_comment_id_seq RESTART WITH 1;")
                cursor.execute("ALTER SEQUENCE pins_pin_id_seq RESTART WITH 1;")
                cursor.execute("ALTER SEQUENCE gamification_pinskin_id_seq RESTART WITH 1;")
                cursor.execute("ALTER SEQUENCE auth_user_id_seq RESTART WITH 1;")
            except Exception:
                # If not PostgreSQL or sequence doesn't exist, ignore the error
                pass

    def test_create_comment(self):
        """Test creating a new comment"""
        data = {
            'pin': self.test_pin.id,
            'text': 'New test comment'
        }
        response = self.client.post(self.list_create_url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['text'], 'New test comment')
        self.assertEqual(response.data['user'], self.user1.id)
            
    def test_create_comment_invalid_blank(self):
        """Test creating a comment with blank text"""
        data = {
            'pin': self.test_pin.id,
            'text': ''
        }
        response = self.client.post(self.list_create_url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            
    def test_create_comment_invalid_too_long(self):
        """Test creating a comment with text too long"""
        data = {
            'pin': self.test_pin.id,
            'text': 'x' * 1001  # Max length is 1000
        }
        response = self.client.post(self.list_create_url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            
    def test_delete_own_comment(self):
        """Test soft deleting own comment"""
        # Create a test comment
        comment = Comment.objects.create(
            user=self.user1,
            pin=self.test_pin,
            text='Test comment'
        )
        detail_url = reverse('comment-detail', kwargs={'pk': comment.pk})
        
        response = self.client.delete(detail_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify comment is hidden
        comment.refresh_from_db()
        self.assertTrue(comment.is_hidden)

    def test_delete_others_comment(self):
        """Test attempting to delete another user's comment"""
        # Create a test comment
        comment = Comment.objects.create(
            user=self.user1,
            pin=self.test_pin,
            text='Test comment'
        )
        detail_url = reverse('comment-detail', kwargs={'pk': comment.pk})
        
        self.client.force_authenticate(user=self.user2)
        response = self.client.delete(detail_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_own_comment(self):
        """Test updating own comment"""
        # Create a test comment
        comment = Comment.objects.create(
            user=self.user1,
            pin=self.test_pin,
            text='Test comment'
        )
        detail_url = reverse('comment-detail', kwargs={'pk': comment.pk})
        
        data = {'text': 'Updated comment'}
        response = self.client.patch(detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['text'], 'Updated comment')

    def test_update_others_comment(self):
        """Test attempting to update another user's comment"""
        # Create a test comment
        comment = Comment.objects.create(
            user=self.user1,
            pin=self.test_pin,
            text='Test comment'
        )
        detail_url = reverse('comment-detail', kwargs={'pk': comment.pk})
        
        self.client.force_authenticate(user=self.user2)
        data = {'text': 'Updated comment'}
        response = self.client.patch(detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_comments(self):
        """Test listing all comments"""
        # Clean up any existing comments
        Comment.objects.all().delete()
        
        # Create a test comment
        self.test_comment = Comment.objects.create(
            user=self.user1,
            pin=self.test_pin,
            text='Test comment'
        )
        
        # Print all comments for debugging
        print("\nAll comments in database:")
        for comment in Comment.objects.all():
            print(f"Comment {comment.id}: {comment.text} by {comment.user.username} on pin {comment.pin.id} (hidden: {comment.is_hidden})")
        
        response = self.client.get(self.list_create_url)
        
        # Print response data for debugging
        print("\nResponse data:")
        if isinstance(response.data, dict) and 'results' in response.data:
            for item in response.data['results']:
                print(f"Comment {item.get('id')}: {item.get('text')} by user {item.get('user')} on pin {item.get('pin')}")
        else:
            print(f"Response data is not paginated: {response.data}")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)  # Total count should be 1
        self.assertEqual(len(response.data['results']), 1)  # One comment in results
        self.assertEqual(response.data['results'][0]['text'], 'Test comment')

    def test_list_comments_by_pin(self):
        """Test filtering comments by pin"""
        # Create another pin
        other_pin = Pin.objects.create(
            owner=self.user2,
            location=Point(-73.985130, 40.758896),
            title='Other Pin',
            track_title='Other Track',
            track_artist='Other Artist',
            track_url='http://example.com/other',
            service='spotify',
            skin=self.default_skin
        )
        
        # Create comments for different pins
        comment1 = Comment.objects.create(
            user=self.user1,
            pin=self.test_pin,
            text='Comment for test pin'
        )
        
        comment2 = Comment.objects.create(
            user=self.user1,
            pin=other_pin,
            text='Comment for other pin'
        )
        
        # Test filtering by pin_id
        response = self.client.get(f"{self.list_create_url}?pin_id={self.test_pin.id}")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)  # Total count should be 1
        self.assertEqual(len(response.data['results']), 1)  # One comment in results
        self.assertEqual(response.data['results'][0]['pin'], self.test_pin.id)

    def test_retrieve_comment(self):
        """Test retrieving a single comment"""
        # Create a test comment
        comment = Comment.objects.create(
            user=self.user1,
            pin=self.test_pin,
            text='Test comment'
        )
        detail_url = reverse('comment-detail', kwargs={'pk': comment.pk})
        
        response = self.client.get(detail_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['text'], 'Test comment')

    def test_hidden_comments_not_listed(self):
        """Test that hidden comments are not included in list"""
        # Create a hidden comment
        hidden_comment = Comment.objects.create(
            user=self.user1,
            pin=self.test_pin,
            text='Hidden comment',
            is_hidden=True
        )
        
        response = self.client.get(self.list_create_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 0)  # Total count should be 0
        self.assertEqual(len(response.data['results']), 0)  # No comments in results

    def test_comment_ordering(self):
        """Test that comments are ordered by created_at descending"""
        # Create comments with different timestamps
        comment1 = Comment.objects.create(
            user=self.user1,
            pin=self.test_pin,
            text='First comment'
        )
        
        comment2 = Comment.objects.create(
            user=self.user1,
            pin=self.test_pin,
            text='Second comment'
        )
        
        response = self.client.get(self.list_create_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 2)  # Total count should be 2
        self.assertEqual(len(response.data['results']), 2)  # Two comments in results
        # Check ordering - newest first
        self.assertEqual(response.data['results'][0]['id'], comment2.id)
        self.assertEqual(response.data['results'][1]['id'], comment1.id)

    def test_report_comment(self):
        """Test reporting a comment"""
        # Create a test comment
        comment = Comment.objects.create(
            user=self.user1,
            pin=self.test_pin,
            text='Test comment'
        )
        detail_url = reverse('comment-detail', kwargs={'pk': comment.pk})
        
        response = self.client.post(f"{detail_url}report/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
