from django.shortcuts import render
from rest_framework import viewsets, status, mixins
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db import transaction
from django.utils import timezone
import logging

from .models import Comment
from .serializers import CommentSerializer
from bopmaps.permissions import IsOwnerOrReadOnly
from bopmaps.utils import create_error_response

logger = logging.getLogger('bopmaps')

class CommentViewSet(viewsets.ModelViewSet):
    """
    API viewset for Comment operations
    """
    serializer_class = CommentSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReadOnly]
    
    def get_queryset(self):
        """Filter comments by pin and exclude hidden comments"""
        # Start with base queryset
        queryset = Comment.objects.all()
        
        # Filter by pin_id if provided
        pin_id = self.request.query_params.get('pin_id')
        if pin_id:
            queryset = queryset.filter(pin_id=pin_id)
        
        # Always exclude hidden comments
        queryset = queryset.filter(is_hidden=False)
        
        # Add related user data and order by created_at
        return queryset.select_related('user').order_by('-created_at')
    
    def perform_create(self, serializer):
        """Create comment"""
        try:
            with transaction.atomic():
                comment = serializer.save()
                logger.info(f"Comment created: {comment}")
        except Exception as e:
            logger.error(f"Error creating comment: {str(e)}")
            raise
            
    def perform_destroy(self, instance):
        """Soft delete by hiding the comment"""
        try:
            instance.is_hidden = True
            instance.save(update_fields=['is_hidden'])
            logger.info(f"Comment hidden: {instance}")
        except Exception as e:
            logger.error(f"Error hiding comment: {str(e)}")
            raise
        
    @action(detail=True, methods=['post'])
    def report(self, request, pk=None):
        """Report inappropriate comment"""
        try:
            comment = self.get_object()
            # Here you would implement your reporting logic
            # For now, we'll just log it
            logger.warning(f"Comment reported: {comment}")
            return Response({"status": "report received"}, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error reporting comment: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
