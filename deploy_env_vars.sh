#!/bin/bash

# Deploy Environment Variables to ECS
# This script updates your Terraform infrastructure to include all environment variables

set -e

echo "🚀 Deploying Environment Variables to ECS Clusters"
echo "=================================================="

# Check if terraform.tfvars exists
if [ ! -f "terraform/terraform.tfvars" ]; then
    echo "❌ Error: terraform/terraform.tfvars not found!"
    echo "Please copy terraform/terraform.tfvars.example to terraform/terraform.tfvars and fill in your values:"
    echo "cp terraform/terraform.tfvars.example terraform/terraform.tfvars"
    exit 1
fi

# Change to terraform directory
cd terraform

echo "📋 Checking terraform.tfvars for required variables..."

# Check if critical variables are set
MISSING_VARS=()

# Check if spotify credentials are set
if ! grep -q "spotify_client_id.*=.*\"[^\"]\+\"" terraform.tfvars || grep -q "spotify_client_id.*=.*\"\"" terraform.tfvars; then
    MISSING_VARS+=("spotify_client_id")
fi

if ! grep -q "spotify_client_secret.*=.*\"[^\"]\+\"" terraform.tfvars || grep -q "spotify_client_secret.*=.*\"\"" terraform.tfvars; then
    MISSING_VARS+=("spotify_client_secret")
fi

# Show warning for missing optional variables but continue
if [ ${#MISSING_VARS[@]} -gt 0 ]; then
    echo "⚠️  Warning: The following important variables are not set in terraform.tfvars:"
    for var in "${MISSING_VARS[@]}"; do
        echo "   - $var"
    done
    echo ""
    echo "Your Spotify API features won't work without these credentials."
    echo "You can get them from: https://developer.spotify.com/dashboard/applications"
    echo ""
    read -p "Do you want to continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Deployment cancelled. Please update your terraform.tfvars file."
        exit 1
    fi
fi

echo "✅ Configuration looks good!"
echo ""

# Initialize terraform if needed
if [ ! -d ".terraform" ]; then
    echo "🔧 Initializing Terraform..."
    terraform init
fi

echo "📊 Planning infrastructure changes..."
terraform plan -out=tfplan

echo ""
echo "🚀 Applying infrastructure changes..."
echo "This will update your ECS task definitions with all environment variables."
echo ""
read -p "Continue with deployment? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Deployment cancelled."
    rm -f tfplan
    exit 1
fi

terraform apply tfplan
rm -f tfplan

echo ""
echo "✅ Deployment completed successfully!"
echo "=================================================="
echo ""
echo "📋 Next steps:"
echo "1. Your ECS services will restart automatically with the new environment variables"
echo "2. Monitor the deployment in AWS ECS Console"
echo "3. Test your Spotify API endpoints once deployment is complete"
echo ""
echo "🔍 To check logs:"
echo "   aws logs tail /ecs/bopmaps-prod/app --follow"
echo ""
echo "🧪 To test Spotify API:"
echo "   curl -X GET 'https://api.bopmaps.com/api/spotify/search/?q=test&type=track'"
echo ""

cd .. 