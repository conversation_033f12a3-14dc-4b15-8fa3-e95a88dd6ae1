version: '3.8'

services:
  db:
    image: postgis/postgis:14-3.2
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    env_file:
      - ./.env
    environment:
      - POSTGRES_PASSWORD=password
      - POSTGRES_USER=postgres
      - POSTGRES_DB=bopmaps
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 30s
      retries: 50

  web:
    build: .
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - .:/code
    ports:
      - "8000:8000"
    env_file:
      - ./.env
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - DATABASE_URL=postgis://postgres:password@db:5432/bopmaps
      - REDIS_URL=redis://redis:6379/0
    
  celery:
    build: .
    command: celery -A bopmaps worker -l INFO
    volumes:
      - .:/code
    env_file:
      - ./.env
    depends_on:
      - web
      - redis
      - db
    environment:
      - DATABASE_URL=postgis://postgres:password@db:5432/bopmaps
      - REDIS_URL=redis://redis:6379/0

  celery-beat:
    build: .
    command: celery -A bopmaps beat -l INFO
    volumes:
      - .:/code
    env_file:
      - ./.env
    depends_on:
      - web
      - redis
      - db
    environment:
      - DATABASE_URL=postgis://postgres:password@db:5432/bopmaps
      - REDIS_URL=redis://redis:6379/0

volumes:
  postgres_data:
  redis_data: 