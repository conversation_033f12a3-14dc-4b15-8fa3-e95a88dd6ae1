# OpenMapTiles Planet Tile Server Dockerfile
FROM node:18-alpine AS builder

# Install system dependencies
RUN apk add --no-cache \
    build-base \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    giflib-dev \
    librsvg-dev \
    pixman-dev

# Install TileServer GL
RUN npm install -g tileserver-gl-light

# Production stage
FROM node:18-alpine

# Install runtime dependencies
RUN apk add --no-cache \
    cairo \
    jpeg \
    pango \
    giflib \
    librsvg \
    pixman \
    curl

# Copy TileServer GL from builder
COPY --from=builder /usr/local/lib/node_modules /usr/local/lib/node_modules
COPY --from=builder /usr/local/bin /usr/local/bin

# Create app directory
WORKDIR /data

# Create directories for data
RUN mkdir -p /data/tiles /data/styles /data/fonts /data/sprites

# Copy configuration files
COPY docker/openmaptiles/config.json /data/config.json
COPY docker/openmaptiles/styles/ /data/styles/
COPY docker/openmaptiles/fonts/ /data/fonts/
COPY docker/openmaptiles/sprites/ /data/sprites/

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Expose port
EXPOSE 8080

# Start TileServer GL
CMD ["tileserver-gl-light", "--config", "/data/config.json", "--port", "8080", "--bind", "0.0.0.0"] 