#!/bin/bash
set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting BOPMaps application...${NC}"

# Function to wait for database
wait_for_db() {
    echo -e "${YELLOW}Waiting for database to be ready...${NC}"
    
    # Check if using SQLite (no network connection needed)
    if [ -n "$DATABASE_URL" ] && echo "$DATABASE_URL" | grep -q "sqlite"; then
        echo -e "${GREEN}Using SQLite database, no connection check needed${NC}"
        return 0
    fi
    
    # Extract database details from DATABASE_URL
    if [ -n "$DATABASE_URL" ]; then
        DB_HOST=$(echo $DATABASE_URL | sed -n 's/.*@\([^:]*\):.*/\1/p')
        DB_PORT=$(echo $DATABASE_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
    else
        DB_HOST=${DB_HOST:-localhost}
        DB_PORT=${DB_PORT:-5432}
    fi
    
    # Skip if we couldn't extract host/port (likely SQLite or other local DB)
    if [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ]; then
        echo -e "${GREEN}Local database configuration detected, skipping connection check${NC}"
        return 0
    fi
    
    echo "Checking database at $DB_HOST:$DB_PORT"
    
    for i in {1..30}; do
        if nc -z "$DB_HOST" "$DB_PORT"; then
            echo -e "${GREEN}Database is ready!${NC}"
            return 0
        fi
        echo "Database not ready yet, waiting... (attempt $i/30)"
        sleep 2
    done
    
    echo -e "${RED}Database is not available after 60 seconds${NC}"
    exit 1
}

# Function to wait for Redis
wait_for_redis() {
    echo -e "${YELLOW}Waiting for Redis to be ready...${NC}"
    
    # Extract Redis details from REDIS_URL
    if [ -n "$REDIS_URL" ]; then
        REDIS_HOST=$(echo $REDIS_URL | sed -n 's/.*@\([^:]*\):.*/\1/p')
        if [ -z "$REDIS_HOST" ]; then
            REDIS_HOST=$(echo $REDIS_URL | sed -n 's/redis:\/\/\([^:]*\):.*/\1/p')
        fi
        REDIS_PORT=$(echo $REDIS_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
        if [ -z "$REDIS_PORT" ]; then
            REDIS_PORT=$(echo $REDIS_URL | sed -n 's/.*:\([0-9]*\)$/\1/p')
        fi
    else
        REDIS_HOST=${REDIS_HOST:-localhost}
        REDIS_PORT=${REDIS_PORT:-6379}
    fi
    
    echo "Checking Redis at $REDIS_HOST:$REDIS_PORT"
    
    for i in {1..30}; do
        if nc -z "$REDIS_HOST" "$REDIS_PORT"; then
            echo -e "${GREEN}Redis is ready!${NC}"
            return 0
        fi
        echo "Redis not ready yet, waiting... (attempt $i/30)"
        sleep 2
    done
    
    echo -e "${YELLOW}Redis is not available after 60 seconds, continuing anyway...${NC}"
}

# Function to run Django migrations
run_migrations() {
    echo -e "${YELLOW}Running database migrations...${NC}"
    python manage.py migrate --noinput
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Migrations completed successfully${NC}"
    else
        echo -e "${RED}Migration failed${NC}"
        exit 1
    fi
    
    # Create cache table for database caching
    echo -e "${YELLOW}Creating cache table...${NC}"
    python manage.py createcachetable || echo -e "${YELLOW}Cache table already exists or creation failed, continuing...${NC}"
}

# Function to create default data
create_default_data() {
    echo -e "${YELLOW}Creating default data...${NC}"
    
    # Create default pin skin
    python manage.py create_default_skin
    
    # Create superuser if it doesn't exist (only in development)
    if [ "$ENVIRONMENT" != "production" ] && [ "$ENVIRONMENT" != "prod" ]; then
        echo -e "${YELLOW}Creating default superuser for development...${NC}"
        python manage.py shell -c "
from django.contrib.auth import get_user_model;
User = get_user_model();
if not User.objects.filter(is_superuser=True).exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123');
    print('Superuser created: admin/admin123');
else:
    print('Superuser already exists');
"
    fi
}

# Function to collect static files
collect_static() {
    echo -e "${YELLOW}Collecting static files (including admin)...${NC}"
    python manage.py collectstatic --noinput --clear
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Static files collected successfully${NC}"
        # List some key admin files to verify they were collected
        echo -e "${YELLOW}Verifying admin static files...${NC}"
        ls -la /app/staticfiles/admin/css/base.css 2>/dev/null && echo "✓ Admin base CSS found" || echo "✗ Admin base CSS missing"
        ls -la /app/staticfiles/admin/css/login.css 2>/dev/null && echo "✓ Admin login CSS found" || echo "✗ Admin login CSS missing"
    else
        echo -e "${RED}Static files collection failed${NC}"
        exit 1
    fi
}

# Function to start Celery worker
start_celery_worker() {
    echo -e "${GREEN}Starting Celery worker...${NC}"
    exec celery -A bopmaps worker -l INFO --max-tasks-per-child=1000
}

# Function to start Celery beat
start_celery_beat() {
    echo -e "${GREEN}Starting Celery beat...${NC}"
    # Ensure the celerybeat-schedule file is writable
    touch /tmp/celerybeat-schedule
    exec celery -A bopmaps beat -l INFO --scheduler django_celery_beat.schedulers:DatabaseScheduler --pidfile=/tmp/celerybeat.pid
}

# Function to start Django application
start_django() {
    echo -e "${GREEN}Starting Django application...${NC}"
    
    # Check if we should run in development mode
    if [ "$ENVIRONMENT" = "development" ] || [ "$DEBUG" = "True" ]; then
        echo -e "${YELLOW}Running in development mode${NC}"
        exec python manage.py runserver 0.0.0.0:8000
    else
        echo -e "${GREEN}Running in production mode with Gunicorn${NC}"
        exec "$@"
    fi
}

# Main execution flow
main() {
    # Check what type of service we're starting
    case "${1:-django}" in
        "celery-worker")
            wait_for_db
            wait_for_redis
            start_celery_worker
            ;;
        "celery-beat")
            wait_for_db
            wait_for_redis
            start_celery_beat
            ;;
        "migrate")
            wait_for_db
            run_migrations
            ;;
        "django"|"gunicorn")
            wait_for_db
            wait_for_redis
            run_migrations
            create_default_data
            collect_static
            start_django "$@"
            ;;
        *)
            echo -e "${GREEN}Running custom command: $@${NC}"
            exec "$@"
            ;;
    esac
}

# Trap SIGTERM and forward to child process
trap 'kill -TERM $PID' TERM

# Run main function
main "$@" &
PID=$!
wait $PID 