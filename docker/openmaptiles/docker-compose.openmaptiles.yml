version: '3.8'

services:
  postgres:
    image: "openmaptiles/postgis:latest"
    environment:
      POSTGRES_DB: openmaptiles
      POSTGRES_USER: openmaptiles
      POSTGRES_PASSWORD: openmaptiles
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
    ports:
      - "5433:5432"
    volumes:
      - openmaptiles_postgres_data:/var/lib/postgresql/data
    networks:
      - openmaptiles_network

  import-data:
    image: "openmaptiles/openmaptiles-tools:latest"
    environment:
      POSTGRES_DB: openmaptiles
      POSTGRES_USER: openmaptiles
      POSTGRES_PASSWORD: openmaptiles
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      DIFF_MODE: false
      OSM_AREA_NAME: planet
      MIN_ZOOM: 0
      MAX_ZOOM: 14
    volumes:
      - openmaptiles_data:/export
      - openmaptiles_cache:/cache
      - ./data:/data
    depends_on:
      - postgres
    networks:
      - openmaptiles_network
    profiles:
      - import

  generate-tiles:
    image: "openmaptiles/openmaptiles-tools:latest"
    environment:
      POSTGRES_DB: openmaptiles
      POSTGRES_USER: openmaptiles
      POSTGRES_PASSWORD: openmaptiles
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      MIN_ZOOM: 0
      MAX_ZOOM: 14
      BBOX: "-180,-85.0511,180,85.0511"
    volumes:
      - openmaptiles_data:/export
      - openmaptiles_cache:/cache
      - ./tiles:/tiles
    depends_on:
      - postgres
    networks:
      - openmaptiles_network
    profiles:
      - generate

  tileserver:
    build:
      context: ../../
      dockerfile: docker/Dockerfile.openmaptiles
    ports:
      - "8080:8080"
    volumes:
      - ./tiles:/data/tiles:ro
      - ./styles:/data/styles:ro
      - ./fonts:/data/fonts:ro
      - ./sprites:/data/sprites:ro
    environment:
      NODE_ENV: production
    depends_on:
      - generate-tiles
    networks:
      - openmaptiles_network
    profiles:
      - serve

volumes:
  openmaptiles_postgres_data:
  openmaptiles_data:
  openmaptiles_cache:

networks:
  openmaptiles_network:
    driver: bridge 