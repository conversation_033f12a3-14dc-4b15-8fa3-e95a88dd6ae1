# Backblaze B2 Migration Guide

This document explains the migration from AWS S3 to Backblaze B2 for storage and map tiles in the BOPMaps infrastructure.

## Overview

The infrastructure has been updated to use Backblaze B2 for:
- Static file storage
- Media file uploads
- Map tile storage

AWS is still used for:
- EC2 instances (via ECS Fargate)
- Auto-scaling
- Load balancing
- Database (RDS)
- Redis (ElastiCache)
- Networking (VPC, subnets, etc.)

## Benefits of Using Backblaze B2

1. **Cost Savings**: B2 storage is typically 75% cheaper than AWS S3
2. **Cloudflare Integration**: Native integration with Cloudflare for CDN
3. **S3-Compatible API**: Works with existing django-storages S3 backend
4. **Simple Pricing**: No data transfer fees when using Cloudflare

## Setup Instructions

### 1. Create Backblaze B2 Account and Application Key

1. Sign up for a Backblaze B2 account at https://www.backblaze.com/b2/
2. Create an Application Key:
   - Go to Application Keys in the B2 dashboard
   - Click "Add a New Application Key"
   - Name: "BOPMaps Production" (or appropriate environment name)
   - Bucket: All buckets (or restrict as needed)
   - Capabilities: All capabilities
   - Save the `keyID` and `applicationKey` securely

### 2. Configure Terraform Variables

Update your `terraform.tfvars` file with B2 credentials:

```hcl
# Backblaze B2 Configuration
b2_application_key_id = "your-key-id"
b2_application_key    = "your-application-key"
b2_bucket_name_prefix = "bopmaps"
b2_region            = "us-west-004"  # Change based on your B2 region
```

### 3. Deploy Infrastructure

```bash
cd terraform
terraform init  # This will download the B2 provider
terraform plan
terraform apply
```

### 4. Update Application Environment Variables

The ECS task definitions will automatically be updated with these environment variables:
- `B2_APPLICATION_KEY_ID`
- `B2_APPLICATION_KEY`
- `B2_BUCKET_NAME`
- `B2_ENDPOINT_URL`
- `B2_REGION`
- `MEDIA_BUCKET_NAME`
- `USE_S3_FOR_MEDIA=True`

### 5. Configure Cloudflare

The Terraform configuration automatically creates Cloudflare DNS records and page rules for:
- `static.api.bopmaps.com` → B2 static bucket
- `media.api.bopmaps.com` → B2 media bucket
- `b2tiles.api.bopmaps.com` → B2 tiles bucket

Page rules are configured for optimal caching:
- Static files: 2-hour edge cache, 24-hour browser cache
- Media files: 1-hour edge cache, 12-hour browser cache
- Map tiles: 24-hour edge cache, 7-day browser cache

## Migration Steps for Existing Data

If you have existing data in AWS S3, follow these steps:

### 1. Install B2 CLI

```bash
pip install b2
b2 authorize-account <keyID> <applicationKey>
```

### 2. Install AWS CLI (if not already installed)

```bash
pip install awscli
aws configure
```

### 3. Sync Data from S3 to B2

```bash
# Sync static files
aws s3 sync s3://your-old-static-bucket s3://bopmaps-prod-static-xxxx --endpoint-url=https://s3.us-west-004.backblazeb2.com

# Sync media files
aws s3 sync s3://your-old-media-bucket s3://bopmaps-prod-media-xxxx --endpoint-url=https://s3.us-west-004.backblazeb2.com

# Sync map tiles
aws s3 sync s3://your-old-tiles-bucket s3://bopmaps-prod-tiles-xxxx --endpoint-url=https://s3.us-west-004.backblazeb2.com
```

## Django Configuration

The Django settings automatically detect B2 configuration and use it when available:

```python
# Storage configuration for production
if not DEBUG:
    # Try Backblaze B2 first, fallback to AWS S3
    B2_APPLICATION_KEY_ID = config('B2_APPLICATION_KEY_ID', default='')
    B2_APPLICATION_KEY = config('B2_APPLICATION_KEY', default='')
    B2_BUCKET_NAME = config('B2_BUCKET_NAME', default='')
    
    if B2_APPLICATION_KEY_ID and B2_APPLICATION_KEY and B2_BUCKET_NAME:
        # Use B2 S3-compatible API
        AWS_ACCESS_KEY_ID = B2_APPLICATION_KEY_ID
        AWS_SECRET_ACCESS_KEY = B2_APPLICATION_KEY
        AWS_STORAGE_BUCKET_NAME = B2_BUCKET_NAME
        AWS_S3_ENDPOINT_URL = B2_ENDPOINT_URL
        # ... rest of configuration
```

## URLs After Migration

- Static files: `https://static.api.bopmaps.com/path/to/file`
- Media files: `https://media.api.bopmaps.com/path/to/file`
- Map tiles: `https://b2tiles.api.bopmaps.com/tiles/{z}/{x}/{y}.pbf`

All URLs remain the same from the application's perspective, as Cloudflare handles the routing to B2.

## Cost Comparison

### AWS S3 (Previous)
- Storage: $0.023/GB/month
- Data Transfer: $0.09/GB (to internet)
- Requests: $0.0004 per 1,000 GET requests

### Backblaze B2 (New)
- Storage: $0.005/GB/month (78% cheaper)
- Data Transfer: $0.00/GB (free with Cloudflare)
- Requests: $0.004 per 10,000 requests

**Estimated Monthly Savings**: 70-80% on storage costs

## Monitoring

Monitor your B2 usage through:
1. Backblaze B2 dashboard
2. Cloudflare Analytics (for CDN performance)
3. AWS CloudWatch (for application metrics)

## Rollback Plan

If you need to rollback to AWS S3:

1. Comment out the B2 environment variables in `terraform/ecs.tf`
2. Uncomment the AWS S3 environment variables
3. Rename `storage_aws_disabled.tf` back to `storage.tf`
4. Rename `storage_b2.tf` to `storage_b2_disabled.tf`
5. Update Cloudflare DNS records to point back to S3
6. Run `terraform apply`

## Troubleshooting

### Common Issues

1. **403 Forbidden errors**: Check B2 bucket permissions and CORS settings
2. **Slow uploads**: Ensure you're using the correct B2 region
3. **Missing files**: Verify the sync completed successfully
4. **CORS errors**: Check the B2 bucket CORS rules in Terraform

### Debug Commands

```bash
# Test B2 connectivity
b2 list-buckets

# Check bucket contents
b2 ls b2://bopmaps-prod-static-xxxx

# Test S3-compatible API
aws s3 ls s3://bopmaps-prod-static-xxxx --endpoint-url=https://s3.us-west-004.backblazeb2.com
```

## Support

For issues or questions:
1. Check Backblaze B2 documentation: https://www.backblaze.com/b2/docs/
2. Review Cloudflare integration guide: https://www.cloudflare.com/partners/backblaze/
3. Consult django-storages S3 documentation: https://django-storages.readthedocs.io/ 