# Bop Drops - Music Status Sharing Feature

## Overview

The **Bop Drops** feature adds social music sharing functionality to BOPMaps, allowing friends to share music recommendations as statuses. Users can drop their favorite songs for friends to discover, like, and interact with.

## Key Features

### 🎵 Music Status Sharing
- Users can share songs with optional captions and mood indicators
- Automatic expiration (24 hours by default)
- Support for multiple music services (Spotify, Apple Music, etc.)

### 👥 Social Feed
- View friends' music recommendations in a chronological feed
- Like and interact with friends' bop drops
- Track views and engagement analytics

### 🔀 Discovery Features
- **Shuffle Mode**: Random browsing through friends' music
- **Trending**: See what's popular among your friend circle
- **Mood Filtering**: Find music based on specific moods

### 🔒 Privacy Controls
- Friends-only visibility by default
- Option to make drops public
- Automatic friendship verification

## API Endpoints

### Core Endpoints

#### Create a Bop Drop
```http
POST /api/bop-drops/
```

**Request Body:**
```json
{
  "track_id": "spotify:track:4iV5W9uYEdYUVa79Axb7Rh",
  "track_title": "Never Gonna Give You Up",
  "track_artist": "<PERSON>",
  "track_album": "Whenever You Need Somebody",
  "album_art_url": "https://...",
  "preview_url": "https://...",
  "music_service": "spotify",
  "caption": "This is a classic! 🎵",
  "mood": "happy",
  "is_currently_playing": true,
  "friends_only": true
}
```

**Response:**
```json
{
  "id": 123,
  "user": {
    "id": 1,
    "username": "musiclover",
    "first_name": "John",
    "last_name": "Doe"
  },
  "track_id": "spotify:track:4iV5W9uYEdYUVa79Axb7Rh",
  "track_title": "Never Gonna Give You Up",
  "track_artist": "Rick Astley",
  "caption": "This is a classic! 🎵",
  "mood": "happy",
  "is_currently_playing": true,
  "like_count": 0,
  "view_count": 0,
  "created_at": "2024-01-15T10:30:00Z",
  "expires_at": "2024-01-16T10:30:00Z",
  "is_liked_by_user": false,
  "time_since_created": "just now"
}
```

#### Get Friends' Feed
```http
GET /api/bop-drops/feed/
```

**Query Parameters:**
- `mood`: Filter by mood (happy, sad, energetic, etc.)
- `currently_playing`: Filter for currently playing tracks (`true`/`false`)
- `page`: Page number for pagination
- `page_size`: Number of results per page

### Social Features

#### Like a Bop Drop
```http
POST /api/bop-drops/{id}/like/
```

#### Unlike a Bop Drop
```http
POST /api/bop-drops/{id}/unlike/
```

#### Record a View
```http
POST /api/bop-drops/{id}/view/
```

### Discovery Features

#### Shuffle Friends' Bop Drops
```http
GET /api/bop-drops/shuffle/?limit=10
```

#### Get Trending Bop Drops
```http
GET /api/bop-drops/trending/
```

#### Filter by Mood
```http
GET /api/bop-drops/by_mood/?mood=happy
```

### Analytics

#### Get Personal Statistics
```http
GET /api/bop-drops/my_stats/
```

**Response:**
```json
{
  "total_drops": 25,
  "active_drops": 3,
  "total_likes_received": 47,
  "total_views_received": 134,
  "most_liked_track": "Bohemian Rhapsody - Queen",
  "favorite_mood": "energetic",
  "drops_this_week": 5,
  "average_likes_per_drop": 1.88
}
```

## Database Models

### BopDrop Model
```python
class BopDrop(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    
    # Music details
    track_id = models.CharField(max_length=255)
    track_title = models.CharField(max_length=255)
    track_artist = models.CharField(max_length=255)
    track_album = models.CharField(max_length=255, blank=True, null=True)
    album_art_url = models.URLField(blank=True, null=True)
    preview_url = models.URLField(blank=True, null=True)
    music_service = models.CharField(max_length=20, default='spotify')
    
    # Status details
    caption = models.TextField(max_length=500, blank=True, null=True)
    mood = models.CharField(max_length=20, choices=MOOD_CHOICES, blank=True, null=True)
    is_currently_playing = models.BooleanField(default=False)
    
    # Visibility and interaction
    is_active = models.BooleanField(default=True)
    friends_only = models.BooleanField(default=True)
    
    # Timestamps and analytics
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    like_count = models.PositiveIntegerField(default=0)
    view_count = models.PositiveIntegerField(default=0)
```

### Available Moods
- `happy` - Happy
- `sad` - Sad
- `energetic` - Energetic
- `chill` - Chill
- `nostalgic` - Nostalgic
- `party` - Party
- `focus` - Focus
- `romantic` - Romantic
- `angry` - Angry
- `other` - Other

## Frontend Integration Examples

### React/JavaScript Integration

#### Creating a Bop Drop
```javascript
// Function to create a bop drop from currently playing track
async function createBopDropFromCurrentTrack(trackData, caption, mood) {
  const bopDropData = {
    track_id: trackData.id,
    track_title: trackData.name,
    track_artist: trackData.artists.map(a => a.name).join(', '),
    track_album: trackData.album.name,
    album_art_url: trackData.album.images[0]?.url,
    preview_url: trackData.preview_url,
    music_service: 'spotify',
    caption: caption,
    mood: mood,
    is_currently_playing: true
  };

  const response = await fetch('/api/bop-drops/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${authToken}`
    },
    body: JSON.stringify(bopDropData)
  });

  return await response.json();
}
```

#### Displaying the Feed
```javascript
// React component for bop drops feed
function BopDropsFeed() {
  const [bopDrops, setBopDrops] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFeed();
  }, []);

  const fetchFeed = async () => {
    const response = await fetch('/api/bop-drops/feed/', {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    const data = await response.json();
    setBopDrops(data.results);
    setLoading(false);
  };

  const likeBopDrop = async (bopDropId) => {
    await fetch(`/api/bop-drops/${bopDropId}/like/`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    fetchFeed(); // Refresh feed
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div className="bop-drops-feed">
      {bopDrops.map(drop => (
        <BopDropCard 
          key={drop.id} 
          bopDrop={drop} 
          onLike={() => likeBopDrop(drop.id)}
        />
      ))}
    </div>
  );
}
```

#### Bop Drop Card Component
```javascript
function BopDropCard({ bopDrop, onLike }) {
  return (
    <div className="bop-drop-card">
      <div className="user-info">
        <span>{bopDrop.user.username}</span>
        <span className="time">{bopDrop.time_since_created}</span>
      </div>
      
      <div className="track-info">
        <img src={bopDrop.album_art_url} alt="Album art" />
        <div>
          <h3>{bopDrop.track_title}</h3>
          <p>{bopDrop.track_artist}</p>
          {bopDrop.mood && <span className="mood">😊 {bopDrop.mood}</span>}
        </div>
      </div>
      
      {bopDrop.caption && (
        <p className="caption">{bopDrop.caption}</p>
      )}
      
      <div className="actions">
        <button 
          onClick={onLike}
          className={bopDrop.is_liked_by_user ? 'liked' : ''}
        >
          ❤️ {bopDrop.like_count}
        </button>
        <span>👀 {bopDrop.view_count}</span>
        {bopDrop.preview_url && (
          <button onClick={() => playPreview(bopDrop.preview_url)}>
            ▶️ Preview
          </button>
        )}
      </div>
    </div>
  );
}
```

### Flutter/Dart Integration

#### Bop Drop Model
```dart
class BopDrop {
  final int id;
  final User user;
  final String trackId;
  final String trackTitle;
  final String trackArtist;
  final String? albumArtUrl;
  final String? caption;
  final String? mood;
  final bool isCurrentlyPlaying;
  final int likeCount;
  final int viewCount;
  final bool isLikedByUser;
  final String timeSinceCreated;

  BopDrop({
    required this.id,
    required this.user,
    required this.trackId,
    required this.trackTitle,
    required this.trackArtist,
    this.albumArtUrl,
    this.caption,
    this.mood,
    required this.isCurrentlyPlaying,
    required this.likeCount,
    required this.viewCount,
    required this.isLikedByUser,
    required this.timeSinceCreated,
  });

  factory BopDrop.fromJson(Map<String, dynamic> json) {
    return BopDrop(
      id: json['id'],
      user: User.fromJson(json['user']),
      trackId: json['track_id'],
      trackTitle: json['track_title'],
      trackArtist: json['track_artist'],
      albumArtUrl: json['album_art_url'],
      caption: json['caption'],
      mood: json['mood'],
      isCurrentlyPlaying: json['is_currently_playing'],
      likeCount: json['like_count'],
      viewCount: json['view_count'],
      isLikedByUser: json['is_liked_by_user'],
      timeSinceCreated: json['time_since_created'],
    );
  }
}
```

#### API Service
```dart
class BopDropsService {
  final String baseUrl = 'https://api.bopmaps.com';
  
  Future<List<BopDrop>> getFeed({String? mood}) async {
    String url = '$baseUrl/api/bop-drops/feed/';
    if (mood != null) {
      url += '?mood=$mood';
    }
    
    final response = await http.get(
      Uri.parse(url),
      headers: {'Authorization': 'Bearer $authToken'},
    );
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return (data['results'] as List)
          .map((item) => BopDrop.fromJson(item))
          .toList();
    }
    throw Exception('Failed to load feed');
  }
  
  Future<void> likeBopDrop(int bopDropId) async {
    await http.post(
      Uri.parse('$baseUrl/api/bop-drops/$bopDropId/like/'),
      headers: {'Authorization': 'Bearer $authToken'},
    );
  }
  
  Future<BopDrop> createBopDrop({
    required String trackId,
    required String trackTitle,
    required String trackArtist,
    String? caption,
    String? mood,
    bool isCurrentlyPlaying = false,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/api/bop-drops/'),
      headers: {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json',
      },
      body: json.encode({
        'track_id': trackId,
        'track_title': trackTitle,
        'track_artist': trackArtist,
        'caption': caption,
        'mood': mood,
        'is_currently_playing': isCurrentlyPlaying,
      }),
    );
    
    if (response.statusCode == 201) {
      return BopDrop.fromJson(json.decode(response.body));
    }
    throw Exception('Failed to create bop drop');
  }
}
```

## Music Service Integration

### Spotify Integration Example

```javascript
// Get currently playing track from Spotify
async function getCurrentlyPlaying() {
  const response = await fetch('https://api.spotify.com/v1/me/player/currently-playing', {
    headers: { 'Authorization': `Bearer ${spotifyToken}` }
  });
  
  if (response.status === 200) {
    const data = await response.json();
    return data.item; // Track object
  }
  return null;
}

// Create bop drop from Spotify track
async function dropCurrentTrack(caption, mood) {
  const track = await getCurrentlyPlaying();
  if (!track) {
    throw new Error('No track currently playing');
  }
  
  return await createBopDropFromCurrentTrack(track, caption, mood);
}
```

## Best Practices

### 1. Real-time Updates
Consider implementing WebSocket connections for real-time feed updates:

```javascript
const socket = new WebSocket('ws://localhost:8000/ws/bop-drops/');
socket.onmessage = function(event) {
  const data = JSON.parse(event.data);
  if (data.type === 'new_bop_drop') {
    // Add new bop drop to feed
    updateFeed(data.bop_drop);
  }
};
```

### 2. Caching Strategy
Implement proper caching for better performance:

```javascript
// Cache feed data for 5 minutes
const cacheKey = 'bop-drops-feed';
const cachedFeed = localStorage.getItem(cacheKey);
const cacheTime = localStorage.getItem(`${cacheKey}-time`);

if (cachedFeed && Date.now() - cacheTime < 300000) {
  return JSON.parse(cachedFeed);
}
```

### 3. Error Handling
Always handle errors gracefully:

```javascript
try {
  await createBopDrop(trackData);
  showSuccess('Bop drop shared!');
} catch (error) {
  if (error.status === 403) {
    showError('You can only share music with friends');
  } else {
    showError('Failed to share track. Please try again.');
  }
}
```

### 4. Privacy Considerations
- Always respect users' music privacy settings
- Implement proper friend verification
- Allow users to control who can see their music activity

## Performance Optimizations

### Database Indexing
The models include optimized indexes for common queries:
- User + created_at for user feeds
- Mood + created_at for mood filtering
- Music service + track_id for duplicate detection

### Query Optimization
- Use `select_related()` for user data
- Implement `prefetch_related()` for likes/views
- Cache friend lists for frequent feed requests

### Caching Strategy
```python
from django.core.cache import cache

def get_user_feed(user_id):
    cache_key = f'bop_drops_feed_{user_id}'
    cached_feed = cache.get(cache_key)
    
    if cached_feed is None:
        # Generate feed
        cached_feed = generate_feed(user_id)
        cache.set(cache_key, cached_feed, timeout=300)  # 5 minutes
    
    return cached_feed
```

## Testing

Run the comprehensive test suite:

```bash
python manage.py test bop_drops -v 2
```

The test suite covers:
- Model functionality and validation
- API endpoint functionality
- Permission and privacy controls
- Social features (likes, views, shares)
- Integration with friends system
- Analytics and statistics

## Security Considerations

1. **Friend Verification**: All interactions verify friendship status
2. **Rate Limiting**: Consider implementing rate limits for API endpoints
3. **Data Validation**: All input is validated and sanitized
4. **Privacy Controls**: Users can control visibility of their music activity

## Future Enhancements

Potential features to consider:
- **Playlists**: Allow users to create shared playlists from bop drops
- **Challenges**: Music discovery challenges among friends
- **Integration**: Direct integration with music streaming apps
- **Recommendations**: AI-powered music recommendations based on friend activity
- **Stories**: Temporary music stories similar to social media stories

## Support

For technical support or feature requests, please refer to the main BOPMaps documentation or contact the development team. 