# Caching and Rate Limiting Update

## Overview

Both LastFM and Spotify services now use cache-aware rate limiting. This means that cached requests don't count against your rate limit, significantly improving performance for repeated requests.

## How It Works

### Previous Behavior
1. Request comes in
2. Rate limit is checked (requests count against limit)
3. If rate limit exceeded → 429 error
4. If allowed → check cache
5. If not cached → make API call

### New Behavior
1. Request comes in
2. Check if request would be served from cache
3. If cached → allow request (skip rate limiting)
4. If not cached → apply rate limiting
5. Make API call only if needed

## Benefits

- **Better Performance**: Cached requests are served immediately without rate limiting
- **Higher Throughput**: You can make unlimited requests for cached data
- **Reduced 429 Errors**: Only uncached requests count against rate limits

## Spotify Endpoints

The correct URLs for Spotify user endpoints are:

```
/api/spotify/search/                      # Search tracks/artists/albums
/api/spotify/me/tracks/                   # User's saved tracks
/api/spotify/me/player/recently-played/   # User's recently played tracks
/api/spotify/me/top/tracks/               # User's top tracks
/api/spotify/me/top/artists/              # User's top artists
/api/spotify/tracks/{track_id}/           # Track details
/api/spotify/playlists/{playlist_id}/tracks/  # Playlist tracks
```

**Note**: All `/me/*` endpoints require the `/api/spotify/` prefix.

## Rate Limits

### LastFM
- **Uncached requests**: 60 per minute per IP
- **Cached requests**: Unlimited

### Spotify
- **Uncached requests**: 100 per minute per IP
- **Cached requests**: Unlimited

## Cache Duration

- **LastFM**: 6 hours
- **Spotify**: 
  - User-specific data: 15 minutes
  - Search results: 2 hours
  - Track/artist details: 6 hours

## Example Usage

### GET Request (with auth header)
```bash
curl -H "Authorization: Bearer YOUR_SPOTIFY_TOKEN" \
  "http://localhost:8000/api/spotify/me/top/tracks/?time_range=short_term&limit=10"
```

### POST Request (with token in body)
```bash
curl -X POST http://localhost:8000/api/spotify/me/top/tracks/ \
  -H "Content-Type: application/json" \
  -d '{
    "access_token": "YOUR_SPOTIFY_TOKEN",
    "time_range": "short_term",
    "limit": 10
  }'
```

### LastFM Request
```bash
curl "http://localhost:8000/api/lastfm/?method=artist.getSimilar&artist=Beatles&limit=10"
```

## Testing Cache Behavior

To verify caching is working:

1. Make the same request twice
2. First request may be slower (API call)
3. Second request should be instant (from cache)
4. Check the response headers or logs to confirm cache hit

## Monitoring

You can monitor cache performance at:
- `/api/spotify/admin/stats/` - Spotify cache statistics
- `/api/lastfm/admin/stats/` - LastFM cache statistics

These endpoints show:
- Total requests
- Cache hit rate
- Number of cached entries
- API calls made 