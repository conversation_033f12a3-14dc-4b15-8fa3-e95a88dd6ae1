# Collected Pins Endpoint

## Overview

The collected pins endpoint allows users to retrieve all pins they have collected (interacted with using the 'collect' action) with full pagination support and flexible sorting options.

## Endpoint

**URL:** `GET /api/pins/collected/`

**Authentication:** Required (JWT token)

## Query Parameters

### Pagination
- `page`: Page number (default: 1)
- `page_size`: Number of items per page (default: 20, max: 100)

### Sorting
- `ordering`: Sort order for the results (default: `collected_recent`)

**Available ordering options:**
- `collected_recent`: Most recently collected first (default)
- `collected_oldest`: Oldest collected first
- `pin_recent`: Pin creation date, most recent first
- `pin_oldest`: Pin creation date, oldest first
- `most_liked`: Most liked pins first
- `most_comments`: Most commented pins first
- `most_views`: Most viewed pins first
- `most_shares`: Most shared pins first
- `least_liked`: Least liked pins first

## Response Format

### Paginated Response
```json
{
  "count": 25,
  "next": "http://api.example.com/api/pins/collected/?page=2",
  "previous": null,
  "results": [
    {
      "id": 123,
      "title": "Amazing Track",
      "track_title": "Song Title",
      "track_artist": "Artist Name",
      "track_url": "https://open.spotify.com/track/...",
      "service": "spotify",
      "location": {
        "type": "Point",
        "coordinates": [-74.0060, 40.7128]
      },
      "location_name": "New York, NY",
      "owner": {
        "id": 456,
        "username": "musiclover",
        "profile_pic": "https://example.com/profile.jpg"
      },
      "created_at": "2023-04-06T12:34:56Z",
      "collected_at": "2023-04-07T15:30:00Z",
      "engagement_counts": {
        "likes": 15,
        "dislikes": 2,
        "comments": 8,
        "views": 120,
        "collects": 25,
        "shares": 5,
        "total_engagement": 175
      }
    }
  ],
  "sorting": {
    "current_ordering": "collected_recent",
    "available_options": [
      "collected_recent", "collected_oldest", "pin_recent", "pin_oldest",
      "most_liked", "most_comments", "most_views", "most_shares", "least_liked"
    ],
    "applied_sort": "python:collected_recent"
  }
}
```

### Non-paginated Response
```json
{
  "results": [...],
  "sorting": {
    "current_ordering": "collected_recent",
    "available_options": [...],
    "applied_sort": "python:collected_recent"
  }
}
```

## Features

### 1. Collection Timestamp
Each pin in the response includes a `collected_at` field showing when the user collected that specific pin.

### 2. Engagement Counts
Each pin includes comprehensive engagement metrics:
- `likes`: Number of upvotes (from voting system)
- `dislikes`: Number of downvotes (from voting system)
- `comments`: Number of comments
- `views`: Number of view interactions
- `collects`: Number of collect interactions
- `shares`: Number of share interactions
- `total_engagement`: Sum of all engagement metrics

### 3. Flexible Sorting
- **Collection-based sorting**: Sort by when you collected the pins
- **Pin-based sorting**: Sort by when the pins were created
- **Engagement-based sorting**: Sort by various engagement metrics

### 4. Pagination
Full pagination support with customizable page sizes and navigation links.

### 5. Performance Optimized
- Uses database annotations for engagement counts
- Efficient queries with proper indexing
- Minimal database hits per request

## Example Usage

### Get recently collected pins
```bash
curl -H "Authorization: Bearer <token>" \
     "https://api.example.com/api/pins/collected/?ordering=collected_recent"
```

### Get most liked collected pins with pagination
```bash
curl -H "Authorization: Bearer <token>" \
     "https://api.example.com/api/pins/collected/?ordering=most_liked&page=1&page_size=10"
```

### Get oldest collected pins
```bash
curl -H "Authorization: Bearer <token>" \
     "https://api.example.com/api/pins/collected/?ordering=collected_oldest"
```

## Error Responses

### 401 Unauthorized
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### 500 Internal Server Error
```json
{
  "error": "Error message describing the issue"
}
```

## Notes

- Only returns pins that the user has actually collected (have a 'collect' interaction)
- Excludes expired pins automatically
- Respects pin visibility (private pins only shown if user owns them)
- Collection timestamps are based on when the user first collected each pin
- Engagement counts are real-time and include all interaction types 