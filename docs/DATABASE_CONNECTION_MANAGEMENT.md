# Database Connection Management

## Problem: "Too Many Clients Already" Error

This error occurs when PostgreSQL reaches its maximum connection limit. Common causes:
- Django keeping connections open too long (`conn_max_age` setting)
- Multiple Django processes accumulating connections
- Stale connections not being closed properly

## Solution

### 1. Updated Database Settings

In `bopmaps/settings.py`, we've configured different connection behaviors for development and production:

```python
DATABASES = {
    'default': dj_database_url.config(
        default=config('DATABASE_URL', default='postgis://postgres:password@localhost:5432/bopmaps'),
        conn_max_age=0,  # Close connections immediately in development
        conn_health_checks=True,
    )
}

# Development: Close connections immediately
if not DEBUG:
    DATABASES['default']['conn_max_age'] = 600  # Production: Keep alive for 10 minutes
else:
    DATABASES['default']['conn_max_age'] = 0    # Development: Close immediately
```

### 2. Why This Works

- **Development (`conn_max_age=0`)**: Connections are closed after each request, preventing accumulation
- **Production (`conn_max_age=600`)**: Connections are reused for performance, with proper pooling

### 3. Additional Settings

```python
DATABASES['default']['ATOMIC_REQUESTS'] = True  # Wrap each request in a transaction
DATABASES['default']['OPTIONS'] = {
    'connect_timeout': 10,  # Timeout for new connections
}
```

## Monitoring Connections

### Check Active Connections
```sql
SELECT count(*) FROM pg_stat_activity;
```

### View Connection Details
```sql
SELECT pid, usename, application_name, client_addr, state 
FROM pg_stat_activity 
WHERE datname = 'bopmaps';
```

### Kill Idle Connections
```sql
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE state = 'idle' 
AND datname = 'bopmaps'
AND pid <> pg_backend_pid();
```

## Quick Fixes When Error Occurs

### 1. Kill Django Processes
```bash
pkill -f "manage.py runserver"
```

### 2. Close Connections via Django
```python
from django.db import connections
for alias in connections:
    connections[alias].close()
```

### 3. PostgreSQL Connection Limits

Default PostgreSQL `max_connections` is usually 100. To check:
```sql
SHOW max_connections;
```

## Best Practices

1. **Use Connection Pooling in Production**: Consider using pgbouncer or similar
2. **Monitor Connection Usage**: Set up alerts for high connection counts
3. **Close Connections Properly**: Ensure connections are closed in error cases
4. **Limit Worker Processes**: Don't run too many Django workers

## Environment-Specific Settings

### Development
- `conn_max_age=0` (close immediately)
- Fewer worker processes
- Regular connection cleanup

### Production
- `conn_max_age=600` (10 minutes)
- Connection pooling (pgbouncer)
- Monitoring and alerts

## Troubleshooting

If you see "too many clients already":

1. Check current connections: `SELECT count(*) FROM pg_stat_activity;`
2. Kill Django processes: `pkill -f "manage.py runserver"`
3. Restart Django with updated settings
4. Monitor connection count

The new settings should prevent this issue in development by closing connections immediately after each request. 