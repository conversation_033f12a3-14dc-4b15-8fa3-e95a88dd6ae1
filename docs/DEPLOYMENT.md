# BOPMaps Production Deployment Guide

This guide provides step-by-step instructions for deploying BOPMaps to AWS using Infrastructure as Code (Terraform) with Cloudflare CDN integration and OpenMapTiles planet data.

## Architecture Overview

The infrastructure includes:
- **ECS Fargate** for containerized Django application, Celery workers, and tile server
- **RDS PostgreSQL** with PostGIS for geospatial data
- **ElastiCache Redis** for caching and Celery message broker
- **EFS** for shared map tile storage
- **S3** for static files, media, and tile storage
- **CloudFront** for AWS-based CDN
- **Cloudflare** for global CDN and DNS management
- **OpenMapTiles** for planet-wide vector tile serving
- **Auto Scaling** for handling traffic spikes
- **CloudWatch** monitoring and alerting

## Prerequisites

### 1. Required Accounts and Services
- AWS Account with administrative access
- Cloudflare Account with domain management
- Domain name managed by <PERSON>flare
- Docker installed locally
- Terraform >= 1.0 installed
- AWS CLI configured

### 2. Required Information
- Your AWS Account ID: `************`
- Domain name (e.g., `bopmaps.com`)
- Cloudflare API Token
- Cloudflare Zone ID

### 3. Hardware Requirements for OpenMapTiles
- **Local Development**: 100GB+ free disk space
- **Production**: 100GB+ EFS storage for planet data

## Step 1: Prepare Your Environment

### 1.1 Clone and Navigate to Project
```bash
cd /Users/<USER>/dev/pythonprojects/BOPMapsBackend
```

### 1.2 Configure AWS CLI
```bash
aws configure
# Use your credentials:
# AWS Access Key ID: [Your AWS Access Key]
# AWS Secret Access Key: [Your AWS Secret Key]
# Default region name: us-east-1
# Default output format: json
```

### 1.3 Verify AWS Access
```bash
aws sts get-caller-identity
# Should return your account ID: ************
```

## Step 2: Set Up OpenMapTiles Planet Data

### 2.1 Download and Prepare OpenMapTiles Data
```bash
# Run the OpenMapTiles setup script
./scripts/setup-openmaptiles.sh

# This will:
# - Download OpenMapTiles planet data (~50GB)
# - Download map styles, fonts, and sprites
# - Set up TileServer GL configuration
# - Build and test local tile server
```

**Note**: The planet download will take several hours depending on your internet connection.

### 2.2 Test Local OpenMapTiles Setup
```bash
# Start the local tile server
cd docker/openmaptiles
docker-compose -f docker-compose.openmaptiles.yml --profile serve up -d

# Test the setup
cd ../..
./scripts/test-openmaptiles.sh

# Verify tiles are serving correctly
curl http://localhost:8080/data/planet/0/0/0.pbf
curl http://localhost:8080/styles/osm-bright.json
```

## Step 3: Configure Terraform Variables

### 3.1 Copy the Example Variables File
```bash
cd terraform
cp terraform.tfvars.example terraform.tfvars
```

### 3.2 Edit terraform.tfvars
```bash
# Edit the file with your actual values
nano terraform.tfvars
```

Required values to update:
```hcl
# Domain Configuration
domain_name       = "api.your-domain.com"
tiles_domain_name = "tiles.your-domain.com"

# Cloudflare Configuration
cloudflare_api_token  = "your-cloudflare-api-token"
cloudflare_zone_id    = "your-cloudflare-zone-id"
cloudflare_account_id = "your-cloudflare-account-id"

# Application Configuration
django_secret_key = "your-super-secret-django-key-here"

# Database Configuration
db_password = "your-secure-database-password"

# Increase resources for tile server
tileserver_cpu    = 1024  # Increase for better performance
tileserver_memory = 2048  # Increase for large datasets
tileserver_count  = 2     # Scale based on expected load
```

### 3.3 Generate Django Secret Key
```python
# Run this in Python to generate a secret key
import secrets
print(secrets.token_urlsafe(50))
```

## Step 4: Initialize Terraform

### 4.1 Initialize Terraform
```bash
terraform init
```

### 4.2 Create S3 Backend for State (Optional but Recommended)
```bash
# Create S3 bucket for Terraform state
aws s3 mb s3://bopmaps-terraform-state-************ --region us-east-1

# Create backend.tf file
cat > backend.tf << EOF
terraform {
  backend "s3" {
    bucket = "bopmaps-terraform-state-************"
    key    = "bopmaps/terraform.tfstate"
    region = "us-east-1"
    encrypt = true
  }
}
EOF

# Re-initialize with backend
terraform init
```

## Step 5: Plan and Deploy Infrastructure

### 5.1 Validate Configuration
```bash
terraform validate
```

### 5.2 Plan Deployment
```bash
terraform plan
```

Review the plan carefully. You should see resources for:
- VPC with subnets across 2 AZs
- RDS PostgreSQL instance
- ECS cluster and services (Django, Celery, TileServer)
- Load balancer and target groups
- S3 buckets and CloudFront distributions
- ElastiCache Redis cluster
- EFS file system with multiple access points
- IAM roles and security groups

### 5.3 Deploy Infrastructure
```bash
terraform apply
```

Type `yes` when prompted. Deployment typically takes 15-20 minutes.

### 5.4 Save Outputs
```bash
terraform output > deployment-outputs.txt
```

## Step 6: Deploy OpenMapTiles to AWS

### 6.1 Deploy Planet Data to EFS
```bash
# Navigate back to project root
cd ..

# Deploy OpenMapTiles data to AWS EFS and ECS
./scripts/deploy-openmaptiles-to-aws.sh

# This will:
# - Create temporary EC2 instance for data upload
# - Upload planet.mbtiles (~50GB) to EFS
# - Upload styles, fonts, and sprites to EFS
# - Build and push TileServer Docker image to ECR
# - Deploy to ECS with auto-scaling
# - Test the deployment
```

**Note**: The data upload will take several hours for the 50GB planet file.

## Step 7: Build and Deploy Django Application

### 7.1 Get ECR Login Command
```bash
# Get the ECR repository URL from Terraform output
ECR_REPO=$(cd terraform && terraform output -raw ecr_repository_url)
echo $ECR_REPO

# Login to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin $ECR_REPO
```

### 7.2 Build and Push Docker Image
```bash
# Build production image
docker build -f Dockerfile.prod -t $ECR_REPO:latest .

# Push to ECR
docker push $ECR_REPO:latest
```

### 7.3 Deploy to ECS
```bash
# Update ECS service to use new image
aws ecs update-service \
  --cluster bopmaps-prod \
  --service bopmaps-prod-app \
  --force-new-deployment \
  --region us-east-1
```

## Step 8: Configure DNS and SSL

### 8.1 Verify DNS Records
Check that Cloudflare has created the DNS records pointing to your AWS load balancer.

### 8.2 Verify SSL Certificates
AWS Certificate Manager should automatically validate SSL certificates via DNS. This may take 5-10 minutes.

### 8.3 Test Application and Tiles
```bash
# Test the Django application
curl https://api.your-domain.com/health/

# Test the tile server
curl https://tiles.your-domain.com/data/planet/0/0/0.pbf

# Test map styles
curl https://tiles.your-domain.com/styles/osm-bright.json

# Run comprehensive tests
TILES_URL=https://tiles.your-domain.com \
API_URL=https://api.your-domain.com \
./scripts/test-openmaptiles.sh
```

## Step 9: Performance Monitoring and Optimization

### 9.1 CloudWatch Monitoring
- Navigate to CloudWatch dashboard (URL in Terraform outputs)
- Monitor ECS service metrics
- Monitor tile server performance
- Monitor EFS throughput

### 9.2 Tile Server Optimization
```bash
# Monitor tile server logs
aws logs tail /ecs/bopmaps-prod/tileserver --follow

# Check tile serving performance
curl -w "@curl-format.txt" -o /dev/null https://tiles.your-domain.com/data/planet/10/512/341.pbf
```

### 9.3 Cloudflare Cache Configuration
- Verify aggressive caching for tiles (30 days)
- Monitor cache hit rates
- Optimize cache rules based on usage patterns

## Step 10: Post-Deployment Configuration

### 10.1 Database Setup
```bash
# Connect to ECS task to run management commands
aws ecs execute-command \
  --cluster bopmaps-prod \
  --task $(aws ecs list-tasks --cluster bopmaps-prod --service bopmaps-prod-app --query 'taskArns[0]' --output text) \
  --container app \
  --interactive \
  --command "/bin/bash"

# Inside the container:
python manage.py migrate
python manage.py create_default_skin
python manage.py collectstatic --noinput
```

### 10.2 Set Up SNS Notifications
```bash
# Subscribe to alerts (replace with your email)
aws sns subscribe \
  --topic-arn $(cd terraform && terraform output -raw sns_alerts_topic_arn) \
  --protocol email \
  --notification-endpoint <EMAIL>
```

## Step 11: Testing and Validation

### 11.1 Comprehensive Testing
```bash
# Run full test suite against production
TILES_URL=https://tiles.your-domain.com \
API_URL=https://api.your-domain.com \
./scripts/test-openmaptiles.sh

# Test tile performance from different locations
# Test map rendering in browser
# Test API endpoints
```

### 11.2 Performance Benchmarks
- **Tile serving**: < 100ms for cached tiles
- **API response**: < 500ms for standard requests
- **Map loading**: < 3s for initial view
- **Memory usage**: < 80% on ECS tasks

## OpenMapTiles Architecture Details

### Vector Tile Structure
- **Format**: Mapbox Vector Tiles (MVT) in Protocol Buffers
- **Zoom levels**: 0-14 for planet data
- **Layers**: Contains all OpenStreetMap data layers
- **Compression**: Optimized for size and performance

### Caching Strategy
```
Browser Cache: 1 day
Cloudflare Edge: 30 days (low zoom), 1 day (high zoom)
EFS Storage: Persistent
Redis Cache: 30 days for metadata
```

### Tile URL Structure
```
Vector Tiles: https://tiles.your-domain.com/data/planet/{z}/{x}/{y}.pbf
Raster Tiles: https://tiles.your-domain.com/styles/{style}/{z}/{x}/{y}.png
Styles: https://tiles.your-domain.com/styles/{style}.json
```

## Troubleshooting OpenMapTiles

### Common Issues

#### 1. Tile Server Not Starting
```bash
# Check ECS service events
aws ecs describe-services \
  --cluster bopmaps-prod \
  --services bopmaps-prod-tileserver

# Check logs
aws logs tail /ecs/bopmaps-prod/tileserver --follow

# Common causes:
# - Missing planet.mbtiles file in EFS
# - Insufficient memory allocation
# - EFS mount issues
```

#### 2. Slow Tile Performance
```bash
# Check EFS throughput
aws efs describe-file-systems --file-system-id $(cd terraform && terraform output -raw efs_id)

# Monitor CloudWatch metrics for EFS
# Consider increasing provisioned throughput
# Check if auto-scaling is working properly
```

#### 3. High Memory Usage
```bash
# Monitor container memory
aws ecs describe-tasks --cluster bopmaps-prod --tasks TASK_ID

# Increase tileserver_memory in terraform.tfvars
# Consider using larger instance types for ECS
```

### Performance Optimization

#### 1. EFS Optimization
```hcl
# In terraform.tfvars
efs_throughput_mode = "provisioned"
efs_provisioned_throughput = 500  # Increase for better performance
```

#### 2. Tile Server Scaling
```hcl
# In terraform.tfvars
tileserver_count = 3          # Scale based on load
tileserver_cpu = 2048         # Increase for better performance
tileserver_memory = 4096      # Increase for large datasets
```

#### 3. Cloudflare Workers
The included Cloudflare Worker optimizes tile delivery:
- Intelligent caching based on zoom levels
- Automatic format conversion
- Request optimization

## Cost Management

### Updated Monthly Costs with OpenMapTiles
- **RDS (db.t4g.medium)**: ~$50-100
- **ECS Fargate** (6 tasks): ~$40-80
- **Application Load Balancer**: ~$16
- **NAT Gateways**: ~$90 (2 gateways)
- **EFS** (100GB+ planet data): ~$30-50
- **ElastiCache**: ~$12
- **S3 & CloudFront**: ~$10-30
- **Data Transfer**: ~$20-100

**Total Estimated**: $260-450/month

### Cost Optimization for Tiles
1. Use CloudFront and Cloudflare for aggressive caching
2. Monitor EFS usage and optimize throughput settings
3. Scale tile servers based on actual usage
4. Use spot instances for development environments

## Security Considerations for OpenMapTiles

1. **Access Control**: EFS access points with proper permissions
2. **Data Encryption**: EFS encryption in transit and at rest
3. **Network Security**: Tile server in private subnets
4. **Rate Limiting**: Cloudflare rate limiting for tile endpoints
5. **Monitoring**: CloudWatch alerts for unusual access patterns

Remember to never commit secrets or credentials to version control! 