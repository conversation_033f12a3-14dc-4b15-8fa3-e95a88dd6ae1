# Email-First Authentication Implementation

This document describes the implementation of the email-first authentication endpoints for BOPMaps.

## Overview

Three new authentication endpoints have been implemented to support the email-first authentication flow:

1. **Check Email**: `POST /api/auth/check-email/`
2. **Login**: `POST /api/auth/login/`
3. **Register**: `POST /api/auth/register/`

## Implementation Details

### 1. Check Email Endpoint

**URL**: `POST /api/auth/check-email/`

**Purpose**: Check if an email address already has an account

**Rate Limiting**: 10 requests per minute per IP

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

**Response Format**:
```json
// User exists
{
  "exists": true,
  "user": {
    "id": "user_id",
    "username": "johndoe",
    "email": "<EMAIL>",
    "profile_pic": "https://example.com/profile.jpg",
    "bio": "User bio",
    "is_verified": false,
    "favorite_genres": [],
    "created_at": "2024-01-01T00:00:00Z"
  }
}

// User doesn't exist
{
  "exists": false,
  "user": null
}
```

### 2. Login Endpoint

**URL**: `POST /api/auth/login/`

**Purpose**: Authenticate existing user with email and password

**Rate Limiting**: 10 requests per minute per IP

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123"
}
```

**Success Response (200)**:
```json
{
  "auth_token": "jwt_access_token_here",
  "refresh_token": "jwt_refresh_token_here",
  "user": {
    "id": "user_id",
    "username": "johndoe",
    "email": "<EMAIL>",
    "profile_pic": "https://example.com/profile.jpg",
    "bio": "Music lover",
    "is_verified": false,
    "favorite_genres": [],
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

**Error Response (401)**:
```json
{
  "error": "Invalid credentials",
  "message": "The password you entered is incorrect"
}
```

### 3. Register Endpoint

**URL**: `POST /api/auth/register/`

**Purpose**: Create a new user account

**Rate Limiting**: 10 requests per minute per IP

**Request Body**:
```json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "SecurePassword123"
}
```

**Success Response (201)**:
```json
{
  "auth_token": "jwt_access_token_here",
  "refresh_token": "jwt_refresh_token_here",
  "user": {
    "id": "new_user_id",
    "username": "johndoe",
    "email": "<EMAIL>",
    "profile_pic": null,
    "bio": "",
    "is_verified": false,
    "favorite_genres": [],
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

**Error Response (400)**:
```json
{
  "error": "Validation error",
  "errors": {
    "username": ["This username is already taken"],
    "email": ["An account with this email already exists"]
  }
}
```

## Validation Rules

### Password Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- Additional Django built-in password validation

### Username Requirements
- 2-30 characters
- Only letters, numbers, underscore (_), and hyphen (-)
- Must be unique (case insensitive)

### Email Requirements
- Valid email format
- Must be unique (case insensitive)
- Automatically converted to lowercase

## Security Features

### Rate Limiting
- Authentication endpoints are limited to **10 requests per minute per IP**
- Uses Django REST Framework's built-in throttling system
- Stricter than general API rate limits (100/hour for anonymous users)

### Password Security
- Passwords are hashed using Django's built-in password hashing (PBKDF2 with SHA256)
- Custom validation ensures strong passwords
- Django's built-in password validators are also applied

### JWT Token Security
- Access tokens expire after 1 hour (configurable)
- Refresh tokens expire after 7 days (configurable)
- Refresh token rotation enabled
- Blacklisting after rotation enabled

### Other Security Measures
- Email normalization (converted to lowercase)
- HTTPS required in production
- CORS configuration for allowed origins
- CSRF protection where applicable

## Files Modified/Created

### New Files
- `EMAIL_AUTH_IMPLEMENTATION.md` - This documentation

### Modified Files
- `users/serializers.py` - Added new serializers for auth endpoints
- `users/views.py` - Added new view classes for auth endpoints
- `bopmaps/urls.py` - Added URL patterns for new endpoints
- `bopmaps/settings.py` - Added auth rate limiting configuration

## Issue Resolution

**Fixed**: Initial implementation had a serializer configuration error where the `UserAuthSerializer` tried to access non-existent model fields (`is_verified` and `favorite_genres`). 

**Solution**: Modified the serializer to use `SerializerMethodField` for these fields:
- `is_verified` now maps to the existing `school_email_verified` field
- `favorite_genres` returns an empty array (ready for future implementation)
- `created_at` now correctly maps to Django's built-in `date_joined` field

This ensures backward compatibility with your API specification while using only fields that exist in the User model.

## Database Considerations

The endpoints use the existing `User` model which extends Django's `AbstractUser`. No database migrations are required.

## Testing

A test script (`test_auth_endpoints.py`) has been created to demonstrate the functionality. Run it with:

```bash
python test_auth_endpoints.py
```

Make sure the Django development server is running first:

```bash
python manage.py runserver
```

## Error Handling

All endpoints include comprehensive error handling:
- Input validation errors
- Database constraint violations
- Authentication failures
- Rate limiting violations
- Internal server errors

Errors are returned in consistent JSON format with appropriate HTTP status codes.

## Backward Compatibility

The new endpoints are in addition to existing authentication endpoints. The original JWT token endpoints remain functional:
- `POST /api/auth/token/` - Original login endpoint
- `POST /api/auth/token/verify/` - Token verification
- `POST /api/auth/logout/` - Logout endpoint

## Usage Examples

### Frontend Integration Example

```javascript
// Check if email exists
async function checkEmail(email) {
  const response = await fetch('/api/auth/check-email/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email })
  });
  return response.json();
}

// Register new user
async function register(username, email, password) {
  const response = await fetch('/api/auth/register/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, email, password })
  });
  return response.json();
}

// Login existing user
async function login(email, password) {
  const response = await fetch('/api/auth/login/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password })
  });
  return response.json();
}
```

## Next Steps

1. Test the endpoints with your frontend application
2. Adjust rate limiting if needed based on usage patterns
3. Consider implementing additional security features like:
   - Email verification for new accounts
   - Two-factor authentication
   - Account lockout after multiple failed attempts
   - Password reset functionality (already exists)

## Support

If you encounter any issues or need modifications to the endpoints, please refer to the implementation in the modified files listed above. 