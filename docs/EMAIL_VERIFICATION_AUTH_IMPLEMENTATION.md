# Email Verification Authentication System

This document describes the complete implementation of the email verification-based authentication system for BOPMaps, which replaces password-based authentication with a passwordless email verification flow.

## 🎯 Overview

The new authentication system uses email verification codes instead of passwords, providing a more secure and user-friendly experience. Users authenticate by receiving a 6-digit code via email, which expires in 10 minutes.

## 📋 Endpoints

### 1. Check Email (Kept from Previous Implementation)
**URL**: `POST /api/auth/check-email/`
**Purpose**: Check if an email address already has an account
**Rate Limit**: 10 requests/minute

### 2. Send Verification Code
**URL**: `POST /api/auth/send-verification-code/`
**Purpose**: Send a 6-digit verification code to the email address
**Rate Limit**: 10 requests/minute

**Request**:
```json
{
  "email": "<EMAIL>"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Verification code <NAME_EMAIL>",
  "expires_in": 600
}
```

### 3. Verify Email Code
**URL**: `POST /api/auth/verify-email-code/`
**Purpose**: Verify the 6-digit code received via email
**Rate Limit**: 10 requests/minute

**Request**:
```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Email verified successfully"
}
```

### 4. Login (Updated for Email Verification)
**URL**: `POST /api/auth/login/`
**Purpose**: Login existing user after email verification
**Rate Limit**: 10 requests/minute

**Request**:
```json
{
  "email": "<EMAIL>",
  "email_verified": true
}
```

**Response**:
```json
{
  "auth_token": "jwt_access_token",
  "refresh_token": "jwt_refresh_token",
  "user": {
    "id": "user_id",
    "username": "johndoe",
    "email": "<EMAIL>",
    "profile_pic": "https://example.com/profile.jpg",
    "bio": "User bio",
    "is_verified": false,
    "favorite_genres": [],
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 5. Register (Updated for Email Verification)
**URL**: `POST /api/auth/register/`
**Purpose**: Register new user after email verification
**Rate Limit**: 10 requests/minute

**Request**:
```json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "email_verified": true
}
```

**Response**: Same as login response with 201 status code.

## 🔄 Authentication Flows

### New User Registration Flow
1. **Check Email**: `POST /api/auth/check-email/` → `exists: false`
2. **Send Code**: `POST /api/auth/send-verification-code/` → User receives email
3. **Verify Code**: `POST /api/auth/verify-email-code/` → Code validated
4. **Register**: `POST /api/auth/register/` → Account created, tokens returned

### Existing User Login Flow
1. **Check Email**: `POST /api/auth/check-email/` → `exists: true`
2. **Send Code**: `POST /api/auth/send-verification-code/` → User receives email
3. **Verify Code**: `POST /api/auth/verify-email-code/` → Code validated
4. **Login**: `POST /api/auth/login/` → Tokens returned

## 🗄️ Database Schema

### EmailVerificationCode Model
```sql
CREATE TABLE email_verification_codes (
    id BIGINT PRIMARY KEY,
    email VARCHAR(254) NOT NULL,
    code VARCHAR(6) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_used BOOLEAN NOT NULL DEFAULT false,
    attempts INTEGER NOT NULL DEFAULT 0
);

-- Indexes
CREATE INDEX email_verification_codes_email_is_used_idx ON email_verification_codes(email, is_used);
CREATE INDEX email_verification_codes_expires_at_idx ON email_verification_codes(expires_at);
```

## 🔒 Security Features

### Code Generation & Expiry
- **6-digit numeric codes** for easy user input
- **10-minute expiration** for security
- **Single-use codes** - marked as used after verification
- **Automatic invalidation** of old codes when new ones are generated

### Rate Limiting & Abuse Prevention
- **10 requests/minute** rate limiting on all auth endpoints
- **5 attempt limit** per verification code
- **Recent verification required** - login/register must happen within 1 hour of verification

### Security Measures
- **Passwordless authentication** - no password storage or management
- **Email normalization** - converted to lowercase
- **JWT token security** - access tokens (1hr), refresh tokens (7 days)
- **Database indexes** for efficient lookups
- **Email validation** - proper format checking

## 📧 Email Configuration

### Development Setup (Console Backend)
```python
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
DEFAULT_FROM_EMAIL = 'BOPMaps <<EMAIL>>'
```

### Production Setup (SMTP)
```python
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = config('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD')
DEFAULT_FROM_EMAIL = 'BOPMaps <<EMAIL>>'
```

### Email Template
The system sends beautiful HTML emails with:
- **Branded styling** with BOPMaps colors
- **Large, readable verification codes**
- **Clear expiration information**
- **Professional layout**

## 🧪 Testing Results

✅ **All features verified and working**:
- Email existence checking
- Verification code generation and sending
- Code validation and expiry handling
- Passwordless registration
- Passwordless login
- Rate limiting and security measures
- Error handling and edge cases

Sample verification email output:
```
Subject: BOPMaps - Verify Your Email

Welcome to BOPMaps!

Your verification code is: 838623

This code will expire in 10 minutes.
```

## 📁 Implementation Files

### New/Modified Files
- `users/models.py` - Added `EmailVerificationCode` model
- `users/serializers.py` - Added email verification serializers
- `users/views.py` - Added email verification views, updated login/register
- `bopmaps/urls.py` - Added new URL patterns
- `bopmaps/settings.py` - Added email configuration
- `users/migrations/0009_emailverificationcode.py` - Database migration

### Database Migration
```bash
python manage.py makemigrations users
python manage.py migrate
```

## 🚀 Deployment Considerations

### Environment Variables
```env
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=BOPMaps <<EMAIL>>
```

### Production Checklist
- [ ] Configure SMTP email backend
- [ ] Set up email service (Gmail, SendGrid, etc.)
- [ ] Update DNS records for email deliverability
- [ ] Monitor email delivery rates
- [ ] Set up email templates in production
- [ ] Configure rate limiting based on load

## 🔄 Migration from Password Authentication

### Backward Compatibility
- ✅ **Existing JWT endpoints remain functional**
- ✅ **CheckEmailView kept for compatibility**
- ✅ **User model unchanged** - no data migration needed
- ✅ **Existing users can login** with email verification

### User Migration Strategy
1. **Existing users**: Use email verification for future logins
2. **Password fields**: Set to unusable password for security
3. **Gradual rollout**: Can be deployed alongside existing auth

## 📊 Performance & Monitoring

### Database Performance
- **Indexed email lookups** for fast verification
- **Automatic cleanup** of expired codes (can be scheduled)
- **Efficient queries** with proper indexing

### Monitoring Points
- Email delivery success rates
- Verification code usage patterns
- Failed verification attempts
- Rate limiting triggers

## 🎨 Frontend Integration Example

```javascript
// Complete authentication flow
class AuthService {
  async checkEmail(email) {
    return fetch('/api/auth/check-email/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email })
    }).then(r => r.json());
  }

  async sendVerificationCode(email) {
    return fetch('/api/auth/send-verification-code/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email })
    }).then(r => r.json());
  }

  async verifyCode(email, code) {
    return fetch('/api/auth/verify-email-code/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, code })
    }).then(r => r.json());
  }

  async login(email) {
    return fetch('/api/auth/login/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, email_verified: true })
    }).then(r => r.json());
  }

  async register(username, email) {
    return fetch('/api/auth/register/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, email, email_verified: true })
    }).then(r => r.json());
  }
}
```

## ✨ Benefits of Email Verification Auth

### User Experience
- 🚫 **No password to remember** or manage
- 📱 **Mobile-friendly** 6-digit codes
- ⚡ **Faster onboarding** - no password complexity requirements
- 🔄 **No password resets** needed

### Security
- 🛡️ **Eliminates password attacks** (brute force, dictionary, etc.)
- 📧 **Email-based verification** ensures email ownership
- ⏰ **Time-limited codes** reduce attack window
- 🔒 **Rate limiting** prevents abuse

### Maintenance
- 🧹 **No password storage** or hashing complexity
- 📊 **Simpler user management** - no password policies
- 🔧 **Fewer security concerns** around password storage
- 📈 **Better analytics** on authentication patterns

## 🎉 Conclusion

The email verification authentication system is now fully implemented and tested. It provides a modern, secure, and user-friendly alternative to password-based authentication while maintaining backward compatibility with existing systems.

The system is production-ready with proper error handling, rate limiting, security measures, and comprehensive documentation for frontend integration. 