# Environment Variables Deployment Guide

## Overview

This deployment updates your ECS infrastructure to include **all** environment variables that your Django application expects. Previously, your ECS containers only had basic configuration variables, which is why your Spotify API (and other third-party services) weren't working in production.

## What Was Missing

Your Django settings file (`bopmaps/settings.py`) was expecting these environment variables, but they weren't being passed to the ECS containers:

### Critical Missing Variables
- `SPOTIFY_CLIENT_ID` - Required for Spotify API access
- `SPOTIFY_CLIENT_SECRET` - Required for Spotify API access
- `LASTFM_API_KEY` - Required for Last.fm integration
- `ONESIGNAL_APP_ID` & `ONESIGNAL_API_KEY` - For push notifications

### Other Missing Variables
- Apple Music API credentials
- SoundCloud API credentials
- Email configuration (SMTP settings)
- JWT token lifetimes
- CORS/CSRF origins
- Security settings

## What We've Done

### 1. Updated Terraform Configuration

**File: `terraform/variables.tf`**
- Added 20+ new environment variable definitions
- All sensitive variables marked as `sensitive = true`
- Proper defaults and descriptions

**File: `terraform/ecs.tf`**
- Updated Django app task definition with all environment variables
- Updated Celery worker task definition with all environment variables  
- Updated planet processor task definition with storage variables

**File: `terraform/terraform.tfvars.example`**
- Added examples for all new variables
- Clear documentation on which are required vs optional
- Removed obsolete tileserver variables

### 2. Created Deployment Script

**File: `deploy_env_vars.sh`**
- Automated deployment script
- Validates that critical variables are set
- Safe deployment with confirmation prompts
- Post-deployment testing instructions

## How to Deploy

### Step 1: Update Your Configuration

```bash
# Copy the example file if you don't have terraform.tfvars yet
cp terraform/terraform.tfvars.example terraform/terraform.tfvars

# Edit with your actual values
nano terraform/terraform.tfvars
```

### Step 2: Set Your Spotify Credentials

**Required for your issue to be fixed:**

```bash
# In terraform/terraform.tfvars, set:
spotify_client_id     = "your-actual-spotify-client-id"
spotify_client_secret = "your-actual-spotify-secret"
```

Get these from: https://developer.spotify.com/dashboard/applications

### Step 3: Deploy

```bash
# Run the deployment script
./deploy_env_vars.sh
```

### Step 4: Monitor Deployment

```bash
# Watch the ECS service update
aws ecs describe-services --cluster bopmaps-prod --services bopmaps-prod-app

# Monitor logs during deployment
aws logs tail /ecs/bopmaps-prod/app --follow
```

### Step 5: Test

```bash
# Test your Spotify API endpoint that was failing
curl -X GET 'https://api.bopmaps.com/api/spotify/playlists/5y8q5qGPQE0n3awBtifloc/tracks/?limit=50&offset=0'
```

## Expected Results

### Before Deployment
```
WARNING 2025-07-02 18:53:10,772 services 37 Client credentials fallback failed: 400
```

### After Deployment
```
INFO 2025-07-02 18:53:10,604 Requesting new client credentials token from Spotify
INFO 2025-07-02 18:53:10,698 Successfully obtained client credentials token (expires in 3600s)
```

## Environment Variables Reference

### Required (for Spotify to work)
```bash
spotify_client_id     = "your-spotify-client-id"
spotify_client_secret = "your-spotify-client-secret"
```

### Optional but Recommended
```bash
lastfm_api_key       = "your-lastfm-api-key"
onesignal_app_id     = "your-onesignal-app-id"
onesignal_api_key    = "your-onesignal-api-key"
```

### Production Configuration
```bash
cors_allowed_origins = "https://bopmaps.com,https://app.bopmaps.com"
csrf_trusted_origins = "https://bopmaps.com,https://app.bopmaps.com"
secure_ssl_redirect  = true
```

### Email Configuration (for production email)
```bash
email_backend        = "django.core.mail.backends.smtp.EmailBackend"
email_host          = "smtp.gmail.com"
email_port          = 587
email_host_user     = "<EMAIL>"
email_host_password = "your-app-password"
email_use_tls       = true
```

## Rollback Plan

If something goes wrong:

```bash
cd terraform
terraform plan -destroy -target=aws_ecs_task_definition.app
# Review the plan, then apply if needed
```

Or revert to a previous task definition revision in the AWS Console.

## Verification Commands

### Check Environment Variables in Running Container
```bash
# Get into a running container
aws ecs execute-command --cluster bopmaps-prod --task <task-id> --container app --interactive --command "/bin/bash"

# Inside container, check variables
printenv | grep SPOTIFY_
printenv | grep LASTFM_
```

### Check Logs for Success Messages
```bash
# Look for these success messages in logs
aws logs filter-log-events --log-group-name /ecs/bopmaps-prod/app --filter-pattern "Successfully obtained client credentials token"
```

## Troubleshooting

### Issue: Spotify API still returns 400
- Check that your Spotify app has the correct redirect URIs configured
- Ensure the client ID and secret are correct
- Verify they're not empty strings in the task definition

### Issue: Container won't start
- Check CloudWatch logs: `/ecs/bopmaps-prod/app`
- Common issue: invalid environment variable format
- Rollback to previous task definition if needed

### Issue: Variables not being loaded
- Verify in AWS ECS Console: Task Definition → Environment tab
- Check that strings are properly quoted in terraform.tfvars
- Ensure no special characters are breaking the Terraform syntax

## Security Notes

- All sensitive variables are marked as `sensitive = true` in Terraform
- They won't appear in Terraform plan/apply output
- Store your `terraform.tfvars` securely and don't commit it to git
- Consider using AWS Secrets Manager for even better security (future enhancement)

## Future Improvements

1. **AWS Secrets Manager Integration**: Move sensitive variables to Secrets Manager
2. **Environment-Specific Variables**: Different values for staging vs production
3. **Variable Validation**: Add validation rules for API keys format
4. **Automated Testing**: Add health checks for third-party API connections

---

**This deployment will fix your Spotify API client credentials issue and enable all other third-party integrations in your production environment.** 