# Flutter Playlist Debug Guide

## Issue Summary
- ✅ Backend endpoints working: `GET /api/spotify/playlists/{id}/tracks/` returns 200
- ❌ Flutter app gets 404 errors when calling the same endpoints  
- ✅ All tested playlist IDs work via curl/backend

## Debug Steps for Flutter

### 1. Verify Base URL Configuration

Check your Flutter app's API base URL configuration:

```dart
// Look for something like this in your Flutter code:
const String baseApiUrl = 'YOUR_BACKEND_URL';

// Make sure it matches your backend server URL
// Should be something like: 'http://localhost:8000' or 'https://your-domain.com'
```

### 2. Add Network Debugging

Add logging to see exactly what Flutter is requesting:

```dart
import 'package:dio/dio.dart';

class ApiClient {
  final Dio _dio;
  
  ApiClient() : _dio = Dio() {
    // Add interceptor to log all requests
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      requestHeader: true,
      responseHeader: true,
      logPrint: (object) => print('🌐 [Network] $object'),
    ));
  }
}
```

### 3. Test Direct Backend Connection

Add this test function to your Flutter app:

```dart
Future<void> testBackendConnection() async {
  try {
    final response = await http.get(
      Uri.parse('${AppConstants.baseApiUrl}/api/spotify/playlists/50Dlif22wDplVdWiXp6tPt/tracks/?limit=5'),
      headers: {
        'Content-Type': 'application/json',
        // Add any auth headers if needed
      },
    );
    
    print('🧪 Test Response Status: ${response.statusCode}');
    print('🧪 Test Response Body: ${response.body}');
    print('🧪 Test Request URL: ${response.request?.url}');
    
  } catch (e) {
    print('🧪 Test Error: $e');
  }
}
```

### 4. Check Headers and Authentication

Ensure you're sending the right headers:

```dart
Map<String, String> get headers => {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  // Add Authorization header if your app uses authentication:
  // 'Authorization': 'Bearer $userToken',
};
```

### 5. Verify SSL/HTTPS Configuration

If using HTTPS, check for SSL certificate issues:

```dart
// For development only - DO NOT use in production
import 'dart:io';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  }
}

// In main():
// HttpOverrides.global = MyHttpOverrides(); // Development only!
```

### 6. Test with Known Working Endpoints

First test a simple endpoint that you know works:

```dart
// Test a simple endpoint first
final response = await http.get(
  Uri.parse('${AppConstants.baseApiUrl}/api/spotify/search/?q=test&type=track&limit=5'),
);
```

## Quick Test Commands

You can verify your backend is accessible from your device/emulator:

```bash
# From your development machine (replace with your actual backend URL)
curl -X GET "http://localhost:8000/api/spotify/playlists/50Dlif22wDplVdWiXp6tPt/tracks/?limit=5"

# If using an Android emulator, test from the emulator's perspective:
curl -X GET "http://********:8000/api/spotify/playlists/50Dlif22wDplVdWiXp6tPt/tracks/?limit=5"
```

## Common Issues and Solutions

### Issue 1: Wrong Base URL
- **Problem**: Flutter calling wrong server
- **Solution**: Check `AppConstants.baseApiUrl` or similar configuration
- **Test**: Print the full URL being called

### Issue 2: Network Policy (Android)
- **Problem**: Android blocking HTTP requests
- **Solution**: Add to `android/app/src/main/AndroidManifest.xml`:
```xml
<application
    android:usesCleartextTraffic="true"
    ...>
```

### Issue 3: CORS Issues (Web)
- **Problem**: Browser blocking requests
- **Solution**: Backend needs CORS headers (already configured in your Django app)

### Issue 4: Emulator Network Issues
- **Problem**: Android emulator can't reach localhost
- **Solution**: Use `********` instead of `localhost` for Android emulator

## Expected Working URLs

These URLs should work when called from Flutter:
```
GET {baseApiUrl}/api/spotify/playlists/50Dlif22wDplVdWiXp6tPt/tracks/
GET {baseApiUrl}/api/spotify/playlists/32K3iIu1DZ2LeDudB4qjh9/tracks/  
GET {baseApiUrl}/api/spotify/playlists/30i8Eu0HNLNhoXvR6irf1N/tracks/
```

## Next Steps

1. Add network logging to your Flutter app
2. Test the direct backend connection function above
3. Compare the logged requests with the working curl commands
4. Check if the base URL is correct for your deployment environment
5. Verify that any authentication headers are being sent correctly

The backend is working perfectly - the issue is in the Flutter-to-backend communication. 