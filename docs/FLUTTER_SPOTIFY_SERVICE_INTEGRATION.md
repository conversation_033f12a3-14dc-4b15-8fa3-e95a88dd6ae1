# Flutter Spotify Service Integration Guide

## Overview

The `SpotifyService` class provides comprehensive Spotify integration for your Flutter application, with seamless fallback support for users without Spotify accounts (e.g., Apple Music users) through the backend's client credentials implementation.

## Key Features

### 🔐 **Dual Authentication Strategy**
- **User Authentication**: Full personalized Spotify features for connected users
- **Client Credentials Fallback**: Public content access via backend for non-Spotify users
- **Automatic Token Management**: Handles token refresh and expiration gracefully

### 🎵 **Comprehensive Music Operations**
- Search tracks, artists, playlists across Spotify's catalog
- Access user's saved tracks, playlists, and listening history
- Get personalized recommendations and top tracks
- Browse by genre with intelligent alias matching

### ⚡ **Performance Optimizations**
- **Local Caching**: In-memory cache for search results
- **Rate Limiting**: Respects Spotify's API limits (4 requests/second)
- **Backend Caching**: Leverages server-side Redis caching
- **Parallel Requests**: Optimized multi-artist lookups

### 🛠 **Development Support**
- **Mock Mode**: Development tokens for testing without real Spotify accounts
- **Comprehensive Logging**: Detailed debugging information
- **Error Recovery**: Graceful fallback mechanisms

---

## Authentication Flows

### 1. **Spotify User Authentication**
```dart
// Check if user has connected Spotify
bool isConnected = await spotifyService.isConnected();

// Store token from Spotify SDK
await spotifyService.storeAccessToken(accessToken);

// Get current access token (with automatic refresh)
String? token = await spotifyService.getAccessToken();
```

### 2. **Client Credentials Fallback (Apple Music Users)**
When no user token is available, the service automatically uses your backend's client credentials flow:

```dart
// These calls work for ANY user (Spotify or Apple Music)
List<MusicTrack> results = await spotifyService.searchTracks("Coldplay");
MusicTrack? track = await spotifyService.getTrackDetails("trackId");
List<MusicTrack> genreTracks = await spotifyService.searchTracksByGenre("pop");
```

The service transparently:
1. Detects missing user authentication
2. Routes requests through your backend API
3. Backend uses client credentials for public Spotify content
4. Returns unified `MusicTrack` objects

---

## Core Methods

### **Search Operations**

#### Search Tracks
```dart
Future<List<MusicTrack>> searchTracks(
  String query, {
  int limit = 20,
  int offset = 0,
  String? context, // For AI analytics
}) async
```

**Example:**
```dart
// Works for both Spotify and Apple Music users
final tracks = await spotifyService.searchTracks(
  "Shape of You Ed Sheeran",
  limit: 10,
  context: "user_search"
);
```

#### Search by Genre
```dart
Future<List<MusicTrack>> searchTracksByGenre(
  String genre, {
  int limit = 20,
  int offset = 0,
  int? year,
  String? artist,
}) async
```

**Example:**
```dart
// Intelligent genre search with aliases
final popTracks = await spotifyService.searchTracksByGenre(
  "j-pop", // Automatically expands to "japanese pop"
  limit: 20,
  year: 2023,
);
```

#### Search Playlists
```dart
Future<List<Map<String, dynamic>>> searchPlaylists(
  String query, {
  int limit = 20,
  int offset = 0,
}) async
```

### **User-Specific Operations** (Require Spotify Authentication)

#### Get Saved Tracks
```dart
Future<Map<String, dynamic>> getSavedTracks({
  int limit = 50,
  int offset = 0,
}) async
```

**Returns:**
```dart
{
  'tracks': List<MusicTrack>,
  'total': int,
  'hasMore': bool
}
```

#### Get Top Tracks
```dart
Future<List<MusicTrack>> getTopTracks({
  int limit = 20,
  BuildContext? context, // For re-authentication
}) async
```

#### Get Recently Played
```dart
Future<List<MusicTrack>> getRecentlyPlayed({
  int limit = 20,
}) async
```

#### Get User Profile
```dart
Future<Map<String, dynamic>?> getUserProfile() async
```

**Returns:**
```dart
{
  'id': 'user_id',
  'display_name': 'User Name',
  'email': '<EMAIL>',
  'product': 'premium', // or 'free'
  'is_premium': true,
  'profile_image_url': 'https://...',
  'followers': {'total': 1234},
  // ... other Spotify profile fields
}
```

### **Artist Operations**

#### Get Multiple Artists by Names
```dart
Future<List<Map<String, dynamic>>> getMultipleArtistsByNames(
  List<String> artistNames
) async
```

**Example:**
```dart
final artists = await spotifyService.getMultipleArtistsByNames([
  "Ed Sheeran",
  "Taylor Swift", 
  "BTS"
]);

// Returns detailed artist info with parallel optimization
for (final artist in artists) {
  print("${artist['name']}: ${artist['popularity']} popularity");
  print("Genres: ${artist['genres'].join(', ')}");
  print("Followers: ${artist['followers']}");
}
```

#### Get Top Tracks by Artist
```dart
Future<List<MusicTrack>> getTopTracksByArtist(
  String artistName, {
  int limit = 10,
}) async
```

### **Advanced Search Operations**

#### Search Tracks Inspired by Artists
```dart
Future<List<MusicTrack>> searchTracksInspiredByArtists(
  List<String> artistNames, {
  String? genre,
  int limit = 20,
  int? year,
}) async
```

**Example:**
```dart
// Get tracks similar to multiple artists
final inspiredTracks = await spotifyService.searchTracksInspiredByArtists(
  ["Radiohead", "Pink Floyd", "Coldplay"],
  genre: "alternative rock",
  limit: 25,
  year: 2020,
);
```

#### Multi-Type Search
```dart
Future<Map<String, dynamic>> searchMultipleTypes(
  String query, {
  List<String> types = const ['track'],
  int limit = 20,
  String? market,
}) async
```

**Example:**
```dart
final results = await spotifyService.searchMultipleTypes(
  "Beatles",
  types: ['track', 'artist', 'album'],
  limit: 10,
);

// Access different result types
final tracks = results['tracks']['items'];
final artists = results['artists']['items']; 
final albums = results['albums']['items'];
```

---

## Backend Integration

### **API Endpoint Mapping**

The service intelligently routes requests based on authentication status:

| Frontend Method | User Authenticated | No User Auth | Backend Endpoint |
|----------------|-------------------|--------------|------------------|
| `searchTracks()` | Direct Spotify API | ✅ Backend API | `/api/spotify/search/` |
| `getTrackDetails()` | Direct Spotify API | ✅ Backend API | `/api/spotify/tracks/{id}/` |
| `searchTracksByGenre()` | Direct Spotify API | ✅ Backend API | `/api/spotify/search/` |
| `getSavedTracks()` | Direct Spotify API | ❌ Requires Auth | `/me/tracks` |
| `getTopTracks()` | Direct Spotify API | ❌ Requires Auth | `/me/top/tracks` |

### **Backend Request Format**

**Search Request:**
```http
GET /api/spotify/search/?q=coldplay&type=track&limit=20
Content-Type: application/json
```

**Response:**
```json
{
  "tracks": {
    "items": [...],
    "total": 1000,
    "limit": 20,
    "offset": 0
  }
}
```

---

## Configuration

### **Required Constants** (`AppConstants`)

```dart
class AppConstants {
  // Spotify Configuration
  static const String spotifyClientId = 'your_spotify_client_id';
  static const String spotifyRedirectUri = 'your://redirect/uri';
  
  // Backend API Configuration  
  static const String baseApiUrl = 'https://your-backend.com/api';
  
  // Development Configuration
  static const String spotifyApiBaseUrl = 'https://your-ngrok-url.ngrok-free.app/api/spotify';
}
```

### **Environment Setup**

**Production:**
```dart
// Uses direct Spotify API with user tokens
// Falls back to backend client credentials for public content
```

**Development:**
```dart
// Uses backend API exclusively with development tokens
// Enables comprehensive testing without real Spotify accounts
```

---

## Usage Examples

### **1. Universal Search (Works for All Users)**

```dart
class MusicSearchScreen extends StatefulWidget {
  @override
  _MusicSearchScreenState createState() => _MusicSearchScreenState();
}

class _MusicSearchScreenState extends State<MusicSearchScreen> {
  final SpotifyService _spotifyService = SpotifyService();
  List<MusicTrack> _searchResults = [];
  bool _isLoading = false;

  Future<void> _performSearch(String query) async {
    setState(() => _isLoading = true);
    
    try {
      // This works for both Spotify and Apple Music users!
      final results = await _spotifyService.searchTracks(
        query,
        limit: 25,
        context: 'user_search',
      );
      
      setState(() {
        _searchResults = results;
        _isLoading = false;
      });
    } catch (e) {
      print('Search error: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Music Search')),
      body: Column(
        children: [
          // Search bar
          TextField(
            onSubmitted: _performSearch,
            decoration: InputDecoration(
              hintText: 'Search for music...',
              prefixIcon: Icon(Icons.search),
            ),
          ),
          
          // Results
          Expanded(
            child: _isLoading
              ? Center(child: CircularProgressIndicator())
              : ListView.builder(
                  itemCount: _searchResults.length,
                  itemBuilder: (context, index) {
                    final track = _searchResults[index];
                    return ListTile(
                      leading: track.albumArt.isNotEmpty
                        ? Image.network(track.albumArt, width: 50, height: 50)
                        : Icon(Icons.music_note),
                      title: Text(track.title),
                      subtitle: Text(track.artist),
                      trailing: Text('${track.popularity}%'),
                      onTap: () => _playTrack(track),
                    );
                  },
                ),
          ),
        ],
      ),
    );
  }

  void _playTrack(MusicTrack track) {
    // Handle track playback
    print('Playing: ${track.title} by ${track.artist}');
  }
}
```

### **2. Genre-Based Discovery**

```dart
class GenreDiscoveryScreen extends StatefulWidget {
  @override
  _GenreDiscoveryScreenState createState() => _GenreDiscoveryScreenState();
}

class _GenreDiscoveryScreenState extends State<GenreDiscoveryScreen> {
  final SpotifyService _spotifyService = SpotifyService();
  final Map<String, List<MusicTrack>> _genreTracks = {};
  bool _isLoading = false;

  final List<String> _popularGenres = [
    'pop', 'rock', 'hip-hop', 'jazz', 'classical', 'electronic'
  ];

  @override
  void initState() {
    super.initState();
    _loadGenreTracks();
  }

  Future<void> _loadGenreTracks() async {
    setState(() => _isLoading = true);

    try {
      // Load tracks for multiple genres in parallel
      final futures = _popularGenres.take(6).map((genre) async {
        final tracks = await _spotifyService.searchTracksByGenre(
          genre,
          limit: 10,
        );
        return MapEntry(genre, tracks);
      });

      final results = await Future.wait(futures);
      
      setState(() {
        for (final entry in results) {
          _genreTracks[entry.key] = entry.value;
        }
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading genre tracks: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Discover by Genre')),
      body: _isLoading
        ? Center(child: CircularProgressIndicator())
        : ListView.builder(
            itemCount: _genreTracks.length,
            itemBuilder: (context, index) {
              final genre = _genreTracks.keys.elementAt(index);
              final tracks = _genreTracks[genre]!;
              
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Genre header
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text(
                      genre.toUpperCase(),
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                  ),
                  
                  // Horizontal track list
                  SizedBox(
                    height: 160,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: tracks.length,
                      itemBuilder: (context, trackIndex) {
                        final track = tracks[trackIndex];
                        return _buildTrackCard(track);
                      },
                    ),
                  ),
                  
                  SizedBox(height: 20),
                ],
              );
            },
          ),
    );
  }

  Widget _buildTrackCard(MusicTrack track) {
    return Container(
      width: 120,
      margin: EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        children: [
          // Album art
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: track.albumArt.isNotEmpty
                ? DecorationImage(
                    image: NetworkImage(track.albumArt),
                    fit: BoxFit.cover,
                  )
                : null,
              color: track.albumArt.isEmpty ? Colors.grey[300] : null,
            ),
            child: track.albumArt.isEmpty
              ? Icon(Icons.music_note, size: 40, color: Colors.grey[600])
              : null,
          ),
          
          SizedBox(height: 8),
          
          // Track title
          Text(
            track.title,
            style: Theme.of(context).textTheme.bodySmall,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),
          
          // Artist
          Text(
            track.artist,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
              fontSize: 10,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
```

---

## Best Practices

### **1. Error Handling**
```dart
// Always wrap API calls in try-catch
try {
  final tracks = await spotifyService.searchTracks(query);
  // Handle success
} catch (e) {
  // Handle error gracefully
  print('Search failed: $e');
  // Show user-friendly error message
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(content: Text('Search failed. Please try again.')),
  );
}
```

### **2. Loading States**
```dart
// Always show loading indicators for async operations
setState(() => _isLoading = true);
try {
  final data = await spotifyService.getSomeData();
  setState(() {
    _data = data;
    _isLoading = false;
  });
} catch (e) {
  setState(() => _isLoading = false);
  _showError(e.toString());
}
```

### **3. Context Passing**
```dart
// Pass context for automatic re-authentication
final topTracks = await spotifyService.getTopTracks(
  context: context, // Enables automatic re-auth on token expiry
);
```

### **4. Cache Management**
```dart
// Clear cache when needed (e.g., after logout)
spotifyService.clearSearchCache();

// Consider cache implications for real-time data
final recentTracks = await spotifyService.getRecentlyPlayed();
// These should be fresh, not cached
```

---

## Apple Music User Flow

### **Seamless Experience for Non-Spotify Users**

Your Flutter app now provides a seamless experience for Apple Music users:

1. **Music Discovery**: Full access to Spotify's search and browse features
2. **No Account Required**: Browse without Spotify authentication
3. **Unified Interface**: Same UI/UX for all users
4. **Performance**: Fast responses through backend caching

### **Feature Comparison**

| Feature | Spotify Users | Apple Music Users |
|---------|---------------|-------------------|
| Search Tracks | ✅ Full Access | ✅ Full Access |
| Track Details | ✅ Full Access | ✅ Full Access |
| Genre Browse | ✅ Full Access | ✅ Full Access |
| Artist Info | ✅ Full Access | ✅ Full Access |
| Playlist Search | ✅ Full Access | ✅ Full Access |
| Saved Tracks | ✅ Personal Library | ❌ Not Available |
| Top Tracks | ✅ Personal Stats | ❌ Not Available |
| Recently Played | ✅ Personal History | ❌ Not Available |
| Recommendations | ✅ Personalized | ✅ Generic |

### **Implementation Notes**

- **Automatic Detection**: Service automatically detects authentication status
- **Transparent Fallback**: No code changes needed for universal features
- **Error Handling**: Graceful degradation for user-specific features
- **Performance**: Backend caching improves response times

---

## Testing & Development

### **Mock Data Support**
```dart
// Development tokens automatically trigger mock mode
const devToken = 'dev_token_user123';
await spotifyService.storeAccessToken(devToken);

// All API calls now return mock data
final mockTracks = await spotifyService.searchTracks('test query');
// Returns realistic mock MusicTrack objects
```

### **Debug Logging**
```dart
if (kDebugMode) {
  // Service automatically provides detailed logs:
  // 🔍 [SPOTIFY AI SEARCH] Making request...
  // ✅ [SPOTIFY AI SEARCH] SUCCESS: 20 tracks returned
  // 📦 [SPOTIFY AI SEARCH] Cache hit for query
}
```

---

## Integration Checklist

- [ ] **Backend Setup**: Ensure client credentials are configured
- [ ] **Constants Configuration**: Set all required `AppConstants`
- [ ] **Authentication Flow**: Implement Spotify sign-in through `AuthService`
- [ ] **Error Handling**: Add proper try-catch blocks around API calls
- [ ] **Loading States**: Show loading indicators during async operations
- [ ] **Offline Handling**: Implement offline state management
- [ ] **Cache Strategy**: Decide on cache retention and clearing policies
- [ ] **Analytics Integration**: Connect to your analytics service for usage tracking

This comprehensive integration enables your Flutter app to provide universal music discovery while respecting user preferences and authentication states. **Apple Music users can now seamlessly explore Spotify's catalog, while Spotify users get full personalized experiences.** 