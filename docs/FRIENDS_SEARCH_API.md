# Friends API Documentation

## Overview
The Friends API provides comprehensive functionality for managing friendships, including friend requests, friend groups, analytics, and real-time updates.

## Authentication
- **Required**: Yes
- **Type**: Bearer Token
- **Header**: `Authorization: Bearer <your_jwt_token>`

## Base Endpoints

### 1. Friend Management
```
GET /api/friends/                  # List friends
POST /api/friends/                 # Create friend request
GET /api/friends/{id}/            # Get friend details
DELETE /api/friends/{id}/         # Remove friend
```

### 2. Friend Requests
```
GET /api/friends/requests/         # List friend requests
POST /api/friends/requests/        # Send friend request
GET /api/friends/requests/{id}/    # Get request details
DELETE /api/friends/requests/{id}/ # Cancel request
```

### 3. Friend Groups
```
GET /api/friends/groups/           # List friend groups
POST /api/friends/groups/          # Create friend group
GET /api/friends/groups/{id}/      # Get group details
PUT /api/friends/groups/{id}/      # Update group
DELETE /api/friends/groups/{id}/   # Delete group
```

## Enhanced Features

### 1. Advanced Friend Search
```
GET /api/friends/advanced-search/
```

Query Parameters:
| Parameter | Type | Description |
|-----------|------|-------------|
| `q` | string | General search query |
| `active_within` | integer | Hours since last activity |
| `friendship_level` | string | Filter by level (new/regular/close/best) |
| `min_interactions` | integer | Minimum interaction count |
| `min_mutual_friends` | integer | Minimum mutual friends count |
| `ordering` | string | Sort field (-friendship_score, -last_interaction, etc.) |

Example:
```javascript
const searchFriends = async (params) => {
  const response = await fetch(`/api/friends/advanced-search/?${new URLSearchParams(params)}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
};

// Usage
searchFriends({
  q: 'john',
  active_within: 24,
  friendship_level: 'close',
  min_mutual_friends: 5,
  ordering: '-friendship_score'
});
```

### 2. Friend Analytics
```
GET /api/friends/analytics/
```

Response:
```json
{
  "total_friends": 42,
  "new_friends_30d": 5,
  "most_active_friends": [
    {
      "id": "123",
      "username": "john_doe",
      "interaction_count": 150,
      "last_interaction": "2024-01-15T10:30:00Z"
    }
  ],
  "best_friends": [
    {
      "id": "456",
      "username": "jane_smith",
      "friendship_score": 950,
      "friendship_level": "best"
    }
  ],
  "activity_trends": [
    {
      "day": "2024-01-15",
      "count": 25
    }
  ]
}
```

### 3. Friend Suggestions
```
GET /api/friends/suggested/
```

Response:
```json
{
  "results": [
    {
      "user": {
        "id": "789",
        "username": "mike_wilson",
        "name": "Mike Wilson"
      },
      "mutual_friends_count": 8,
      "common_interests": ["music", "travel"],
      "matching_score": 85.5
    }
  ]
}
```

### 4. Bulk Operations

#### Send Multiple Friend Requests
```
POST /api/friends/requests/bulk-send/
```

Request:
```json
{
  "recipient_ids": ["123", "456", "789"]
}
```

#### Respond to Multiple Requests
```
POST /api/friends/requests/bulk-respond/
```

Request:
```json
{
  "responses": [
    {
      "request_id": "123",
      "action": "accept"
    },
    {
      "request_id": "456",
      "action": "reject"
    }
  ]
}
```

### 5. Friend Groups

#### Create Group
```
POST /api/friends/groups/
```

Request:
```json
{
  "name": "Close Friends",
  "description": "My closest friends group",
  "is_private": false
}
```

#### Add Members to Group
```
POST /api/friends/groups/{id}/add_members/
```

Request:
```json
{
  "friend_ids": ["123", "456"]
}
```

#### Remove Members from Group
```
POST /api/friends/groups/{id}/remove_members/
```

Request:
```json
{
  "friend_ids": ["789"]
}
```

## Real-time Updates

### WebSocket Connection
```javascript
const ws = new WebSocket('ws://your-domain.com/ws/friends/');
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  handleFriendUpdate(data);
};
```

### Event Types

1. Friend Request
```json
{
  "type": "new_friend_request",
  "from_user": "123",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

2. Request Response
```json
{
  "type": "friend_request_response",
  "response": "accepted",
  "from_user": "456",
  "timestamp": "2024-01-15T10:35:00Z"
}
```

3. Friend Activity
```json
{
  "type": "friend_activity",
  "activity_type": "new_pin",
  "user_id": "789",
  "timestamp": "2024-01-15T11:00:00Z"
}
```

4. Status Update
```json
{
  "type": "status_update",
  "user_id": "123",
  "status": "online",
  "timestamp": "2024-01-15T11:15:00Z"
}
```

## Error Handling

All endpoints return appropriate HTTP status codes:
- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 500: Server Error

Error Response Format:
```json
{
  "error": "Error message",
  "detail": "Detailed error description"
}
```

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Bulk operations: 50 requests per batch
- WebSocket connections: 1 connection per user

## Best Practices

1. **Caching**
   - Friend lists are cached for 5 minutes
   - User online status is cached for 5 minutes
   - Mutual friends count is cached for 1 hour

2. **Performance**
   - Use pagination for large lists (default page size: 20)
   - Use advanced search filters to reduce response size
   - Implement WebSocket connection retry with exponential backoff

3. **Security**
   - Always validate friend request permissions
   - Check group ownership before modifications
   - Sanitize user input in search queries

4. **Error Handling**
   - Implement proper error handling for all API calls
   - Use try-catch blocks for WebSocket operations
   - Handle network disconnections gracefully

## URL Examples

```
GET /api/friends/search/?q=john
GET /api/friends/search/?username=john_doe
GET /api/friends/search/?name=John
GET /api/friends/search/?q=music&username=dj
```

## Notes

- Search is case-insensitive
- Partial matches are supported (uses `icontains` lookup)
- Only searches within the current user's accepted friends
- Returns the same detailed friend structure as the main friends list
- Multiple search parameters can be combined (OR logic) 