# Frontend Pin Skin System Integration Guide

## Overview

The pin skin system allows users to customize their pins with various visual skins. There are two main types:
- **House Skins**: Basic customizations (free and premium)
- **Artist Skins**: Limited-time skins tied to weekly challenges

### Image System
Pin skin images are stored as URLs pointing to external image services (primarily [Picsum Photos](https://picsum.photos/) for beautiful, consistent images). The system uses seeded URLs to ensure the same image is always returned for each skin, providing visual consistency across sessions.

## API Endpoints

### Base URL
All endpoints are under `/api/pins/`

### Authentication
All endpoints require authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## 1. Skin Management Endpoints

### 1.1 Get Available Skins
**GET** `/api/pins/skins/available/`

Returns all skins that can be displayed to the user (both locked and unlocked).

**Response:**
```json
[
  {
    "id": 1,
    "name": "Classic House",
    "slug": "classic-house",
    "image": "https://picsum.photos/seed/classic-blue/64/64",
    "description": "Basic house skin",
    "skin_type": "HOUSE",
    "artist": null,
    "is_premium": false,
    "challenge_id": null,
    "unlock_rule": "FREE",
    "locked": false,
    "metadata": {"unlock_type": "free"}
  },
  {
    "id": 5,
    "name": "Deadmau5 Badge",
    "slug": "deadmau5-badge",
    "image": "https://picsum.photos/seed/deadmau5-badge/64/64",
    "description": "Exclusive Deadmau5 participation skin",
    "skin_type": "ARTIST",
    "artist": "Deadmau5",
    "is_premium": false,
    "challenge_id": 1,
    "unlock_rule": "COMPLETE_WEEKLY_CHALLENGE",
    "locked": true,
    "metadata": {"unlock_type": "PARTICIPATE"}
  }
]
```

### 1.2 Get User's Unlocked Skins
**GET** `/api/pins/skins/unlocked/`

Returns only the skins the current user has access to.

**Response:**
```json
[
  {
    "id": 1,
    "name": "Classic House",
    "slug": "classic-house",
    "image": "https://picsum.photos/seed/classic-blue/64/64",
    "skin_type": "HOUSE",
    "locked": false
  }
]
```

### 1.3 Claim a Skin
**POST** `/api/pins/skins/{skin_id}/claim/`

Attempt to manually claim a skin if eligible.

**Request Body:** None required

**Response (Success):**
```json
{
  "success": true,
  "message": "Successfully claimed skin: Deadmau5 Badge"
}
```

**Response (Already Unlocked):**
```json
{
  "success": true,
  "message": "Skin 'Classic House' already unlocked"
}
```

**Response (Not Eligible):**
```json
{
  "error": "Not eligible to claim this skin. Must participate in challenge and wait for it to end"
}
```

### 1.4 Equip a Skin
**POST** `/api/pins/skins/{skin_id}/equip/`

Equip a skin for future pins.

**Request Body:** None required

**Response (Success):**
```json
{
  "success": true,
  "message": "Equipped skin: Deadmau5 Badge"
}
```

**Response (Not Unlocked):**
```json
{
  "error": "You don't have access to this skin"
}
```

## 2. Challenge Endpoints

### 2.1 Get All Challenges (Paginated)
**GET** `/api/challenges/`

Returns paginated list of active challenges.

**Response:**
```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "title": "EDM Remix Challenge",
      "description": "Drop your best EDM remixes this week!",
      "entries": 15,
      "time_left": {
        "inDays": 3,
        "inHours": 72,
        "inMinutes": 4320,
        "inSeconds": 259200
      },
      "gradient_colors": ["#6200EE", "#3700B3"],
      "icon": "heart_broken_rounded",
      "is_active": true,
      "created_at": "2024-06-10T00:00:00Z",
      "has_participated": false
    }
  ]
}
```

### 2.2 Participate in Challenge
**POST** `/api/challenges/{challenge_id}/participate/`

Submit a song for a challenge.

**Request Body:**
```json
{
  "song": {
    "spotify_id": "4uLU6hMCjMI75M1A2tKUQC",
    "title": "Song Title",
    "artist": "Artist Name",
    "album": "Album Name",
    "duration_ms": 240000,
    "album_art": "https://i.scdn.co/image/...",
    "preview_url": "https://p.scdn.co/mp3-preview/..."
  },
  "latitude": 40.7128,
  "longitude": -74.0060
}
```

**Response:**
```json
{
  "id": 1,
  "entry_id": 1,
  "user": {
    "id": 123,
    "username": "user123",
    "profile_pic": "https://example.com/profile.jpg"
  },
  "challenge": {
    "id": 1,
    "title": "EDM Remix Challenge",
    "entries": 16,
    "time_left": {...},
    "has_participated": true
  },
  "song": {
    "id": 456,
    "spotify_id": "4uLU6hMCjMI75M1A2tKUQC",
    "title": "Song Title",
    "artist": "Artist Name",
    "album": "Album Name",
    "duration_ms": 240000,
    "album_art": "https://i.scdn.co/image/...",
    "preview_url": "https://p.scdn.co/mp3-preview/..."
  },
  "location": {
    "type": "Point",
    "coordinates": [-74.0060, 40.7128]
  },
  "upvotes": 0,
  "downvotes": 0,
  "vote_score": 0,
  "user_vote": null,
  "created_at": "2024-06-15T10:30:00Z"
}
```

### 2.3 Get Challenge Entries
**GET** `/api/challenges/{challenge_id}/entries/`

Get all entries for a specific challenge with filtering options.

**Query Parameters:**
- `scope`: Filter scope (`all`, `friends`, `local`, `school`)
- `lat`: Latitude for local filtering
- `lng`: Longitude for local filtering  
- `radius_km`: Radius in kilometers for local filtering (default: 5)

**Response:**
```json
[
  {
    "id": 1,
    "entry_id": 1,
    "user": {
      "id": 123,
      "username": "user123",
      "profile_pic": "https://example.com/profile.jpg"
    },
    "song": {
      "id": 456,
      "title": "Amazing Track",
      "artist": "Cool Artist"
    },
    "upvotes": 25,
    "downvotes": 3,
    "vote_score": 22,
    "user_vote": 1,
    "created_at": "2024-06-15T10:30:00Z"
  }
]
```

### 2.4 Get Challenge Leaderboard
**GET** `/api/challenges/leaderboard/`

Get user rankings based on challenge participation and votes.

**Query Parameters:**
- `scope`: Filter scope (`all`, `friends`, `local`, `school`)
- `lat`: Latitude for local filtering
- `lng`: Longitude for local filtering
- `radius_km`: Radius in kilometers for local filtering
- `limit`: Number of results to return (default: 20)

**Response:**
```json
[
  {
    "id": "123",
    "name": "John Doe",
    "username": "johndoe",
    "avatar": "https://example.com/avatar.jpg",
    "score": "150",
    "change": "up",
    "changeAmount": "2",
    "isCurrentUser": false
  }
]
```

### 2.5 Vote on Challenge Entries
**POST** `/api/challenges/votes/upvote/`
**POST** `/api/challenges/votes/downvote/`

Vote on a challenge entry.

**Request Body:**
```json
{
  "entry": 1
}
```

**Response:**
```json
{
  "upvotes": 26,
  "downvotes": 3,
  "score": 23,
  "user_vote": 1
}
```

**Note:** Pin skin rewards are handled separately by the pin skin system based on challenge participation and results.

## 3. User Skin Status

### 3.1 Get User's Unlocked Skins
**GET** `/api/pins/user-skins/`

Returns detailed information about user's unlocked skins.

**Response:**
```json
[
  {
    "id": 1,
    "skin": {
      "id": 1,
      "name": "Classic House",
      "image": "https://picsum.photos/seed/classic-blue/64/64"
    },
    "unlocked_at": "2024-06-15T10:30:00Z"
  }
]
```

## 4. Frontend Implementation Guide

### 4.1 Skin Gallery Component

```typescript
interface PinSkin {
  id: number;
  name: string;
  slug: string;
  image: string;  // External URL (e.g., https://picsum.photos/seed/skin-name/64/64)
  description: string;
  skin_type: 'HOUSE' | 'ARTIST';
  artist?: string;
  is_premium: boolean;
  challenge_id?: number;
  unlock_rule: string;
  locked: boolean;
  metadata: Record<string, any>;
}

class SkinGallery {
  async loadAvailableSkins(): Promise<PinSkin[]> {
    const response = await fetch('/api/pins/skins/available/', {
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`,
        'Content-Type': 'application/json'
      }
    });
    return response.json();
  }

  async claimSkin(skinId: number): Promise<{success: boolean, message: string}> {
    const response = await fetch(`/api/pins/skins/${skinId}/claim/`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`,
        'Content-Type': 'application/json'
      }
    });
    return response.json();
  }

  async equipSkin(skinId: number): Promise<{success: boolean, message: string}> {
    const response = await fetch(`/api/pins/skins/${skinId}/equip/`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`,
        'Content-Type': 'application/json'
      }
    });
    return response.json();
  }
}
```

### 4.2 Challenge Integration

```typescript
interface WeeklyChallenge {
  id: number;
  title: string;
  description: string;
  entries: number;
  time_left: {
    inDays: number;
    inHours: number;
    inMinutes: number;
    inSeconds: number;
  };
  gradient_colors: [string, string];
  icon: string;
  is_active: boolean;
  created_at: string;
  has_participated: boolean;
}

interface ChallengeEntry {
  id: number;
  entry_id: number;
  user: {
    id: number;
    username: string;
    profile_pic?: string;
  };
  song: {
    id: number;
    spotify_id: string;
    title: string;
    artist: string;
    album: string;
    duration_ms: number;
    album_art: string;
    preview_url?: string;
  };
  location?: {
    type: 'Point';
    coordinates: [number, number];
  };
  upvotes: number;
  downvotes: number;
  vote_score: number;
  user_vote?: 1 | -1 | null;
  created_at: string;
}

class ChallengeManager {
  async getChallenges(): Promise<{results: WeeklyChallenge[], count: number}> {
    const response = await fetch('/api/challenges/', {
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`
      }
    });
    return response.json();
  }

  async participateInChallenge(challengeId: number, songData: any, location?: {lat: number, lng: number}) {
    const body: any = {
      song: songData
    };
    
    if (location) {
      body.latitude = location.lat;
      body.longitude = location.lng;
    }

    const response = await fetch(`/api/challenges/${challengeId}/participate/`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });
    return response.json();
  }

  async getChallengeEntries(challengeId: number, filters?: {
    scope?: 'all' | 'friends' | 'local' | 'school';
    lat?: number;
    lng?: number;
    radius_km?: number;
  }): Promise<ChallengeEntry[]> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`/api/challenges/${challengeId}/entries/?${params}`, {
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`
      }
    });
    return response.json();
  }

  async voteOnEntry(entryId: number, voteType: 'upvote' | 'downvote') {
    const response = await fetch(`/api/challenges/votes/${voteType}/`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ entry: entryId })
    });
    return response.json();
  }
}
```

### 4.3 UI State Management

```typescript
interface AppState {
  availableSkins: PinSkin[];
  unlockedSkins: PinSkin[];
  equippedSkin: PinSkin | null;
  challenges: WeeklyChallenge[];
  challengeEntries: Record<number, ChallengeEntry[]>;
  loading: boolean;
  error: string | null;
}

class AppStateManager {
  private state: AppState = {
    availableSkins: [],
    unlockedSkins: [],
    equippedSkin: null,
    challenges: [],
    challengeEntries: {},
    loading: false,
    error: null
  };

  async initializeApp() {
    this.state.loading = true;
    try {
      const [availableSkins, unlockedSkins, challengesResponse] = await Promise.all([
        this.skinGallery.loadAvailableSkins(),
        this.loadUnlockedSkins(),
        this.challengeManager.getChallenges()
      ]);

      this.state.availableSkins = availableSkins;
      this.state.unlockedSkins = unlockedSkins;
      this.state.challenges = challengesResponse.results;
      this.state.loading = false;
    } catch (error) {
      this.state.error = error.message;
      this.state.loading = false;
    }
  }

  async loadChallengeEntries(challengeId: number, filters?: any) {
    try {
      const entries = await this.challengeManager.getChallengeEntries(challengeId, filters);
      this.state.challengeEntries[challengeId] = entries;
    } catch (error) {
      this.state.error = error.message;
    }
  }

  async refreshSkinStatus() {
    try {
      const [availableSkins, unlockedSkins] = await Promise.all([
        this.skinGallery.loadAvailableSkins(),
        this.loadUnlockedSkins()
      ]);
      
      this.state.availableSkins = availableSkins;
      this.state.unlockedSkins = unlockedSkins;
    } catch (error) {
      this.state.error = error.message;
    }
  }
}
```

## 5. User Flow Examples

### 5.1 Skin Selection Flow

1. **Display Available Skins**
   - Load `/api/pins/skins/available/`
   - Show locked/unlocked status
   - Display unlock requirements for locked skins

2. **Handle Skin Interaction**
   ```typescript
   async handleSkinClick(skin: PinSkin) {
     if (skin.locked) {
       // Show unlock requirements
       if (skin.unlock_rule === 'COMPLETE_WEEKLY_CHALLENGE') {
         this.showChallengeRequirements(skin);
       } else if (skin.is_premium) {
         this.showPremiumUpgrade();
       }
     } else {
       // Equip the skin
       await this.equipSkin(skin.id);
     }
   }
   ```

### 5.2 Challenge Participation Flow

1. **Display Available Challenges**
   - Load `/api/challenges/`
   - Show challenge details with time remaining and entry counts

2. **Participate in Challenge**
   ```typescript
   async participateInChallenge(challengeId: number, songData: any, userLocation?: {lat: number, lng: number}) {
     try {
       const result = await this.challengeManager.participateInChallenge(challengeId, songData, userLocation);
       
       // Show success message
       this.showSuccessMessage('Successfully submitted entry to challenge!');
       
       // Refresh challenge data to show updated entry count and participation status
       await this.refreshChallengeData();
       
       // Check for any unlocked skins based on participation
       await this.checkForNewSkins();
     } catch (error) {
       this.showErrorMessage(error.message);
     }
   }
   ```

3. **Browse Challenge Entries**
   ```typescript
   async loadChallengeEntries(challengeId: number, scope: 'all' | 'friends' | 'local' = 'all') {
     try {
       const entries = await this.challengeManager.getChallengeEntries(challengeId, { scope });
       this.displayEntries(entries);
     } catch (error) {
       this.showErrorMessage('Failed to load challenge entries');
     }
   }
   ```

4. **Vote on Entries**
   ```typescript
   async voteOnEntry(entryId: number, voteType: 'upvote' | 'downvote') {
     try {
       const result = await this.challengeManager.voteOnEntry(entryId, voteType);
       
       // Update UI with new vote counts
       this.updateEntryVotes(entryId, result);
     } catch (error) {
       this.showErrorMessage('Failed to submit vote');
     }
   }
   ```

### 5.3 Claim Completion Rewards

After challenges end, users can manually claim completion rewards:

```typescript
async claimChallengeRewards() {
  const availableSkins = await this.skinGallery.loadAvailableSkins();
  
  for (const skin of availableSkins) {
    if (skin.locked && skin.challenge_id && skin.unlock_rule === 'TOP_N') {
      try {
        const result = await this.skinGallery.claimSkin(skin.id);
        if (result.success) {
          this.showSuccessMessage(`Claimed ${skin.name}!`);
        }
      } catch (error) {
        // User wasn't eligible, continue to next skin
      }
    }
  }
}
```

## 6. Error Handling

### Common Error Responses

- **403 Forbidden**: User not eligible for skin
- **404 Not Found**: Skin or challenge doesn't exist
- **400 Bad Request**: Invalid request data

```typescript
async handleApiError(response: Response) {
  if (response.status === 403) {
    const error = await response.json();
    this.showErrorMessage(error.error || 'Not authorized');
  } else if (response.status === 404) {
    this.showErrorMessage('Resource not found');
  } else {
    this.showErrorMessage('An unexpected error occurred');
  }
}
```

## 7. Real-time Updates

Consider implementing WebSocket connections or periodic polling to:
- Update challenge status
- Notify when new skins are unlocked
- Refresh skin availability

```typescript
// Poll for updates every 5 minutes
setInterval(async () => {
  await this.refreshSkinStatus();
  await this.checkForNewChallenges();
}, 5 * 60 * 1000);
```

## 8. Caching Strategy

- Cache available skins for 5-10 minutes
- Cache unlocked skins until user performs actions
- Always fetch fresh challenge data
- Invalidate cache after skin operations

```typescript
class SkinCache {
  private cache = new Map();
  private TTL = 5 * 60 * 1000; // 5 minutes

  async getCachedSkins(): Promise<PinSkin[]> {
    const cached = this.cache.get('available_skins');
    if (cached && Date.now() - cached.timestamp < this.TTL) {
      return cached.data;
    }
    
    const fresh = await this.skinGallery.loadAvailableSkins();
    this.cache.set('available_skins', {
      data: fresh,
      timestamp: Date.now()
    });
    return fresh;
  }
}
```

## 9. Image Handling Best Practices

### URL-based Image System
Since pin skin images are external URLs, consider these implementation details:

```typescript
class ImageHandler {
  // Add error handling for failed image loads
  handleImageError(event: Event, fallbackUrl?: string) {
    const img = event.target as HTMLImageElement;
    if (fallbackUrl) {
      img.src = fallbackUrl;
    } else {
      // Fallback to a default skin image
      img.src = 'https://picsum.photos/seed/default-fallback/64/64';
    }
  }

  // Preload skin images for better UX
  async preloadSkinImages(skins: PinSkin[]) {
    const imagePromises = skins.map(skin => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = resolve;
        img.onerror = reject;
        img.src = skin.image;
      });
    });
    
    try {
      await Promise.allSettled(imagePromises);
    } catch (error) {
      console.warn('Some skin images failed to preload');
    }
  }
}
```

### Caching Considerations
- External image URLs may change or become unavailable
- Consider implementing client-side image caching
- Validate image URLs before displaying
- Have fallback images ready for error states

## 10. UI/UX Recommendations

### Visual Indicators
- **Locked Skins**: Gray overlay with lock icon
- **Premium Skins**: Crown/star icon
- **Challenge Skins**: Timer icon showing challenge status
- **Equipped Skin**: Checkmark or highlighted border

### Tooltips/Info
- Show unlock requirements on hover
- Display challenge end times
- Show skin rarity/exclusivity

### Animations
- Unlock animations when claiming skins
- Smooth transitions between skin previews
- Loading states for API calls

## 11. Testing Considerations

- Test with different user states (free/premium)
- Test challenge participation edge cases
- Test network failure scenarios
- Test with expired challenges
- Verify skin unlock logic across different rule types

This integration guide provides everything needed to implement the pin skin system in your frontend application, including proper error handling, caching, and user experience considerations. 