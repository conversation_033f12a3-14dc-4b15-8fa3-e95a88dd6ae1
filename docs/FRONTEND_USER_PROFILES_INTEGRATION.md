# Frontend Integration Guide: User Profiles, Pins & Collections

This guide explains how to integrate the new user profile viewing functionality that allows users to view each other's public profiles, pins, and collections.

## Overview

The new functionality includes:
1. **User Search** - Search for users by username
2. **Public Profile View** - View another user's public profile information
3. **Public Pins View** - View another user's public pins
4. **Public Collections View** - View another user's public collections

## API Endpoints

### 1. Search Users
```
GET /api/users/search/?q={search_query}
```

**Purpose:** Find users by username for profile discovery

**Parameters:**
- `q` (required): Search query, minimum 2 characters

**Response:**
```json
[
  {
    "id": "user_id",
    "username": "username",
    "profile_pic": "profile_image_url",
    "bio": "User bio",
    "last_active": "2023-04-06T12:34:56Z",
    "pin_count": 15,
    "public_collection_count": 3
  }
]
```

### 2. Get User Public Profile
```
GET /api/users/{user_id}/public_profile/
```

**Purpose:** Get detailed public information about a specific user

**Response:**
```json
{
  "id": "user_id",
  "username": "username",
  "profile_pic": "profile_image_url",
  "bio": "User bio",
  "last_active": "2023-04-06T12:34:56Z",
  "pin_count": 15,
  "public_collection_count": 3
}
```

### 3. Get User Public Pins
```
GET /api/users/{user_id}/public_pins/
```

**Purpose:** Get paginated list of user's public pins

**Response:** Paginated list of pins (same structure as regular pins endpoint)

### 4. Get User Public Collections
```
GET /api/users/{user_id}/public_collections/
```

**Purpose:** Get paginated list of user's public collections

**Response:** Paginated list of collections (same structure as regular collections endpoint)

## Frontend Implementation Guide

### 1. User Search Component

Create a search component that allows users to find other users:

```typescript
// UserSearch.tsx
interface User {
  id: string;
  username: string;
  profile_pic: string;
  bio: string;
  last_active: string;
  pin_count: number;
  public_collection_count: number;
}

const UserSearch: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);

  const searchUsers = async (query: string) => {
    if (query.length < 2) {
      setSearchResults([]);
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/users/search/?q=${encodeURIComponent(query)}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });
      
      if (response.ok) {
        const users = await response.json();
        setSearchResults(users);
      }
    } catch (error) {
      console.error('Error searching users:', error);
    } finally {
      setLoading(false);
    }
  };

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      searchUsers(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  return (
    <div className="user-search">
      <input
        type="text"
        placeholder="Search users..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        className="search-input"
      />
      
      {loading && <div className="loading">Searching...</div>}
      
      <div className="search-results">
        {searchResults.map((user) => (
          <UserSearchResult key={user.id} user={user} />
        ))}
      </div>
    </div>
  );
};
```

### 2. User Profile Screen

Create a comprehensive user profile screen:

```typescript
// UserProfile.tsx
interface UserProfileProps {
  userId: string;
}

const UserProfile: React.FC<UserProfileProps> = ({ userId }) => {
  const [profile, setProfile] = useState<User | null>(null);
  const [pins, setPins] = useState<Pin[]>([]);
  const [collections, setCollections] = useState<Collection[]>([]);
  const [activeTab, setActiveTab] = useState<'pins' | 'collections'>('pins');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadUserProfile();
  }, [userId]);

  const loadUserProfile = async () => {
    setLoading(true);
    try {
      // Load profile
      const profileResponse = await fetch(`/api/users/${userId}/public_profile/`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });
      
      if (profileResponse.ok) {
        const profileData = await profileResponse.json();
        setProfile(profileData);
      }

      // Load pins and collections in parallel
      await Promise.all([
        loadUserPins(),
        loadUserCollections()
      ]);
    } catch (error) {
      console.error('Error loading user profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadUserPins = async () => {
    try {
      const response = await fetch(`/api/users/${userId}/public_pins/`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setPins(data.results || []);
      }
    } catch (error) {
      console.error('Error loading user pins:', error);
    }
  };

  const loadUserCollections = async () => {
    try {
      const response = await fetch(`/api/users/${userId}/public_collections/`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setCollections(data.results || []);
      }
    } catch (error) {
      console.error('Error loading user collections:', error);
    }
  };

  if (loading) {
    return <div className="loading">Loading profile...</div>;
  }

  if (!profile) {
    return <div className="error">Profile not found</div>;
  }

  return (
    <div className="user-profile">
      {/* Profile Header */}
      <div className="profile-header">
        <img 
          src={profile.profile_pic || '/default-avatar.png'} 
          alt={profile.username}
          className="profile-picture"
        />
        <div className="profile-info">
          <h1 className="username">{profile.username}</h1>
          {profile.bio && <p className="bio">{profile.bio}</p>}
          <div className="stats">
            <span className="stat">
              <strong>{profile.pin_count}</strong> Pins
            </span>
            <span className="stat">
              <strong>{profile.public_collection_count}</strong> Collections
            </span>
          </div>
          <p className="last-active">
            Last active: {formatDate(profile.last_active)}
          </p>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="tab-navigation">
        <button 
          className={`tab ${activeTab === 'pins' ? 'active' : ''}`}
          onClick={() => setActiveTab('pins')}
        >
          Pins ({profile.pin_count})
        </button>
        <button 
          className={`tab ${activeTab === 'collections' ? 'active' : ''}`}
          onClick={() => setActiveTab('collections')}
        >
          Collections ({profile.public_collection_count})
        </button>
      </div>

      {/* Content */}
      <div className="tab-content">
        {activeTab === 'pins' && (
          <div className="pins-grid">
            {pins.length > 0 ? (
              pins.map((pin) => (
                <PinCard key={pin.id} pin={pin} />
              ))
            ) : (
              <div className="empty-state">
                <p>No public pins yet</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'collections' && (
          <div className="collections-grid">
            {collections.length > 0 ? (
              collections.map((collection) => (
                <CollectionCard key={collection.id} collection={collection} />
              ))
            ) : (
              <div className="empty-state">
                <p>No public collections yet</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
```

### 3. Navigation Integration

Add navigation to user profiles from various parts of your app:

```typescript
// From pin cards
const PinCard: React.FC<{ pin: Pin }> = ({ pin }) => {
  const navigateToUserProfile = () => {
    // Navigate to user profile
    navigation.navigate('UserProfile', { userId: pin.owner.id });
  };

  return (
    <div className="pin-card">
      {/* Pin content */}
      <div className="pin-owner" onClick={navigateToUserProfile}>
        <img src={pin.owner.profile_pic} alt={pin.owner.username} />
        <span>{pin.owner.username}</span>
      </div>
    </div>
  );
};

// From search results
const UserSearchResult: React.FC<{ user: User }> = ({ user }) => {
  const navigateToProfile = () => {
    navigation.navigate('UserProfile', { userId: user.id });
  };

  return (
    <div className="search-result" onClick={navigateToProfile}>
      <img src={user.profile_pic} alt={user.username} />
      <div className="user-info">
        <h3>{user.username}</h3>
        <p>{user.bio}</p>
        <div className="stats">
          {user.pin_count} pins • {user.public_collection_count} collections
        </div>
      </div>
    </div>
  );
};
```

### 4. Pagination Handling

For pins and collections, implement pagination:

```typescript
const usePaginatedData = <T>(url: string) => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [nextUrl, setNextUrl] = useState<string | null>(url);

  const loadMore = async () => {
    if (!nextUrl || loading) return;

    setLoading(true);
    try {
      const response = await fetch(nextUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        setData(prev => [...prev, ...result.results]);
        setNextUrl(result.next);
        setHasMore(!!result.next);
      }
    } catch (error) {
      console.error('Error loading more data:', error);
    } finally {
      setLoading(false);
    }
  };

  const refresh = async () => {
    setData([]);
    setNextUrl(url);
    setHasMore(true);
    await loadMore();
  };

  return { data, loading, hasMore, loadMore, refresh };
};
```

### 5. Error Handling

Implement proper error handling:

```typescript
const handleApiError = (error: any, context: string) => {
  console.error(`Error in ${context}:`, error);
  
  if (error.status === 404) {
    // User not found
    showError('User not found');
  } else if (error.status === 403) {
    // Permission denied
    showError('You do not have permission to view this profile');
  } else {
    // Generic error
    showError('Something went wrong. Please try again.');
  }
};
```

## UI/UX Recommendations

### 1. Profile Screen Layout
- **Header Section**: Profile picture, username, bio, stats (pin count, collection count)
- **Tab Navigation**: Switch between "Pins" and "Collections"
- **Grid Layout**: Display pins/collections in a responsive grid
- **Empty States**: Show helpful messages when users have no public content

### 2. Search Experience
- **Debounced Search**: Wait 300ms after user stops typing before searching
- **Minimum Characters**: Require at least 2 characters before searching
- **Loading States**: Show loading indicators during search
- **Result Previews**: Show profile picture, username, bio, and stats in search results

### 3. Navigation Patterns
- **Clickable Usernames**: Make usernames clickable throughout the app (pin cards, collection cards, etc.)
- **Back Navigation**: Provide clear back navigation from profile screens
- **Deep Linking**: Support direct links to user profiles

### 4. Performance Considerations
- **Lazy Loading**: Load profile data, pins, and collections progressively
- **Image Optimization**: Use appropriate image sizes for profile pictures
- **Caching**: Cache user profile data to avoid repeated API calls
- **Pagination**: Implement infinite scroll or pagination for large lists

## Security Notes

1. **Authentication Required**: All endpoints require authentication
2. **Public Data Only**: Only public pins and collections are accessible
3. **No Private Information**: Email addresses and other private data are not exposed
4. **Rate Limiting**: Search endpoints may have rate limiting to prevent abuse

## Testing Checklist

- [ ] User search works with various queries
- [ ] Profile screens load correctly for different users
- [ ] Pagination works for pins and collections
- [ ] Empty states display properly
- [ ] Error handling works for non-existent users
- [ ] Navigation between screens works smoothly
- [ ] Loading states provide good user feedback
- [ ] Profile pictures and images load correctly
- [ ] Responsive design works on different screen sizes

## Example Styling (CSS)

```css
.user-profile {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  gap: 20px;
}

.profile-picture {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
}

.profile-info {
  flex: 1;
}

.username {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 10px 0;
}

.bio {
  color: #666;
  margin: 0 0 15px 0;
}

.stats {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
}

.stat {
  font-size: 14px;
  color: #888;
}

.tab-navigation {
  display: flex;
  border-bottom: 1px solid #eee;
  margin-bottom: 20px;
}

.tab {
  padding: 10px 20px;
  border: none;
  background: none;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.tab.active {
  border-bottom-color: #007bff;
  color: #007bff;
}

.pins-grid,
.collections-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #888;
}

.search-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
}

.search-results {
  margin-top: 10px;
}

.search-result {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  gap: 12px;
}

.search-result:hover {
  background-color: #f5f5f5;
}

.search-result img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
}

.user-info p {
  margin: 0 0 4px 0;
  color: #666;
  font-size: 14px;
}

.user-info .stats {
  font-size: 12px;
  color: #888;
}
```

This implementation provides a complete user profile viewing system that allows users to discover and explore each other's public content while maintaining privacy and security. 