# Last.fm Caching Service Implementation

## Overview

This document describes the implementation of a Last.fm caching service for BOPMaps. The service acts as a proxy between the mobile application and the Last.fm API, providing caching, rate limiting, and analytics functionality.

## Architecture

```
Flutter App ──► /api/lastfm?method=artist.getSimilar&artist=Coldplay&limit=20
                         │
                         ▼
Backend Cache  ──► (Redis / Database) ──► Last.fm REST API
```

## Features

- **API Proxy**: Proxies requests to Last.fm API with identical response format
- **Two-tier Caching**: Redis for fast access + Database for persistence
- **Rate Limiting**: Both client-side and Last.fm API rate limiting
- **Analytics**: Comprehensive usage statistics and cache performance metrics
- **Admin Interface**: Django admin for monitoring and management
- **Automatic Cleanup**: Management commands for cache maintenance

## API Endpoints

### Main API Endpoint

**GET** `/api/lastfm/`

Proxy requests to Last.fm API with caching.

**Parameters:**
- `method` (required): Last.fm API method (e.g., `artist.getSimilar`)
- `artist` (optional): Artist name
- `limit` (optional): Number of results (default: 20, max: 200)
- `mbid` (optional): MusicBrainz ID
- Other Last.fm API parameters

**Example:**
```bash
GET /api/lastfm/?method=artist.getSimilar&artist=Coldplay&limit=10
```

**Response:**
Returns the exact JSON structure from Last.fm API.

### Convenience Endpoints

**GET** `/api/lastfm/similar-artists/`

Simplified endpoint for getting similar artists.

**Parameters:**
- `artist` (required): Artist name
- `limit` (optional): Number of results (default: 20)

### Admin Endpoints

**GET** `/api/lastfm/admin/stats/`
Get cache statistics and performance metrics.

**GET** `/api/lastfm/admin/entries/`
List cache entries with filtering options.

**GET** `/api/lastfm/admin/usage/`
Get API usage statistics by date and method.

**DELETE** `/api/lastfm/admin/cleanup/`
Clean up expired cache entries.

## Configuration

### Environment Variables

Add to your `.env` file:

```bash
# Last.fm API Key (required)
LASTFM_API_KEY=your_lastfm_api_key_here

# Redis configuration (optional, defaults to localhost)
REDIS_URL=redis://localhost:6379/1
```

### Django Settings

The service is automatically configured when you add `'lastfm'` to `INSTALLED_APPS`. Key settings:

```python
# Added to INSTALLED_APPS
'lastfm',  # Last.fm caching service

# Rate limiting configuration
REST_FRAMEWORK = {
    'DEFAULT_THROTTLE_RATES': {
        'lastfm': '60/min'
    },
}

# Last.fm API key
LASTFM_API_KEY = config('LASTFM_API_KEY', default='')
```

## Implementation Details

### Models

#### LastFmCacheEntry
Stores cached Last.fm API responses with metadata:
- `cache_key`: Unique identifier for the cache entry
- `method`: Last.fm API method
- `response_data`: JSON response from Last.fm
- `expires_at`: Cache expiration timestamp
- `hit_count`: Number of times this cache entry has been accessed

#### LastFmApiUsage
Tracks API usage statistics by date and method:
- `date`: Date of usage
- `method`: Last.fm API method
- `total_requests`: Total requests made
- `cache_hits`/`cache_misses`: Cache performance metrics
- `average_response_time`: API performance metrics

#### LastFmRateLimit
Tracks rate limiting to Last.fm API:
- `window_start`: Start of the rate limit window
- `requests_made`: Number of requests in this window

### Caching Strategy

1. **Key Generation**: Cache keys are generated using SHA1 hash of sorted query parameters
2. **Two-tier Storage**: 
   - Redis for fast access (6-hour TTL)
   - Database for persistence and analytics
3. **Cache Lookup Flow**:
   - Check Redis first
   - If miss, check database
   - If hit in database, restore to Redis
   - If miss in both, make API request and cache result

### Rate Limiting

- **Client Rate Limiting**: 60 requests/minute per IP address
- **Last.fm API Rate Limiting**: 300 requests/minute (5 req/sec average)
- **Rate Limit Windows**: 1-minute sliding windows

## Database Schema

```sql
-- Cache entries
CREATE TABLE lastfm_lastfmcacheentry (
    id BIGINT PRIMARY KEY,
    cache_key VARCHAR(255) UNIQUE,
    method VARCHAR(100),
    query_hash VARCHAR(64),
    response_data JSONB,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    expires_at TIMESTAMP,
    hit_count INTEGER,
    last_accessed TIMESTAMP
);

-- API usage statistics
CREATE TABLE lastfm_lastfmapiusage (
    id BIGINT PRIMARY KEY,
    date DATE,
    method VARCHAR(100),
    total_requests INTEGER,
    cache_hits INTEGER,
    cache_misses INTEGER,
    api_calls_made INTEGER,
    average_response_time REAL,
    UNIQUE(date, method)
);

-- Rate limiting
CREATE TABLE lastfm_lastfmratelimit (
    id BIGINT PRIMARY KEY,
    window_start TIMESTAMP,
    method VARCHAR(100),
    requests_made INTEGER
);
```

## Usage Examples

### Basic Similar Artists Request

```bash
curl "http://localhost:8000/api/lastfm/?method=artist.getSimilar&artist=Radiohead&limit=5"
```

### Using the Convenience Endpoint

```bash
curl "http://localhost:8000/api/lastfm/similar-artists/?artist=The%20Beatles&limit=10"
```

### Getting Cache Statistics

```bash
curl "http://localhost:8000/api/lastfm/admin/stats/"
```

Response:
```json
{
  "today_stats": {
    "total_requests": 150,
    "total_cache_hits": 120,
    "total_cache_misses": 30,
    "total_api_calls": 30
  },
  "total_cache_entries": 500,
  "expired_entries": 50,
  "active_entries": 450
}
```

## Management Commands

### Clean Up Expired Cache

```bash
# Dry run to see what would be deleted
python manage.py cleanup_lastfm_cache --dry-run

# Clean up expired entries
python manage.py cleanup_lastfm_cache

# Clean up entries older than 3 days
python manage.py cleanup_lastfm_cache --days 3

# Clean up rate limit records older than 12 hours
python manage.py cleanup_lastfm_cache --rate-limit-hours 12
```

## Error Handling

The service handles various error conditions:

### Client Errors (400)
- Missing required parameters
- Invalid method names
- Invalid parameter values

### Server Errors (502)
- Last.fm API unavailable
- Invalid API responses
- Network timeouts

### Rate Limiting (429)
- Client rate limit exceeded
- Returns appropriate retry headers

### Example Error Response

```json
{
  "error": 400,
  "message": "Invalid request parameters",
  "details": {
    "method": ["This field is required."]
  }
}
```

## Performance Considerations

### Cache Hit Rates
- Target: >80% cache hit rate for popular artists
- Monitoring: Track via `LastFmApiUsage` model

### Response Times
- Cached responses: <50ms
- API requests: 200-1000ms (depending on Last.fm)
- Cache miss penalty: ~500ms average

### Memory Usage
- Redis memory usage scales with cache size
- Database storage for long-term persistence
- Automatic cleanup prevents unbounded growth

## Monitoring and Analytics

### Cache Performance Metrics
- Cache hit/miss ratios
- Average response times
- Popular artists and methods
- Usage patterns over time

### Django Admin Interface
Access via `/admin/lastfm/` to:
- View cache entries and their performance
- Monitor API usage statistics
- Manage rate limiting
- Perform cache cleanup operations

### Logging
Comprehensive logging for:
- Cache hits/misses
- API request performance
- Error conditions
- Rate limiting events

Log files location: `logs/bopmaps.log`

## Security Considerations

### API Key Protection
- Last.fm API key stored securely in environment variables
- Never exposed to client applications
- Rotation capability through environment updates

### Rate Limiting
- Prevents abuse of both our service and Last.fm API
- Configurable limits per client type
- Graceful degradation under high load

### Input Validation
- All input parameters validated
- SQL injection prevention through Django ORM
- XSS protection in API responses

## Testing

### Automated Testing

Run the test script:
```bash
python test_lastfm_cache.py
```

This tests:
- Basic API functionality
- Cache performance
- Error handling
- Admin endpoints

### Manual Testing

1. **Test Cache Functionality**:
   ```bash
   # First request (should be slow)
   time curl "http://localhost:8000/api/lastfm/?method=artist.getSimilar&artist=Coldplay"
   
   # Second request (should be fast)
   time curl "http://localhost:8000/api/lastfm/?method=artist.getSimilar&artist=Coldplay"
   ```

2. **Test Rate Limiting**:
   ```bash
   # Make rapid requests to trigger rate limiting
   for i in {1..70}; do curl "http://localhost:8000/api/lastfm/?method=artist.getSimilar&artist=TestArtist$i"; done
   ```

## Deployment

### Production Checklist

1. **Environment Variables**:
   - Set `LASTFM_API_KEY` in production environment
   - Configure `REDIS_URL` for production Redis instance

2. **Database Migrations**:
   ```bash
   python manage.py migrate
   ```

3. **Redis Configuration**:
   - Ensure Redis is running and accessible
   - Configure appropriate memory limits
   - Set up Redis persistence if desired

4. **Monitoring Setup**:
   - Configure log aggregation
   - Set up cache performance monitoring
   - Create alerts for high error rates

5. **Performance Tuning**:
   - Adjust cache TTL based on usage patterns
   - Optimize Redis memory settings
   - Monitor and adjust rate limits

### Docker Configuration

If using Docker, add Redis service:

```yaml
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    
volumes:
  redis_data:
```

## Future Enhancements

### Planned Features
- **GraphQL Endpoint**: Expose curated fields instead of full Last.fm payload
- **Advanced Cache Invalidation**: Purge on artist release events
- **Edge Caching**: Cloudflare Workers KV for ultra-low latency
- **Metrics Dashboard**: Grafana integration for monitoring

### Scalability Improvements
- **Horizontal Scaling**: Support for multiple Redis instances
- **Connection Pooling**: Optimize database connections
- **Background Processing**: Async cache warming for popular artists

## Troubleshooting

### Common Issues

1. **"LASTFM_API_KEY not configured"**
   - Add `LASTFM_API_KEY=your_key` to `.env` file
   - Restart Django server

2. **Cache not working (Redis errors)**
   - Ensure Redis is running: `redis-cli ping`
   - Check Redis connection settings in `REDIS_URL`

3. **Rate limiting too aggressive**
   - Adjust `THROTTLE_RATES` in Django settings
   - Monitor usage patterns and adjust accordingly

4. **High memory usage**
   - Run cache cleanup: `python manage.py cleanup_lastfm_cache`
   - Consider reducing cache TTL

### Debug Mode

Enable debug logging by adding to Django settings:

```python
LOGGING['loggers']['lastfm'] = {
    'handlers': ['console'],
    'level': 'DEBUG',
    'propagate': False,
}
```

## API Documentation

Complete API documentation is available through:
- Swagger UI: `http://localhost:8000/api/schema/swagger-ui/`
- ReDoc: `http://localhost:8000/api/schema/redoc/`

Tags in documentation:
- **Last.fm Cache API**: Main endpoints for client applications
- **Last.fm Cache Admin**: Administrative and monitoring endpoints 