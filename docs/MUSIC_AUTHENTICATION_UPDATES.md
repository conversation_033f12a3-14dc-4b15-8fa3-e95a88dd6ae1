# Music Authentication Backend Updates

## Overview
This document summarizes the backend implementation for improved music service authentication, addressing the issues with Spotify token refresh and adding Apple Music user token synchronization.

## New Endpoints Added

### 1. Apple Music Token Endpoint (Flutter App Compatible)
- **Endpoint**: `/api/music/auth/apple/token/`
- **Method**: `POST`
- **Authentication**: Required (JWT)
- **Purpose**: Handle Apple Music tokens from mobile app (matches Flutter app expectations)
- **Request Body** (either token required):
  ```json
  {
    "developer_token": "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCIs...",
    "music_user_token": "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCIs..."
  }
  ```
- **Response (200 OK)** with user token:
  ```json
  {
    "message": "Apple Music token processed successfully",
    "user": {
      "id": 123,
      "username": "john_doe", 
      "apple_music_connected": true
    },
    "service": {
      "service_type": "apple",
      "connected_at": "2024-01-01T12:00:00Z",
      "expires_at": "2024-07-01T12:00:00Z",
      "is_new": true
    }
  }
  ```

### 2. Apple Music User Token Sync
- **Endpoint**: `/api/music/auth/apple/sync_user_token/`
- **Method**: `POST`
- **Authentication**: Required (JWT)
- **Purpose**: Dedicated endpoint for syncing Apple Music user tokens
- **Request Body**:
  ```json
  {
    "music_user_token": "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCIs..."
  }
  ```
- **Response (200 OK)**:
  ```json
  {
    "message": "Apple Music user token synced successfully",
    "user": {
      "id": 123,
      "username": "john_doe", 
      "apple_music_connected": true
    },
    "service": {
      "service_type": "apple",
      "connected_at": "2024-01-01T12:00:00Z",
      "expires_at": "2024-07-01T12:00:00Z",
      "is_new": true
    }
  }
  ```

### 3. Spotify Backend Token Refresh
- **Endpoint**: `/api/music/auth/spotify/refresh/`
- **Method**: `POST`
- **Authentication**: Required (JWT)
- **Purpose**: Refresh Spotify access tokens using stored refresh tokens
- **Request Body**: Empty (uses stored refresh token)
- **Response (200 OK)**:
  ```json
  {
    "access_token": "BQB_new_access_token",
    "refresh_token": "AQA_new_refresh_token", 
    "expires_in": 3600,
    "token_type": "Bearer",
    "scope": "user-read-private user-read-email"
  }
  ```

## Files Modified

### 1. `music/urls.py`
- Added imports for `apple_music_user_token_sync`, `apple_music_token`, and `spotify_refresh_token`
- Added URL patterns:
  - `path('auth/apple/token/', apple_music_token, name='apple-music-token')`
  - `path('auth/apple/sync_user_token/', apple_music_user_token_sync, name='apple-music-user-token-sync')`
  - `path('auth/spotify/refresh/', spotify_refresh_token, name='spotify-refresh-token')`

### 2. `music/views.py`
- **Added `apple_music_token` function**:
  - Handles both developer tokens and user tokens
  - Validates and stores user tokens when provided
  - Updates user's `apple_music_connected` status
  - Flexible endpoint that matches Flutter app expectations

- **Added `apple_music_user_token_sync` function**:
  - Dedicated endpoint for user token synchronization
  - Validates incoming Apple Music user tokens
  - Stores tokens in `MusicService` model
  - Handles validation errors and exceptions

- **Added `spotify_refresh_token` function**:
  - Retrieves stored refresh token for authenticated user
  - Makes request to Spotify API for token refresh
  - Updates stored tokens in database
  - Handles refresh token expiry by clearing service
  - Returns new tokens to mobile app

### 3. `music/tests.py`
- **Added `AppleMusicTokenTests` class** (9 test cases):
  - User token and developer token processing
  - Token priority when both are provided
  - Missing token validation
  - Invalid token handling
  - Authentication requirements
  - Exception handling
  - Service update scenarios

- **Added `AppleMusicUserTokenSyncTests` class** (8 test cases):
  - Success scenarios (new and existing token updates)
  - Missing/empty token validation
  - Invalid token handling
  - Authentication requirements
  - Exception handling
  - HTTP method restrictions

- **Added `SpotifyBackendRefreshTests` class** (10 test cases):
  - Successful token refresh scenarios
  - Missing refresh token handling
  - Invalid refresh token error handling
  - Network error handling
  - Authentication requirements
  - Proper request parameter validation

## Error Handling

### Apple Music Token Errors
- `400 Bad Request`: Missing both `developer_token` and `music_user_token`
- `400 Bad Request`: Invalid Apple Music user token (validation failed)
- `401 Unauthorized`: Authentication credentials not provided
- `500 Internal Server Error`: Validation service exceptions

### Apple Music Sync Errors
- `400 Bad Request`: Missing or empty `music_user_token`
- `400 Bad Request`: Invalid Apple Music user token (validation failed)
- `401 Unauthorized`: Authentication credentials not provided
- `500 Internal Server Error`: Validation service exceptions

### Spotify Refresh Errors
- `400 Bad Request`: Spotify service not connected
- `400 Bad Request`: No refresh token available
- `401 Unauthorized`: Invalid refresh token (service cleared, re-auth required)
- `500 Internal Server Error`: Network errors or Spotify API failures

## Integration with Flutter App

### Required Constants
Add to `lib/config/constants.dart`:
```dart
static const String appleMusicTokenEndpoint = '/api/music/auth/apple/token/';
static const String appleMusicUserTokenSyncEndpoint = '/api/music/auth/apple/sync_user_token/';
static const String spotifyRefreshEndpoint = '/api/music/auth/spotify/refresh/';
```

### Apple Music Integration
**Option 1: Use existing Flutter endpoint pattern**
1. Send POST request to `/api/music/auth/apple/token/`
2. Include `music_user_token` and/or `developer_token` in request body
3. Backend processes and stores the tokens

**Option 2: Use dedicated sync endpoint**
1. After obtaining user token from MusicKit in `AppleMusicTab`
2. Call `appleMusicService.sendUserTokenToBackend(userToken)`
3. Service sends POST request to `/api/music/auth/apple/sync_user_token/`
4. Backend validates and stores the token

### Spotify Token Refresh Integration
1. When Spotify API returns 401 (token expired)
2. Call backend endpoint `/api/music/auth/spotify/refresh/`
3. Backend uses stored refresh token to get new tokens
4. Returns fresh tokens to mobile app
5. App updates its stored tokens

## Security Considerations

### Apple Music
- Tokens are validated using `AppleMusicService.validate_user_token()`
- Long-lived tokens (180 days expiration)
- User authentication required for all endpoints
- Flexible token handling (developer + user tokens)

### Spotify
- Refresh tokens stored securely in database
- Automatic cleanup of invalid refresh tokens
- Rate limiting applied to PKCE endpoints
- Comprehensive logging for debugging

## Testing

All new endpoints are thoroughly tested:
- **73 total tests** in music app (all passing)
- **27 new tests** added for the new functionality
- **Mock-based testing** for external API calls
- **Error scenario coverage** for robust handling

## Deployment Notes

1. **Database**: No migrations required (uses existing `MusicService` model)
2. **Environment**: Ensure `SPOTIFY_CLIENT_ID`, `SPOTIFY_CLIENT_SECRET`, and `SPOTIFY_TOKEN_URL` are configured
3. **Dependencies**: No new Python packages required
4. **Monitoring**: Check logs for authentication flow issues

## Benefits

1. **Flutter App Compatibility**: Added endpoint that matches existing app expectations
2. **Improved Reliability**: Backend-managed token refresh reduces mobile app complexity
3. **Centralized Control**: All music service tokens managed server-side
4. **Better Error Handling**: Comprehensive error responses for troubleshooting
5. **Security**: Sensitive refresh tokens stored securely on server
6. **Consistency**: Unified approach for both Spotify and Apple Music
7. **Flexibility**: Multiple endpoints for different integration patterns

## Next Steps

1. Deploy backend changes
2. Update Flutter app constants (if needed)
3. Test complete authentication flows
4. Monitor for any token refresh issues
5. Consider adding similar backend refresh for Apple Music if needed

---

**Created**: June 2025  
**Last Updated**: June 2025  
**Status**: Ready for deployment 