# Music Preferences API

## Overview
The Music Preferences API allows users to save their music preferences including top genres, top artists, artist genres mapping, artist image URLs, and Spotify IDs.

## Endpoint
```
PATCH /api/users/music-preferences/
```

## Authentication
Requires JWT authentication token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Request Body
All fields are optional and can be sent individually or together:

```json
{
  "top_genres": ["pop", "rock", "electronic"],
  "top_artists": ["Taylor Swift", "The Beatles", "Daft Punk"],
  "artist_genres": {
    "Taylor Swift": ["pop", "country"],
    "The Beatles": ["rock", "pop"],
    "Daft Punk": ["electronic", "house"]
  },
  "artist_image_urls": {
    "Taylor Swift": "https://i.scdn.co/image/ab6761610000e5eb859e4c14fa59296c8649e0e4",
    "The Beatles": "https://i.scdn.co/image/ab6761610000e5ebe9348cc01ff5d55971b22433",
    "Daft Punk": "https://i.scdn.co/image/ab6761610000e5eb0326b42c7c0e8b5e5b6b5b5b"
  },
  "artist_spotify_ids": {
    "Taylor Swift": "06HL4z0CvFAxyc27GXpf02",
    "The Beatles": "3WrFJ7ztbogyGnTHbHJFl2",
    "Daft Punk": "4tZwfgrHOc3mvqYlEYSvVi"
  }
}
```

## Field Descriptions

### top_genres
- **Type**: Array of strings
- **Description**: User's preferred music genres
- **Example**: `["pop", "rock", "hip-hop"]`

### top_artists
- **Type**: Array of strings  
- **Description**: User's favorite artists
- **Example**: `["Taylor Swift", "Drake", "Billie Eilish"]`

### artist_genres
- **Type**: Object (mapping)
- **Description**: Maps artist names to their associated genres
- **Example**: `{"Drake": ["hip-hop", "rap"], "Taylor Swift": ["pop", "country"]}`

### artist_image_urls
- **Type**: Object (mapping)
- **Description**: Maps artist names to their image URLs (typically from Spotify)
- **Example**: `{"Drake": "https://i.scdn.co/image/ab6761610000e5eb..."}`

### artist_spotify_ids
- **Type**: Object (mapping)
- **Description**: Maps artist names to their Spotify IDs for API integration
- **Example**: `{"Drake": "3TVXtAsR1Inumwj472S9r4"}`

## Response

### Success (200 OK)
```json
{
  "top_genres": ["pop", "rock", "electronic"],
  "top_artists": ["Taylor Swift", "The Beatles", "Daft Punk"],
  "artist_genres": {
    "Taylor Swift": ["pop", "country"],
    "The Beatles": ["rock", "pop"],
    "Daft Punk": ["electronic", "house"]
  },
  "artist_image_urls": {
    "Taylor Swift": "https://i.scdn.co/image/ab6761610000e5eb859e4c14fa59296c8649e0e4",
    "The Beatles": "https://i.scdn.co/image/ab6761610000e5ebe9348cc01ff5d55971b22433",
    "Daft Punk": "https://i.scdn.co/image/ab6761610000e5eb0326b42c7c0e8b5e5b6b5b5b"
  },
  "artist_spotify_ids": {
    "Taylor Swift": "06HL4z0CvFAxyc27GXpf02",
    "The Beatles": "3WrFJ7ztbogyGnTHbHJFl2",
    "Daft Punk": "4tZwfgrHOc3mvqYlEYSvVi"
  }
}
```

### Error (400 Bad Request)
```json
{
  "error": "Validation error message"
}
```

### Error (401 Unauthorized)
```json
{
  "detail": "Authentication credentials were not provided."
}
```

## Flutter Integration

The API matches the Flutter app's `saveMusicPreferences` method signature:

```dart
Future<Map<String, dynamic>> saveMusicPreferences({
  required List<String> topGenres,
  required List<String> topArtists,
  Map<String, List<String>> artistGenres = const {},
  Map<String, String> artistImageUrls = const {},
  Map<String, String> artistSpotifyIds = const {},
}) async {
  // Implementation matches the API structure above
}
```

## Usage Examples

### Save basic preferences
```bash
curl -X PATCH http://localhost:8000/api/users/music-preferences/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "top_genres": ["pop", "rock"],
    "top_artists": ["Taylor Swift", "The Beatles"]
  }'
```

### Save complete preferences with Spotify data
```bash
curl -X PATCH http://localhost:8000/api/users/music-preferences/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "top_genres": ["pop", "rock"],
    "top_artists": ["Taylor Swift"],
    "artist_genres": {"Taylor Swift": ["pop", "country"]},
    "artist_image_urls": {"Taylor Swift": "https://i.scdn.co/image/..."},
    "artist_spotify_ids": {"Taylor Swift": "06HL4z0CvFAxyc27GXpf02"}
  }'
```

## Notes

- All fields are optional and can be updated independently
- The API uses PATCH method for partial updates
- Data is stored as JSON fields in the database
- Artist names are used as keys in the mapping objects
- Image URLs should be valid HTTP/HTTPS URLs
- Spotify IDs should be valid Spotify artist IDs
