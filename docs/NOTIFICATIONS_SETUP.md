# BOP Maps Notification System Documentation

## Overview

The BOP Maps notification system is a comprehensive push notification solution built with Django and OneSignal integration. This system handles all user notifications across the app, from pin interactions to challenge updates and social features.

## Production-Ready Features

### Core Components

1. **Notification Manager** (`notifications/manager.py`)
   - Centralized notification creation and delivery
   - User preference checking and quiet hours support
   - Batching system to prevent spam
   - OneSignal integration for push notifications

2. **Models** (`notifications/models.py`)
   - `Notification`: Individual notification records
   - `NotificationTemplate`: Reusable templates with dynamic content
   - `UserNotificationSettings`: User preferences and settings
   - `NotificationBatch`: Batching system for grouped notifications
   - `OneSignalPlayerID`: Device registration for push notifications

3. **API Endpoints** (`notifications/views.py`)
   - Full REST API for notification management
   - User settings management
   - OneSignal player registration
   - Statistics and analytics
   - Bulk operations support

4. **Utility Functions** (`notifications/utils.py`)
   - Pre-built notification triggers for common app events
   - Geo-fenced notifications
   - Batching utilities
   - Template rendering helpers

### Supported Notification Categories

- **Map & Pins**: Pin likes, trending pins, nearby friends
- **Social & Friends**: Friend requests, music chat, activity digests
- **Music**: New releases, weekly mixes, live listening sessions
- **Challenges & Gamification**: Challenge progress, achievements, level-ups
- **Collections**: Milestones, collaborative updates
- **Exploration**: New AR pins, seasonal events, city trending
- **Customization**: Skin unlocks, limited offers
- **General**: Welcome messages, retention campaigns

### Key Features

#### Smart Batching
- Prevents notification spam by grouping similar notifications
- Configurable batch size and time windows
- Automatic summary generation for batched notifications

#### User Preferences
- Granular category-level controls
- Quiet hours support with timezone awareness
- Daily notification limits
- Push and email preferences

#### OneSignal Integration
- Multi-platform support (iOS, Android, Web)
- Player ID management
- Delivery tracking and analytics
- Bulk notification support

#### Template System
- Dynamic content generation using Django templates
- Reusable templates for consistent messaging
- Variable substitution for personalized content

## Environment Configuration

### Required Environment Variables

```bash
# OneSignal Configuration
ONESIGNAL_APP_ID=your_onesignal_app_id
ONESIGNAL_REST_API_KEY=your_onesignal_rest_api_key

# Optional: OneSignal User Auth Key (for advanced features)
ONESIGNAL_USER_AUTH_KEY=your_onesignal_user_auth_key
```

### Django Settings

```python
# In settings.py
INSTALLED_APPS = [
    # ... other apps
    'notifications',
]

# OneSignal Settings
ONESIGNAL_APP_ID = os.environ.get('ONESIGNAL_APP_ID')
ONESIGNAL_REST_API_KEY = os.environ.get('ONESIGNAL_REST_API_KEY')
ONESIGNAL_USER_AUTH_KEY = os.environ.get('ONESIGNAL_USER_AUTH_KEY')
```

## API Usage

### Core Endpoints

- `GET /api/notifications/notifications/` - List user notifications
- `POST /api/notifications/notifications/{id}/mark_read/` - Mark as read
- `POST /api/notifications/notifications/mark_all_read/` - Mark all as read
- `DELETE /api/notifications/notifications/bulk_delete/` - Bulk delete
- `GET /api/notifications/notifications/stats/` - Get statistics

### Settings Management

- `GET /api/notifications/settings/` - Get user settings
- `PUT /api/notifications/settings/` - Update user settings

### OneSignal Integration

- `POST /api/notifications/onesignal/register_player/` - Register device
- `POST /api/notifications/onesignal/unregister_player/` - Unregister device
- `GET /api/notifications/onesignal/players/` - List user devices

### Admin Endpoints

- `POST /api/notifications/send-bulk/` - Send bulk notifications
- `POST /api/notifications/send-test/` - Send test notification
- `POST /api/notifications/process-batched/` - Process pending batches

## Usage Examples

### Creating Notifications Programmatically

```python
from notifications.manager import notification_manager
from notifications.models import NotificationType, NotificationCategory, NotificationPriority

# Simple notification
notification_manager.create_notification(
    recipient=user,
    notification_type=NotificationType.PIN_LIKE,
    category=NotificationCategory.MAP,
    priority=NotificationPriority.MEDIUM,
    title="Your pin was liked! 👍",
    message=f"{liker.username} liked your pin",
    action_data={
        'pin_id': pin.id,
        'type': 'pin_detail'
    }
)

# Bulk notification
notification_manager.send_bulk_notification(
    recipients=[user1, user2, user3],
    notification_type=NotificationType.SEASONAL_EVENT,
    category=NotificationCategory.EXPLORATION,
    priority=NotificationPriority.HIGH,
    title="Special event live! 🎉",
    message="Halloween map layer is now available!",
    action_data={'type': 'map_explore'}
)
```

### Using Utility Functions

```python
from notifications.utils import (
    notify_pin_liked, notify_friend_request, 
    notify_challenge_complete, notify_skin_unlocked
)

# Pin interaction
notify_pin_liked(pin, liked_by_user, like_count=5)

# Social interaction
notify_friend_request(recipient=user, sender=friend)

# Achievement
notify_challenge_complete(user, "Weekly Explorer", xp_earned=100)

# Customization
notify_skin_unlocked(user, "Neon Glow", unlock_reason="Challenge completion")
```

### Django Signals Integration

The system automatically handles notifications for common events through Django signals:

```python
# Automatically triggered when:
# - Pin is liked/collected/shared
# - Friend request is sent/accepted
# - Achievement is unlocked
# - User levels up
# - Collection reaches milestone
```

## Management Commands

### Process Notifications

```bash
# Process pending batched notifications
python manage.py process_notifications

# With verbose output
python manage.py process_notifications --verbose

# Dry run (show what would be processed)
python manage.py process_notifications --dry-run
```

## Testing

The system includes comprehensive tests covering:

- Model functionality and relationships
- API endpoints and authentication
- Notification manager operations
- OneSignal service integration
- Utility functions
- User preferences and quiet hours

```bash
# Run all notification tests
python manage.py test notifications

# Run with coverage
coverage run --source='notifications' manage.py test notifications
coverage report
```

## Database Schema

### Migrations

The notification system includes proper database migrations:

1. `0001_initial.py` - Initial schema creation
2. `0002_usernotificationsettings_ai_recommendation_time_and_more.py` - User settings
3. `0003_remove_ai_features.py` - Production cleanup (AI features removed)

### Key Indexes

- User notifications by creation date
- Notification type and category lookups
- Read status filtering
- Batch processing optimization

## Performance Considerations

### Optimization Features

1. **Database Indexing**: Strategic indexes for common queries
2. **Bulk Operations**: Efficient bulk notification creation and sending
3. **Batching**: Reduces notification volume and improves user experience
4. **Lazy Loading**: OneSignal players loaded only when needed
5. **Caching**: User settings cached to reduce database queries

### Monitoring

- OneSignal delivery tracking
- Failed notification logging
- User engagement statistics
- System performance metrics

## Security

### Authentication
- All API endpoints require authentication
- User can only access their own notifications and settings

### Privacy
- Users can disable notification categories
- Quiet hours support for user comfort
- Daily limits prevent spam

### Data Protection
- Minimal data storage in OneSignal
- User consent respected for all notifications
- Easy data deletion support

## Deployment

### Production Checklist

1. ✅ Set OneSignal environment variables
2. ✅ Run database migrations
3. ✅ Configure user notification settings signals
4. ✅ Set up periodic task for batch processing
5. ✅ Configure logging for notification errors
6. ✅ Test OneSignal integration with real devices

### Monitoring and Logs

- Monitor OneSignal dashboard for delivery rates
- Check Django logs for notification creation errors
- Track user engagement through notification statistics API
- Set up alerts for failed notification batches

## Troubleshooting

### Common Issues

1. **OneSignal Connection Errors**
   - Verify API keys are correct
   - Check network connectivity
   - Ensure OneSignal app is properly configured

2. **Missing Notifications**
   - Check user notification settings
   - Verify quiet hours configuration
   - Confirm OneSignal player registration

3. **Performance Issues**
   - Monitor batch processing frequency
   - Check database query performance
   - Optimize OneSignal bulk operations

### Debug Mode

```python
# Enable debug logging
import logging
logging.getLogger('notifications').setLevel(logging.DEBUG)
```

## Flutter Frontend Integration

### Overview

Complete integration guide for Flutter applications using the Provider pattern with comprehensive API documentation and type definitions.

### Dependencies

Add these to your `pubspec.yaml`:

```yaml
dependencies:
  flutter: ^3.0.0
  provider: ^6.0.0
  http: ^0.13.0
  onesignal_flutter: ^3.5.0
  json_annotation: ^4.8.0

dev_dependencies:
  json_serializable: ^6.6.0
  build_runner: ^2.3.0
```

### Model Definitions

#### NotificationModel

```dart
// lib/models/notification_model.dart
import 'package:json_annotation/json_annotation.dart';

part 'notification_model.g.dart';

@JsonSerializable()
class NotificationModel {
  final String id;
  final NotificationType type;
  final NotificationCategory category;
  final NotificationPriority priority;
  final String title;
  final String message;
  final DateTime timestamp;
  final bool isRead;
  final String? imageUrl;
  final Map<String, dynamic>? actionData;
  final String? onesignalId;
  final DateTime? readAt;

  const NotificationModel({
    required this.id,
    required this.type,
    required this.category,
    required this.priority,
    required this.title,
    required this.message,
    required this.timestamp,
    required this.isRead,
    this.imageUrl,
    this.actionData,
    this.onesignalId,
    this.readAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationModelToJson(this);

  NotificationModel copyWith({
    String? id,
    NotificationType? type,
    NotificationCategory? category,
    NotificationPriority? priority,
    String? title,
    String? message,
    DateTime? timestamp,
    bool? isRead,
    String? imageUrl,
    Map<String, dynamic>? actionData,
    String? onesignalId,
    DateTime? readAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      type: type ?? this.type,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      title: title ?? this.title,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      imageUrl: imageUrl ?? this.imageUrl,
      actionData: actionData ?? this.actionData,
      onesignalId: onesignalId ?? this.onesignalId,
      readAt: readAt ?? this.readAt,
    );
  }
}

enum NotificationType {
  @JsonValue('pin_like')
  pinLike,
  @JsonValue('pin_comment')
  pinComment,
  @JsonValue('pin_trending')
  pinTrending,
  @JsonValue('friend_nearby')
  friendNearby,
  @JsonValue('friend_request')
  friendRequest,
  @JsonValue('friend_accepted')
  friendAccepted,
  @JsonValue('music_chat')
  musicChat,
  @JsonValue('new_release')
  newRelease,
  @JsonValue('challenge_complete')
  challengeComplete,
  @JsonValue('challenge_available')
  challengeAvailable,
  @JsonValue('level_up')
  levelUp,
  @JsonValue('achievement_unlocked')
  achievementUnlocked,
  @JsonValue('collection_update')
  collectionUpdate,
  @JsonValue('skin_unlocked')
  skinUnlocked,
  @JsonValue('general')
  general,
}

enum NotificationCategory {
  @JsonValue('all')
  all,
  @JsonValue('map')
  map,
  @JsonValue('social')
  social,
  @JsonValue('music')
  music,
  @JsonValue('gamification')
  gamification,
  @JsonValue('collection')
  collection,
  @JsonValue('exploration')
  exploration,
  @JsonValue('customization')
  customization,
  @JsonValue('general')
  general,
}

enum NotificationPriority {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('urgent')
  urgent,
}
```

#### UserNotificationSettings

```dart
// lib/models/user_notification_settings.dart
import 'package:json_annotation/json_annotation.dart';

part 'user_notification_settings.g.dart';

@JsonSerializable()
class UserNotificationSettings {
  final bool mapEnabled;
  final bool socialEnabled;
  final bool musicEnabled;
  final bool gamificationEnabled;
  final bool collectionEnabled;
  final bool explorationEnabled;
  final bool customizationEnabled;
  final bool generalEnabled;
  final bool pushNotificationsEnabled;
  final bool emailNotificationsEnabled;
  final bool quietHoursEnabled;
  final String quietStartTime;
  final String quietEndTime;
  final int maxDailyNotifications;

  const UserNotificationSettings({
    required this.mapEnabled,
    required this.socialEnabled,
    required this.musicEnabled,
    required this.gamificationEnabled,
    required this.collectionEnabled,
    required this.explorationEnabled,
    required this.customizationEnabled,
    required this.generalEnabled,
    required this.pushNotificationsEnabled,
    required this.emailNotificationsEnabled,
    required this.quietHoursEnabled,
    required this.quietStartTime,
    required this.quietEndTime,
    required this.maxDailyNotifications,
  });

  factory UserNotificationSettings.fromJson(Map<String, dynamic> json) =>
      _$UserNotificationSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$UserNotificationSettingsToJson(this);
}
```

### API Service

#### NotificationApiService

```dart
// lib/services/notifications/notification_api_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../models/notification_model.dart';
import '../../models/user_notification_settings.dart';

class NotificationApiService {
  final String baseUrl;
  final String Function() getAuthToken;

  NotificationApiService({
    required this.baseUrl,
    required this.getAuthToken,
  });

  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ${getAuthToken()}',
  };

  /// GET /api/notifications/notifications/
  /// Returns: NotificationListResponse
  Future<NotificationListResponse> getNotifications({
    NotificationCategory? category,
    bool? isRead,
    int limit = 50,
    int offset = 0,
  }) async {
    final queryParams = <String, String>{
      'limit': limit.toString(),
      'offset': offset.toString(),
    };

    if (category != null && category != NotificationCategory.all) {
      queryParams['category'] = category.name;
    }
    if (isRead != null) {
      queryParams['is_read'] = isRead.toString();
    }

    final uri = Uri.parse('$baseUrl/api/notifications/notifications/')
        .replace(queryParameters: queryParams);

    final response = await http.get(uri, headers: _headers);

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return NotificationListResponse.fromJson(data);
    } else {
      throw ApiException(
        statusCode: response.statusCode,
        message: 'Failed to load notifications',
        details: response.body,
      );
    }
  }

  /// POST /api/notifications/notifications/{id}/mark_read/
  /// Returns: SuccessResponse
  Future<SuccessResponse> markAsRead(String notificationId) async {
    final uri = Uri.parse('$baseUrl/api/notifications/notifications/$notificationId/mark_read/');

    final response = await http.post(uri, headers: _headers);

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return SuccessResponse.fromJson(data);
    } else {
      throw ApiException(
        statusCode: response.statusCode,
        message: 'Failed to mark notification as read',
        details: response.body,
      );
    }
  }

  /// POST /api/notifications/notifications/mark_all_read/
  /// Body: {"category": "map"} (optional)
  /// Returns: BulkActionResponse
  Future<BulkActionResponse> markAllAsRead({NotificationCategory? category}) async {
    final uri = Uri.parse('$baseUrl/api/notifications/notifications/mark_all_read/');

    final body = <String, dynamic>{};
    if (category != null && category != NotificationCategory.all) {
      body['category'] = category.name;
    }

    final response = await http.post(
      uri,
      headers: _headers,
      body: json.encode(body),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return BulkActionResponse.fromJson(data);
    } else {
      throw ApiException(
        statusCode: response.statusCode,
        message: 'Failed to mark all notifications as read',
        details: response.body,
      );
    }
  }

  /// DELETE /api/notifications/notifications/bulk_delete/
  /// Body: {"notification_ids": ["1", "2", "3"]}
  /// Returns: BulkActionResponse
  Future<BulkActionResponse> deleteNotifications(List<String> notificationIds) async {
    final uri = Uri.parse('$baseUrl/api/notifications/notifications/bulk_delete/');

    final body = {'notification_ids': notificationIds};

    final response = await http.delete(
      uri,
      headers: _headers,
      body: json.encode(body),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return BulkActionResponse.fromJson(data);
    } else {
      throw ApiException(
        statusCode: response.statusCode,
        message: 'Failed to delete notifications',
        details: response.body,
      );
    }
  }

  /// GET /api/notifications/notifications/stats/
  /// Returns: NotificationStatsResponse
  Future<NotificationStatsResponse> getStats() async {
    final uri = Uri.parse('$baseUrl/api/notifications/notifications/stats/');

    final response = await http.get(uri, headers: _headers);

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return NotificationStatsResponse.fromJson(data);
    } else {
      throw ApiException(
        statusCode: response.statusCode,
        message: 'Failed to load notification stats',
        details: response.body,
      );
    }
  }

  /// GET /api/notifications/settings/
  /// Returns: UserNotificationSettings
  Future<UserNotificationSettings> getSettings() async {
    final uri = Uri.parse('$baseUrl/api/notifications/settings/');

    final response = await http.get(uri, headers: _headers);

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return UserNotificationSettings.fromJson(data);
    } else {
      throw ApiException(
        statusCode: response.statusCode,
        message: 'Failed to load notification settings',
        details: response.body,
      );
    }
  }

  /// PUT /api/notifications/settings/
  /// Body: UserNotificationSettings
  /// Returns: UserNotificationSettings
  Future<UserNotificationSettings> updateSettings(UserNotificationSettings settings) async {
    final uri = Uri.parse('$baseUrl/api/notifications/settings/');

    final response = await http.put(
      uri,
      headers: _headers,
      body: json.encode(settings.toJson()),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return UserNotificationSettings.fromJson(data);
    } else {
      throw ApiException(
        statusCode: response.statusCode,
        message: 'Failed to update notification settings',
        details: response.body,
      );
    }
  }

  /// POST /api/notifications/onesignal/register_player/
  /// Body: {"player_id": "...", "platform": "ios|android"}
  /// Returns: SuccessResponse
  Future<SuccessResponse> registerOneSignalPlayer({
    required String playerId,
    required String platform,
  }) async {
    final uri = Uri.parse('$baseUrl/api/notifications/onesignal/register_player/');

    final body = {
      'player_id': playerId,
      'platform': platform,
    };

    final response = await http.post(
      uri,
      headers: _headers,
      body: json.encode(body),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return SuccessResponse.fromJson(data);
    } else {
      throw ApiException(
        statusCode: response.statusCode,
        message: 'Failed to register OneSignal player',
        details: response.body,
      );
    }
  }

  /// POST /api/notifications/onesignal/unregister_player/
  /// Body: {"player_id": "..."}
  /// Returns: SuccessResponse
  Future<SuccessResponse> unregisterOneSignalPlayer(String playerId) async {
    final uri = Uri.parse('$baseUrl/api/notifications/onesignal/unregister_player/');

    final body = {'player_id': playerId};

    final response = await http.post(
      uri,
      headers: _headers,
      body: json.encode(body),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return SuccessResponse.fromJson(data);
    } else {
      throw ApiException(
        statusCode: response.statusCode,
        message: 'Failed to unregister OneSignal player',
        details: response.body,
      );
    }
  }
}

// Response Models
@JsonSerializable()
class NotificationListResponse {
  final List<NotificationModel> results;
  final int totalCount;
  final int unreadCount;

  const NotificationListResponse({
    required this.results,
    required this.totalCount,
    required this.unreadCount,
  });

  factory NotificationListResponse.fromJson(Map<String, dynamic> json) =>
      _$NotificationListResponseFromJson(json);
}

@JsonSerializable()
class SuccessResponse {
  final bool success;
  final String message;

  const SuccessResponse({
    required this.success,
    required this.message,
  });

  factory SuccessResponse.fromJson(Map<String, dynamic> json) =>
      _$SuccessResponseFromJson(json);
}

@JsonSerializable()
class BulkActionResponse {
  final bool success;
  final int updatedCount;
  final String message;

  const BulkActionResponse({
    required this.success,
    required this.updatedCount,
    required this.message,
  });

  factory BulkActionResponse.fromJson(Map<String, dynamic> json) =>
      _$BulkActionResponseFromJson(json);
}

@JsonSerializable()
class NotificationStatsResponse {
  final int totalNotifications;
  final int unreadNotifications;
  final Map<String, int> categoryCounts;
  final Map<String, int> unreadCategoryCounts;

  const NotificationStatsResponse({
    required this.totalNotifications,
    required this.unreadNotifications,
    required this.categoryCounts,
    required this.unreadCategoryCounts,
  });

  factory NotificationStatsResponse.fromJson(Map<String, dynamic> json) =>
      _$NotificationStatsResponseFromJson(json);
}

class ApiException implements Exception {
  final int statusCode;
  final String message;
  final String? details;

  const ApiException({
    required this.statusCode,
    required this.message,
    this.details,
  });

  @override
  String toString() {
    return 'ApiException: $message (Status: $statusCode)${details != null ? ' - $details' : ''}';
  }
}
```

### OneSignal Integration

#### OneSignalService

```dart
// lib/services/notifications/onesignal_service.dart
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'notification_api_service.dart';

class OneSignalService {
  final NotificationApiService _apiService;
  final String appId;

  OneSignalService({
    required this.appId,
    required NotificationApiService apiService,
  }) : _apiService = apiService;

  /// Initialize OneSignal
  Future<void> initialize({
    required String userId,
    bool enableVibration = true,
    bool enableSound = true,
  }) async {
    // Initialize OneSignal
    OneSignal.shared.setLogLevel(OSLogLevel.warn, OSLogLevel.none);
    
    await OneSignal.shared.setAppId(appId);

    // Set external user ID (your backend user ID)
    await OneSignal.shared.setExternalUserId(userId);

    // Configure notification settings
    await OneSignal.shared.promptUserForPushNotificationPermission(
      fallbackToSettings: true,
    );

    // Set up notification handlers
    OneSignal.shared.setNotificationWillShowInForegroundHandler(_handleForegroundNotification);
    OneSignal.shared.setNotificationOpenedHandler(_handleNotificationOpened);
    OneSignal.shared.setSubscriptionObserver(_handleSubscriptionChanged);

    // Get player ID and register with backend
    final deviceState = await OneSignal.shared.getDeviceState();
    if (deviceState?.userId != null) {
      await _registerPlayer(deviceState!.userId!);
    }
  }

  /// Handle notification received while app is in foreground
  Future<void> _handleForegroundNotification(OSNotificationReceivedEvent event) async {
    // Customize notification display behavior
    event.complete(event.notification);
  }

  /// Handle notification opened/clicked
  Future<void> _handleNotificationOpened(OSNotificationOpenedResult result) async {
    final notification = result.notification;
    final additionalData = notification.additionalData;

    // Handle notification action based on type and data
    if (additionalData != null) {
      final type = additionalData['type'] as String?;
      
      switch (type) {
        case 'pin_detail':
          // Navigate to pin detail
          break;
        case 'friend_request':
          // Navigate to friends screen
          break;
        case 'challenge':
          // Navigate to challenges screen
          break;
        default:
          // Navigate to notifications screen
          break;
      }
    }
  }

  /// Handle subscription changes
  Future<void> _handleSubscriptionChanged(OSSubscriptionStateChanges changes) async {
    final to = changes.to;
    
    if (to.isSubscribed && to.userId != null) {
      await _registerPlayer(to.userId!);
    }
  }

  /// Register player with backend
  Future<void> _registerPlayer(String playerId) async {
    try {
      final platform = _getPlatform();
      await _apiService.registerOneSignalPlayer(
        playerId: playerId,
        platform: platform,
      );
    } catch (e) {
      print('Failed to register OneSignal player: $e');
    }
  }

  /// Get current platform
  String _getPlatform() {
    // You can use dart:io Platform.isIOS / Platform.isAndroid
    // or a platform detection package
    return 'flutter'; // Simplified for example
  }

  /// Request notification permissions
  Future<bool> requestPermissions() async {
    final permission = await OneSignal.shared.promptUserForPushNotificationPermission();
    return permission;
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    final deviceState = await OneSignal.shared.getDeviceState();
    return deviceState?.isSubscribed ?? false;
  }

  /// Get player ID
  Future<String?> getPlayerId() async {
    final deviceState = await OneSignal.shared.getDeviceState();
    return deviceState?.userId;
  }

  /// Set tags for targeting
  Future<void> setTags(Map<String, String> tags) async {
    await OneSignal.shared.sendTags(tags);
  }

  /// Remove tags
  Future<void> removeTags(List<String> tagKeys) async {
    await OneSignal.shared.deleteTags(tagKeys);
  }
}
```

### Updated NotificationProvider

```dart
// lib/providers/notification_provider.dart
import 'package:flutter/foundation.dart';
import '../models/notification_model.dart';
import '../models/user_notification_settings.dart';
import '../services/notifications/notification_api_service.dart';
import '../services/notifications/onesignal_service.dart';

class NotificationProvider extends ChangeNotifier {
  final NotificationApiService _apiService;
  final OneSignalService _oneSignalService;

  List<NotificationModel> _notifications = [];
  UserNotificationSettings? _settings;
  bool _isLoading = false;
  String? _error;

  NotificationProvider({
    required NotificationApiService apiService,
    required OneSignalService oneSignalService,
  }) : _apiService = apiService,
       _oneSignalService = oneSignalService;

  // Getters
  List<NotificationModel> get notifications => _notifications;
  List<NotificationModel> get unreadNotifications => 
      _notifications.where((n) => !n.isRead).toList();
  int get unreadCount => unreadNotifications.length;
  bool get isLoading => _isLoading;
  String? get error => _error;
  UserNotificationSettings? get settings => _settings;

  /// Initialize provider and OneSignal
  Future<void> initialize(String userId) async {
    try {
      await _oneSignalService.initialize(userId: userId);
      await loadNotifications();
      await loadSettings();
    } catch (e) {
      _setError('Failed to initialize notifications: $e');
    }
  }

  /// Load notifications from backend
  Future<void> loadNotifications({
    NotificationCategory? category,
    bool? isRead,
    int limit = 50,
    int offset = 0,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _apiService.getNotifications(
        category: category,
        isRead: isRead,
        limit: limit,
        offset: offset,
      );

      _notifications = response.results;
      notifyListeners();
    } catch (e) {
      _setError('Failed to load notifications: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh notifications
  Future<void> refreshNotifications() async {
    await loadNotifications();
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await _apiService.markAsRead(notificationId);
      
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index >= 0) {
        _notifications[index] = _notifications[index].copyWith(
          isRead: true,
          readAt: DateTime.now(),
        );
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to mark notification as read: $e');
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead({NotificationCategory? category}) async {
    try {
      await _apiService.markAllAsRead(category: category);
      
      for (int i = 0; i < _notifications.length; i++) {
        if (category == null || 
            category == NotificationCategory.all ||
            _notifications[i].category == category) {
          _notifications[i] = _notifications[i].copyWith(
            isRead: true,
            readAt: DateTime.now(),
          );
        }
      }
      notifyListeners();
    } catch (e) {
      _setError('Failed to mark all notifications as read: $e');
    }
  }

  /// Delete notifications
  Future<void> deleteNotifications(List<String> notificationIds) async {
    try {
      await _apiService.deleteNotifications(notificationIds);
      
      _notifications.removeWhere((n) => notificationIds.contains(n.id));
      notifyListeners();
    } catch (e) {
      _setError('Failed to delete notifications: $e');
    }
  }

  /// Load user settings
  Future<void> loadSettings() async {
    try {
      _settings = await _apiService.getSettings();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load settings: $e');
    }
  }

  /// Update user settings
  Future<void> updateSettings(UserNotificationSettings settings) async {
    try {
      _settings = await _apiService.updateSettings(settings);
      notifyListeners();
    } catch (e) {
      _setError('Failed to update settings: $e');
    }
  }

  /// Get statistics
  Future<NotificationStatsResponse> getStats() async {
    return await _apiService.getStats();
  }

  // Filter methods
  List<NotificationModel> getNotificationsByCategory(NotificationCategory category) {
    if (category == NotificationCategory.all) {
      return _notifications;
    }
    return _notifications.where((n) => n.category == category).toList();
  }

  List<NotificationModel> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  // Real-time updates (for when notifications are received)
  void addNotification(NotificationModel notification) {
    _notifications.insert(0, notification);
    notifyListeners();
  }

  void updateNotification(NotificationModel notification) {
    final index = _notifications.indexWhere((n) => n.id == notification.id);
    if (index >= 0) {
      _notifications[index] = notification;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _notifications.clear();
    super.dispose();
  }
}
```

### Usage Example

```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/notification_provider.dart';
import 'services/notifications/notification_api_service.dart';
import 'services/notifications/onesignal_service.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) {
            final apiService = NotificationApiService(
              baseUrl: 'https://your-api.com',
              getAuthToken: () => 'your-auth-token',
            );
            
            final oneSignalService = OneSignalService(
              appId: 'your-onesignal-app-id',
              apiService: apiService,
            );

            return NotificationProvider(
              apiService: apiService,
              oneSignalService: oneSignalService,
            );
          },
        ),
      ],
      child: MaterialApp(
        title: 'BOP Maps',
        home: NotificationScreen(),
      ),
    );
  }
}

// lib/screens/notification_screen.dart
class NotificationScreen extends StatefulWidget {
  @override
  _NotificationScreenState createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize with current user ID (replace with actual user ID)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NotificationProvider>().initialize('9567');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Notifications'),
        actions: [
          Consumer<NotificationProvider>(
            builder: (context, provider, child) {
              if (provider.unreadCount > 0) {
                return Badge(
                  label: Text('${provider.unreadCount}'),
                  child: IconButton(
                    icon: Icon(Icons.mark_email_read),
                    onPressed: () => provider.markAllAsRead(),
                  ),
                );
              }
              return SizedBox.shrink();
            },
          ),
        ],
      ),
      body: Consumer<NotificationProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return Center(child: CircularProgressIndicator());
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Error: ${provider.error}'),
                  ElevatedButton(
                    onPressed: provider.refreshNotifications,
                    child: Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final notifications = provider.notifications;

          if (notifications.isEmpty) {
            return Center(child: Text('No notifications'));
          }

          return RefreshIndicator(
            onRefresh: provider.refreshNotifications,
            child: ListView.builder(
              itemCount: notifications.length,
              itemBuilder: (context, index) {
                final notification = notifications[index];
                return NotificationTile(
                  notification: notification,
                  onTap: () => provider.markAsRead(notification.id),
                  onDelete: () => provider.deleteNotifications([notification.id]),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
```

### Error Handling

The API service includes comprehensive error handling with typed exceptions:

```dart
try {
  await notificationProvider.loadNotifications();
} on ApiException catch (e) {
  if (e.statusCode == 401) {
    // Handle authentication error
    await authService.refreshToken();
  } else if (e.statusCode >= 500) {
    // Handle server error
    showErrorSnackbar('Server error, please try again later');
  } else {
    // Handle other errors
    showErrorSnackbar(e.message);
  }
} catch (e) {
  // Handle unexpected errors
  showErrorSnackbar('An unexpected error occurred');
}
```

### Testing

```dart
// test/providers/notification_provider_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import '../mocks/mock_notification_api_service.dart';
import '../mocks/mock_onesignal_service.dart';

void main() {
  group('NotificationProvider', () {
    late NotificationProvider provider;
    late MockNotificationApiService mockApiService;
    late MockOneSignalService mockOneSignalService;

    setUp(() {
      mockApiService = MockNotificationApiService();
      mockOneSignalService = MockOneSignalService();
      provider = NotificationProvider(
        apiService: mockApiService,
        oneSignalService: mockOneSignalService,
      );
    });

    test('loadNotifications updates notifications list', () async {
      // Arrange
      final mockResponse = NotificationListResponse(
        results: [/* mock notifications */],
        totalCount: 1,
        unreadCount: 1,
      );
      when(mockApiService.getNotifications()).thenAnswer((_) async => mockResponse);

      // Act
      await provider.loadNotifications();

      // Assert
      expect(provider.notifications, equals(mockResponse.results));
      expect(provider.isLoading, false);
    });

    // Add more tests...
  });
}
```

## Future Enhancements

Potential areas for expansion:

- Email notification support
- SMS notifications for critical alerts
- Advanced analytics and user behavior tracking
- A/B testing for notification content
- Machine learning for optimal timing
- Rich media support (images, actions)

## Support

For questions or issues with the notification system:

1. Check the test suite for usage examples
2. Review OneSignal documentation for platform-specific features
3. Monitor Django logs for error details
4. Use the debug utilities for troubleshooting

---

*Last updated: December 2024*
*Version: 1.0 (Production Ready)* 