# Pin API Documentation

## Adding a Pin

### Endpoint
```
POST /api/pins/
```

### Authentication
- Required
- Bearer Token in header: `Authorization: Bearer <your_token>`

### Request Body
```json
{
    "location": {
        "type": "Point",
        "coordinates": [longitude, latitude]  // Note: order is [longitude, latitude]
    },
    "title": "Pin Title",
    "description": "Pin Description",  // Optional
    "caption": "Perfect spot for a sunset vibe 🌅",  // Optional, max 150 chars
    "track_title": "Song Title",
    "track_artist": "Artist Name",
    "album": "Album Name",  // Optional
    "track_url": "https://music-service.com/track",
    "service": "spotify",  // Options: "spotify", "apple", "soundcloud"
    "skin": 1,  // skin ID
    "aura_radius": 100,  // Optional
    "is_private": false  // Optional, defaults to false
}
```

### Required Fields
- `location` (with coordinates)
- `track_title`
- `track_artist`
- `track_url`
- `service`
- `skin`

### Optional Fields
- `description`
- `caption` (max 150 characters)
- `album`
- `aura_radius`
- `is_private`

### Success Response (201 Created)
```json
{
    "id": "new_pin_id",
    "owner": {
        "id": "user_id",
        "username": "username"
    },
    "location": {
        "type": "Point",
        "coordinates": [longitude, latitude]
    },
    "title": "Pin Title",
    "description": "Pin Description",
    "caption": "Perfect spot for a sunset vibe 🌅",
    "track_title": "Song Title",
    "track_artist": "Artist Name",
    "album": "Album Name",
    "track_url": "https://music-service.com/track",
    "service": "spotify",
    "skin": "skin_id",
    "skin_details": {
        "id": "skin_id",
        "name": "Skin Name",
        "image": "skin_image_url"
    },
    "rarity": "common",
    "aura_radius": 100,
    "is_private": false,
    "created_at": "timestamp",
    "updated_at": "timestamp",
    "interaction_count": {
        "view": 0,
        "like": 0,
        "collect": 0,
        "share": 0
    }
}
```

### Error Responses
- `400 Bad Request`: Invalid input data
  ```json
  {
    "error": "ValidationError",
    "detail": {
      "field_name": ["Error message"]
    }
  }
  ```
- `401 Unauthorized`: Missing or invalid authentication
- `403 Forbidden`: Permission denied

### Example Usage

#### JavaScript/TypeScript
```javascript
async function addPin(pinData) {
    const response = await fetch('/api/pins/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${yourAuthToken}`
        },
        body: JSON.stringify({
            location: {
                type: "Point",
                coordinates: [-73.985130, 40.758896]
            },
            title: "Times Square Jam",
            description: "My favorite song at Times Square",
            caption: "Perfect spot for a sunset vibe 🌅",
            track_title: "Empire State of Mind",
            track_artist: "Jay-Z ft. Alicia Keys",
            album: "The Blueprint 3",
            track_url: "https://spotify.com/track/123",
            service: "spotify",
            skin: 1,
            aura_radius: 100,
            is_private: false
        })
    });
    
    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
}
```

#### Python
```python
import requests

def add_pin(auth_token, pin_data):
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {auth_token}'
    }
    
    response = requests.post(
        'http://your-api-url/api/pins/',
        json=pin_data,
        headers=headers
    )
    
    response.raise_for_status()
    return response.json()

# Example usage
pin_data = {
    "location": {
        "type": "Point",
        "coordinates": [-73.985130, 40.758896]
    },
    "title": "Times Square Jam",
    "description": "My favorite song at Times Square",
    "caption": "Perfect spot for a sunset vibe 🌅",
    "track_title": "Empire State of Mind",
    "track_artist": "Jay-Z ft. Alicia Keys",
    "album": "The Blueprint 3",
    "track_url": "https://spotify.com/track/123",
    "service": "spotify",
    "skin": 1,
    "aura_radius": 100,
    "is_private": False
}

try:
    result = add_pin('your-auth-token', pin_data)
    print(f"Pin created with ID: {result['id']}")
except requests.exceptions.RequestException as e:
    print(f"Error creating pin: {e}")
```

## Pin Interactions

After creating a pin, users can interact with it in several ways:

### View Pin
```
POST /api/pins/{pin_id}/view/
```

### Like Pin
```
POST /api/pins/{pin_id}/like/
```

### Collect Pin
```
POST /api/pins/{pin_id}/collect/
```

### Share Pin
```
POST /api/pins/{pin_id}/share/
```

All interaction endpoints return:
```json
{
    "success": true,
    "message": "Pin [interaction_type] recorded successfully"
}
```

## Getting Pins

### List All Pins
```
GET /api/pins/
```

### Get Nearby Pins
```
GET /api/pins/nearby/?latitude=40.7128&longitude=-74.0060&radius=1000
```

### Get Map Pins
```
GET /api/pins/list_map/?latitude=40.7128&longitude=-74.0060&radius=1000
```

### Get Trending Pins
```
GET /api/pins/trending/?days=7&limit=20
```

## Best Practices

1. **Location Format**
   - Always send coordinates in [longitude, latitude] order
   - Use appropriate precision (6 decimal places is usually sufficient)

2. **Error Handling**
   - Always implement proper error handling
   - Check response status codes
   - Parse error messages from response body

3. **Authentication**
   - Always include the authentication token
   - Handle token expiration and refresh

4. **Validation**
   - Validate coordinates before sending
   - Check string lengths (especially for caption)
   - Verify required fields are present

5. **Rate Limiting**
   - Check rate limit headers
   - Implement appropriate backoff strategies
   ```javascript
   X-RateLimit-Limit: requests_allowed
   X-RateLimit-Remaining: requests_remaining
   X-RateLimit-Reset: reset_timestamp
   ```

## Testing

The API includes comprehensive tests that verify:
- Pin creation with valid/invalid data
- Pin interactions (view, like, collect, share)
- Access control (private/public pins)
- Location-based queries
- Authentication requirements

You can run the tests using:
```bash
python manage.py test pins -v 2
```

## Common Issues and Solutions

1. **Coordinate Order**
   - Problem: Pin appears in wrong location
   - Solution: Ensure coordinates are in [longitude, latitude] order

2. **Authentication Errors**
   - Problem: 401 Unauthorized
   - Solution: Check token validity and presence in headers

3. **Validation Errors**
   - Problem: 400 Bad Request
   - Solution: Verify all required fields and format

4. **Rate Limiting**
   - Problem: 429 Too Many Requests
   - Solution: Implement request throttling and respect rate limits 