# Pin Skins Production Setup Guide

This guide explains how to set up the production pin skins and challenges for BOPMaps.

## Overview

The system includes:
- **25 House Pin Skins** with different unlock mechanisms
- **Production Challenges** that award specific skins
- **Automatic unlock system** for rank-based skins
- **OneSignal notifications** when users earn skins

## Prerequisites

1. Ensure B2 bucket is deployed and environment variables are set:
   ```
   B2_APPLICATION_KEY_ID
   B2_APPLICATION_KEY
   B2_BUCKET_NAME
   B2_ENDPOINT_URL
   B2_REGION
   ```

2. Pin images should be in the `pin_images/` folder at the project root

## Step 1: Generate Production Pin Skins

First, run a dry run to verify everything is set up correctly:

```bash
python manage.py generate_production_pins --dry-run
```

This will show you what skins would be created and verify that all images are found.

To actually create the skins and upload images to B2:

```bash
python manage.py generate_production_pins
```

For local testing without B2 upload:

```bash
python manage.py generate_production_pins --skip-upload
```

### What This Command Does:
- Creates 25 production pin skins
- Uploads pin images to B2 bucket
- Sets up metadata for unlock rules
- Automatically unlocks rank-based skins for users who have already reached those ranks

## Step 2: Generate Production Challenges

Run a dry run first:

```bash
python manage.py generate_production_challenges --dry-run
```

To create the challenges:

```bash
python manage.py generate_production_challenges
```

To clear existing challenges and recreate:

```bash
python manage.py generate_production_challenges --clear-existing
```

### What This Command Does:
- Creates achievement challenges that award specific skins
- Links challenges to their reward skins
- Initializes achievement tracking for all existing users
- Awards "First Drop" skin to users who already have pins

## Step 3: Award Special Skins

### OG Listener Skin

Award to early users who joined before a specific date:

```bash
# Dry run to see who would get it
python manage.py award_og_listener --cutoff-date 2024-12-31 --dry-run

# Actually award the skin
python manage.py award_og_listener --cutoff-date 2024-12-31
```

### Surprise Drops (BOP Drops)

Set up a cron job to randomly award surprise skins:

```bash
# Award to 5 random active users
python manage.py award_surprise_drops --count 5

# Dry run
python manage.py award_surprise_drops --count 10 --dry-run

# Consider users active in last 30 days
python manage.py award_surprise_drops --count 5 --min-activity-days 30
```

Suggested cron entry (weekly surprise drops):
```cron
0 12 * * 1 cd /path/to/project && /path/to/venv/bin/python manage.py award_surprise_drops --count 5
```

## Pin Skin Types and Unlock Mechanisms

### 1. Rank-Based Skins (Automatic)
- **Basement Bopper** - Level 1 (all users start with this)
- **Selector** - Level 5
- **Tastemaker** - Level 10
- **Trendsetter** - Level 15
- **Icon** - Level 20
- **Architect** - Level 25
- **Legend** - Level 30

These unlock automatically when users reach the required level through earning XP.

### 2. Achievement-Based Skins
Users earn these by completing specific challenges:
- **Nostalgic Connection** - Memory Lane challenge
- **Euphoric Moment** - Peak Experience challenge
- **Heartbreak Anthem** - Emotional Journey challenge
- **Party Vibes** - Party Starter challenge
- **Rainy Day Listening** - Weather Vibes challenge
- **Chill Lo-Fi Beat** - Chill Master challenge
- **Hyped EDM Energy** - Bass Drop Master challenge
- **Jazzy Groove** - Jazz Aficionado challenge
- **New City Anthem** - City Explorer challenge
- **Hidden Gem** - Music Archaeologist challenge
- **Road Trip Anthem** - Road Warrior challenge
- **Secret Pin** - Secret Finder challenge (hidden)
- **Shared Memory** - Social Butterfly challenge
- **First Drop** - First Steps challenge

### 3. Special Event Skins
- **OG Listener** - Awarded to early users (LIMITED_TIME)
- **Ultra Rare** - Reserved for special events (SPECIAL_EVENT)
- **Surprise Drop** - Randomly awarded to active users (BOP_DROP)

## How the System Works

### Achievement Completion Flow:
1. User completes challenge criteria
2. `UserAchievement` is marked as completed
3. Signal triggers XP update and level check
4. If achievement has reward skin, it's unlocked
5. If user leveled up, rank skins are checked
6. OneSignal notifications are sent

### Rank Progression:
1. Users earn XP by completing challenges
2. When XP threshold is reached, user levels up
3. Rank-based skins are automatically unlocked
4. Notification is sent about new rank and skin

### Progress Tracking:
The `ProgressTracker` service monitors user activities and updates achievement progress in real-time:
- Pin creation
- Reactions received
- Comments given/received
- Location-based activities
- Genre/artist specific actions

## Testing

### Verify Skins are Created:
```python
from pins.models import PinSkin
PinSkin.objects.all().count()  # Should be 25+
```

### Check User's Unlocked Skins:
```python
from pins.models import UserSkin
from users.models import User
user = User.objects.get(username='testuser')
UserSkin.objects.filter(user=user).values_list('skin__name', flat=True)
```

### Manually Award a Skin:
```python
from pins.models import PinSkin, UserSkin
from users.models import User
user = User.objects.get(username='testuser')
skin = PinSkin.objects.get(slug='hidden-gem')
UserSkin.objects.create(user=user, skin=skin)
```

## API Endpoints

The frontend can use these endpoints:

- `GET /api/skins/` - List all skins with lock status
- `GET /api/skins/available/` - Get available skins
- `GET /api/skins/unlocked/` - Get user's unlocked skins
- `POST /api/skins/{id}/claim/` - Claim an eligible skin
- `POST /api/skins/{id}/equip/` - Equip an unlocked skin

## Monitoring

Check the logs for skin unlock events:
```bash
grep "Unlocked.*skin" /path/to/logs/django.log
```

Monitor achievement completions:
```bash
grep "Achievement completed" /path/to/logs/django.log
```

## Troubleshooting

### Images Not Uploading to B2:
1. Check B2 credentials in environment
2. Verify `pin_images/` folder exists with PNG files
3. Check B2 bucket permissions
4. Look for upload errors in command output

### Skins Not Unlocking:
1. Verify achievement criteria in database
2. Check if user has progress tracked
3. Look for errors in gamification signals
4. Ensure OneSignal is configured

### Notifications Not Sending:
1. Check OneSignal configuration
2. Verify user has player_id registered
3. Check notification logs
4. Test with manual notification send

## Next Steps

1. Set up monitoring for achievement completion rates
2. Create admin dashboard for skin statistics
3. Plan special events for Ultra Rare skin
4. Consider seasonal/holiday themed skins
5. Implement skin trading or gifting system 