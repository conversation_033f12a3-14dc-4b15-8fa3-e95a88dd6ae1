# BOPMaps OpenMapTiles Integration

This document describes the OpenMapTiles planet integration for the BOPMaps project, providing self-hosted vector tiles for the entire planet.

## Overview

The OpenMapTiles integration provides:
- **Planet-wide vector tiles** from OpenStreetMap data
- **TileServer GL** for efficient tile serving
- **Multiple map styles** (OSM Bright, Positron, Dark Matter)
- **AWS EFS storage** for scalable tile storage
- **Cloudflare optimization** for global tile delivery
- **Complete self-hosting** - no external tile dependencies

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │   BOPMaps API   │    │  TileServer GL  │
│                 │    │                 │    │                 │
│ Map Rendering   │◄──►│ Tile Proxy      │◄──►│ Vector Tiles    │
│ Leaflet/MapGL   │    │ Authentication  │    │ Raster Tiles    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Cloudflare    │    │   Application   │    │      EFS        │
│                 │    │ Load Balancer   │    │                 │
│ Global CDN      │    │ SSL Termination │    │ Planet Tiles    │
│ Edge Caching    │    │ Auto Scaling    │    │ Styles/Fonts    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Quick Start

### 1. Local Development Setup

```bash
# Clone repository
git clone <repository-url>
cd BOPMapsBackend

# Setup OpenMapTiles locally (requires 100GB+ disk space)
./scripts/setup-openmaptiles.sh

# Start local tile server
cd docker/openmaptiles
docker-compose -f docker-compose.openmaptiles.yml --profile serve up -d

# Test the setup
curl http://localhost:8080/data/planet/0/0/0.pbf
curl http://localhost:8080/styles/osm-bright.json
```

### 2. Production Deployment

```bash
# Deploy infrastructure with Terraform
cd terraform
terraform init
terraform plan
terraform apply

# Deploy OpenMapTiles to AWS
cd ..
./scripts/deploy-openmaptiles-to-aws.sh

# Test production deployment
./scripts/test-openmaptiles.sh
```

## File Structure

```
BOPMapsBackend/
├── docker/
│   ├── Dockerfile.openmaptiles          # TileServer GL container
│   └── openmaptiles/
│       ├── config.json                  # TileServer configuration
│       ├── docker-compose.openmaptiles.yml
│       ├── styles/                      # Map styles
│       ├── fonts/                       # Font files
│       ├── sprites/                     # Map sprites
│       └── tiles/                       # Planet tiles storage
├── scripts/
│   ├── setup-openmaptiles.sh           # Local setup script
│   ├── deploy-openmaptiles-to-aws.sh   # AWS deployment script
│   └── test-openmaptiles.sh            # Testing suite
├── terraform/
│   ├── ecs.tf                          # Updated for tile server
│   └── storage.tf                      # EFS access points
└── README-OPENMAPTILES.md              # This file
```

## Configuration

### TileServer GL Configuration

The `docker/openmaptiles/config.json` file configures:

```json
{
  "options": {
    "maxzoom": 20,
    "maxsize": 2048,
    "domains": ["tiles.bopmaps.com"]
  },
  "styles": {
    "osm-bright": { "style": "/data/styles/osm-bright/style.json" },
    "positron": { "style": "/data/styles/positron/style.json" },
    "dark-matter": { "style": "/data/styles/dark-matter/style.json" }
  },
  "data": {
    "planet": {
      "mbtiles": "/data/tiles/planet.mbtiles",
      "tilejson": {
        "bounds": [-180, -85.0511, 180, 85.0511],
        "maxzoom": 14,
        "format": "pbf"
      }
    }
  }
}
```

### Terraform Variables

Update `terraform/terraform.tfvars` for optimal performance:

```hcl
# Tile server resources
tileserver_cpu    = 1024  # 1 vCPU
tileserver_memory = 2048  # 2GB RAM
tileserver_count  = 2     # Number of instances

# EFS configuration for large dataset
efs_throughput_mode = "provisioned"
efs_provisioned_throughput = 500  # MiB/s
```

## API Endpoints

### Vector Tiles
```
GET /data/planet/{z}/{x}/{y}.pbf
```
Returns Mapbox Vector Tile (MVT) data in Protocol Buffer format.

**Example:**
```bash
curl https://tiles.bopmaps.com/data/planet/10/512/341.pbf
```

### Raster Tiles
```
GET /styles/{style}/{z}/{x}/{y}.{format}
```

**Supported styles:**
- `osm-bright` - OpenStreetMap Bright style
- `positron` - Light/minimal style
- `dark-matter` - Dark theme style

**Supported formats:**
- `png` - PNG raster tiles
- `webp` - WebP raster tiles (smaller size)

**Example:**
```bash
curl https://tiles.bopmaps.com/styles/osm-bright/10/512/341.png
```

### Style Definitions
```
GET /styles/{style}.json
```
Returns Mapbox GL Style JSON for client-side rendering.

**Example:**
```bash
curl https://tiles.bopmaps.com/styles/osm-bright.json
```

### Fonts
```
GET /fonts/{font}/{range}.pbf
```
Returns font glyphs in Protocol Buffer format.

**Example:**
```bash
curl https://tiles.bopmaps.com/fonts/Open%20Sans%20Regular/0-255.pbf
```

## Caching Strategy

### Multi-Level Caching

1. **Browser Cache**: 1 day for tiles, 1 hour for styles
2. **Cloudflare Edge**: 30 days for low zoom, 1 day for high zoom
3. **Application Cache**: Redis caching for metadata
4. **EFS Storage**: Persistent storage for all tile data

### Cache Headers

The tile server sets appropriate cache headers:

```http
Cache-Control: public, max-age=2592000, immutable
ETag: "unique-tile-identifier"
Vary: Accept-Encoding
```

### Cloudflare Workers

Custom Cloudflare Worker optimizes tile delivery:

```javascript
// Intelligent caching based on zoom level
if (zoom <= 10) {
    // Low zoom tiles - cache for 30 days
    response.headers.set('Cache-Control', 'public, max-age=2592000, immutable');
} else {
    // High zoom tiles - cache for 1 day
    response.headers.set('Cache-Control', 'public, max-age=86400, immutable');
}
```

## Performance Optimization

### EFS Optimization

For large datasets like planet tiles:

```hcl
# terraform/terraform.tfvars
efs_throughput_mode = "provisioned"
efs_provisioned_throughput = 500  # Increase based on load
```

### Container Scaling

Scale tile servers based on usage:

```hcl
# terraform/terraform.tfvars
tileserver_count = 3     # Scale horizontally
tileserver_cpu = 2048    # Scale vertically
tileserver_memory = 4096
```

### Auto Scaling Triggers

The infrastructure includes auto-scaling policies for:
- CPU utilization (target: 70%)
- Memory utilization (target: 80%)
- Request count (target: 1000 requests/target)

## Data Updates

### Planet Data Updates

OpenMapTiles planet data is updated weekly. To update:

```bash
# Download new planet data
wget https://data.maptiler.com/downloads/planet/v3.14/planet.mbtiles

# Replace existing data (requires deployment)
./scripts/deploy-openmaptiles-to-aws.sh
```

### Incremental Updates

For more frequent updates, consider:
1. **Regional extracts** for specific areas
2. **Differential updates** using OSM changesets
3. **Custom tile generation** using OpenMapTiles tools

## Monitoring

### CloudWatch Metrics

Monitor key metrics:
- **EFS Throughput**: Track read/write performance
- **ECS CPU/Memory**: Monitor container resource usage
- **ALB Request Count**: Track tile request volume
- **Response Times**: Monitor tile serving latency

### Custom Metrics

Additional monitoring via CloudWatch:
- Tile cache hit rates
- Popular zoom levels and regions
- Error rates by tile coordinate

### Alerts

Configured CloudWatch alarms for:
- High memory usage (>85%)
- High CPU usage (>80%)
- Slow response times (>1s)
- EFS throughput limits

## Troubleshooting

### Common Issues

#### Tile Server Won't Start
```bash
# Check ECS service status
aws ecs describe-services --cluster bopmaps-prod --services bopmaps-prod-tileserver

# Check container logs
aws logs tail /ecs/bopmaps-prod/tileserver --follow

# Common causes:
# - Missing planet.mbtiles file
# - Insufficient memory allocation
# - EFS mount issues
```

#### Slow Tile Loading
```bash
# Check EFS performance
aws efs describe-file-systems --file-system-id <EFS_ID>

# Monitor CloudWatch metrics
# Consider increasing provisioned throughput
```

#### High Memory Usage
```bash
# Scale up resources
terraform apply -var="tileserver_memory=4096"

# Or scale out instances
terraform apply -var="tileserver_count=3"
```

### Performance Testing

Use the included test suite:

```bash
# Local testing
./scripts/test-openmaptiles.sh

# Production testing
TILES_URL=https://tiles.bopmaps.com \
./scripts/test-openmaptiles.sh
```

## Cost Analysis

### Monthly Costs (Planet Setup)

| Service | Cost Range | Notes |
|---------|------------|-------|
| EFS (100GB) | $30-50 | Planet tiles storage |
| ECS Fargate | $20-40 | Tile server containers |
| Data Transfer | $10-50 | Varies with usage |
| **Total** | **$60-140** | Additional to base infrastructure |

### Cost Optimization

1. **Aggressive Caching**: Reduce data transfer costs
2. **Right-sizing**: Monitor and adjust container resources
3. **Regional Data**: Use extracts for specific regions
4. **Spot Instances**: Use for development environments

## Security

### Access Control

- **EFS Access Points**: Separate access points for different data types
- **Security Groups**: Restrictive network access
- **IAM Roles**: Least privilege principle

### Data Protection

- **Encryption**: EFS encryption in transit and at rest
- **Network Security**: Tile server in private subnets
- **Rate Limiting**: Cloudflare protection against abuse

### Authentication

While tiles are public, consider:
- **API Key Authentication** for high-volume usage
- **Rate Limiting** per IP/user
- **Usage Analytics** for monitoring

## Integration with BOPMaps

### Django Integration

The BOPMaps Django application includes:

```python
# geo/views.py - Tile proxy with caching
class OSMTileView(APIView):
    def get(self, request, z, x, y):
        # Proxy to internal tile server
        # Add authentication/rate limiting
        # Cache responses
```

### Frontend Integration

Configure map clients to use your tile server:

```javascript
// Leaflet configuration
L.tileLayer('https://tiles.bopmaps.com/styles/osm-bright/{z}/{x}/{y}.png', {
    attribution: '© OpenMapTiles, © OpenStreetMap contributors',
    maxZoom: 18
});

// Mapbox GL JS configuration
const map = new mapboxgl.Map({
    container: 'map',
    style: 'https://tiles.bopmaps.com/styles/osm-bright.json',
    center: [0, 0],
    zoom: 2
});
```

## Contributing

### Development Setup

1. Fork the repository
2. Set up local OpenMapTiles environment
3. Make changes and test locally
4. Submit pull request

### Testing

All changes should include:
- Unit tests for new functionality
- Integration tests for tile serving
- Performance tests for large datasets

## Resources

### Documentation
- [OpenMapTiles Documentation](https://openmaptiles.org/docs/)
- [TileServer GL Documentation](https://tileserver.readthedocs.io/)
- [Mapbox Vector Tile Specification](https://docs.mapbox.com/vector-tiles/specification/)

### Community
- [OpenMapTiles GitHub](https://github.com/openmaptiles/openmaptiles)
- [OpenStreetMap Community](https://www.openstreetmap.org/community)

## License

This project uses OpenMapTiles which includes:
- **Code**: BSD 3-Clause License
- **Data**: OpenStreetMap data under ODbL
- **Styles**: Creative Commons licenses

See individual component licenses for details. 