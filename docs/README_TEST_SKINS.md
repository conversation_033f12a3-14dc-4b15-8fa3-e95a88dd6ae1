# Test Skin Generation Script

This document explains how to use the test skin generation script for development and testing.

## Overview

The `generate_test_skins` management command creates sample data for the pin skin system, including:
- **House Skins**: Basic pin customizations (free and premium)
- **Artist Skins**: Limited-time skins tied to weekly challenges
- **Weekly Challenges**: Sample challenges with different statuses
- **Test Artists**: Sample artists for challenge skins

## Usage

### Basic Usage
Generate all test data (house skins, challenges, artists, and artist skins):
```bash
python manage.py generate_test_skins
```

### Options

#### Create Only House Skins
If you only want house skins without challenges or artist skins:
```bash
python manage.py generate_test_skins --house-skins-only
```

#### Clear Existing Test Data
To remove existing test data before creating new data:
```bash
python manage.py generate_test_skins --clear-existing
```

#### Combine Options
```bash
python manage.py generate_test_skins --clear-existing --house-skins-only
```

#### Get Help
```bash
python manage.py generate_test_skins --help
```

## Generated Data

### House Skins (6 total)
- **Test Classic Blue** 🆓 - Basic blue house skin
- **Test Modern Green** 🆓 - Modern green house design  
- **Test Premium Gold** 👑 - Premium gold house skin
- **Test Premium Diamond** 👑 - Ultra-exclusive diamond skin
- **Test Neon Pink** 🆓 - Vibrant neon pink skin
- **Test Cyber Orange** 🆓 - Futuristic orange cyber house

### Test Artists (5 total)
- Test Artist - DJ Nebula
- Test Artist - Bass Prophet
- Test Artist - Synth Master
- Test Artist - Beat Wizard
- Test Artist - Echo Chamber

### Weekly Challenges (5 total)
- **Test EDM Blast Challenge** 🟢 - Currently active
- **Test Hip-Hop Cypher** 🔴 - Recently ended (for testing claim mechanics)
- **Test Lo-Fi Vibes** 🟡 - Upcoming challenge
- **Test Electronic Fusion** 🔴 - Ended and processed
- **Test Ambient Soundscapes** 🟡 - Future challenge

### Artist Skins (7 total)
**Participation Skins (auto-unlock on join):**
- Test DJ Nebula Badge (EDM Challenge)
- Test Bass Prophet Mic (Hip-Hop Challenge)
- Test Synth Master Vinyl (Lo-Fi Challenge)
- Test Echo Chamber Wave (Ambient Challenge)

**Completion Skins (manual claim after challenge ends):**
- Test DJ Nebula Gold (Top 3 in EDM Challenge)
- Test Bass Prophet Crown (Top 1 in Hip-Hop Challenge)
- Test Beat Wizard Orb (Top 5 in Electronic Fusion)

## Image Assets

All test skins use placeholder images from `via.placeholder.com` with:
- **64x64 pixel dimensions**
- **Color-coded backgrounds** for easy identification
- **Emoji icons** for visual distinction
- **URLs that work immediately** without file uploads

## Testing Scenarios

The generated data supports various testing scenarios:

### Free vs Premium Skins
- Test free skin accessibility for all users
- Test premium skin restriction logic

### Challenge States
- **Active Challenges**: Test participation and auto-unlock
- **Ended Challenges**: Test manual claim mechanics
- **Upcoming Challenges**: Test UI for future events

### Unlock Types
- **PARTICIPATE**: Auto-unlock when joining challenge
- **TOP_N**: Manual claim for top performers (various N values)

### Rarity Levels
- **Common**: Basic participation rewards
- **Epic**: Mid-tier completion rewards
- **Legendary**: High-tier completion rewards
- **Mythic**: Ultra-rare top performer rewards

## API Testing

After generating test data, you can test the API endpoints:

```bash
# Get available skins
curl -H "Authorization: Bearer <token>" http://localhost:8000/api/pins/skins/available/

# Get active challenges
curl -H "Authorization: Bearer <token>" http://localhost:8000/api/pins/challenges/active/

# Get unlocked skins for user
curl -H "Authorization: Bearer <token>" http://localhost:8000/api/pins/skins/unlocked/
```

## Cleanup

To remove all test data:
```bash
python manage.py generate_test_skins --clear-existing
```

This will delete:
- All skins with "Test" in the name (except default skin ID=1)
- All challenges with "Test" in the title
- All artists with "Test" in the name

## Notes

- Script is idempotent - running multiple times won't create duplicates
- Uses `get_or_create()` to prevent data conflicts
- All test data is clearly labeled with "Test" prefix
- Placeholder images are hosted externally and load immediately
- Script provides detailed progress feedback with emojis and status indicators

## Example Output

```bash
🎨 Generating test skins...
   ✓ Created house skin: Test Classic Blue
   ✓ Created house skin: Test Modern Green
   ✓ Created artist: Test Artist - DJ Nebula
   ✓ Created challenge: Test EDM Blast Challenge 🟢 Active
   ✓ Created artist skin: Test DJ Nebula Badge (PARTICIPATE, common)

✅ Test data generation completed!
   📦 House skins created: 6
   🎤 Artists created: 5
   🏆 Challenges created: 5
   🎯 Artist skins created: 7

💡 You can now use these test skins in your development!
``` 