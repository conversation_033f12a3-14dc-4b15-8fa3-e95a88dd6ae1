# Amazon SES Setup for BOP Maps 📧

This document outlines the AWS Simple Email Service (SES) configuration for BOP Maps email verification system.

## 🚀 What Was Implemented

### 1. AWS SES Infrastructure (Terraform)
- **Email Identity**: `<EMAIL>` verification
- **Domain Identity**: `bopmaps.com` domain verification  
- **DKIM Authentication**: Cryptographic email authentication
- **Configuration Set**: `bopmaps-emails` for tracking metrics
- **Email Templates**: Beautiful HTML templates with app-like design
- **CloudWatch Logging**: Email delivery monitoring
- **IAM Policies**: Secure permissions for email sending

### 2. Beautiful Email Templates 🎨
- **User Verification**: Modern gradient design for email verification codes
- **School Verification**: Campus-themed template for student verification
- **School Success**: Celebration template for completed verification
- All templates include:
  - Responsive design for mobile/desktop
  - App-consistent branding and colors
  - Fallback plain text versions
  - Accessibility features

### 3. Django Email Service Integration
- **SES Service Class**: `bopmaps/aws_ses_service.py`
- **Template Support**: Automated SES template usage
- **Fallback System**: Raw email if templates fail
- **Error Handling**: Comprehensive logging and error recovery
- **Testing Command**: Django management command for testing

### 4. Updated Email Services
- **User Authentication**: Updated to use SES templates
- **School Verification**: Enhanced with SES templates
- **Success Notifications**: Beautiful confirmation emails

## 📋 Deployment Instructions

### Step 1: Deploy SES Resources
```bash
# Deploy only SES resources for testing
./scripts/deploy-ses-only.sh
```

### Step 2: Verify Email Address
1. Go to AWS SES Console
2. Navigate to "Verified identities"
3. Find `<EMAIL>`
4. Click the verification link sent to that email

### Step 3: Configure DNS (Optional for Production)
Add the DKIM records shown in terraform output to your DNS:
```bash
# Get DKIM tokens from terraform output
terraform output ses_dkim_tokens
```

### Step 4: Test Email Sending
```bash
# Basic test
python manage.py test_ses <EMAIL>

# Test user verification template
python manage.py test_ses <EMAIL> --template=user --username=TestUser --code=123456

# Test school verification template  
python manage.py test_ses <EMAIL> --template=school --username=StudentName --school="Vanderbilt University" --code=654321

# Test school success template
python manage.py test_ses <EMAIL> --template=school-success --username=StudentName --school="Vanderbilt University"
```

## 🎯 Testing Checklist

- [ ] SES resources deployed successfully
- [ ] `<EMAIL>` email verified in SES Console
- [ ] Test email received at `<EMAIL>`
- [ ] User verification template working
- [ ] School verification template working
- [ ] School verification success template working
- [ ] Email appears in inbox (not spam)
- [ ] Email design looks good on mobile/desktop

## 🔧 Configuration

### Environment Variables
The following environment variables are automatically configured:
```bash
SES_CONFIGURATION_SET=bopmaps-emails
AWS_REGION=us-east-1  # or your configured region
DEFAULT_FROM_EMAIL=<EMAIL>
```

### AWS Permissions
The ECS tasks automatically have permissions to:
- Send emails via SES
- Use SES templates  
- Write to CloudWatch logs
- Access SES configuration sets

## 📊 Monitoring & Troubleshooting

### CloudWatch Logs
- Log Group: `/aws/ses/bopmaps`
- Contains email delivery status
- Bounce and complaint tracking

### SES Dashboard
- Sending statistics
- Bounce/complaint rates
- Suppression list management
- Template usage metrics

### Django Logs
- Email sending attempts logged
- Template fallback notifications
- Error details for debugging

## 🎨 Email Template Features

### User Verification Email
- **Subject**: `🎵 Verify Your Email - BOP Maps`
- **Design**: Colorful gradient background with app branding
- **Features**: Large verification code, feature highlights, expiration notice
- **Variables**: `{{username}}`, `{{code}}`

### School Verification Email  
- **Subject**: `🎓 Verify Your School Email - BOP Maps`
- **Design**: Campus-themed with school badge
- **Features**: Step-by-step instructions, campus feature list
- **Variables**: `{{username}}`, `{{school_name}}`, `{{code}}`

### School Verification Success
- **Subject**: `🎉 Welcome to BOP Maps - School Verification Complete!`
- **Design**: Celebration theme with feature showcase
- **Features**: Success confirmation, feature highlights, next steps
- **Variables**: `{{username}}`, `{{school_name}}`

## 🚧 Production Considerations

### SES Sandbox Mode
- **Current**: SES is in sandbox mode (can only send to verified emails)
- **Production**: Request production access to send to any email
- **Process**: Submit sending quota increase request in AWS Console

### Domain Verification (Recommended)
1. Add DKIM records to DNS
2. Verify domain in SES Console
3. Update `DEFAULT_FROM_EMAIL` to use verified domain

### Monitoring Setup
- Set up CloudWatch alarms for bounce rates
- Monitor suppression list
- Track email delivery success rates

## 🔧 Development Usage

### In Code
```python
from bopmaps.aws_ses_service import ses_service

# Send user verification
ses_service.send_user_verification_email(
    recipient_email="<EMAIL>",
    username="john_doe", 
    verification_code="123456"
)

# Send school verification
ses_service.send_school_verification_email(
    recipient_email="<EMAIL>",
    username="jane_smith",
    school_name="University Name",
    verification_code="654321"
)
```

### Testing Commands
```bash
# Test all template types
python manage.py test_ses <EMAIL> --template=user
python manage.py test_ses <EMAIL> --template=school  
python manage.py test_ses <EMAIL> --template=school-success
python manage.py test_ses <EMAIL> --template=raw
```

## 🎯 Success Criteria

✅ **Email Infrastructure**: AWS SES configured with proper templates and monitoring  
✅ **Beautiful Templates**: App-consistent design with responsive layouts  
✅ **Integration**: Django services updated to use SES  
✅ **Testing**: Management command for easy testing  
✅ **Documentation**: Complete setup and usage guide  
✅ **Security**: Proper IAM permissions and error handling  

## 🤝 Support

If you encounter issues:
1. Check CloudWatch logs for detailed error messages
2. Verify AWS credentials and permissions
3. Ensure SES identities are verified
4. Test with the management command for debugging
5. Check spam folders for test emails

The email system is now ready for production use with beautiful, app-consistent templates! 🎉 