# Spotify Caching Service Implementation

## Overview

This document describes the implementation of a Spotify caching service for BOPMaps. The service acts as a proxy between the mobile application and the Spotify API, providing intelligent caching, rate limiting, user-specific data handling, and comprehensive analytics.

## Architecture

```
Flutter App ──► /api/spotify/search/?q=coldplay&type=track&limit=20
                         │
                         ▼
Backend Cache  ──► (Redis + Database) ──► Spotify Web API
```

## Features

### Core Functionality
- **API Proxy**: Mirrors Spotify API responses with identical JSON structure
- **Dual-tier Caching**: Redis for speed + Database for persistence and analytics
- **User-specific Caching**: Separate cache handling for user data vs. public data
- **Smart Rate Limiting**: Both client-side and Spotify API rate limiting
- **Token Management**: Automatic tracking and monitoring of Spotify tokens
- **Comprehensive Analytics**: Usage statistics, cache performance metrics, token health

### Advanced Features
- **Client Credentials Support**: Automatic fallback for public endpoints
- **Cache Invalidation**: TTL-based expiration with different policies per endpoint
- **Error Handling**: Robust error handling with fallback strategies
- **Admin Interface**: Django admin integration for monitoring and management

## API Endpoints

### Public Endpoints

#### Search
```bash
GET /api/spotify/search/
```
**Parameters:**
- `q` (required): Search query
- `type` (required): Search type (`track`, `artist`, `album`, `playlist`)
- `limit` (optional): Results limit (default: 20, max: 50)
- `offset` (optional): Pagination offset
- `market` (optional): Market/country code (e.g., 'US', 'GB')

**Headers:**
- `Authorization: Bearer <token>` (optional): For authenticated requests

#### Track Details
```bash
GET /api/spotify/tracks/<track_id>/
```
**Parameters:**
- `market` (optional): Market/country code

### User-specific Endpoints (Require Authentication)

#### User's Saved Tracks
```bash
GET /api/spotify/me/tracks/
Authorization: Bearer <spotify_access_token>
```

#### User's Top Tracks
```bash
GET /api/spotify/me/top/tracks/
Authorization: Bearer <spotify_access_token>
```
**Parameters:**
- `time_range` (optional): `short_term`, `medium_term`, `long_term`
- `limit` (optional): Number of results

#### User's Top Artists
```bash
GET /api/spotify/me/top/artists/
Authorization: Bearer <spotify_access_token>
```

### Admin Endpoints

#### Cache Statistics
```bash
GET /api/spotify/admin/stats/
```

#### Cache Management
```bash
DELETE /api/spotify/admin/cleanup/
```

#### Usage Analytics
```bash
GET /api/spotify/admin/usage/
```

## Caching Strategy

### Cache Types

| Cache Type | Storage | Use Case | TTL |
|------------|---------|----------|-----|
| **Redis** | In-memory | Fast retrieval | Variable by endpoint |
| **Database (General)** | PostgreSQL | Public data persistence | 2-6 hours |
| **Database (User)** | PostgreSQL | User-specific data | 15 minutes - 2 hours |

### Cache Keys

**General Format:**
```
spotify:<sha1_hash_of_endpoint_and_params>
```

**User-specific Format:**
```
spotify:<sha1_hash_of_endpoint_params_and_user_hash>
```

### TTL Policies

| Endpoint Type | TTL | Reasoning |
|---------------|-----|-----------|
| Search Results | 2 hours | Search results change moderately |
| Track/Artist Details | 6 hours | Metadata rarely changes |
| User Saved Tracks | 15 minutes | Users actively modify libraries |
| User Top Items | 2 hours | Personal stats update slowly |

## Rate Limiting

### Client-side Limits
- **General**: 100 requests/minute per IP
- **Burst Protection**: Short-term throttling for rapid requests

### Spotify API Limits
- **Global**: 600 requests/minute (10 req/sec average)
- **Per-endpoint Tracking**: Separate limits for different endpoints
- **Automatic Backoff**: Service pauses when limits approached

## Database Schema

### Core Models

#### SpotifyCacheEntry
```python
- cache_key (unique): Generated cache identifier
- endpoint: Spotify API endpoint
- query_hash: Hash of request parameters
- response_data (JSON): Cached API response
- user_specific (boolean): Whether cache is user-specific
- expires_at: Cache expiration timestamp
- hit_count: Number of cache hits
- created_at/updated_at: Timestamps
```

#### SpotifyUserCache
```python
- cache_key (unique): User-specific cache identifier
- endpoint: Spotify API endpoint
- user_hash: Privacy-preserving user identifier
- response_data (JSON): Cached API response
- expires_at: Cache expiration timestamp
- hit_count: Usage tracking
```

#### SpotifyApiUsage
```python
- date: Statistics date
- endpoint: API endpoint
- total_requests: Total requests received
- cache_hits/misses: Cache performance
- api_calls_made: Actual Spotify API calls
- average_response_time: Performance metrics
- unique_users: Daily active users
```

#### SpotifyTokenUsage
```python
- token_hash: Privacy-preserving token identifier
- request_count: Total requests with token
- successful_requests: Successful API calls
- failed_requests: Failed API calls
- is_active: Token status
- first_used/last_used: Usage timestamps
```

## Configuration

### Environment Variables
```bash
# Required
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret

# Optional (defaults provided)
SPOTIFY_CACHE_TTL=7200  # 2 hours
SPOTIFY_USER_CACHE_TTL=900  # 15 minutes
SPOTIFY_RATE_LIMIT=600  # requests per minute
```

### Django Settings
```python
# Add to INSTALLED_APPS
INSTALLED_APPS = [
    # ...
    'spotify',
]

# Rate limiting configuration
REST_FRAMEWORK = {
    'DEFAULT_THROTTLE_RATES': {
        'spotify': '100/min',
    },
}
```

## Usage Examples

### Flutter Integration
```dart
// Search for tracks
final response = await http.get(
  Uri.parse('${AppConstants.baseApiUrl}/api/spotify/search/'),
  headers: {'Content-Type': 'application/json'},
  queryParameters: {
    'q': 'coldplay',
    'type': 'track',
    'limit': '20',
  },
);

// User's top tracks (requires authentication)
final response = await http.get(
  Uri.parse('${AppConstants.baseApiUrl}/api/spotify/me/top/tracks/'),
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer $userAccessToken',
  },
);
```

### Direct HTTP Requests
```bash
# Search (public)
curl -X GET "http://localhost:8000/api/spotify/search/" \
  -G -d "q=coldplay" -d "type=track" -d "limit=10"

# User tracks (authenticated)
curl -X GET "http://localhost:8000/api/spotify/me/tracks/" \
  -H "Authorization: Bearer YOUR_SPOTIFY_TOKEN"

# Cache statistics (admin)
curl -X GET "http://localhost:8000/api/spotify/admin/stats/"
```

## Monitoring and Management

### Django Admin Interface
- **Cache Entries**: View, filter, and manage cache entries
- **Usage Statistics**: Track API usage and performance
- **Token Management**: Monitor token health and usage
- **Rate Limiting**: View current rate limit status

### Management Commands
```bash
# Clean up expired cache entries
python manage.py cleanup_spotify_cache

# Clean up with custom parameters
python manage.py cleanup_spotify_cache --days=3 --dry-run

# View what would be cleaned
python manage.py cleanup_spotify_cache --dry-run
```

### Health Monitoring
- **Cache Hit Rate**: Target >70% for optimal performance
- **API Response Time**: Monitor for degradation
- **Token Success Rate**: Track authentication issues
- **Rate Limit Usage**: Prevent hitting Spotify limits

## Error Handling

### Common Error Responses

#### Invalid Request (400)
```json
{
  "error": "invalid_request",
  "message": "Invalid request parameters",
  "details": {
    "field": ["Error description"]
  }
}
```

#### Unauthorized (401)
```json
{
  "error": "invalid_token",
  "message": "Access token required"
}
```

#### Rate Limited (429)
```json
{
  "error": "rate_limit_exceeded",
  "message": "Rate limit exceeded, please try again later"
}
```

#### Service Unavailable (502)
```json
{
  "error": "service_unavailable",
  "message": "Spotify service temporarily unavailable"
}
```

## Performance Optimization

### Best Practices
1. **Cache Warming**: Pre-populate cache for popular queries
2. **Batch Requests**: Use batch endpoints when available
3. **Client-side Caching**: Implement additional caching in Flutter
4. **Request Deduplication**: Avoid duplicate simultaneous requests

### Monitoring Metrics
- **Cache Hit Rate**: >70% target
- **Average Response Time**: <200ms target
- **Error Rate**: <1% target
- **Token Success Rate**: >95% target

## Security Considerations

### Data Privacy
- **Token Hashing**: Spotify tokens are hashed for privacy
- **User Anonymization**: User identifiers are hashed
- **Response Filtering**: Sensitive data removed from cache

### Access Control
- **Rate Limiting**: Prevents abuse
- **Token Validation**: Validates Spotify tokens
- **Admin Restrictions**: Admin endpoints should be restricted

## Deployment

### Development Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Start development server
python manage.py runserver
```

### Production Considerations
- **Redis Configuration**: Set up Redis cluster for high availability
- **Database Optimization**: Ensure proper indexing
- **Load Balancing**: Distribute requests across multiple instances
- **Monitoring**: Set up comprehensive monitoring and alerting

## Testing

### Run Tests
```bash
# Run all Spotify tests
python manage.py test spotify

# Run specific test cases
python manage.py test spotify.tests.SpotifyServiceTestCase

# Run with coverage
coverage run manage.py test spotify
coverage report
```

### Test Categories
- **Unit Tests**: Service methods and model functionality
- **Integration Tests**: Full caching flow and database interactions
- **API Tests**: Endpoint responses and error handling
- **Performance Tests**: Cache efficiency and response times

## Troubleshooting

### Common Issues

#### High Cache Miss Rate
- Check TTL settings
- Verify cache key generation
- Monitor parameter variations

#### Rate Limit Issues
- Review request patterns
- Check rate limit configuration
- Monitor burst traffic

#### Token Authentication Failures
- Verify token format and validity
- Check Spotify API credentials
- Monitor token expiration

### Debug Commands
```bash
# Check cache statistics
python manage.py shell -c "
from spotify.services import SpotifyService
service = SpotifyService()
print(service.get_cache_stats())
"

# Clear all cache
python manage.py shell -c "
from django.core.cache import cache
cache.clear()
"
```

## Future Enhancements

### Planned Features
- **Intelligent Prefetching**: Predict and pre-load likely requests
- **Geographic Caching**: Location-based cache optimization
- **ML-based TTL**: Dynamic TTL based on request patterns
- **WebSocket Support**: Real-time cache invalidation
- **Advanced Analytics**: User behavior insights

### Scalability Improvements
- **Cache Sharding**: Distribute cache across multiple Redis instances
- **CDN Integration**: Use CDN for static content caching
- **Database Read Replicas**: Improve read performance
- **Microservice Architecture**: Split into focused services 