# Spotify PKCE Authentication Implementation

## Overview

This implementation provides secure PKCE-based Spotify authentication for mobile applications. The backend serves as a token swap service that exchanges authorization codes for access and refresh tokens while maintaining security best practices.

## Features

- ✅ PKCE (Proof Key for Code Exchange) support
- ✅ Rate limiting (100 requests/minute per IP)
- ✅ Input validation and sanitization
- ✅ Secure error handling
- ✅ Comprehensive logging
- ✅ CSRF protection exemption for API endpoints
- ✅ Redirect URI validation

## Endpoints

### 1. Token Exchange
**POST** `/api/music/spotify/token/exchange/`

Exchange authorization code for access/refresh tokens.

**Request Body:**
```json
{
  "code": "authorization_code_from_spotify",
  "code_verifier": "pkce_code_verifier",
  "redirect_uri": "bopmaps://callback"
}
```

**Response:**
```json
{
  "access_token": "BQB...XYZ",
  "refresh_token": "AQA...123",
  "expires_in": 3600,
  "token_type": "Bearer",
  "scope": "user-read-private user-read-email"
}
```

### 2. Token Refresh
**POST** `/api/music/spotify/token/refresh/`

Refresh an expired access token.

**Request Body:**
```json
{
  "refresh_token": "refresh_token_from_client"
}
```

**Response:**
```json
{
  "access_token": "new_access_token",
  "refresh_token": "new_or_same_refresh_token",
  "expires_in": 3600,
  "token_type": "Bearer"
}
```

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Spotify PKCE Configuration
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here
SPOTIFY_REDIRECT_URI=http://localhost:8888/callback
SPOTIFY_MOBILE_REDIRECT_URI=bopmaps://callback
ALLOWED_REDIRECT_URIS=bopmaps://callback,your-app://callback

# Security Settings
DEBUG=False  # Set to False in production
SECRET_KEY=your-very-secure-secret-key
```

### Django Settings

The following settings are automatically configured:

- `SPOTIFY_TOKEN_URL`: Spotify's token endpoint
- `ALLOWED_REDIRECT_URIS`: List of valid redirect URIs
- Rate limiting via `django-ratelimit`
- CORS headers for API access

## Installation

1. **Install Dependencies:**
   ```bash
   pip install django-ratelimit==4.1.0
   ```

2. **Configure Environment Variables:**
   Set up your `.env` file with the required Spotify credentials.

3. **Run Migrations:**
   ```bash
   python manage.py migrate
   ```

4. **Test the Endpoints:**
   ```bash
   python manage.py runserver
   ```

## Security Features

### 1. Rate Limiting
- 100 requests per minute per IP address
- Prevents brute force attacks
- Configurable rate limits

### 2. Input Validation
- All request parameters are validated using Django REST Framework serializers
- Redirect URI validation against whitelist
- PKCE code verifier validation

### 3. Error Handling
- Generic error messages to prevent information disclosure
- Detailed logging for debugging
- Proper HTTP status codes

### 4. CSRF Protection
- API endpoints are exempt from CSRF protection (safe for mobile apps)
- Bearer token authentication for subsequent API calls

## Testing

### Manual Testing

1. **Test Token Exchange:**
   ```bash
   curl -X POST http://localhost:8000/api/music/spotify/token/exchange/ \
     -H "Content-Type: application/json" \
     -d '{
       "code": "your_auth_code",
       "code_verifier": "your_code_verifier",
       "redirect_uri": "bopmaps://callback"
     }'
   ```

2. **Test Token Refresh:**
   ```bash
   curl -X POST http://localhost:8000/api/music/spotify/token/refresh/ \
     -H "Content-Type: application/json" \
     -d '{
       "refresh_token": "your_refresh_token"
     }'
   ```

### Expected Responses

**Success (200):**
```json
{
  "access_token": "BQB...",
  "refresh_token": "AQA...",
  "expires_in": 3600,
  "token_type": "Bearer"
}
```

**Error (400):**
```json
{
  "error": "Invalid authorization code or parameters"
}
```

**Rate Limited (429):**
```json
{
  "error": "Too many requests"
}
```

## Monitoring

### Logs

The implementation logs all important events:

- Successful token exchanges
- Failed authentication attempts
- Rate limit violations
- Invalid redirect URI attempts
- Network errors

### Key Metrics to Monitor

- Token exchange success rate
- Token refresh success rate
- Rate limit hit frequency
- Error rate by type
- Response times

## Deployment Considerations

### Production Settings

1. **Environment Variables:**
   ```bash
   DEBUG=False
   SECURE_SSL_REDIRECT=True
   ALLOWED_REDIRECT_URIS=your-production-app://callback
   ```

2. **SSL/HTTPS:**
   - Always use HTTPS in production
   - Configure proper SSL certificates
   - Enable HSTS headers

3. **Database:**
   - Use PostgreSQL for production
   - Configure connection pooling
   - Set up database backups

### Scaling

1. **Load Balancing:**
   - Use a load balancer for multiple app instances
   - Configure sticky sessions if needed

2. **Caching:**
   - Redis is already configured for caching
   - Consider caching token validation results

3. **Monitoring:**
   - Set up application monitoring (e.g., Sentry)
   - Configure log aggregation
   - Monitor API response times

## Troubleshooting

### Common Issues

1. **Invalid Client Credentials:**
   - Verify `SPOTIFY_CLIENT_ID` and `SPOTIFY_CLIENT_SECRET`
   - Ensure they match your Spotify app configuration

2. **Invalid Redirect URI:**
   - Check that redirect URI is in `ALLOWED_REDIRECT_URIS`
   - Verify it matches your Spotify app settings

3. **Rate Limiting:**
   - Implement exponential backoff in client
   - Consider implementing user-based rate limiting

4. **CORS Issues:**
   - Configure `CORS_ALLOWED_ORIGINS` for your frontend domain
   - Ensure preflight requests are handled correctly

### Debug Mode

To enable verbose logging, set:
```python
LOGGING['loggers']['bopmaps']['level'] = 'DEBUG'
```

## Security Checklist

- [ ] Client secret is stored as environment variable
- [ ] Rate limiting is enabled and tested
- [ ] CORS is properly configured
- [ ] SSL/TLS is enabled in production
- [ ] Input validation is implemented
- [ ] Error messages don't expose sensitive information
- [ ] Logging is configured for security events
- [ ] Dependencies are kept up to date

## Integration with Flutter App

Your Flutter app should:

1. **Generate PKCE parameters:**
   ```dart
   final codeVerifier = generateCodeVerifier();
   final codeChallenge = generateCodeChallenge(codeVerifier);
   ```

2. **Call your backend endpoints:**
   ```dart
   final response = await http.post(
     Uri.parse('${baseUrl}/api/music/spotify/token/exchange/'),
     headers: {'Content-Type': 'application/json'},
     body: jsonEncode({
       'code': authCode,
       'code_verifier': codeVerifier,
       'redirect_uri': 'bopmaps://callback',
     }),
   );
   ```

3. **Handle token refresh:**
   ```dart
   final refreshResponse = await http.post(
     Uri.parse('${baseUrl}/api/music/spotify/token/refresh/'),
     headers: {'Content-Type': 'application/json'},
     body: jsonEncode({
       'refresh_token': storedRefreshToken,
     }),
   );
   ```

## Support

For issues or questions:
1. Check the logs for detailed error messages
2. Verify all environment variables are set correctly
3. Test with the provided curl commands
4. Check that all dependencies are installed 