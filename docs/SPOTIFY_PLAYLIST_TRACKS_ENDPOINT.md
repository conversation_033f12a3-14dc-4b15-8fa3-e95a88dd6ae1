# Spotify Playlist Tracks Endpoint

## Overview

The playlist tracks endpoint allows you to fetch tracks from any Spotify playlist by its ID. This endpoint supports both GET and POST methods and includes full caching support.

## Endpoint

**URL Pattern**: `/api/spotify/playlists/{playlist_id}/tracks/`

**Methods**: GET, POST

**Authentication**: Optional (required only for private playlists)

## URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `playlist_id` | string | Yes | Spotify playlist ID (e.g., `37i9dQZF1DX0XUsuxWHRQd`) |

## Query Parameters (GET) / Request Body (POST)

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `access_token` | string | No | - | Spotify access token (required for private playlists) |
| `limit` | integer | No | 50 | Number of tracks (1-100) |
| `offset` | integer | No | 0 | Pagination offset |
| `market` | string | No | - | Country code (e.g., "US", "GB") |

## Usage Examples

### GET Request
```bash
# Public playlist (no auth needed)
curl "http://localhost:8000/api/spotify/playlists/37i9dQZF1DX0XUsuxWHRQd/tracks/?limit=10"

# Private playlist (with auth)
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/spotify/playlists/PRIVATE_PLAYLIST_ID/tracks/?limit=20"
```

### POST Request
```bash
# Public playlist
curl -X POST http://localhost:8000/api/spotify/playlists/37i9dQZF1DX0XUsuxWHRQd/tracks/ \
  -H "Content-Type: application/json" \
  -d '{"limit": 10, "offset": 0}'

# Private playlist  
curl -X POST http://localhost:8000/api/spotify/playlists/PRIVATE_PLAYLIST_ID/tracks/ \
  -H "Content-Type: application/json" \
  -d '{"access_token": "YOUR_TOKEN", "limit": 20}'
```

### JavaScript (fetch)
```javascript
// Public playlist
const response = await fetch('/api/spotify/playlists/37i9dQZF1DX0XUsuxWHRQd/tracks/?limit=10');
const data = await response.json();

// Private playlist via POST
const response = await fetch('/api/spotify/playlists/PRIVATE_PLAYLIST_ID/tracks/', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        access_token: userSpotifyToken,
        limit: 20
    })
});
```

## Response Format

```json
{
    "href": "https://api.spotify.com/v1/playlists/37i9dQZF1DX0XUsuxWHRQd/tracks",
    "items": [
        {
            "added_at": "2023-12-01T00:00:00Z",
            "added_by": {
                "external_urls": {...},
                "id": "spotify",
                "type": "user"
            },
            "is_local": false,
            "track": {
                "album": {...},
                "artists": [{...}],
                "duration_ms": 210000,
                "explicit": false,
                "external_ids": {...},
                "external_urls": {...},
                "href": "https://api.spotify.com/v1/tracks/TRACK_ID",
                "id": "TRACK_ID",
                "name": "Track Name",
                "popularity": 85,
                "preview_url": "https://...",
                "track_number": 1,
                "type": "track",
                "uri": "spotify:track:TRACK_ID"
            }
        }
    ],
    "limit": 50,
    "next": "https://api.spotify.com/v1/playlists/37i9dQZF1DX0XUsuxWHRQd/tracks?offset=50&limit=50",
    "offset": 0,
    "previous": null,
    "total": 100
}
```

## Error Responses

### 404 Not Found
```json
{
    "error": "not_found",
    "message": "Playlist not found"
}
```

### 401 Unauthorized
```json
{
    "error": "unauthorized", 
    "message": "Access token required for private playlist"
}
```

### 400 Bad Request
```json
{
    "error": "invalid_request",
    "message": "Invalid request parameters",
    "details": {
        "limit": ["Ensure this value is less than or equal to 100."]
    }
}
```

## Features

- ✅ **Public Playlist Support**: Access public playlists without authentication
- ✅ **Private Playlist Support**: Access private playlists with user tokens
- ✅ **Pagination**: Support for limit/offset pagination
- ✅ **Caching**: Full Redis and database caching support
- ✅ **Rate Limiting**: Cache-aware rate limiting (cached requests don't count)
- ✅ **Market Support**: Country-specific track availability
- ✅ **Dual Methods**: Both GET and POST support
- ✅ **Error Handling**: Proper HTTP status codes and error messages

## Caching Behavior

- **Cache Duration**: 2 hours for playlist tracks
- **Cache Key**: Based on playlist ID, limit, offset, and market
- **Cache-Aware Rate Limiting**: Cached responses don't count against rate limits
- **User-Specific Caching**: Private playlists are cached per user

## Rate Limits

- **Uncached Requests**: 100 per minute per IP
- **Cached Requests**: Unlimited (served instantly from cache)

## Common Playlist IDs for Testing

| Playlist | ID | Description |
|----------|----|-----------| 
| RapCaviar | `37i9dQZF1DX0XUsuxWHRQd` | Popular rap playlist |
| Today's Top Hits | `37i9dQZF1DXcBWIGoYBM5M` | Global top songs |
| Peaceful Piano | `37i9dQZF1DX4sWSpwABfVy` | Relaxing piano music |

## Implementation Notes

- The endpoint uses Spotify's `/playlists/{id}/tracks` API
- Authentication is handled automatically (user token → client credentials fallback)
- All responses maintain Spotify's original structure
- Supports all Spotify playlist types (user playlists, Spotify curated, etc.)

## Migration from 404 Errors

If you were previously getting 404 errors for playlist tracks URLs, they should now work immediately:

**Before**: `404 Not Found`  
**After**: `200 OK` with playlist tracks data

No client-side changes required - the fix is entirely backend-side. 