# Spotify POST Endpoints Documentation

## Overview

All Spotify user-specific endpoints (`/me/*`) now support both GET and POST methods. This allows you to pass the Spotify access token in the request body instead of the Authorization header, which can be useful for frontend applications.

## Endpoints Supporting POST

### 1. Search Tracks
**Endpoint:** `POST /api/spotify/search/`

**Request Body:**
```json
{
    "access_token": "your_spotify_access_token",  // Optional
    "q": "search query",
    "type": "track",
    "limit": 20,
    "offset": 0,
    "market": "US"
}
```

### 2. User's Top Tracks
**Endpoint:** `POST /api/spotify/me/top/tracks/`

**Request Body:**
```json
{
    "access_token": "your_spotify_access_token",  // Required
    "time_range": "medium_term",  // Options: short_term, medium_term, long_term
    "limit": 20,
    "offset": 0
}
```

### 3. User's Top Artists
**Endpoint:** `POST /api/spotify/me/top/artists/`

**Request Body:**
```json
{
    "access_token": "your_spotify_access_token",  // Required
    "time_range": "medium_term",  // Options: short_term, medium_term, long_term
    "limit": 20,
    "offset": 0
}
```

### 4. User's Saved Tracks
**Endpoint:** `POST /api/spotify/me/tracks/`

**Request Body:**
```json
{
    "access_token": "your_spotify_access_token",  // Required
    "limit": 50,
    "offset": 0,
    "market": "US"
}
```

### 5. User's Recently Played Tracks
**Endpoint:** `POST /api/spotify/me/player/recently-played/`

**Request Body:**
```json
{
    "access_token": "your_spotify_access_token",  // Required
    "limit": 20
}
```

### 6. Playlist Tracks
**Endpoint:** `POST /api/spotify/playlists/{playlist_id}/tracks/`

**Request Body:**
```json
{
    "access_token": "your_spotify_access_token",  // Optional for public playlists
    "limit": 50,
    "offset": 0,
    "market": "US"
}
```

## Example Usage

### Using curl:
```bash
curl -X POST http://localhost:8000/api/spotify/me/top/tracks/ \
  -H "Content-Type: application/json" \
  -d '{
    "access_token": "BQD...your_token_here",
    "time_range": "short_term",
    "limit": 10
  }'
```

### Using JavaScript (fetch):
```javascript
const response = await fetch('http://localhost:8000/api/spotify/me/top/tracks/', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        access_token: userSpotifyToken,
        time_range: 'medium_term',
        limit: 20
    })
});

const data = await response.json();
```

### Using Python (requests):
```python
import requests

response = requests.post(
    'http://localhost:8000/api/spotify/me/top/tracks/',
    json={
        'access_token': spotify_token,
        'time_range': 'long_term',
        'limit': 50
    }
)

data = response.json()
```

## Response Format

All endpoints return the same response format as their GET counterparts. Successful responses will contain the Spotify API data, while errors will follow this format:

```json
{
    "error": "error_code",
    "message": "Human-readable error message",
    "details": {}  // Optional, contains validation errors
}
```

## Common Error Responses

- **401 Unauthorized**: Invalid or expired Spotify access token
- **400 Bad Request**: Invalid request parameters
- **502 Bad Gateway**: Spotify service temporarily unavailable
- **500 Internal Server Error**: Server error

## Notes

1. The GET methods with Authorization headers are still supported and work exactly as before.
2. For POST requests, the `access_token` is required for all user-specific endpoints (`/me/*`).
3. For the search endpoint, `access_token` is optional - if not provided, it will use client credentials.
4. All rate limiting and caching mechanisms work the same for both GET and POST methods. 