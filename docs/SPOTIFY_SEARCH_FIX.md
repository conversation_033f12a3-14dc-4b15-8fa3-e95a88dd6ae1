# Spotify Search Type Fix

## Issue
The Spotify search endpoint was not properly handling the `type` parameter. When clients requested `type=playlist`, the backend was still searching for tracks and returning a `tracks` response instead of a `playlists` response.

## Root Cause
The `search_tracks()` method in `SpotifyService` was hardcoded to always use `type=track`, ignoring the client's requested search type.

```python
# OLD - Always searched for tracks
def search_tracks(self, query: str, ...):
    params = {
        'q': query,
        'type': 'track',  # ← Hardcoded!
        'limit': str(limit),
        'offset': str(offset),
    }
```

## Solution
1. **Created Generic Search Method**: New `search()` method that accepts a `search_type` parameter
2. **Updated Views**: Both GET and POST endpoints now properly forward the `type` parameter
3. **Backward Compatibility**: Kept `search_tracks()` method for existing code

```python
# NEW - Generic search that respects type parameter
def search(self, query: str, search_type: str = 'track', ...):
    # Validate search type
    valid_types = ['track', 'artist', 'album', 'playlist']
    if search_type not in valid_types:
        search_type = 'track'
    
    params = {
        'q': query,
        'type': search_type,  # ← Now uses the requested type!
        'limit': str(limit),
        'offset': str(offset),
    }
```

## API Changes

### Before
```bash
# Request: type=playlist
GET /api/spotify/search/?q=jazz&type=playlist

# Response: Still returned tracks!
{
  "tracks": {
    "items": [...]
  }
}
```

### After
```bash
# Request: type=playlist  
GET /api/spotify/search/?q=jazz&type=playlist

# Response: Now correctly returns playlists!
{
  "playlists": {
    "items": [...]
  }
}
```

## Supported Search Types

| Type | Response Key | Description |
|------|-------------|-------------|
| `track` | `tracks` | Individual songs |
| `playlist` | `playlists` | User-created playlists |
| `artist` | `artists` | Artist profiles |
| `album` | `albums` | Album releases |

## Testing

All search types are now working correctly:

```bash
# Playlists
curl "http://localhost:8000/api/spotify/search/?q=jazz&type=playlist&limit=5"

# Tracks  
curl "http://localhost:8000/api/spotify/search/?q=beatles&type=track&limit=5"

# Artists
curl "http://localhost:8000/api/spotify/search/?q=taylor&type=artist&limit=5"

# Albums
curl "http://localhost:8000/api/spotify/search/?q=abbey&type=album&limit=5"
```

## Impact

- ✅ **Flutter App**: Playlist searches now work correctly
- ✅ **All Clients**: Can search for any Spotify content type
- ✅ **Backward Compatible**: Existing track searches continue to work
- ✅ **Caching**: All search types benefit from caching system

## Migration Notes

No client-side changes required. The fix is entirely backend-side and maintains full backward compatibility. 