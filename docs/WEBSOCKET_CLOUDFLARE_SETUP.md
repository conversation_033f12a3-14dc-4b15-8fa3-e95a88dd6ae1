# Enable WebSocket Support in Cloudflare

## Issue
The WebSocket connection to `wss://api.bopmaps.com/ws/achievements/` is failing with a 404 error because <PERSON>flare's proxy doesn't have WebSocket support enabled.

## Solution
You need to manually enable WebSocket support in your Cloudflare dashboard:

### Step 1: Log into Cloudflare Dashboard
1. Go to https://dash.cloudflare.com/
2. Select your domain (`bopmaps.com`)

### Step 2: Enable WebSocket Support
1. Navigate to **Network** tab in the left sidebar
2. Scroll down to find **WebSockets** setting
3. Toggle **WebSockets** to **On**

### Step 3: Verify SSL/TLS Settings
1. Navigate to **SSL/TLS** tab
2. Ensure SSL/TLS encryption mode is set to **Full** or **Full (strict)**

### Step 4: Check Speed Settings (Optional)
1. Navigate to **Speed** tab
2. Ensure **Auto Minify** settings don't interfere with WebSocket connections
3. You might want to disable minification for JavaScript if it causes issues

### Alternative: Bypass Cloudflare for WebSocket (Not Recommended)
If WebSocket support doesn't work with Cloudflare proxy:
1. Navigate to **DNS** tab
2. Find the `api.bopmaps.com` record
3. Toggle the **Proxy status** from **Proxied** (orange cloud) to **DNS only** (gray cloud)
4. **Note**: This will disable Cloudflare's CDN and security features

## Testing After Configuration
Once WebSocket support is enabled, test the connection:

```bash
cd /Users/<USER>/dev/pythonprojects/BOPMapsBackend
python scripts/test-websockets.py api.bopmaps.com
```

## Expected Result
After enabling WebSocket support, you should see:
```
✓ WebSocket connection closed normally (expected without authentication)
✓ Basic WebSocket test passed
```

The WebSocket URL will be accessible at:
```
wss://api.bopmaps.com/ws/achievements/
``` 