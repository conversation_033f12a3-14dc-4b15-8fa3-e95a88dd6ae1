# WebSocket Setup for BOPMaps Gamification

This document outlines the WebSocket configuration implemented for real-time gamification notifications in BOPMaps.

## Overview

The WebSocket implementation enables real-time communication between the server and clients for:
- Achievement notifications
- Real-time user progress updates
- Gamification events
- Live updates for user interactions

## Architecture

### WebSocket Stack
- **ASGI Server**: <PERSON> (replaced <PERSON><PERSON> for WebSocket support)
- **Channel Layer**: Redis (production) / InMemory (development)
- **WebSocket Consumer**: `gamification.consumers.AchievementConsumer`
- **Authentication**: JWT token-based authentication

### WebSocket URL Pattern
```
wss://your-domain.com/ws/achievements/
```

## Configuration Changes Made

### 1. Channel Layer Configuration (`bopmaps/settings.py`)

Updated the channel layer configuration to use Redis in production:

```python
# Channel Layers - Use Redis in production, InMemory for development/testing
if DEBUG or IS_TESTING:
    CHANNEL_LAYERS = {
        'default': {
            'BACKEND': 'channels.layers.InMemoryChannelLayer',
        },
    }
else:
    # Production/staging - use Redis for WebSocket channel layer
    CHANNEL_LAYERS = {
        'default': {
            'BACKEND': 'channels_redis.core.RedisChannelLayer',
            'CONFIG': {
                'hosts': [config('REDIS_URL', default='redis://localhost:6379/0')],
                'capacity': 1500,  # Maximum number of messages to hold in memory
                'expiry': 60,      # How long to hold messages (seconds)
                'group_expiry': 86400,  # How long to hold group membership (seconds)
                'symmetric_encryption_keys': [SECRET_KEY],
            },
        },
    }
```

### 2. Docker Configuration Updates

#### Dockerfile.prod
```dockerfile
# Changed from Gunicorn to Daphne for ASGI support
CMD ["daphne", "-b", "0.0.0.0", "-p", "8000", "--access-log", "-", "bopmaps.asgi:application"]
```

#### Dockerfile
```dockerfile
# Changed from Gunicorn to Daphne for ASGI support
CMD ["daphne", "-b", "0.0.0.0", "-p", "8000", "--access-log", "-", "bopmaps.asgi:application"]
```

### 3. Existing WebSocket Components

The following components were already properly configured:

#### ASGI Application (`bopmaps/asgi.py`)
```python
application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter(
            geo.routing.websocket_urlpatterns +
            friends.routing.websocket_urlpatterns +
            music.routing.websocket_urlpatterns +
            gamification.routing.websocket_urlpatterns
        )
    ),
})
```

#### Gamification WebSocket Consumer (`gamification/consumers.py`)
```python
class AchievementConsumer(AsyncJsonWebsocketConsumer):
    async def connect(self):
        # Connect user to their personal achievement channel
        if self.scope["user"].is_anonymous:
            await self.close()
            return
        
        self.user_id = str(self.scope["user"].id)
        self.achievement_group = f"achievements_{self.user_id}"
        
        await self.channel_layer.group_add(
            self.achievement_group,
            self.channel_name
        )
        await self.accept()
```

#### WebSocket URL Routing (`gamification/routing.py`)
```python
websocket_urlpatterns = [
    re_path(r'ws/achievements/$', consumers.AchievementConsumer.as_asgi()),
]
```

## Deployment

### 1. Build and Deploy with WebSocket Support

Use the provided deployment script:

```bash
./scripts/deploy-with-websockets.sh
```

This script:
- Builds the Docker image with Daphne ASGI server
- Pushes to ECR
- Updates ECS service with new image
- Waits for deployment completion
- Tests the deployment

### 2. Test WebSocket Connection

Use the provided test script:

```bash
# Basic connection test
python scripts/test-websockets.py your-domain.com

# Authenticated connection test
python scripts/test-websockets.py your-domain.com your-jwt-token
```

## ECS Configuration

The existing ECS configuration already includes:
- Redis cluster for channel layer
- Proper security groups for WebSocket traffic
- Load balancer configuration that supports WebSocket connections

## Usage Example

### Client-Side Connection (JavaScript)
```javascript
// Connect to WebSocket with authentication
const token = 'your-jwt-token';
const ws = new WebSocket('wss://your-domain.com/ws/achievements/', [], {
    headers: {
        'Authorization': `Bearer ${token}`
    }
});

ws.onopen = function() {
    console.log('Connected to achievement notifications');
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Achievement update:', data);
    // Handle achievement notification
};
```

### Server-Side Notification (Python)
```python
from gamification.consumers import AchievementConsumer

# Send achievement notification to user
await AchievementConsumer.notify_achievement(user_id, {
    'type': 'achievement_unlocked',
    'achievement_id': achievement.id,
    'title': achievement.title,
    'description': achievement.description,
    'timestamp': datetime.now().isoformat()
})
```

## Monitoring and Troubleshooting

### Check WebSocket Connection
```bash
# Check ECS service status
aws ecs describe-services --cluster bopmaps-prod --services bopmaps-prod-app

# Check application logs
aws logs tail /ecs/bopmaps-prod/app --follow

# Check Redis connectivity
aws elasticache describe-replication-groups --replication-group-id your-redis-cluster
```

### Common Issues

1. **Connection Refused**: Check if Daphne is running and port 8000 is properly exposed
2. **Authentication Failed**: Verify JWT token is valid and properly formatted
3. **Redis Connection Error**: Check Redis cluster status and network connectivity
4. **Channel Layer Issues**: Verify Redis URL and channel layer configuration

## Security Considerations

1. **Authentication**: All WebSocket connections require valid JWT tokens
2. **Authorization**: Users can only subscribe to their own achievement channels
3. **Rate Limiting**: Consider implementing rate limiting for WebSocket connections
4. **CORS**: Ensure proper CORS configuration for WebSocket origins

## Performance Optimization

1. **Connection Pooling**: Redis connection pooling is configured for optimal performance
2. **Message Expiry**: Messages expire after 60 seconds to prevent memory buildup
3. **Group Expiry**: Group memberships expire after 24 hours
4. **Capacity Limits**: Channel layer capacity is set to 1500 messages

## Future Enhancements

1. **Multiple Channels**: Add support for other real-time features (chat, live updates)
2. **Message Persistence**: Consider persisting important messages for offline users
3. **Scaling**: Implement horizontal scaling for WebSocket connections
4. **Monitoring**: Add WebSocket-specific monitoring and alerting

## Dependencies

Ensure these packages are in `requirements.txt`:
```
channels>=4.0.0
channels-redis>=4.1.0
daphne==4.0.0
```

All dependencies are already included in the current `requirements.txt` file.

## Testing

The WebSocket implementation includes comprehensive testing:
- Unit tests for consumers
- Integration tests for channel layer
- End-to-end tests for real-time notifications
- Performance tests for connection handling

Use the provided test script to verify deployment:
```bash
python scripts/test-websockets.py your-domain.com
```

This ensures the WebSocket server is properly configured and ready for real-time gamification notifications. 