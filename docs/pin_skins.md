# Pin Skins System Documentation

## Overview

The Pin Skins system allows users to customize the appearance of their pins through a hybrid system of **House skins** (always available or premium-gated) and **Artist-limited skins** (unlocked through weekly challenges).

## Model Structure

### PinSkin Model
```python
class PinSkin(models.Model):
    HOUSE = "HOUSE"
    ARTIST = "ARTIST"
    SKIN_TYPES = [(HOUSE, "House"), (ARTIST, "Artist limited")]

    name = models.CharField(max_length=50)
    slug = models.SlugField(unique=True)
    image = models.ImageField(upload_to="pin_skins/")
    description = models.TextField(blank=True)
    skin_type = models.CharField(max_length=10, choices=SKIN_TYPES, default=HOUSE)
    artist = models.ForeignKey("music.Artist", null=True, blank=True, on_delete=models.SET_NULL)
    is_premium = models.BooleanField(default=False)
    challenge = models.ForeignKey("WeeklyChallenge", null=True, blank=True, on_delete=models.SET_NULL)
    metadata = models.JSONField(default=dict, blank=True)
```

### UserSkin Model
```python
class UserSkin(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    skin = models.ForeignKey(PinSkin, on_delete=models.CASCADE)
    unlocked_at = models.DateTimeField(auto_now_add=True)
```

### WeeklyChallenge Model
```python
class WeeklyChallenge(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField()
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    max_participants = models.PositiveIntegerField(null=True, blank=True)
```

## Unlock Rules

### House Skins
- **Non-premium**: Always available to all users
- **Premium**: Requires premium subscription

### Artist Skins
Tied to weekly challenges with two unlock types:

1. **PARTICIPATE**: Unlocked immediately when user participates in challenge
   ```json
   {"unlock_type": "PARTICIPATE"}
   ```

2. **TOP_N**: Only top N participants by vote score can claim after challenge ends
   ```json
   {"unlock_type": "TOP_N", "n": 3}
   ```

## API Endpoints

### Skins Management
- `GET /api/v1/pins/skins/` - List all skins
- `GET /api/v1/pins/skins/available/` - Get available skins with lock status
- `GET /api/v1/pins/skins/unlocked/` - Get user's unlocked skins
- `POST /api/v1/pins/skins/{id}/claim/` - Claim a skin (if eligible)
- `POST /api/v1/pins/skins/{id}/equip/` - Equip a skin

### Challenges
- `GET /api/v1/pins/challenges/` - List challenges
- `GET /api/v1/pins/challenges/active/` - Get active challenges
- `GET /api/v1/pins/challenges/upcoming/` - Get upcoming challenges
- `POST /api/v1/pins/challenge-participations/` - Participate in challenge

## Example API Responses

### Skin with Lock Status
```json
{
  "id": 1,
  "name": "Synth Badge",
  "slug": "synth-badge",
  "image_url": "https://cdn.../synth_badge.png",
  "skin_type": "ARTIST",
  "artist": {
    "id": 1,
    "name": "Deadmau5"
  },
  "locked": true,
  "unlock_rule": "COMPLETE_WEEKLY_CHALLENGE",
  "challenge_id": 42,
  "metadata": {"unlock_type": "PARTICIPATE"}
}
```

### Available House Skin
```json
{
  "id": 2,
  "name": "Classic House",
  "slug": "classic-house",
  "skin_type": "HOUSE",
  "locked": false,
  "unlock_rule": "ALWAYS_AVAILABLE",
  "is_premium": false
}
```

## Celery Tasks

### Scheduled Tasks
- `close_completed_challenges()` - Daily @ 00:15 UTC
  - Marks expired challenges as inactive
  - Grants TOP_N skins to winners
  - Sends notifications

- `cleanup_inactive_challenges()` - Weekly
  - Cleans up old inactive challenges

### Realtime Tasks
- `auto_unlock_participation_skin(challenge_id, user_id)`
  - Triggered on challenge participation
  - Auto-unlocks PARTICIPATE type skins

## Admin Usage

### Creating Artist Skins
1. Navigate to Admin → Pin Skins
2. Click "Add Pin Skin"
3. Set:
   - **Name**: "Deadmau5 Badge"
   - **Skin Type**: "Artist limited"
   - **Artist**: Select from dropdown
   - **Challenge**: Link to weekly challenge
   - **Metadata**: `{"unlock_type": "PARTICIPATE"}` or `{"unlock_type": "TOP_N", "n": 3}`

### Creating Weekly Challenges
1. Navigate to Admin → Weekly Challenges
2. Set start/end dates
3. Create associated artist skins with challenge linked

## Client Integration Examples

### Check Available Skins
```javascript
fetch('/api/v1/pins/skins/available/')
  .then(response => response.json())
  .then(skins => {
    skins.forEach(skin => {
      if (skin.locked) {
        console.log(`${skin.name} is locked: ${skin.unlock_rule}`);
      } else {
        console.log(`${skin.name} is available to equip`);
      }
    });
  });
```

### Participate in Challenge
```javascript
fetch('/api/v1/pins/challenge-participations/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    challenge: 42,
    pin: 123  // or virtual_pin: 456
  })
});
// PARTICIPATE skins will be auto-unlocked
```

### Claim Skin After Challenge
```javascript
fetch('/api/v1/pins/skins/synth-badge/claim/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  }
})
.then(response => {
  if (response.ok) {
    console.log('Skin claimed successfully!');
  } else {
    console.log('Not eligible to claim this skin');
  }
});
```

## Migration Guide

The PinSkin model has been moved from `gamification` app to `pins` app. Update any imports:

```python
# Before
from gamification.models import PinSkin

# After  
from pins.models import PinSkin
```

Foreign key references have been updated automatically through migrations. 