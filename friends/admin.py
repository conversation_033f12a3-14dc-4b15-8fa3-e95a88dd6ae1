from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.db.models import Q
from .models import Friend

@admin.register(Friend)
class FriendAdmin(admin.ModelAdmin):
    list_display = ('requester_link', 'recipient_link', 'status', 'created_at', 'updated_at')
    list_filter = ('status', 'created_at')
    search_fields = ('requester__username', 'recipient__username', 'status')
    raw_id_fields = ('requester', 'recipient')
    list_select_related = ('requester', 'recipient')
    date_hierarchy = 'created_at'
    
    def requester_link(self, obj):
        url = reverse('admin:friends_friend_changelist')
        filter_url = f"{url}?q={obj.requester.username}"
        return format_html(
            '<div style="display: flex; gap: 10px; align-items: center;">'
            '<span>{}</span>'
            '<a href="{}" class="button" style="background: #79aec8; padding: 5px 10px; border-radius: 4px; color: white; text-decoration: none;">View Friends</a>'
            '</div>',
            obj.requester.username,
            filter_url,
        )
    requester_link.short_description = 'Requester'
    requester_link.admin_order_field = 'requester__username'
    
    def recipient_link(self, obj):
        url = reverse('admin:friends_friend_changelist')
        filter_url = f"{url}?q={obj.recipient.username}"
        return format_html(
            '<div style="display: flex; gap: 10px; align-items: center;">'
            '<span>{}</span>'
            '<a href="{}" class="button" style="background: #79aec8; padding: 5px 10px; border-radius: 4px; color: white; text-decoration: none;">View Friends</a>'
            '</div>',
            obj.recipient.username,
            filter_url,
        )
    recipient_link.short_description = 'Recipient'
    recipient_link.admin_order_field = 'recipient__username'

    def get_search_results(self, request, queryset, search_term):
        """Custom search to find all friendships for a user"""
        queryset, may_have_duplicates = super().get_search_results(request, queryset, search_term)
        
        if search_term:
            user_filter = (
                Q(requester__username__icontains=search_term) |
                Q(recipient__username__icontains=search_term)
            )
            queryset |= self.model.objects.filter(user_filter)
        
        return queryset, True
    
    def save_model(self, request, obj, form, change):
        if not change and not obj.status:
            obj.status = 'accepted'
        super().save_model(request, obj, form, change)
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name in ["requester", "recipient"]:
            kwargs["queryset"] = db_field.related_model.objects.order_by('username')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        if not obj:
            form.base_fields['status'].initial = 'accepted'
        return form
    
    class Media:
        css = {
            'all': ('admin/css/widgets.css',)
        }
        js = ('admin/js/admin/RelatedObjectLookups.js',)
