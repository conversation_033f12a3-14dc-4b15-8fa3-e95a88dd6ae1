import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.core.cache import cache
from django.utils import timezone

class FriendConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        """
        Connect to the WebSocket and join user-specific group
        """
        if not self.scope["user"].is_authenticated:
            await self.close()
            return

        self.user_group_name = f"user_{self.scope['user'].id}_friends"
        
        # Join user-specific group
        await self.channel_layer.group_add(
            self.user_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Send initial online status
        await self.update_user_status(True)

    async def disconnect(self, close_code):
        """
        Leave groups and update status on disconnect
        """
        if hasattr(self, 'user_group_name'):
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )
            await self.update_user_status(False)

    async def receive(self, text_data):
        """
        Handle incoming WebSocket messages
        """
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'friend_request':
                await self.handle_friend_request(data)
            elif message_type == 'friend_response':
                await self.handle_friend_response(data)
            elif message_type == 'friend_activity':
                await self.handle_friend_activity(data)
            elif message_type == 'status_update':
                await self.handle_status_update(data)
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'error': 'Invalid JSON format'
            }))
        except Exception as e:
            await self.send(text_data=json.dumps({
                'error': str(e)
            }))

    @database_sync_to_async
    def update_user_status(self, is_online):
        """
        Update user's online status and notify friends
        """
        # Import models here to avoid Django app loading issues
        from .models import Friend
        
        cache_key = f"user_{self.scope['user'].id}_online"
        if is_online:
            cache.set(cache_key, True, timeout=300)  # 5 minutes
        else:
            cache.delete(cache_key)
        
        # Get all friends to notify
        friends = Friend.objects.filter(
            status='accepted'
        ).filter(
            requester=self.scope['user']
        ).values_list('recipient_id', flat=True)
        
        return list(friends)

    async def friend_update(self, event):
        """
        Handle friend update events
        """
        await self.send(text_data=json.dumps(event['data']))

    async def handle_friend_request(self, data):
        """
        Handle incoming friend requests
        """
        recipient_id = data.get('recipient_id')
        if recipient_id:
            await self.channel_layer.group_send(
                f"user_{recipient_id}_friends",
                {
                    'type': 'friend_update',
                    'data': {
                        'type': 'new_friend_request',
                        'from_user': self.scope['user'].id,
                        'timestamp': timezone.now().isoformat()
                    }
                }
            )

    async def handle_friend_response(self, data):
        """
        Handle friend request responses (accept/reject)
        """
        requester_id = data.get('requester_id')
        response = data.get('response')
        if requester_id and response:
            await self.channel_layer.group_send(
                f"user_{requester_id}_friends",
                {
                    'type': 'friend_update',
                    'data': {
                        'type': 'friend_request_response',
                        'response': response,
                        'from_user': self.scope['user'].id,
                        'timestamp': timezone.now().isoformat()
                    }
                }
            )

    async def handle_friend_activity(self, data):
        """
        Handle and broadcast friend activity updates
        """
        activity_type = data.get('activity_type')
        if activity_type:
            friends = await self.get_user_friends()
            for friend_id in friends:
                await self.channel_layer.group_send(
                    f"user_{friend_id}_friends",
                    {
                        'type': 'friend_update',
                        'data': {
                            'type': 'friend_activity',
                            'activity_type': activity_type,
                            'user_id': self.scope['user'].id,
                            'timestamp': timezone.now().isoformat()
                        }
                    }
                )

    @database_sync_to_async
    def get_user_friends(self):
        """
        Get list of user's friend IDs
        """
        # Import models here to avoid Django app loading issues
        from .models import Friend
        
        return list(Friend.objects.filter(
            status='accepted'
        ).filter(
            requester=self.scope['user']
        ).values_list('recipient_id', flat=True))

    async def handle_status_update(self, data):
        """
        Handle user status updates
        """
        status = data.get('status')
        if status:
            friends = await self.get_user_friends()
            for friend_id in friends:
                await self.channel_layer.group_send(
                    f"user_{friend_id}_friends",
                    {
                        'type': 'friend_update',
                        'data': {
                            'type': 'status_update',
                            'user_id': self.scope['user'].id,
                            'status': status,
                            'timestamp': timezone.now().isoformat()
                        }
                    }
                ) 