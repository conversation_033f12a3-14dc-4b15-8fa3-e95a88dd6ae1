from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from friends.models import Friend
import random

User = get_user_model()

class Command(BaseCommand):
    help = 'Create test friend data for development'

    def add_arguments(self, parser):
        parser.add_argument(
            '--users',
            type=int,
            default=10,
            help='Number of test users to create'
        )
        parser.add_argument(
            '--friends',
            type=int,
            default=5,
            help='Number of friendships to create'
        )
        parser.add_argument(
            '--requests',
            type=int,
            default=3,
            help='Number of pending friend requests to create'
        )

    def handle(self, *args, **options):
        self.stdout.write('Creating test friend data...')
        
        # Create test users if they don't exist
        test_users = []
        user_data = [
            {'username': 'musiclover42', 'email': '<EMAIL>', 'first_name': '<PERSON>', 'last_name': '<PERSON>'},
            {'username': 'beatmaker', 'email': '<EMAIL>', 'first_name': '<PERSON>', 'last_name': '<PERSON>'},
            {'username': 'vinylcollector', 'email': '<EMAIL>', 'first_name': '<PERSON>', 'last_name': '<PERSON>'},
            {'username': 'soundexplorer', 'email': '<EMAIL>', 'first_name': '<PERSON>', 'last_name': 'Chen'},
            {'username': 'jazzfan', 'email': '<EMAIL>', 'first_name': 'Lisa', 'last_name': '<PERSON>'},
            {'username': 'rockenthusiast', 'email': '<EMAIL>', 'first_name': 'Alex', 'last_name': 'Smith'},
            {'username': 'hiphophead', 'email': '<EMAIL>', 'first_name': 'Jordan', 'last_name': 'Brown'},
            {'username': 'indievibes', 'email': '<EMAIL>', 'first_name': 'Taylor', 'last_name': 'Miller'},
            {'username': 'electronicbeats', 'email': '<EMAIL>', 'first_name': 'Casey', 'last_name': 'Garcia'},
            {'username': 'classicalmusic', 'email': '<EMAIL>', 'first_name': 'Morgan', 'last_name': 'Lee'},
        ]
        
        for i, data in enumerate(user_data[:options['users']]):
            user, created = User.objects.get_or_create(
                username=data['username'],
                defaults={
                    'email': data['email'],
                    'first_name': data['first_name'],
                    'last_name': data['last_name'],
                    'is_active': True,
                }
            )
            if created:
                user.set_password('testpass123')
                user.save()
                self.stdout.write(f'Created user: {user.username}')
            else:
                self.stdout.write(f'User already exists: {user.username}')
            test_users.append(user)
        
        # Create accepted friendships
        friendships_created = 0
        for _ in range(options['friends']):
            if len(test_users) < 2:
                break
                
            user1, user2 = random.sample(test_users, 2)
            
            # Check if friendship already exists
            existing = Friend.objects.filter(
                requester__in=[user1, user2],
                recipient__in=[user1, user2]
            ).exists()
            
            if not existing:
                Friend.objects.create(
                    requester=user1,
                    recipient=user2,
                    status='accepted'
                )
                friendships_created += 1
                self.stdout.write(f'Created friendship: {user1.username} <-> {user2.username}')
        
        # Create pending friend requests
        requests_created = 0
        for _ in range(options['requests']):
            if len(test_users) < 2:
                break
                
            requester, recipient = random.sample(test_users, 2)
            
            # Check if request already exists
            existing = Friend.objects.filter(
                requester=requester,
                recipient=recipient
            ).exists()
            
            if not existing:
                Friend.objects.create(
                    requester=requester,
                    recipient=recipient,
                    status='pending'
                )
                requests_created += 1
                self.stdout.write(f'Created friend request: {requester.username} -> {recipient.username}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {len(test_users)} users, '
                f'{friendships_created} friendships, and '
                f'{requests_created} friend requests'
            )
        ) 