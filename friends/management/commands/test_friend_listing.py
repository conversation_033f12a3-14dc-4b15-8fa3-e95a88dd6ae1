from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from friends.models import Friend
from django.db import transaction
from django.db.models import Q
import random
from datetime import timedelta
from django.utils import timezone

User = get_user_model()

class Command(BaseCommand):
    help = 'Test friend listing functionality with real data'

    def create_test_users(self, num_users=10):
        """Create test users"""
        users = []
        for i in range(num_users):
            username = f'testuser{i}'
            email = f'testuser{i}@example.com'
            try:
                user = User.objects.get(username=username)
            except User.DoesNotExist:
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password='testpass123',
                    first_name=f'Test{i}',
                    last_name=f'User{i}'
                )
            users.append(user)
        return users

    def create_friendships(self, users, friendships_per_user=5):
        """Create random friendships between users"""
        for user in users:
            # Get potential friends (excluding self and existing friends)
            existing_friends = Friend.objects.filter(
                (Q(requester=user) | Q(recipient=user)),
                status='accepted'
            ).values_list('requester', 'recipient')
            
            existing_friend_ids = set()
            for req, rec in existing_friends:
                existing_friend_ids.add(req)
                existing_friend_ids.add(rec)
            
            potential_friends = [u for u in users if u != user and u.id not in existing_friend_ids]
            
            # Create random friendships
            num_friends = min(friendships_per_user, len(potential_friends))
            friends = random.sample(potential_friends, num_friends)
            
            for friend in friends:
                # Randomly decide who is requester and recipient
                if random.choice([True, False]):
                    requester, recipient = user, friend
                else:
                    requester, recipient = friend, user
                
                # Create friendship with random interaction data
                friendship = Friend.objects.create(
                    requester=requester,
                    recipient=recipient,
                    status='accepted',
                    interaction_count=random.randint(1, 100),
                    friendship_score=random.uniform(0.1, 1.0),
                    friendship_level=random.choice(['bronze', 'silver', 'gold', 'platinum']),
                    last_interaction=timezone.now() - timedelta(days=random.randint(0, 30))
                )
                self.stdout.write(f'Created friendship: {friendship}')

    def verify_friend_listing(self, user):
        """Verify friend listing for a user"""
        from rest_framework.test import APIClient
        from django.urls import reverse
        
        client = APIClient()
        client.force_authenticate(user=user)
        
        # Test paginated endpoint
        response = client.get(reverse('friends:friends-list'))
        self.stdout.write(f'\nPaginated friends for {user.username}:')
        self.stdout.write(f'Total friends: {response.data["count"]}')
        
        # Test all_friends endpoint
        response = client.get(reverse('friends:friends-all-friends'))
        self.stdout.write(f'\nAll friends for {user.username}:')
        for friend in response.data:
            friend_data = friend['friend']
            self.stdout.write(f'- {friend_data["username"]} (Level: {friend["friendship_level"]}, Score: {friend["friendship_score"]:.2f})')
        
        # Verify against database
        db_friends = Friend.objects.filter(
            (Q(requester=user) | Q(recipient=user)),
            status='accepted'
        ).count()
        self.stdout.write(f'\nDatabase friends count: {db_friends}')
        assert db_friends == len(response.data), 'Mismatch between database and API response'

    def handle(self, *args, **options):
        self.stdout.write('Creating test data and verifying friend listing...')
        
        with transaction.atomic():
            # Create test users
            users = self.create_test_users(10)
            self.stdout.write(f'Created {len(users)} test users')
            
            # Create friendships
            self.create_friendships(users, 5)
            
            # Test friend listing for each user
            for user in users[:3]:  # Test first 3 users
                self.stdout.write(f'\n{"="*50}')
                self.stdout.write(f'Testing friend listing for {user.username}')
                self.verify_friend_listing(user)
            
            self.stdout.write(self.style.SUCCESS('\nFriend listing verification completed successfully!')) 