from django.core.management.base import BaseCommand
from django.utils import timezone
from friends.models import Friend
from django.db.models import F
import logging

logger = logging.getLogger('bopmaps')

class Command(BaseCommand):
    help = 'Update friend analytics including friendship scores and mutual friends counts'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update all friendships regardless of last update time',
        )

    def handle(self, *args, **options):
        try:
            self.stdout.write('Starting friend analytics update...')
            
            # Get friendships to update
            queryset = Friend.objects.filter(status='accepted')
            if not options['force']:
                # Only update friendships that haven't been updated in the last 24 hours
                queryset = queryset.filter(
                    updated_at__lte=timezone.now() - timezone.timedelta(hours=24)
                )
            
            total = queryset.count()
            self.stdout.write(f'Found {total} friendships to update')
            
            updated = 0
            skipped = 0
            
            for friendship in queryset:
                try:
                    # Update mutual friends count
                    friendship.update_mutual_friends_count()
                    
                    # Update friendship score
                    friendship.update_friendship_score()
                    
                    updated += 1
                    if updated % 100 == 0:
                        self.stdout.write(f'Updated {updated}/{total} friendships')
                except Exception as e:
                    logger.error(f'Error updating friendship {friendship.id}: {str(e)}')
                    skipped += 1
            
            self.stdout.write(self.style.SUCCESS(
                f'Successfully updated {updated} friendships. Skipped {skipped}.'
            ))
            
        except Exception as e:
            logger.error(f'Error in update_friend_analytics command: {str(e)}')
            self.stdout.write(self.style.ERROR(f'Command failed: {str(e)}'))
            raise 