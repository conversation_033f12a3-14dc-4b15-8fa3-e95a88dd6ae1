# Generated by Django 4.2.7 on 2025-06-08 19:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('friends', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FriendGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_private', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AlterModelOptions(
            name='friend',
            options={'ordering': ['-friendship_score', '-last_interaction']},
        ),
        migrations.AddField(
            model_name='friend',
            name='friendship_level',
            field=models.CharField(choices=[('new', 'New Friend'), ('regular', 'Regular Friend'), ('close', 'Close Friend'), ('best', 'Best Friend')], default='new', max_length=10),
        ),
        migrations.AddField(
            model_name='friend',
            name='friendship_score',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='friend',
            name='interaction_count',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='friend',
            name='last_interaction',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='friend',
            name='mutual_friends_count',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='friend',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('accepted', 'Accepted'), ('rejected', 'Rejected'), ('blocked', 'Blocked')], default='pending', max_length=10),
        ),
        migrations.AddIndex(
            model_name='friend',
            index=models.Index(fields=['status', 'created_at'], name='friends_fri_status_07aa7b_idx'),
        ),
        migrations.AddIndex(
            model_name='friend',
            index=models.Index(fields=['requester', 'status'], name='friends_fri_request_fe5751_idx'),
        ),
        migrations.AddIndex(
            model_name='friend',
            index=models.Index(fields=['recipient', 'status'], name='friends_fri_recipie_7e82f7_idx'),
        ),
        migrations.AddIndex(
            model_name='friend',
            index=models.Index(fields=['friendship_level', 'interaction_count'], name='friends_fri_friends_8d8d60_idx'),
        ),
        migrations.AddIndex(
            model_name='friend',
            index=models.Index(fields=['last_interaction'], name='friends_fri_last_in_ac0df9_idx'),
        ),
        migrations.AddIndex(
            model_name='friend',
            index=models.Index(fields=['friendship_score'], name='friends_fri_friends_8b4e61_idx'),
        ),
        migrations.AddField(
            model_name='friendgroup',
            name='owner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='owned_friend_groups', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='friend',
            name='groups',
            field=models.ManyToManyField(blank=True, related_name='members', to='friends.friendgroup'),
        ),
        migrations.AddIndex(
            model_name='friendgroup',
            index=models.Index(fields=['owner', 'created_at'], name='friends_fri_owner_i_98a23e_idx'),
        ),
        migrations.AddIndex(
            model_name='friendgroup',
            index=models.Index(fields=['is_private', 'created_at'], name='friends_fri_is_priv_e5d70d_idx'),
        ),
    ]
