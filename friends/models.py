from django.db import models
from django.conf import settings
from django.core.cache import cache
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta

class FriendGroup(models.Model):
    """
    Model for organizing friends into groups/circles
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='owned_friend_groups'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_private = models.BooleanField(default=False)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['owner', 'created_at']),
            models.Index(fields=['is_private', 'created_at'])
        ]

    def __str__(self):
        return f"{self.name} (by {self.owner.username})"

class Friend(models.Model):
    """
    Enhanced model representing a friendship between two users with analytics
    """
    STATUSES = (
        ('pending', 'Pending'),
        ('accepted', 'Accepted'),
        ('rejected', 'Rejected'),
        ('blocked', 'Blocked')
    )

    FRIENDSHIP_LEVELS = (
        ('new', 'New Friend'),
        ('regular', 'Regular Friend'),
        ('close', 'Close Friend'),
        ('best', 'Best Friend')
    )
    
    # Core relationship fields
    requester = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='friend_requests_sent'
    )
    recipient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='friend_requests_received'
    )
    status = models.CharField(max_length=10, choices=STATUSES, default='pending')
    
    # Analytics and tracking fields
    friendship_level = models.CharField(
        max_length=10,
        choices=FRIENDSHIP_LEVELS,
        default='new'
    )
    interaction_count = models.PositiveIntegerField(default=0)
    last_interaction = models.DateTimeField(null=True, blank=True)
    friendship_score = models.FloatField(default=0)
    mutual_friends_count = models.PositiveIntegerField(default=0)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Group membership
    groups = models.ManyToManyField(
        FriendGroup,
        related_name='members',
        blank=True
    )

    class Meta:
        unique_together = ('requester', 'recipient')
        indexes = [
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['requester', 'status']),
            models.Index(fields=['recipient', 'status']),
            models.Index(fields=['friendship_level', 'interaction_count']),
            models.Index(fields=['last_interaction']),
            models.Index(fields=['friendship_score'])
        ]
        ordering = ['-friendship_score', '-last_interaction']

    def __str__(self):
        return f"{self.requester.username} -> {self.recipient.username} ({self.status})"

    def update_interaction(self):
        """Update interaction count and timestamp"""
        self.interaction_count += 1
        self.last_interaction = timezone.now()
        self.update_friendship_score()
        self.save()

    def update_friendship_score(self):
        """Calculate friendship score based on various factors"""
        base_score = self.interaction_count * 10
        
        # Time factor: more recent interactions get higher scores
        if self.last_interaction:
            days_since_interaction = (timezone.now() - self.last_interaction).days
            time_factor = max(1, 100 - days_since_interaction) / 100
        else:
            time_factor = 0.5

        # Mutual friends factor
        mutual_factor = min(1, self.mutual_friends_count / 10)

        self.friendship_score = base_score * time_factor * (1 + mutual_factor)
        
        # Update friendship level based on score
        if self.friendship_score >= 1000:
            self.friendship_level = 'best'
        elif self.friendship_score >= 500:
            self.friendship_level = 'close'
        elif self.friendship_score >= 100:
            self.friendship_level = 'regular'
        else:
            self.friendship_level = 'new'

    def update_mutual_friends_count(self):
        """Update the count of mutual friends"""
        cache_key = f'mutual_friends_{self.requester.id}_{self.recipient.id}'
        count = cache.get(cache_key)
        
        if count is None:
            requester_friends = Friend.objects.filter(
                (Q(requester=self.requester) | Q(recipient=self.requester)),
                status='accepted'
            ).values_list('requester_id', 'recipient_id')
            
            recipient_friends = Friend.objects.filter(
                (Q(requester=self.recipient) | Q(recipient=self.recipient)),
                status='accepted'
            ).values_list('requester_id', 'recipient_id')
            
            requester_friend_ids = set()
            for req_id, rec_id in requester_friends:
                if req_id != self.requester.id:
                    requester_friend_ids.add(req_id)
                if rec_id != self.requester.id:
                    requester_friend_ids.add(rec_id)
            
            recipient_friend_ids = set()
            for req_id, rec_id in recipient_friends:
                if req_id != self.recipient.id:
                    recipient_friend_ids.add(req_id)
                if rec_id != self.recipient.id:
                    recipient_friend_ids.add(rec_id)
            
            count = len(requester_friend_ids.intersection(recipient_friend_ids))
            cache.set(cache_key, count, timeout=3600)  # Cache for 1 hour
        
        self.mutual_friends_count = count
        self.save()
