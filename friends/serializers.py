from rest_framework import serializers
from .models import Friend, FriendGroup
from users.serializers import UserSerializer
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'email', 'profile_pic']

class FriendSerializer(serializers.ModelSerializer):
    """
    Enhanced serializer for Friend model with detailed friend information
    """
    friend = serializers.SerializerMethodField()
    
    class Meta:
        model = Friend
        fields = [
            'id', 'friend', 'status', 'created_at', 'updated_at',
            'friendship_level', 'interaction_count', 'friendship_score',
            'mutual_friends_count'
        ]
        read_only_fields = [
            'id', 'status', 'created_at', 'updated_at',
            'friendship_level', 'interaction_count',
            'friendship_score', 'mutual_friends_count'
        ]
    
    def get_friend(self, obj):
        """Get the friend user object (opposite of the current user)"""
        request = self.context.get('request')
        if not request:
            return None
            
        request_user = request.user
        friend_user = obj.recipient if obj.requester == request_user else obj.requester
        
        return {
            'id': friend_user.id,
            'username': friend_user.username,
            'first_name': friend_user.first_name,
            'last_name': friend_user.last_name,
            'email': friend_user.email,
            'profile_pic': self.get_profile_pic_url(friend_user, request)
        }
    
    def get_profile_pic_url(self, user, request=None):
        """Get the absolute URL for the user's profile picture"""
        if not user.profile_pic:
            return None
            
        if request is None:
            request = self.context.get('request')
            
        if request:
            return request.build_absolute_uri(user.profile_pic)
        return user.profile_pic

class FriendRequestSerializer(serializers.ModelSerializer):
    """
    Enhanced serializer for Friend model with request handling
    """
    requester = UserSerializer(read_only=True)
    recipient = UserSerializer(read_only=True)
    recipient_id = serializers.PrimaryKeyRelatedField(
        write_only=True,
        queryset=User.objects.all(),
        source='recipient'
    )
    
    class Meta:
        model = Friend
        fields = [
            'id', 'requester', 'recipient',
            'recipient_id', 'status', 'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'requester', 'created_at', 'updated_at']

    def validate(self, attrs):
        request_user = self.context['request'].user
        recipient = attrs.get('recipient')

        if recipient == request_user:
            raise serializers.ValidationError("You cannot send a friend request to yourself.")

        # Check for existing pending or accepted requests in either direction
        if Friend.objects.filter(
            requester=request_user, recipient=recipient, status__in=['pending', 'accepted']
        ).exists() or Friend.objects.filter(
            requester=recipient, recipient=request_user, status__in=['pending', 'accepted']
        ).exists():
            raise serializers.ValidationError("A friend request already exists or you are already friends.")
            
        return attrs

class FriendGroupSerializer(serializers.ModelSerializer):
    """
    Serializer for friend groups/circles
    """
    member_count = serializers.SerializerMethodField()
    members = FriendSerializer(many=True, read_only=True)
    
    class Meta:
        model = FriendGroup
        fields = [
            'id', 'name', 'description', 'owner',
            'created_at', 'updated_at', 'is_private',
            'member_count', 'members'
        ]
        read_only_fields = ['owner', 'created_at', 'updated_at']
    
    def get_member_count(self, obj):
        return obj.members.count()

class FriendAnalyticsSerializer(serializers.Serializer):
    """
    Serializer for friend analytics data
    """
    total_friends = serializers.IntegerField()
    new_friends_30d = serializers.IntegerField()
    most_active_friends = FriendSerializer(many=True)
    best_friends = FriendSerializer(many=True)
    activity_trends = serializers.ListField(
        child=serializers.DictField()
    )

class FriendSuggestionSerializer(serializers.Serializer):
    """
    Serializer for friend suggestions
    """
    user = UserSerializer()
    mutual_friends_count = serializers.IntegerField()
    common_interests = serializers.ListField(
        child=serializers.CharField(),
        required=False
    )
    last_active = serializers.DateTimeField(required=False)
    matching_score = serializers.FloatField(required=False) 