from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import FriendViewSet, FriendRequestViewSet, FriendGroupViewSet

app_name = 'friends'

router = DefaultRouter()
# Register more specific routes first
router.register(r'groups', FriendGroupViewSet, basename='friend-groups')
router.register(r'requests', FriendRequestViewSet, basename='friend-requests')
router.register(r'', FriendViewSet, basename='friends')

urlpatterns = [
    # Add specific path for status before router URLs
    path('status/<str:user_id>/', FriendViewSet.as_view({'get': 'status'}), name='friend-status'),
    path('', include(router.urls)),
] 