from django.shortcuts import render
from django.db import transaction
from django.db.models import Q, Count, F, Value, FloatField
from django.db.models.functions import Coalesce
from django.utils import timezone
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.core.cache import cache
from datetime import timedel<PERSON>
from .models import Friend, FriendGroup
from .serializers import (
    FriendSerializer, 
    FriendRequestSerializer,
    FriendGroupSerializer,
    FriendAnalyticsSerializer,
    FriendSuggestionSerializer
)
from bopmaps.views import BaseModelViewSet
import logging
from django.core.exceptions import ValidationError
from django.http import Http404
from django.contrib.auth import get_user_model

logger = logging.getLogger('bopmaps')

class StandardResultsSetPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100

class FriendViewSet(BaseModelViewSet):
    """
    Enhanced API viewset for Friend management with analytics and real-time features
    """
    serializer_class = FriendSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['requester__username', 'recipient__username', 
                    'requester__first_name', 'requester__last_name',
                    'recipient__first_name', 'recipient__last_name']
    ordering_fields = ['created_at', 'updated_at', 'friendship_score', 
                      'interaction_count', 'last_interaction']
    
    def get_queryset(self):
        """Return all accepted friendships for the current user with caching"""
        user = self.request.user
        cache_key = f'user_friends_{user.id}'
        queryset = cache.get(cache_key)
        
        if queryset is None:
            # Get friendships where the user is either requester or recipient
            queryset = Friend.objects.filter(
                (Q(requester=user) | Q(recipient=user)),
                status='accepted'
            ).select_related('requester', 'recipient').order_by('-updated_at')
            cache.set(cache_key, queryset, timeout=300)  # Cache for 5 minutes
        
        return queryset
    
    def get_object(self):
        """Override to handle non-existent objects with 400 instead of 404"""
        try:
            obj = Friend.objects.get(pk=self.kwargs['pk'])
            self.check_object_permissions(self.request, obj)
            return obj
        except Friend.DoesNotExist:
            raise ValidationError("Friendship not found")
    
    @action(detail=False, methods=['GET'])
    def analytics(self, request):
        """Get friendship analytics and insights"""
        queryset = self.get_queryset()
        
        # Calculate various metrics
        total_friends = queryset.count()
        new_friends = queryset.filter(
            created_at__gte=timezone.now() - timedelta(days=30)
        ).count()
        
        most_active = queryset.order_by('-interaction_count')[:5]
        best_friends = queryset.order_by('-friendship_score')[:5]
        
        # Get friend activity trends
        activity_by_day = queryset.filter(
            last_interaction__gte=timezone.now() - timedelta(days=7)
        ).extra(
            select={'day': 'date(last_interaction)'}
        ).values('day').annotate(
            count=Count('id')
        ).order_by('day')
        
        return Response({
            'total_friends': total_friends,
            'new_friends_30d': new_friends,
            'most_active_friends': FriendSerializer(most_active, many=True).data,
            'best_friends': FriendSerializer(best_friends, many=True).data,
            'activity_trends': activity_by_day
        })
    
    @action(detail=False, methods=['GET'])
    def suggested(self, request):
        """Get friend suggestions based on mutual connections"""
        User = get_user_model()
        
        # Get current user's friend IDs
        my_friendships = Friend.objects.filter(
            (Q(requester=request.user) | Q(recipient=request.user)),
            status='accepted'
        ).values_list('requester_id', 'recipient_id')
        
        my_friend_ids = set()
        for req_id, rec_id in my_friendships:
            if req_id != request.user.id:
                my_friend_ids.add(req_id)
            if rec_id != request.user.id:
                my_friend_ids.add(rec_id)
        
        # Get all users except current user and existing friends
        excluded_ids = my_friend_ids.copy()
        excluded_ids.add(request.user.id)
        
        potential_friends = User.objects.exclude(id__in=excluded_ids)
        
        # Calculate mutual friends for each potential friend
        suggestions = []
        for user in potential_friends[:50]:  # Limit to avoid performance issues
            # Get this user's friend IDs
            user_friendships = Friend.objects.filter(
                (Q(requester=user) | Q(recipient=user)),
                status='accepted'
            ).values_list('requester_id', 'recipient_id')
            
            user_friend_ids = set()
            for req_id, rec_id in user_friendships:
                if req_id != user.id:
                    user_friend_ids.add(req_id)
                if rec_id != user.id:
                    user_friend_ids.add(rec_id)
            
            # Count mutual friends
            mutual_count = len(my_friend_ids.intersection(user_friend_ids))
            
            if mutual_count > 0:  # Only suggest if there are mutual friends
                suggestions.append({
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'name': user.get_full_name() or user.username,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'profile_pic': request.build_absolute_uri(user.profile_pic) if user.profile_pic else None
                    },
                    'mutual_friends_count': mutual_count,
                    'matching_score': float(mutual_count)  # Simple scoring for now
                })
        
        # Sort by mutual friends count (descending)
        suggestions.sort(key=lambda x: x['mutual_friends_count'], reverse=True)
        
        return Response(suggestions[:10])  # Return top 10
    
    @action(detail=False, methods=['GET'])
    def advanced_search(self, request):
        """Advanced friend search with multiple filters"""
        queryset = self.get_queryset()
        
        # Filter by activity
        active_within = request.query_params.get('active_within')
        if active_within:
            hours = int(active_within)
            queryset = queryset.filter(
                last_interaction__gte=timezone.now() - timedelta(hours=hours)
            )
        
        # Filter by friendship level
        level = request.query_params.get('friendship_level')
        if level:
            queryset = queryset.filter(friendship_level=level)
        
        # Filter by interaction count
        min_interactions = request.query_params.get('min_interactions')
        if min_interactions:
            queryset = queryset.filter(interaction_count__gte=int(min_interactions))
        
        # Filter by mutual friends count
        min_mutual = request.query_params.get('min_mutual_friends')
        if min_mutual:
            queryset = queryset.filter(mutual_friends_count__gte=int(min_mutual))
        
        # Apply ordering
        ordering = request.query_params.get('ordering', '-friendship_score')
        queryset = queryset.order_by(ordering)
        
        page = self.paginate_queryset(queryset)
        serializer = self.get_serializer(page, many=True)
        return self.get_paginated_response(serializer.data)
    
    @action(detail=True, methods=['POST'])
    def record_interaction(self, request, pk=None):
        """Record an interaction with a friend"""
        friendship = self.get_object()
        
        if friendship.requester != request.user and friendship.recipient != request.user:
            return Response(
                {"error": "Cannot record interaction for a friendship you're not part of"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        friendship.update_interaction()
        return Response(self.get_serializer(friendship).data)

    @action(detail=False, methods=['GET'])
    def all_friends(self, request):
        """Get all friends without pagination"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['POST'])
    def unfriend(self, request, pk=None):
        """Remove a friend"""
        try:
            friendship = self.get_object()
            
            if friendship.requester != request.user and friendship.recipient != request.user:
                return Response(
                    {"error": "You are not part of this friendship"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if friendship.status != 'accepted':
                return Response(
                    {"error": "Can only unfriend accepted friendships"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            friendship.delete()
            # Invalidate cache
            cache.delete(f'user_friends_{request.user.id}')
            if request.user == friendship.requester:
                cache.delete(f'user_friends_{friendship.recipient.id}')
            else:
                cache.delete(f'user_friends_{friendship.requester.id}')
            
            return Response({"message": "Friend removed successfully"})
        except ValidationError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['GET'])
    def status(self, request, user_id=None):
        """Get friendship status with another user"""
        if not user_id:
            return Response(
                {"error": "user_id parameter required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            target_user = get_user_model().objects.get(id=user_id)
        except get_user_model().DoesNotExist:
            return Response(
                {"error": "User not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        current_user = request.user
        
        if current_user == target_user:
            return Response({
                "status": "none",
                "request_id": None
            })
        
        # Check existing friendship
        friendship = Friend.objects.filter(
            (Q(requester=current_user, recipient=target_user) | 
             Q(requester=target_user, recipient=current_user))
        ).first()
        
        if not friendship:
            return Response({
                "status": "none",
                "request_id": None
            })
        
        # Map the status to match frontend enum
        if friendship.status == 'accepted':
            status_value = 'friends'
        elif friendship.status == 'pending':
            if friendship.requester == current_user:
                status_value = 'pendingSent'
            else:
                status_value = 'pendingReceived'
        else:
            status_value = 'none'
        
        return Response({
            "status": status_value,
            "request_id": str(friendship.id) if friendship else None
        })

class FriendRequestViewSet(BaseModelViewSet):
    """
    Enhanced API viewset for FriendRequest management with bulk operations
    """
    serializer_class = FriendRequestSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    http_method_names = ['get', 'post', 'head', 'options', 'delete']
    
    def get_queryset(self):
        """Return all friend requests for the current user with caching"""
        cache_key = f'user_requests_{self.request.user.id}'
        queryset = cache.get(cache_key)
        
        if queryset is None:
            queryset = Friend.objects.filter(
                Q(requester=self.request.user) | Q(recipient=self.request.user)
            ).select_related('requester', 'recipient')
            cache.set(cache_key, queryset, timeout=300)
        
        return queryset
    
    def perform_create(self, serializer):
        """Set the requester when creating a friend request"""
        serializer.save(requester=self.request.user)
    
    def get_object(self):
        """Override to handle non-existent objects with 400 instead of 404"""
        try:
            obj = Friend.objects.get(pk=self.kwargs['pk'])
            self.check_object_permissions(self.request, obj)
            return obj
        except Friend.DoesNotExist:
            raise ValidationError("Friend request not found")
    
    @action(detail=False, methods=['GET'])
    def received(self, request):
        """Get friend requests received by the user"""
        queryset = Friend.objects.filter(
            recipient=request.user,
            status='pending'
        ).select_related('requester')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['GET'])
    def sent(self, request):
        """Get friend requests sent by the user"""
        queryset = Friend.objects.filter(
            requester=request.user,
            status='pending'
        ).select_related('recipient')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['POST'])
    def accept(self, request, pk=None):
        """Accept a friend request"""
        try:
            friend_request = self.get_object()
            
            if friend_request.recipient != request.user:
                return Response(
                    {"error": "You can only accept requests sent to you"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if friend_request.status != 'pending':
                return Response(
                    {"error": "Can only accept pending requests"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            friend_request.status = 'accepted'
            friend_request.save()
            return Response(self.get_serializer(friend_request).data)
        except ValidationError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['POST'])
    def reject(self, request, pk=None):
        """Reject a friend request"""
        try:
            friend_request = self.get_object()
            
            if friend_request.recipient != request.user:
                return Response(
                    {"error": "You can only reject requests sent to you"},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            if friend_request.status != 'pending':
                return Response(
                    {"error": "Can only reject pending requests"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            friend_request.status = 'rejected'
            friend_request.save()
            return Response(self.get_serializer(friend_request).data)
        except ValidationError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['POST'])
    def cancel(self, request, pk=None):
        """Cancel a sent friend request"""
        try:
            friend_request = self.get_object()
            
            if friend_request.requester != request.user:
                return Response(
                    {"error": "You can only cancel requests you sent"},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            if friend_request.status != 'pending':
                return Response(
                    {"error": "Can only cancel pending requests"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            friend_request.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except ValidationError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class FriendGroupViewSet(viewsets.ModelViewSet):
    """
    API viewset for managing friend groups/circles
    """
    serializer_class = FriendGroupSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    
    def get_queryset(self):
        return FriendGroup.objects.filter(
            owner=self.request.user
        ).prefetch_related('members')
    
    def perform_create(self, serializer):
        serializer.save(owner=self.request.user)
    
    @action(detail=True, methods=['POST'])
    def add_members(self, request, pk=None):
        """Add multiple friends to a group"""
        group = self.get_object()
        friend_ids = request.data.get('friend_ids', [])
        
        if not friend_ids:
            return Response(
                {"error": "No friends specified"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        valid_friends = Friend.objects.filter(
            (Q(requester=request.user) | Q(recipient=request.user)),
            status='accepted',
            id__in=friend_ids
        )
        
        group.members.add(*valid_friends)
        return Response(self.get_serializer(group).data)
    
    @action(detail=True, methods=['POST'])
    def remove_members(self, request, pk=None):
        """Remove multiple friends from a group"""
        group = self.get_object()
        friend_ids = request.data.get('friend_ids', [])
        
        if not friend_ids:
            return Response(
                {"error": "No friends specified"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        group.members.remove(*friend_ids)
        return Response(self.get_serializer(group).data)
