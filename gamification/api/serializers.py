from rest_framework import serializers
from ..models import Achievement, UserAchievement
from pins.serializers import PinSkinSerializer

class AchievementSerializer(serializers.ModelSerializer):
    """
    Serializer for Achievement model
    """
    reward_skin = PinSkinSerializer(read_only=True)
    category = serializers.CharField(source='get_type_display')
    icon_data = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(read_only=True)
    progress = serializers.SerializerMethodField()
    completed_at = serializers.SerializerMethodField()
    
    class Meta:
        model = Achievement
        fields = [
            'id', 'name', 'description', 'icon', 'icon_name',
            'criteria', 'type', 'challenge_id', 'tier',
            'xp_reward', 'is_secret', 'created_at',
            'background_color', 'primary_color', 'category',
            'reward_skin', 'icon_data',
            'progress',
            'completed_at',
        ]
    
    def get_icon_data(self, obj):
        """Return icon data if available"""
        if obj.icon:
            return {
                'url': obj.icon.url,
                'name': obj.icon_name or ''
            }
        return None

    def get_progress(self, obj):
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return {'current_count': 0, 'progress_percentage': 0}
            
        user_achievement = UserAchievement.objects.filter(
            user=request.user,
            achievement=obj
        ).first()
        
        if not user_achievement:
            return {'current_count': 0, 'progress_percentage': 0}
            
        progress = user_achievement.progress or {}
        current_count = progress.get('current_count', 0)
        required_count = obj.criteria.get('required_count', 100)
        
        return {
            'current_count': current_count,
            'progress_percentage': min(100, (current_count / required_count * 100))
        }
    
    def get_completed_at(self, obj):
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return None
            
        user_achievement = UserAchievement.objects.filter(
            user=request.user,
            achievement=obj
        ).first()
        
        return user_achievement.completed_at if user_achievement else None

class UserAchievementSerializer(serializers.ModelSerializer):
    """
    Serializer for UserAchievement model
    """
    achievement = AchievementSerializer()
    progress_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = UserAchievement
        fields = ['id', 'user', 'achievement', 'progress', 'completed_at', 'progress_percentage', 'last_updated']
    
    def get_progress_percentage(self, obj):
        """Calculate progress percentage"""
        if not obj.progress:
            return 0
            
        criteria = obj.achievement.criteria
        if not criteria or 'required_count' not in criteria:
            return 0
            
        current = obj.progress.get('current_count', 0)
        required = criteria['required_count']
        
        if required <= 0:
            return 100 if current > 0 else 0
            
        percentage = (current / required) * 100
        return min(round(percentage, 2), 100)

class UserAchievementListSerializer(UserAchievementSerializer):
    """
    Serializer for listing user achievements with minimal achievement data
    """
    class Meta(UserAchievementSerializer.Meta):
        fields = ['id', 'achievement', 'progress', 'completed_at', 'progress_percentage', 'last_updated'] 