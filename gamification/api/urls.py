from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import AchievementViewSet, UserAchievementViewSet

router = DefaultRouter()
router.register(r'achievements', AchievementViewSet, basename='achievements')
router.register(r'user-achievements', UserAchievementViewSet, basename='user-achievements')

urlpatterns = [
    path('', include(router.urls)),
]

app_name = 'gamification' 