from channels.generic.websocket import AsyncJsonWebsocketConsumer
from channels.db import database_sync_to_async
from channels.layers import get_channel_layer
import json

class AchievementConsumer(AsyncJsonWebsocketConsumer):
    async def connect(self):
        """
        Connect user to their personal achievement channel
        """
        if self.scope["user"].is_anonymous:
            await self.close()
            return

        self.user_id = str(self.scope["user"].id)
        self.achievement_group = f"achievements_{self.user_id}"
        
        await self.channel_layer.group_add(
            self.achievement_group,
            self.channel_name
        )
        await self.accept()

    async def disconnect(self, close_code):
        """
        Leave achievement channel on disconnect
        """
        if hasattr(self, 'achievement_group'):
            await self.channel_layer.group_discard(
                self.achievement_group,
                self.channel_name
            )

    async def achievement_update(self, event):
        """
        Handle achievement updates (completion, progress, level-up)
        """
        await self.send_json(event['data'])

    @classmethod
    async def notify_achievement(cls, user_id, data):
        """
        Send achievement notification to user's channel
        """
        channel_layer = get_channel_layer()
        group = f"achievements_{user_id}"
        
        await channel_layer.group_send(
            group,
            {
                "type": "achievement_update",
                "data": data
            }
        ) 