from django.core.management.base import BaseCommand
from pins.models import PinSkin


class Command(BaseCommand):
    help = 'Create default pin skin if it does not exist'

    def handle(self, *args, **options):
        """Create the default pin skin."""
        if not PinSkin.objects.filter(id=1).exists():
            PinSkin.objects.create(
                id=1,
                name='Default Skin',
                slug='default-skin',
                image='pin_skins/default.png',
                skin_type='HOUSE',
                is_premium=False,
                metadata={'unlock_type': 'ALWAYS_AVAILABLE'}
            )
            self.stdout.write(
                self.style.SUCCESS('Successfully created default pin skin')
            )
        else:
            self.stdout.write(
                self.style.WARNING('Default pin skin already exists')
            )
