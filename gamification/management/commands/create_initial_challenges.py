from django.core.management.base import BaseCommand
from gamification.models import Achievement

class Command(BaseCommand):
    help = 'Create initial set of challenges'

    def handle(self, *args, **options):
        # Location-based challenges
        location_challenges = [
            # T0 - On-Ramp
            {
                'name': 'First Drop',
                'description': '1 pin anywhere',
                'type': 'location',
                'challenge_id': 'first_drop',
                'tier': 'T0',
                'xp_reward': 25,
                'criteria': {
                    'total_pins': 1,
                    'public_only': True
                }
            },
            {
                'name': 'Neighborhood Navigator',
                'description': '5 pins within 1 km of each other (any time span)',
                'type': 'location',
                'challenge_id': 'close_quarters',
                'tier': 'T0',
                'xp_reward': 50,
                'criteria': {
                    'pins_in_radius': 5,
                    'radius_km': 1,
                    'public_only': True
                }
            },
            # T1 - Local Presence
            {
                'name': 'City Sampler',
                'description': '3 pins on 3 different days in the same city',
                'type': 'location',
                'challenge_id': 'city_sampler',
                'tier': 'T1',
                'xp_reward': 75,
                'criteria': {
                    'pins_in_city': 3,
                    'different_days': 3,
                    'public_only': True
                }
            },
            {
                'name': 'Midnight Bop',
                'description': '1 pin between 00:00–03:00 local',
                'type': 'location',
                'challenge_id': 'after_hours',
                'tier': 'T1',
                'xp_reward': 75,
                'criteria': {
                    'time_range': {'start': '00:00', 'end': '03:00'},
                    'pins_count': 1,
                    'public_only': True
                }
            },
            # T2 - City-Wide
            {
                'name': 'Metro Mover',
                'description': '5 districts/neighborhoods in one city (≥ 1 km apart)',
                'type': 'location',
                'challenge_id': 'metro_mover',
                'tier': 'T2',
                'xp_reward': 125,
                'criteria': {
                    'districts': 5,
                    'min_distance_km': 1,
                    'same_city': True,
                    'public_only': True
                }
            },
            {
                'name': 'Cross-Town Sound',
                'description': '2 pins 10 km+ apart in < 24 h',
                'type': 'location',
                'challenge_id': 'cross_town_sound',
                'tier': 'T2',
                'xp_reward': 150,
                'criteria': {
                    'pins_count': 2,
                    'min_distance_km': 10,
                    'time_window_hours': 24,
                    'public_only': True
                }
            },
            # T3 - Regional
            {
                'name': 'City Collector I',
                'description': '3 different cities (same state/region)',
                'type': 'location',
                'challenge_id': 'city_collector_I',
                'tier': 'T3',
                'xp_reward': 175,
                'criteria': {
                    'different_cities': 3,
                    'same_region': True,
                    'public_only': True
                }
            },
            {
                'name': 'Backroads Bopper',
                'description': '3 pins in towns < 100k population',
                'type': 'location',
                'challenge_id': 'backroads_bopper',
                'tier': 'T3',
                'xp_reward': 200,
                'criteria': {
                    'pins_count': 3,
                    'max_population': 100000,
                    'public_only': True
                }
            },
            # T4 - National
            {
                'name': 'State-Line Dropper',
                'description': '2 states/administrative regions',
                'type': 'location',
                'challenge_id': 'state_line',
                'tier': 'T4',
                'xp_reward': 250,
                'criteria': {
                    'different_states': 2,
                    'public_only': True
                }
            },
            {
                'name': 'City Collector II',
                'description': '6 cities total (country-wide)',
                'type': 'location',
                'challenge_id': 'city_collector_II',
                'tier': 'T4',
                'xp_reward': 300,
                'criteria': {
                    'different_cities': 6,
                    'same_country': True,
                    'public_only': True
                }
            },
            # T5 - Continental
            {
                'name': 'Border Crosser',
                'description': '3 countries, same continent',
                'type': 'location',
                'challenge_id': 'border_crosser',
                'tier': 'T5',
                'xp_reward': 350,
                'criteria': {
                    'different_countries': 3,
                    'same_continent': True,
                    'public_only': True
                }
            },
            {
                'name': 'Jet-Set Sound',
                'description': '2 countries within 7 days',
                'type': 'location',
                'challenge_id': 'jet_set',
                'tier': 'T5',
                'xp_reward': 400,
                'criteria': {
                    'different_countries': 2,
                    'time_window_days': 7,
                    'public_only': True
                }
            },
            # T6 - Global
            {
                'name': 'Continent Toucher',
                'description': '3 different continents',
                'type': 'location',
                'challenge_id': 'continent_toucher',
                'tier': 'T6',
                'xp_reward': 500,
                'criteria': {
                    'different_continents': 3,
                    'public_only': True
                }
            },
            {
                'name': 'Globe Trotter',
                'description': '10 cities across ≥ 5 countries',
                'type': 'location',
                'challenge_id': 'globe_trotter',
                'tier': 'T6',
                'xp_reward': 600,
                'criteria': {
                    'different_cities': 10,
                    'min_countries': 5,
                    'public_only': True
                }
            },
            # T7 - Elite
            {
                'name': 'Atlas Mode',
                'description': '10 cities in ≥ 5 countries plus 25 total pins',
                'type': 'location',
                'challenge_id': 'atlas_mode',
                'tier': 'T7',
                'xp_reward': 800,
                'criteria': {
                    'different_cities': 10,
                    'min_countries': 5,
                    'total_pins': 25,
                    'public_only': True
                }
            },
            {
                'name': 'World Tour',
                'description': '≥ 1 city on every continent except Antarctica',
                'type': 'location',
                'challenge_id': 'world_tour',
                'tier': 'T7',
                'xp_reward': 1000,
                'criteria': {
                    'continents_required': ['NA', 'SA', 'EU', 'AF', 'AS', 'OC'],
                    'public_only': True
                }
            },
            # Secret
            {
                'name': 'Pin Pioneer',
                'description': 'Drop in a location with no prior pins',
                'type': 'location',
                'challenge_id': 'pin_pioneer',
                'tier': 'SECRET',
                'xp_reward': 800,
                'is_secret': True,
                'criteria': {
                    'no_prior_pins': True,
                    'public_only': True,
                    'auto_reveal': True
                }
            }
        ]
        
        # Artist-based challenges
        artist_challenges = [
            # T0 - Entry Level
            {
                'name': 'First Fan',
                'description': '1 pin by any artist',
                'type': 'artist',
                'challenge_id': 'first_fan',
                'tier': 'T0',
                'xp_reward': 25,
                'criteria': {
                    'total_pins': 1
                }
            },
            {
                'name': 'Triple Taste',
                'description': '3 unique artists',
                'type': 'artist',
                'challenge_id': 'triple_taste',
                'tier': 'T0',
                'xp_reward': 50,
                'criteria': {
                    'unique_artists': 3
                }
            },
            # T1 - Getting Started
            {
                'name': 'Loyal Listener',
                'description': '3 pins, same artist',
                'type': 'artist',
                'challenge_id': 'loyal_listener',
                'tier': 'T1',
                'xp_reward': 75,
                'criteria': {
                    'same_artist_pins': 3
                }
            },
            {
                'name': 'Ten-Artist Sampler',
                'description': '10 artists total',
                'type': 'artist',
                'challenge_id': 'ten_artist_sampler',
                'tier': 'T1',
                'xp_reward': 100,
                'criteria': {
                    'unique_artists': 10
                }
            },
            # T2 - Enthusiast
            {
                'name': 'Superfan-in-Training',
                'description': '7 pins, same artist (≥ 3 days)',
                'type': 'artist',
                'challenge_id': 'superfan_in_training',
                'tier': 'T2',
                'xp_reward': 150,
                'criteria': {
                    'same_artist_pins': 7,
                    'different_days': 3
                }
            },
            {
                'name': 'Twenty-Artist Tour',
                'description': '20 artists',
                'type': 'artist',
                'challenge_id': 'twenty_artist_tour',
                'tier': 'T2',
                'xp_reward': 175,
                'criteria': {
                    'unique_artists': 20
                }
            },
            # T3 - Advanced
            {
                'name': 'Album Run',
                'description': 'Tracks from 3 albums, same artist',
                'type': 'artist',
                'challenge_id': 'album_run',
                'tier': 'T3',
                'xp_reward': 200,
                'criteria': {
                    'unique_albums': 3,
                    'same_artist': True
                }
            },
            {
                'name': 'Thirty-Artist Explorer',
                'description': '30 artists',
                'type': 'artist',
                'challenge_id': 'thirty_artist_explorer',
                'tier': 'T3',
                'xp_reward': 225,
                'criteria': {
                    'unique_artists': 30
                }
            },
            # T4 - Expert
            {
                'name': 'Discography Diver',
                'description': '6 albums, same artist',
                'type': 'artist',
                'challenge_id': 'discography_diver',
                'tier': 'T4',
                'xp_reward': 300,
                'criteria': {
                    'unique_albums': 6,
                    'same_artist': True
                }
            },
            {
                'name': 'Genre Hopper',
                'description': '5 artists in 5 genres',
                'type': 'artist',
                'challenge_id': 'genre_hopper',
                'tier': 'T4',
                'xp_reward': 325,
                'criteria': {
                    'artists_per_genre': 5,
                    'unique_genres': 5
                }
            },
            # T5 - Professional
            {
                'name': 'Album Explorer',
                'description': 'Pin songs from 5 different albums by the same artist',
                'type': 'artist',
                'challenge_id': 'album_explorer',
                'tier': 'T5',
                'xp_reward': 400,
                'criteria': {
                    'unique_albums': 5,
                    'same_artist': True
                }
            },
            {
                'name': 'Fifty-Artist Collection',
                'description': '50 artists',
                'type': 'artist',
                'challenge_id': 'fifty_artist_collection',
                'tier': 'T5',
                'xp_reward': 450,
                'criteria': {
                    'unique_artists': 50
                }
            },
            # T6 - Master
            {
                'name': 'Artist Variety Master',
                'description': '25 different artists in a single city',
                'type': 'artist',
                'challenge_id': 'artist_variety_master',
                'tier': 'T6',
                'xp_reward': 600,
                'criteria': {
                    'unique_artists_in_city': 25,
                    'same_city': True
                }
            },
            {
                'name': 'Hundred-Artist Explorer',
                'description': '100 artists',
                'type': 'artist',
                'challenge_id': 'hundred_artist_explorer',
                'tier': 'T6',
                'xp_reward': 650,
                'criteria': {
                    'unique_artists': 100
                }
            },
            # T7 - Elite
            {
                'name': 'Discography Master',
                'description': '10 albums, same artist',
                'type': 'artist',
                'challenge_id': 'discography_master',
                'tier': 'T7',
                'xp_reward': 800,
                'criteria': {
                    'unique_albums': 10,
                    'same_artist': True
                }
            },
            {
                'name': 'Worldwide Tastemaker',
                'description': '150 artists',
                'type': 'artist',
                'challenge_id': 'worldwide_tastemaker',
                'tier': 'T7',
                'xp_reward': 1000,
                'criteria': {
                    'unique_artists': 150
                }
            },
            # Secret
            {
                'name': 'Artist Pioneer',
                'description': 'Be the first to pin an artist in your city (auto-reveals when achieved)',
                'type': 'artist',
                'challenge_id': 'artist_pioneer',
                'tier': 'SECRET',
                'xp_reward': 350,
                'is_secret': True,
                'criteria': {
                    'first_artist_in_city': True,
                    'auto_reveal': True
                }
            }
        ]
        
        # Genre-based challenges
        genre_challenges = [
            # T0 - Discovery Spark
            {
                'name': 'First Flavor',
                'description': 'Pin a track in any genre',
                'type': 'genre',
                'challenge_id': 'first_genre_pin',
                'tier': 'T0',
                'xp_reward': 25,
                'criteria': {
                    'total_pins': 1
                }
            },
            {
                'name': 'Triple Dip',
                'description': 'Pin tracks from 3 different genres',
                'type': 'genre',
                'challenge_id': 'triple_genre',
                'tier': 'T0',
                'xp_reward': 50,
                'criteria': {
                    'unique_genres': 3
                }
            },
            # T1 - Casual Curator
            {
                'name': 'Genre Devotee I',
                'description': '3 pins in the same genre',
                'type': 'genre',
                'challenge_id': 'genre_devotee_I',
                'tier': 'T1',
                'xp_reward': 75,
                'criteria': {
                    'same_genre_pins': 3
                }
            },
            {
                'name': 'Five-Flavor Flight',
                'description': '5 unique genres total',
                'type': 'genre',
                'challenge_id': 'five_flavors',
                'tier': 'T1',
                'xp_reward': 100,
                'criteria': {
                    'unique_genres': 5
                }
            },
            # T2 - Vibing Voyager
            {
                'name': 'Genre Devotee II',
                'description': '7 pins, same genre (≥ 3 days)',
                'type': 'genre',
                'challenge_id': 'genre_devotee_II',
                'tier': 'T2',
                'xp_reward': 150,
                'criteria': {
                    'same_genre_pins': 7,
                    'different_days': 3
                }
            },
            {
                'name': 'Ten-Flavor Sampler',
                'description': '10 unique genres',
                'type': 'genre',
                'challenge_id': 'ten_flavors',
                'tier': 'T2',
                'xp_reward': 175,
                'criteria': {
                    'unique_genres': 10
                }
            },
            # T3 - Scene Hopper
            {
                'name': 'Genre Explorer',
                'description': 'Pin 3 different genres in the same city',
                'type': 'genre',
                'challenge_id': 'genre_explorer',
                'tier': 'T3',
                'xp_reward': 200,
                'criteria': {
                    'unique_genres_in_city': 3,
                    'same_city': True
                }
            },
            {
                'name': 'Fifteen-Flavor Tour',
                'description': '15 unique genres',
                'type': 'genre',
                'challenge_id': 'fifteen_flavors',
                'tier': 'T3',
                'xp_reward': 225,
                'criteria': {
                    'unique_genres': 15
                }
            },
            # T4 - Genre Guru
            {
                'name': 'Genre Devotee III',
                'description': '12 pins, same genre',
                'type': 'genre',
                'challenge_id': 'genre_devotee_III',
                'tier': 'T4',
                'xp_reward': 300,
                'criteria': {
                    'same_genre_pins': 12
                }
            },
            {
                'name': 'Daily Genre Mix',
                'description': 'Pin 2 different genres on the same day (3 separate days)',
                'type': 'genre',
                'challenge_id': 'daily_genre_mix',
                'tier': 'T4',
                'xp_reward': 325,
                'criteria': {
                    'different_genres_same_day': 2,
                    'different_days': 3
                }
            },
            # T5 - Sonic Cartographer
            {
                'name': 'Genre Traveler',
                'description': 'Pin the same genre in 3 different countries',
                'type': 'genre',
                'challenge_id': 'genre_traveler',
                'tier': 'T5',
                'xp_reward': 400,
                'criteria': {
                    'same_genre_different_countries': 3,
                    'same_genre': True
                }
            },
            {
                'name': 'Twenty-Flavor Collection',
                'description': '20 unique genres',
                'type': 'genre',
                'challenge_id': 'twenty_flavors',
                'tier': 'T5',
                'xp_reward': 450,
                'criteria': {
                    'unique_genres': 20
                }
            },
            # T6 - Cultural Connoisseur
            {
                'name': 'Weekend Warrior',
                'description': 'Pin 5 different genres on weekends only',
                'type': 'genre',
                'challenge_id': 'weekend_warrior',
                'tier': 'T6',
                'xp_reward': 600,
                'criteria': {
                    'unique_genres_weekend': 5,
                    'weekend_only': True
                }
            },
            {
                'name': 'Thirty-Flavor Explorer',
                'description': '30 unique genres',
                'type': 'genre',
                'challenge_id': 'thirty_flavors',
                'tier': 'T6',
                'xp_reward': 650,
                'criteria': {
                    'unique_genres': 30
                }
            },
            # T7 - Elite
            {
                'name': 'Genre Master',
                'description': '20 pins in one genre plus 25 total unique genres',
                'type': 'genre',
                'challenge_id': 'genre_master',
                'tier': 'T7',
                'xp_reward': 800,
                'criteria': {
                    'same_genre_pins': 20,
                    'unique_genres': 25
                }
            },
            {
                'name': 'Worldly Tastemaker',
                'description': '40 unique genres',
                'type': 'genre',
                'challenge_id': 'worldly_tastemaker',
                'tier': 'T7',
                'xp_reward': 1000,
                'criteria': {
                    'unique_genres': 40
                }
            },
            # Secret
            {
                'name': 'Electronic Explorer',
                'description': 'Pin a track with "electronic", "house", "techno", or "EDM" in the genre',
                'type': 'genre',
                'challenge_id': 'electronic_explorer',
                'tier': 'SECRET',
                'xp_reward': 350,
                'is_secret': True,
                'criteria': {
                    'genre_patterns': ['electronic', 'house', 'techno', 'edm'],
                    'auto_reveal': True
                }
            }
        ]
        
        # Social challenges
        social_challenges = [
            # T0 - Warm-Up
            {
                'name': 'First React',
                'description': 'Give 1 upvote',
                'type': 'social',
                'challenge_id': 'first_react',
                'tier': 'T0',
                'xp_reward': 25,
                'criteria': {
                    'reactions_given': 1
                }
            },
            {
                'name': 'Noticed',
                'description': 'Get 1 upvote on one of your pins',
                'type': 'social',
                'challenge_id': 'first_like_received',
                'tier': 'T0',
                'xp_reward': 25,
                'criteria': {
                    'reactions_received': 1
                }
            },
            # T1 - Initiate
            {
                'name': 'Reaction Rookie',
                'description': 'Give 25 upvotes',
                'type': 'social',
                'challenge_id': 'react_25_given',
                'tier': 'T1',
                'xp_reward': 100,
                'criteria': {
                    'reactions_given': 25
                }
            },
            {
                'name': 'Local Buzz',
                'description': 'Get 10 upvotes from 5 unique users',
                'type': 'social',
                'challenge_id': 'local_buzz',
                'tier': 'T1',
                'xp_reward': 100,
                'criteria': {
                    'reactions_received': 10,
                    'unique_reactors': 5
                }
            },
            # T2 - Contributor
            {
                'name': 'Week-Long Hype',
                'description': 'Give ≥ 1 upvote 7 days in a row',
                'type': 'social',
                'challenge_id': 'react_streak_7',
                'tier': 'T2',
                'xp_reward': 150,
                'criteria': {
                    'consecutive_days': 7,
                    'daily_reactions': 1
                }
            },
            {
                'name': 'Conversation Starter',
                'description': 'Comment on 10 different pins',
                'type': 'social',
                'challenge_id': 'comments_10',
                'tier': 'T2',
                'xp_reward': 150,
                'criteria': {
                    'comments_given': 10
                }
            },
            {
                'name': 'Popular Pin',
                'description': 'Get 15 upvotes on a single pin',
                'type': 'social',
                'challenge_id': 'popular_pin',
                'tier': 'T2',
                'xp_reward': 150,
                'criteria': {
                    'single_pin_reactions': 15
                }
            },
            # T3 - Influencer
            {
                'name': 'Hype Machine',
                'description': 'Give 150 upvotes (lifetime)',
                'type': 'social',
                'challenge_id': 'react_100_given',
                'tier': 'T3',
                'xp_reward': 200,
                'criteria': {
                    'reactions_given': 150
                }
            },
            {
                'name': 'Community Voice',
                'description': 'Write 25 comments',
                'type': 'social',
                'challenge_id': 'comments_25',
                'tier': 'T3',
                'xp_reward': 225,
                'criteria': {
                    'comments_given': 25
                }
            },
            {
                'name': 'Rising Star',
                'description': '20 unique users upvoted your pins',
                'type': 'social',
                'challenge_id': 'unique_supporters_15',
                'tier': 'T3',
                'xp_reward': 225,
                'criteria': {
                    'unique_reactors': 20
                }
            },
            # T4 - Crowd Favorite
            {
                'name': 'Crowd Favorite',
                'description': 'Collect 200 upvotes (lifetime)',
                'type': 'social',
                'challenge_id': 'react_100_received',
                'tier': 'T4',
                'xp_reward': 300,
                'criteria': {
                    'reactions_received': 200
                }
            },
            {
                'name': 'Consistency King',
                'description': 'Give ≥ 2 upvotes 15 consecutive days',
                'type': 'social',
                'challenge_id': 'react_streak_15',
                'tier': 'T4',
                'xp_reward': 300,
                'criteria': {
                    'consecutive_days': 15,
                    'daily_reactions': 2
                }
            },
            {
                'name': 'Weekly Warrior',
                'description': 'Complete 3 different weekly challenges',
                'type': 'social',
                'challenge_id': 'weekly_challenges_3',
                'tier': 'T4',
                'xp_reward': 300,
                'criteria': {
                    'weekly_challenges_completed': 3
                }
            },
            # T5 - Connector
            {
                'name': 'Hot Pin',
                'description': 'Get 40 upvotes on a single pin',
                'type': 'social',
                'challenge_id': 'hot_pin',
                'tier': 'T5',
                'xp_reward': 400,
                'criteria': {
                    'single_pin_reactions': 40
                }
            },
            {
                'name': 'Ripple Effect',
                'description': '50 unique users upvoted your pins',
                'type': 'social',
                'challenge_id': 'unique_supporters_40',
                'tier': 'T5',
                'xp_reward': 450,
                'criteria': {
                    'unique_reactors': 50
                }
            },
            # T6 - Trendsetter
            {
                'name': 'Super Hype',
                'description': 'Give 750 upvotes total',
                'type': 'social',
                'challenge_id': 'react_500_given',
                'tier': 'T6',
                'xp_reward': 600,
                'criteria': {
                    'reactions_given': 750
                }
            },
            {
                'name': 'Multi-Hit Maker',
                'description': '3 pins with ≥ 30 upvotes each',
                'type': 'social',
                'challenge_id': 'multi_hit',
                'tier': 'T6',
                'xp_reward': 650,
                'criteria': {
                    'pins_with_reactions': 3,
                    'reactions_per_pin': 30
                }
            },
            {
                'name': 'Comment Legend',
                'description': 'Write 100 comments',
                'type': 'social',
                'challenge_id': 'comments_100',
                'tier': 'T6',
                'xp_reward': 650,
                'criteria': {
                    'comments_given': 100
                }
            },
            # T7 - Community Legend
            {
                'name': 'Social Icon',
                'description': '500 upvotes received + 100 unique supporters',
                'type': 'social',
                'challenge_id': 'react_350_received',
                'tier': 'T7',
                'xp_reward': 800,
                'criteria': {
                    'reactions_received': 500,
                    'unique_reactors': 100
                }
            },
            {
                'name': 'Viral Pin',
                'description': 'One pin hits 75 upvotes',
                'type': 'social',
                'challenge_id': 'viral_pin',
                'tier': 'T7',
                'xp_reward': 1000,
                'criteria': {
                    'single_pin_reactions': 75
                }
            },
            # Secret
            {
                'name': 'Silent Impact',
                'description': 'A Legend-rank user upvotes your pin',
                'type': 'social',
                'challenge_id': 'legend_like',
                'tier': 'SECRET',
                'xp_reward': 350,
                'is_secret': True,
                'criteria': {
                    'legend_rank_reaction': True,
                    'auto_reveal': True
                }
            }
        ]
        
        # Secret challenges
        secret_challenges = [
            {
                'name': 'Night Owl',
                'description': 'Create 3 pins between midnight and 4 AM',
                'type': 'achievement',
                'challenge_id': 'night_owl_secret',
                'tier': 'T0',
                'xp_reward': 500,
                'is_secret': True,
                'criteria': {
                    'night_pins': 3
                }
            },
            {
                'name': 'Global Citizen',
                'description': 'Create pins in 3 different countries',
                'type': 'location',
                'challenge_id': 'global_citizen_secret',
                'tier': 'T0',
                'xp_reward': 1000,
                'is_secret': True,
                'criteria': {
                    'different_countries': 3
                }
            }
        ]
        
        # Combine all challenges
        all_challenges = (
            location_challenges +
            artist_challenges +
            genre_challenges +
            social_challenges +
            secret_challenges
        )
        
        # Create challenges
        created_count = 0
        for challenge in all_challenges:
            _, created = Achievement.objects.get_or_create(
                challenge_id=challenge['challenge_id'],
                defaults=challenge
            )
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f"Created challenge: {challenge['name']}")
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f"Challenge already exists: {challenge['name']}")
                )
        
        self.stdout.write(
            self.style.SUCCESS(f"Successfully created {created_count} new challenges")
        ) 