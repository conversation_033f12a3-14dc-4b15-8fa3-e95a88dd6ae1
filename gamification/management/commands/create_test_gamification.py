from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.core.files.base import ContentFile
from gamification.models import PinSkin, Achievement, UserAchievement
import random
import json

User = get_user_model()

class Command(BaseCommand):
    help = 'Create test gamification data (achievements and pin skins) for development'

    def add_arguments(self, parser):
        parser.add_argument(
            '--skins',
            type=int,
            default=15,
            help='Number of pin skins to create'
        )
        parser.add_argument(
            '--achievements',
            type=int,
            default=12,
            help='Number of achievements to create'
        )
        parser.add_argument(
            '--user-achievements',
            type=int,
            default=8,
            help='Number of random user achievements to create'
        )

    def handle(self, *args, **options):
        self.stdout.write('Creating test gamification data...')
        
        # Create test pin skins with different categories
        skin_data = [
            # Artist skins
            {
                'name': 'Drake Golden Pin',
                'description': 'A golden pin for Drake superfans',
                'is_premium': True,
                'category': 'artist'
            },
            {
                'name': 'Swift Sparkle Pin',
                'description': 'A sparkling pin inspired by Taylor Swift',
                'is_premium': True,
                'category': 'artist'
            },
            {
                'name': 'Weeknd Neon Pin',
                'description': 'A neon-style pin for The Weeknd fans',
                'is_premium': True,
                'category': 'artist'
            },
            
            # Genre skins
            {
                'name': 'Hip-Hop Chrome Pin',
                'description': 'A chrome-finished pin for hip-hop lovers',
                'is_premium': True,
                'category': 'genre'
            },
            {
                'name': 'Pop Rainbow Pin',
                'description': 'A colorful rainbow pin for pop music fans',
                'is_premium': True,
                'category': 'genre'
            },
            {
                'name': 'Rock Lightning Pin',
                'description': 'An electric pin for rock enthusiasts',
                'is_premium': True,
                'category': 'genre'
            },
            
            # Location skins
            {
                'name': 'Downtown Skyline Pin',
                'description': 'A pin featuring a city skyline',
                'is_premium': True,
                'category': 'location'
            },
            {
                'name': 'Campus Classic Pin',
                'description': 'A scholarly pin for university areas',
                'is_premium': True,
                'category': 'location'
            },
            
            # Social skins
            {
                'name': 'Viral Flame Pin',
                'description': 'A fiery pin for viral content creators',
                'is_premium': True,
                'category': 'social'
            },
            {
                'name': 'Community Heart Pin',
                'description': 'A heart-shaped pin for beloved community members',
                'is_premium': True,
                'category': 'social'
            },
            
            # Special skins
            {
                'name': 'Early Bird Pin',
                'description': 'A special pin for early adopters',
                'is_premium': True,
                'category': 'special'
            },
            {
                'name': 'Master Pin',
                'description': 'The ultimate pin for pin masters',
                'is_premium': True,
                'category': 'special'
            },
            
            # Basic skins (always unlocked)
            {
                'name': 'Classic Blue Pin',
                'description': 'A classic blue music pin',
                'is_premium': False,
                'category': 'basic'
            },
            {
                'name': 'Classic Red Pin',
                'description': 'A classic red music pin',
                'is_premium': False,
                'category': 'basic'
            },
            {
                'name': 'Classic Green Pin',
                'description': 'A classic green music pin',
                'is_premium': False,
                'category': 'basic'
            },
        ]
        
        created_skins = []
        for i, data in enumerate(skin_data[:options['skins']]):
            skin, created = PinSkin.objects.get_or_create(
                name=data['name'],
                defaults={
                    'description': data['description'],
                    'is_premium': data['is_premium'],
                    'image': f'pin_skins/{data["category"]}/{data["name"].lower().replace(" ", "_")}.png'
                }
            )
            if created:
                self.stdout.write(f'Created pin skin: {skin.name}')
            else:
                self.stdout.write(f'Pin skin already exists: {skin.name}')
            created_skins.append(skin)
        
        # Create test achievements with different categories
        achievement_data = [
            # Artist achievements
            {
                'name': 'Drake Devotee',
                'description': 'Add 10 Drake pins to the map',
                'criteria': {'artist_pins': 10, 'artist': 'Drake'},
                'reward_skin': 'Drake Golden Pin',
                'category': 'artist'
            },
            {
                'name': 'Taylor Swift Superfan',
                'description': 'Add 15 Taylor Swift pins to the map',
                'criteria': {'artist_pins': 15, 'artist': 'Taylor Swift'},
                'reward_skin': 'Swift Sparkle Pin',
                'category': 'artist'
            },
            {
                'name': 'The Weeknd Wanderer',
                'description': 'Add 8 The Weeknd pins to the map',
                'criteria': {'artist_pins': 8, 'artist': 'The Weeknd'},
                'reward_skin': 'Weeknd Neon Pin',
                'category': 'artist'
            },
            
            # Genre achievements
            {
                'name': 'Hip-Hop Head',
                'description': 'Add 20 hip-hop tracks to the map',
                'criteria': {'genre_pins': 20, 'genre': 'hip-hop'},
                'reward_skin': 'Hip-Hop Chrome Pin',
                'category': 'genre'
            },
            {
                'name': 'Pop Princess',
                'description': 'Add 25 pop tracks to the map',
                'criteria': {'genre_pins': 25, 'genre': 'pop'},
                'reward_skin': 'Pop Rainbow Pin',
                'category': 'genre'
            },
            {
                'name': 'Rock Legend',
                'description': 'Add 15 rock tracks to the map',
                'criteria': {'genre_pins': 15, 'genre': 'rock'},
                'reward_skin': 'Rock Lightning Pin',
                'category': 'genre'
            },
            
            # Location achievements
            {
                'name': 'Downtown Music Mapper',
                'description': 'Add 30 pins in downtown areas',
                'criteria': {'location_badges': 30, 'area': 'downtown'},
                'reward_skin': 'Downtown Skyline Pin',
                'category': 'location'
            },
            {
                'name': 'Campus Curator',
                'description': 'Add 20 pins on university campuses',
                'criteria': {'location_badges': 20, 'area': 'campus'},
                'reward_skin': 'Campus Classic Pin',
                'category': 'location'
            },
            
            # Social achievements
            {
                'name': 'Viral Vibes',
                'description': 'Get 100 upvotes on your pins',
                'criteria': {'upvotes': 100},
                'reward_skin': 'Viral Flame Pin',
                'category': 'social'
            },
            {
                'name': 'Community Favorite',
                'description': 'Get 50 upvotes on your pins',
                'criteria': {'upvotes': 50},
                'reward_skin': 'Community Heart Pin',
                'category': 'social'
            },
            
            # Special achievements
            {
                'name': 'Early Bird',
                'description': 'Be among the first 100 users',
                'criteria': {'early_user': 1},
                'reward_skin': 'Early Bird Pin',
                'category': 'special'
            },
            {
                'name': 'Pin Master',
                'description': 'Add 100 pins to the map',
                'criteria': {'total_pins': 100},
                'reward_skin': 'Master Pin',
                'category': 'special'
            },
        ]
        
        created_achievements = []
        for i, data in enumerate(achievement_data[:options['achievements']]):
            # Find reward skin if specified
            reward_skin = None
            if data['reward_skin']:
                try:
                    reward_skin = PinSkin.objects.get(name=data['reward_skin'])
                except PinSkin.DoesNotExist:
                    self.stdout.write(f'Warning: Reward skin "{data["reward_skin"]}" not found')
            
            achievement, created = Achievement.objects.get_or_create(
                name=data['name'],
                defaults={
                    'description': data['description'],
                    'criteria': data['criteria'],
                    'reward_skin': reward_skin,
                    'icon': f'achievements/{data["category"]}/{data["name"].lower().replace(" ", "_")}.png'
                }
            )
            if created:
                self.stdout.write(f'Created achievement: {achievement.name} ({data["category"]})')
            else:
                self.stdout.write(f'Achievement already exists: {achievement.name}')
            created_achievements.append(achievement)
        
        # Create some random user achievements with realistic progress
        users = list(User.objects.all()[:10])  # Get up to 10 users
        user_achievements_created = 0
        
        if users and created_achievements:
            for _ in range(options['user_achievements']):
                user = random.choice(users)
                achievement = random.choice(created_achievements)
                
                # Check if user achievement already exists
                existing = UserAchievement.objects.filter(
                    user=user,
                    achievement=achievement
                ).exists()
                
                if not existing:
                    # Create realistic progress based on achievement criteria
                    progress = {}
                    is_completed = random.choice([True, False, False])  # 33% chance of completion
                    
                    for key, target_value in achievement.criteria.items():
                        if isinstance(target_value, (int, float)):
                            if is_completed:
                                # If completed, set progress to target or higher
                                progress[key] = random.randint(int(target_value), int(target_value * 1.5))
                            else:
                                # If not completed, set progress to 0-90% of target
                                progress[key] = random.randint(0, int(target_value * 0.9))
                        else:
                            # For non-numeric criteria (like artist names), just copy the value
                            progress[key] = target_value
                    
                    UserAchievement.objects.create(
                        user=user,
                        achievement=achievement,
                        progress=progress
                    )
                    user_achievements_created += 1
                    status = "✅ COMPLETED" if is_completed else "🔄 IN PROGRESS"
                    self.stdout.write(f'Created user achievement: {user.username} -> {achievement.name} ({status})')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {len(created_skins)} pin skins, '
                f'{len(created_achievements)} achievements, and '
                f'{user_achievements_created} user achievements'
            )
        ) 