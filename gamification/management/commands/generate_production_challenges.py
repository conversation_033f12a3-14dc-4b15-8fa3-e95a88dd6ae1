#!/usr/bin/env python
"""
Django management command to generate production challenges for BOPMaps
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from gamification.models import Achievement, UserAchievement
from pins.models import Pin<PERSON>kin, UserSkin
from users.models import User
from notifications.utils import notify_skin_unlocked, notify_achievement_unlocked
import json


class Command(BaseCommand):
    help = 'Generate production challenges that award pin skins'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating',
        )
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing challenges before creating new ones',
        )
        parser.add_argument(
            '--cleanup-duplicates',
            action='store_true',
            help='Clean up duplicate achievements before creating new ones',
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        self.cleanup_duplicates = options['cleanup_duplicates']
        
        if self.dry_run:
            self.stdout.write('🔍 DRY RUN MODE - No changes will be made')
        
        if options['clear_existing'] and not self.dry_run:
            self.stdout.write('🧹 Clearing existing challenges...')
            self.clear_existing_challenges()
        
        # Clean up duplicates if requested
        if self.cleanup_duplicates and not self.dry_run:
            self.stdout.write('🧹 Cleaning up duplicate achievements...')
            self.cleanup_duplicate_achievements()
        
        self.stdout.write('🎯 Generating production challenges for BOPMaps...')
        
        # Get pin skins that need challenges
        achievement_skins = PinSkin.objects.filter(
            metadata__unlock_type='ACHIEVEMENT'
        )
        
        self.stdout.write(f'📌 Found {achievement_skins.count()} skins that need achievement challenges')
        
        # Define all production challenges
        challenges_data = self.get_challenges_data()
        
        # Process each challenge
        created_count = 0
        updated_count = 0
        
        for challenge_data in challenges_data:
            result = self.process_challenge(challenge_data)
            if result == 'created':
                created_count += 1
            elif result == 'updated':
                updated_count += 1
        
        # Initialize achievements for all users
        if not self.dry_run:
            self.initialize_user_achievements()
        
        self.stdout.write('\n✅ Production challenge generation completed!')
        self.stdout.write(f'   🎯 Challenges created: {created_count}')
        self.stdout.write(f'   🔄 Challenges updated: {updated_count}')
        
        if not self.dry_run:
            self.stdout.write('\n💡 Challenges are now ready for production use!')

    def clear_existing_challenges(self):
        """Clear existing production challenges (but keep test ones)"""
        # Clear all achievements and their related UserAchievements
        achievement_count = Achievement.objects.count()
        user_achievement_count = UserAchievement.objects.count()
        
        # Delete all UserAchievements first (to avoid foreign key constraints)
        UserAchievement.objects.all().delete()
        # Delete all Achievements
        Achievement.objects.all().delete()
        
        self.stdout.write(f'   ✓ Cleared {achievement_count} existing achievements')
        self.stdout.write(f'   ✓ Cleared {user_achievement_count} user achievement relationships')

    def get_challenges_data(self):
        """Define all production challenges with their criteria and rewards"""
        return [
            # === ONBOARDING & MILESTONE CHALLENGES ===
            {
                'challenge_id': 'first_steps',
                'name': 'First Steps',
                'description': 'Drop your very first pin on BOPMaps',
                'type': 'achievement',
                'tier': 'T0',
                'xp_reward': 50,
                'criteria': {
                    'total_pins': 1
                },
                'icon_name': 'flag',
                'primary_color': '#26C6DA',
                'background_color': '#E0F7FA',
                'reward_skin_slug': 'first-drop'
            },
            {
                'challenge_id': 'getting_started',
                'name': 'Getting Started',
                'description': 'Drop 5 pins to start building your musical map',
                'type': 'achievement',
                'tier': 'T0',
                'xp_reward': 75,
                'criteria': {
                    'total_pins': 5
                },
                'icon_name': 'rocket_launch',
                'primary_color': '#4CAF50',
                'background_color': '#E8F5E8'
            },
            {
                'challenge_id': 'map_explorer',
                'name': 'Map Explorer',
                'description': 'Drop 10 pins to explore the musical landscape',
                'type': 'achievement',
                'tier': 'T1',
                'xp_reward': 100,
                'criteria': {
                    'total_pins': 10
                },
                'icon_name': 'explore',
                'primary_color': '#2196F3',
                'background_color': '#E3F2FD'
            },
            {
                'challenge_id': 'seasoned_mapper',
                'name': 'Seasoned Mapper',
                'description': 'Drop 25 pins to become a seasoned musical mapper',
                'type': 'achievement',
                'tier': 'T1',
                'xp_reward': 150,
                'criteria': {
                    'total_pins': 25
                },
                'icon_name': 'map',
                'primary_color': '#FF9800',
                'background_color': '#FFF3E0'
            },
            {
                'challenge_id': 'prolific_pinner',
                'name': 'Prolific Pinner',
                'description': 'Drop 50 pins to show your dedication to music mapping',
                'type': 'achievement',
                'tier': 'T2',
                'xp_reward': 200,
                'criteria': {
                    'total_pins': 50
                },
                'icon_name': 'star',
                'primary_color': '#9C27B0',
                'background_color': '#F3E5F5'
            },
            {
                'challenge_id': 'music_architect',
                'name': 'Music Architect',
                'description': 'Drop 100 pins to become a true architect of musical experiences',
                'type': 'achievement',
                'tier': 'T3',
                'xp_reward': 300,
                'criteria': {
                    'total_pins': 100
                },
                'icon_name': 'construction',
                'primary_color': '#E91E63',
                'background_color': '#FCE4EC'
            },

            # === DAILY HABITS & STREAK CHALLENGES ===
            {
                'challenge_id': 'daily_vibes',
                'name': 'Daily Vibes',
                'description': 'Drop at least 1 pin for 3 consecutive days',
                'type': 'achievement',
                'tier': 'T1',
                'xp_reward': 100,
                'criteria': {
                    'consecutive_days': 3,
                    'daily_pins': 1
                },
                'icon_name': 'calendar_today',
                'primary_color': '#00BCD4',
                'background_color': '#E0F2F1'
            },
            {
                'challenge_id': 'week_warrior',
                'name': 'Week Warrior',
                'description': 'Drop at least 1 pin for 7 consecutive days',
                'type': 'achievement',
                'tier': 'T2',
                'xp_reward': 200,
                'criteria': {
                    'consecutive_days': 7,
                    'daily_pins': 1
                },
                'icon_name': 'sports_martial_arts',
                'primary_color': '#FF5722',
                'background_color': '#FBE9E7'
            },
            {
                'challenge_id': 'streak_master',
                'name': 'Streak Master',
                'description': 'Drop at least 1 pin for 14 consecutive days',
                'type': 'achievement',
                'tier': 'T3',
                'xp_reward': 350,
                'criteria': {
                    'consecutive_days': 14,
                    'daily_pins': 1
                },
                'icon_name': 'whatshot',
                'primary_color': '#FF1744',
                'background_color': '#FFEBEE'
            },
            {
                'challenge_id': 'dedication_legend',
                'name': 'Dedication Legend',
                'description': 'Drop at least 1 pin for 30 consecutive days',
                'type': 'achievement',
                'tier': 'T4',
                'xp_reward': 500,
                'criteria': {
                    'consecutive_days': 30,
                    'daily_pins': 1
                },
                'icon_name': 'emoji_events',
                'primary_color': '#FFD700',
                'background_color': '#FFFDE7'
            },

            # === MOOD & ACTIVITY CHALLENGES ===
            {
                'challenge_id': 'memory_lane',
                'name': 'Memory Lane',
                'description': 'Drop 5 pins with nostalgic memories in their descriptions',
                'type': 'achievement',
                'tier': 'T1',
                'xp_reward': 100,
                'criteria': {
                    'pins_with_mood': 5,
                    'mood_keywords': ['nostalgic', 'memory', 'remember', 'childhood', 'past']
                },
                'icon_name': 'history',
                'primary_color': '#FF6B9D',
                'background_color': '#FFE5EC',
                'reward_skin_slug': 'nostalgic-connection'
            },
            {
                'challenge_id': 'peak_experience',
                'name': 'Peak Experience',
                'description': 'Drop 10 pins during peak music moments (concerts, festivals, parties)',
                'type': 'achievement',
                'tier': 'T2',
                'xp_reward': 200,
                'criteria': {
                    'pins_with_tags': 10,
                    'required_tags': ['concert', 'festival', 'live', 'party']
                },
                'icon_name': 'celebration',
                'primary_color': '#C44569',
                'background_color': '#F8E1E7',
                'reward_skin_slug': 'euphoric-moment'
            },
            {
                'challenge_id': 'emotional_journey',
                'name': 'Emotional Journey',
                'description': 'Drop 15 pins spanning different emotional moods',
                'type': 'achievement',
                'tier': 'T2',
                'xp_reward': 250,
                'criteria': {
                    'unique_moods': 5,
                    'total_mood_pins': 15
                },
                'icon_name': 'favorite',
                'primary_color': '#7B68EE',
                'background_color': '#E8E3FF',
                'reward_skin_slug': 'heartbreak-anthem'
            },
            {
                'challenge_id': 'mood_explorer',
                'name': 'Mood Explorer',
                'description': 'Drop pins representing 8 different emotional states',
                'type': 'achievement',
                'tier': 'T3',
                'xp_reward': 300,
                'criteria': {
                    'unique_moods': 8,
                    'mood_categories': ['happy', 'sad', 'excited', 'calm', 'nostalgic', 'energetic', 'melancholic', 'hopeful']
                },
                'icon_name': 'psychology',
                'primary_color': '#9C27B0',
                'background_color': '#F3E5F5'
            },
            {
                'challenge_id': 'party_starter',
                'name': 'Party Starter',
                'description': 'Drop 20 high-energy party tracks that get 50+ total reactions',
                'type': 'social',
                'tier': 'T3',
                'xp_reward': 300,
                'criteria': {
                    'pins_with_mood': 20,
                    'mood': 'party',
                    'total_reactions': 50
                },
                'icon_name': 'nightlife',
                'primary_color': '#FF1744',
                'background_color': '#FFE0E5',
                'reward_skin_slug': 'party-vibes'
            },
            {
                'challenge_id': 'weather_vibes',
                'name': 'Weather Vibes',
                'description': 'Drop pins on 5 different rainy days',
                'type': 'achievement',
                'tier': 'T1',
                'xp_reward': 150,
                'criteria': {
                    'pins_on_weather': 5,
                    'weather_condition': 'rainy'
                },
                'icon_name': 'water_drop',
                'primary_color': '#546E7A',
                'background_color': '#E2E8EB',
                'reward_skin_slug': 'rainy-day-listening'
            },
            
            # === GENRE CHALLENGES ===
            {
                'challenge_id': 'chill_master',
                'name': 'Chill Master',
                'description': 'Drop 25 lo-fi or chill tracks',
                'type': 'genre',
                'tier': 'T2',
                'xp_reward': 200,
                'criteria': {
                    'genre_pins': 25,
                    'genres': ['lo-fi', 'chill', 'ambient']
                },
                'icon_name': 'self_improvement',
                'primary_color': '#A1887F',
                'background_color': '#EDE7E3',
                'reward_skin_slug': 'chill-lofi-beat'
            },
            {
                'challenge_id': 'bass_drop',
                'name': 'Bass Drop Master',
                'description': 'Drop 30 EDM tracks with at least 10 getting replayed',
                'type': 'genre',
                'tier': 'T3',
                'xp_reward': 350,
                'criteria': {
                    'genre_pins': 30,
                    'genre': 'electronic',
                    'pins_with_replays': 10
                },
                'icon_name': 'graphic_eq',
                'primary_color': '#00E676',
                'background_color': '#E0F7E9',
                'reward_skin_slug': 'hyped-edm-energy'
            },
            {
                'challenge_id': 'jazz_aficionado',
                'name': 'Jazz Aficionado',
                'description': 'Drop 20 jazz tracks in sophisticated venues',
                'type': 'genre',
                'tier': 'T2',
                'xp_reward': 250,
                'criteria': {
                    'genre_pins': 20,
                    'genre': 'jazz',
                    'location_types': ['bar', 'lounge', 'club', 'restaurant']
                },
                'icon_name': 'music_note',
                'primary_color': '#FFB300',
                'background_color': '#FFF3E0',
                'reward_skin_slug': 'jazzy-groove'
            },
            {
                'challenge_id': 'genre_hopper',
                'name': 'Genre Hopper',
                'description': 'Drop pins from 10 different music genres',
                'type': 'genre',
                'tier': 'T2',
                'xp_reward': 200,
                'criteria': {
                    'unique_genres': 10
                },
                'icon_name': 'shuffle',
                'primary_color': '#E91E63',
                'background_color': '#FCE4EC'
            },
            {
                'challenge_id': 'genre_master',
                'name': 'Genre Master',
                'description': 'Drop pins from 20 different music genres',
                'type': 'genre',
                'tier': 'T3',
                'xp_reward': 350,
                'criteria': {
                    'unique_genres': 20
                },
                'icon_name': 'library_music',
                'primary_color': '#3F51B5',
                'background_color': '#E8EAF6'
            },
            
            # === LOCATION CHALLENGES ===
            {
                'challenge_id': 'city_explorer',
                'name': 'City Explorer',
                'description': 'Drop pins in 5 different cities',
                'type': 'location',
                'tier': 'T2',
                'xp_reward': 300,
                'criteria': {
                    'unique_cities': 5
                },
                'icon_name': 'location_city',
                'primary_color': '#00ACC1',
                'background_color': '#E0F7FA',
                'reward_skin_slug': 'new-city-anthem'
            },
            {
                'challenge_id': 'road_warrior',
                'name': 'Road Warrior',
                'description': 'Drop 15 pins while on road trips (100+ miles from home)',
                'type': 'location',
                'tier': 'T3',
                'xp_reward': 400,
                'criteria': {
                    'pins_far_from_home': 15,
                    'min_distance_miles': 100
                },
                'icon_name': 'directions_car',
                'primary_color': '#F4511E',
                'background_color': '#FFEBE7',
                'reward_skin_slug': 'road-trip-anthem'
            },
            {
                'challenge_id': 'neighborhood_hero',
                'name': 'Neighborhood Hero',
                'description': 'Drop 20 pins within 5 miles of your home base',
                'type': 'location',
                'tier': 'T1',
                'xp_reward': 150,
                'criteria': {
                    'pins_near_home': 20,
                    'max_distance_miles': 5
                },
                'icon_name': 'home',
                'primary_color': '#4CAF50',
                'background_color': '#E8F5E8'
            },
            {
                'challenge_id': 'globe_trotter',
                'name': 'Globe Trotter',
                'description': 'Drop pins in 3 different countries',
                'type': 'location',
                'tier': 'T4',
                'xp_reward': 500,
                'criteria': {
                    'unique_countries': 3
                },
                'icon_name': 'public',
                'primary_color': '#FF9800',
                'background_color': '#FFF3E0'
            },
            
            # === DISCOVERY CHALLENGES ===
            {
                'challenge_id': 'music_archaeologist',
                'name': 'Music Archaeologist',
                'description': 'Drop 10 tracks with less than 1000 plays on streaming',
                'type': 'achievement',
                'tier': 'T3',
                'xp_reward': 350,
                'criteria': {
                    'underground_tracks': 10,
                    'max_play_count': 1000
                },
                'icon_name': 'search',
                'primary_color': '#8E24AA',
                'background_color': '#F3E5F5',
                'reward_skin_slug': 'hidden-gem'
            },
            {
                'challenge_id': 'secret_finder',
                'name': 'Secret Finder',
                'description': 'Discover and interact with 20 hidden pins',
                'type': 'achievement',
                'tier': 'T4',
                'xp_reward': 500,
                'criteria': {
                    'hidden_pin_interactions': 20
                },
                'icon_name': 'visibility_off',
                'primary_color': '#424242',
                'background_color': '#F5F5F5',
                'reward_skin_slug': 'secret-pin',
                'is_secret': True
            },
            {
                'challenge_id': 'early_adopter',
                'name': 'Early Adopter',
                'description': 'Drop 5 pins of songs within 24 hours of their release',
                'type': 'achievement',
                'tier': 'T3',
                'xp_reward': 300,
                'criteria': {
                    'early_pins': 5,
                    'hours_after_release': 24
                },
                'icon_name': 'new_releases',
                'primary_color': '#FF6B35',
                'background_color': '#FFF3E0'
            },
            {
                'challenge_id': 'vinyl_hunter',
                'name': 'Vinyl Hunter',
                'description': 'Drop 15 pins of tracks that were originally released on vinyl',
                'type': 'achievement',
                'tier': 'T2',
                'xp_reward': 250,
                'criteria': {
                    'vinyl_tracks': 15,
                    'original_format': 'vinyl'
                },
                'icon_name': 'album',
                'primary_color': '#795548',
                'background_color': '#EFEBE9'
            },
            
            # === SOCIAL CHALLENGES ===
            {
                'challenge_id': 'social_butterfly',
                'name': 'Social Butterfly',
                'description': 'Share 25 pins with friends and get 100 friend reactions',
                'type': 'social',
                'tier': 'T3',
                'xp_reward': 400,
                'criteria': {
                    'shared_pins': 25,
                    'friend_reactions': 100
                },
                'icon_name': 'group',
                'primary_color': '#EC407A',
                'background_color': '#FCE4EC',
                'reward_skin_slug': 'shared-memory'
            },
            {
                'challenge_id': 'viral_sensation',
                'name': 'Viral Sensation',
                'description': 'Have a single pin get 100+ reactions',
                'type': 'social',
                'tier': 'T4',
                'xp_reward': 500,
                'criteria': {
                    'single_pin_reactions': 100
                },
                'icon_name': 'trending_up',
                'primary_color': '#FF5722',
                'background_color': '#FBE9E7'
            },
            {
                'challenge_id': 'community_contributor',
                'name': 'Community Contributor',
                'description': 'Leave 50 thoughtful comments on other users\' pins',
                'type': 'social',
                'tier': 'T2',
                'xp_reward': 250,
                'criteria': {
                    'comments_given': 50
                },
                'icon_name': 'comment',
                'primary_color': '#3F51B5',
                'background_color': '#E8EAF6'
            },
            {
                'challenge_id': 'hype_person',
                'name': 'Hype Person',
                'description': 'Give 100 upvotes to support other users\' musical discoveries',
                'type': 'social',
                'tier': 'T2',
                'xp_reward': 200,
                'criteria': {
                    'reactions_given': 100
                },
                'icon_name': 'thumb_up',
                'primary_color': '#2196F3',
                'background_color': '#E3F2FD'
            },
            {
                'challenge_id': 'conversation_starter',
                'name': 'Conversation Starter',
                'description': 'Get 25 comments on your pins from unique users',
                'type': 'social',
                'tier': 'T3',
                'xp_reward': 350,
                'criteria': {
                    'comments_received': 25,
                    'unique_commenters': 15
                },
                'icon_name': 'chat_bubble',
                'primary_color': '#4CAF50',
                'background_color': '#E8F5E8'
            },

            # === REFERRAL & GROWTH CHALLENGES ===
            {
                'challenge_id': 'connector',
                'name': 'Connector',
                'description': 'Invite 3 friends to join BOPMaps',
                'type': 'social',
                'tier': 'T2',
                'xp_reward': 300,
                'criteria': {
                    'successful_referrals': 3
                },
                'icon_name': 'person_add',
                'primary_color': '#FF9800',
                'background_color': '#FFF3E0'
            },
            {
                'challenge_id': 'community_builder',
                'name': 'Community Builder',
                'description': 'Invite 5 friends who each drop at least 5 pins',
                'type': 'social',
                'tier': 'T3',
                'xp_reward': 500,
                'criteria': {
                    'successful_referrals': 5,
                    'referral_activity_threshold': 5
                },
                'icon_name': 'groups',
                'primary_color': '#9C27B0',
                'background_color': '#F3E5F5'
            },
            {
                'challenge_id': 'influencer',
                'name': 'Influencer',
                'description': 'Invite 10 friends to join the musical revolution',
                'type': 'social',
                'tier': 'T4',
                'xp_reward': 750,
                'criteria': {
                    'successful_referrals': 10
                },
                'icon_name': 'star',
                'primary_color': '#FFD700',
                'background_color': '#FFFDE7'
            },
            
            # === ARTIST CHALLENGES (Gen Z Popular Artists) ===
            {
                'challenge_id': 'artist_devotee_taylor',
                'name': 'Swiftie Forever',
                'description': 'Drop 15 Taylor Swift tracks that get 50+ reactions',
                'type': 'artist',
                'tier': 'T3',
                'xp_reward': 300,
                'criteria': {
                    'artist_pins': 15,
                    'artist': 'Taylor Swift',
                    'total_reactions': 50
                },
                'icon_name': 'favorite_border',
                'primary_color': '#E91E63',
                'background_color': '#FCE4EC'
            },
            {
                'challenge_id': 'artist_devotee_drake',
                'name': 'Drake Devotee',
                'description': 'Drop 10 Drake tracks across different locations',
                'type': 'artist',
                'tier': 'T2',
                'xp_reward': 200,
                'criteria': {
                    'artist_pins': 10,
                    'artist': 'Drake',
                    'unique_locations': 5
                },
                'icon_name': 'star',
                'primary_color': '#FFD700',
                'background_color': '#FFFDE7'
            },
            {
                'challenge_id': 'artist_devotee_billie',
                'name': 'Billie Believer',
                'description': 'Drop 12 Billie Eilish tracks with emotional descriptions',
                'type': 'artist',
                'tier': 'T2',
                'xp_reward': 250,
                'criteria': {
                    'artist_pins': 12,
                    'artist': 'Billie Eilish',
                    'emotional_descriptions': True
                },
                'icon_name': 'sentiment_satisfied',
                'primary_color': '#00E676',
                'background_color': '#E0F7E9'
            },
            {
                'challenge_id': 'artist_devotee_bad_bunny',
                'name': 'Bad Bunny Brigade',
                'description': 'Drop 15 Bad Bunny tracks at party locations',
                'type': 'artist',
                'tier': 'T3',
                'xp_reward': 300,
                'criteria': {
                    'artist_pins': 15,
                    'artist': 'Bad Bunny',
                    'location_types': ['club', 'party', 'festival']
                },
                'icon_name': 'party_mode',
                'primary_color': '#FF6B35',
                'background_color': '#FFF3E0'
                },
            {
                'challenge_id': 'artist_devotee_doja',
                'name': 'Doja Cat Devotee',
                'description': 'Drop 10 Doja Cat tracks that get high engagement',
                'type': 'artist',
                'tier': 'T2',
                'xp_reward': 250,
                'criteria': {
                    'artist_pins': 10,
                    'artist': 'Doja Cat',
                    'high_engagement': True
                },
                'icon_name': 'pets',
                'primary_color': '#E91E63',
                'background_color': '#FCE4EC'
            },
            {
                'challenge_id': 'artist_devotee_weekend',
                'name': 'Weeknd Warrior',
                'description': 'Drop 10 The Weeknd tracks during weekend nights',
                'type': 'artist',
                'tier': 'T2',
                'xp_reward': 200,
                'criteria': {
                    'artist_pins': 10,
                    'artist': 'The Weeknd',
                    'weekend_nights': True
                },
                'icon_name': 'nightlife',
                'primary_color': '#9C27B0',
                'background_color': '#F3E5F5'
            },

            # === COMPETITIVE & LEADERBOARD CHALLENGES ===
            {
                'challenge_id': 'top_curator',
                'name': 'Top Curator',
                'description': 'Be in the top 10% of users for total pins this month',
                'type': 'competitive',
                'tier': 'T4',
                'xp_reward': 500,
                'criteria': {
                    'leaderboard_position': 10,
                    'timeframe': 'month',
                    'metric': 'total_pins'
                },
                'icon_name': 'leaderboard',
                'primary_color': '#FFD700',
                'background_color': '#FFFDE7'
            },
            {
                'challenge_id': 'reaction_champion',
                'name': 'Reaction Champion',
                'description': 'Be in the top 5% of users for reactions received this week',
                'type': 'competitive',
                'tier': 'T3',
                'xp_reward': 350,
                'criteria': {
                    'leaderboard_position': 5,
                    'timeframe': 'week',
                    'metric': 'reactions_received'
                },
                'icon_name': 'emoji_events',
                'primary_color': '#FF5722',
                'background_color': '#FBE9E7'
            },
            {
                'challenge_id': 'discovery_leader',
                'name': 'Discovery Leader',
                'description': 'Be the first to drop 5 trending songs in your city',
                'type': 'competitive',
                'tier': 'T3',
                'xp_reward': 400,
                'criteria': {
                    'first_to_drop': 5,
                    'trending_songs': True,
                    'city_scope': True
                },
                'icon_name': 'trending_up',
                'primary_color': '#00BCD4',
                'background_color': '#E0F2F1'
            },
            
            # === SPECIAL CHALLENGES ===
            {
                'challenge_id': 'night_owl',
                'name': 'Night Owl',
                'description': 'Drop 20 pins between midnight and 4 AM',
                'type': 'achievement',
                'tier': 'T2',
                'xp_reward': 200,
                'criteria': {
                    'pins_at_time': 20,
                    'time_range': {'start': '00:00', 'end': '04:00'}
                },
                'icon_name': 'bedtime',
                'primary_color': '#311B92',
                'background_color': '#EDE7F6'
            },
            {
                'challenge_id': 'festival_goer',
                'name': 'Festival Goer',
                'description': 'Drop pins at 3 different music festivals',
                'type': 'location',
                'tier': 'T3',
                'xp_reward': 400,
                'criteria': {
                    'festival_pins': 3,
                    'unique_festivals': 3
                },
                'icon_name': 'festival',
                'primary_color': '#F50057',
                'background_color': '#FCE4EC'
            },
            {
                'challenge_id': 'seasonal_spirit',
                'name': 'Seasonal Spirit',
                'description': 'Drop pins during each season of the year',
                'type': 'achievement',
                'tier': 'T4',
                'xp_reward': 400,
                'criteria': {
                    'seasonal_pins': True,
                    'unique_seasons': 4
                },
                'icon_name': 'ac_unit',
                'primary_color': '#4CAF50',
                'background_color': '#E8F5E8'
            },

            # === TIME-LIMITED SEASONAL CHALLENGES ===
            {
                'challenge_id': 'summer_vibes_2024',
                'name': 'Summer Vibes 2024',
                'description': 'Drop 20 summer-themed tracks during summer months',
                'type': 'seasonal',
                'tier': 'T2',
                'xp_reward': 300,
                'criteria': {
                    'seasonal_pins': 20,
                    'season': 'summer',
                    'year': 2024
                },
                'icon_name': 'wb_sunny',
                'primary_color': '#FF9800',
                'background_color': '#FFF3E0',
                'is_limited_time': True,
                'expiry_date': '2024-09-22'  # End of summer
            },
            {
                'challenge_id': 'back_to_school_2024',
                'name': 'Back to School 2024',
                'description': 'Drop 15 study/focus tracks during back-to-school season',
                'type': 'seasonal',
                'tier': 'T2',
                'xp_reward': 250,
                'criteria': {
                    'study_tracks': 15,
                    'timeframe': 'back_to_school_2024'
                },
                'icon_name': 'school',
                'primary_color': '#3F51B5',
                'background_color': '#E8EAF6',
                'is_limited_time': True,
                'expiry_date': '2024-10-31'  # End of back-to-school period
            },
            {
                'challenge_id': 'halloween_hits_2024',
                'name': 'Halloween Hits 2024',
                'description': 'Drop 10 spooky or Halloween-themed tracks in October',
                'type': 'seasonal',
                'tier': 'T1',
                'xp_reward': 200,
                'criteria': {
                    'halloween_tracks': 10,
                    'month': 'october',
                    'year': 2024
                },
                'icon_name': 'nights_stay',
                'primary_color': '#FF6B35',
                'background_color': '#FFF3E0',
                'is_limited_time': True,
                'expiry_date': '2024-11-01'  # End of October
            },
            {
                'challenge_id': 'winter_wonderland_2024',
                'name': 'Winter Wonderland 2024',
                'description': 'Drop 15 winter/holiday tracks during winter season',
                'type': 'seasonal',
                'tier': 'T2',
                'xp_reward': 300,
                'criteria': {
                    'winter_tracks': 15,
                    'season': 'winter',
                    'year': 2024
                },
                'icon_name': 'ac_unit',
                'primary_color': '#00BCD4',
                'background_color': '#E0F2F1',
                'is_limited_time': True,
                'expiry_date': '2025-03-20'  # End of winter
            },

            # === XP-ONLY CHALLENGES (For Regular Engagement) ===
            {
                'challenge_id': 'pin_perfectionist',
                'name': 'Pin Perfectionist',
                'description': 'Add detailed descriptions to 20 pins',
                'type': 'achievement',
                'tier': 'T1',
                'xp_reward': 150,
                'criteria': {
                    'detailed_descriptions': 20,
                    'min_words': 10
                },
                'icon_name': 'edit',
                'primary_color': '#607D8B',
                'background_color': '#ECEFF1'
            },
            {
                'challenge_id': 'weekend_warrior',
                'name': 'Weekend Warrior',
                'description': 'Drop 15 pins on weekends only',
                'type': 'achievement',
                'tier': 'T1',
                'xp_reward': 125,
                'criteria': {
                    'weekend_pins': 15,
                    'weekend_only': True
                },
                'icon_name': 'weekend',
                'primary_color': '#795548',
                'background_color': '#EFEBE9'
            },
            {
                'challenge_id': 'morning_person',
                'name': 'Morning Person',
                'description': 'Drop 10 pins between 6 AM and 10 AM',
                'type': 'achievement',
                'tier': 'T1',
                'xp_reward': 100,
                'criteria': {
                    'pins_at_time': 10,
                    'time_range': {'start': '06:00', 'end': '10:00'}
                },
                'icon_name': 'wb_sunny',
                'primary_color': '#FFC107',
                'background_color': '#FFF8E1'
            },
            {
                'challenge_id': 'music_matcher',
                'name': 'Music Matcher',
                'description': 'Pin songs that match your friends\' music taste 5 times',
                'type': 'social',
                'tier': 'T2',
                'xp_reward': 175,
                'criteria': {
                    'friend_taste_matches': 5
                },
                'icon_name': 'people_outline',
                'primary_color': '#8BC34A',
                'background_color': '#F1F8E9'
            },
            {
                'challenge_id': 'activity_tracker',
                'name': 'Activity Tracker',
                'description': 'Be active on BOPMaps for 5 non-consecutive days',
                'type': 'achievement',
                'tier': 'T1',
                'xp_reward': 125,
                'criteria': {
                    'active_days': 5,
                    'activity_threshold': 1  # At least 1 action per day
                },
                'icon_name': 'timeline',
                'primary_color': '#00BCD4',
                'background_color': '#E0F7FA'
            },
            {
                'challenge_id': 'exploration_bonus',
                'name': 'Exploration Bonus',
                'description': 'Drop pins in 3 different venue types',
                'type': 'location',
                'tier': 'T1',
                'xp_reward': 100,
                'criteria': {
                    'unique_venue_types': 3,
                    'venue_types': ['restaurant', 'park', 'home', 'car', 'gym', 'office', 'club', 'bar']
                },
                'icon_name': 'explore',
                'primary_color': '#9C27B0',
                'background_color': '#F3E5F5'
            }
        ]

    def process_challenge(self, challenge_data):
        """Process a single challenge - create or update"""
        try:
            # Check if challenge exists by challenge_id (primary check)
            existing_challenge = Achievement.objects.filter(
                challenge_id=challenge_data['challenge_id']
            ).first()
            
            # If not found by challenge_id, check by name (secondary check for safety)
            if not existing_challenge:
                existing_challenge_by_name = Achievement.objects.filter(
                    name__iexact=challenge_data['name']
                ).first()
                if existing_challenge_by_name:
                    self.stdout.write(
                        self.style.WARNING(
                            f'   ⚠️  Found existing achievement with same name but different challenge_id: '
                            f'{existing_challenge_by_name.name} (challenge_id: {existing_challenge_by_name.challenge_id}). '
                            f'Using existing achievement.'
                        )
                    )
                    existing_challenge = existing_challenge_by_name
            
            # Find reward skin if specified
            reward_skin = None
            if 'reward_skin_slug' in challenge_data:
                from pins.models import PinSkin
                reward_skin = PinSkin.objects.filter(
                    slug=challenge_data['reward_skin_slug']
                ).first()
                if not reward_skin and not self.dry_run:
                    self.stdout.write(
                        self.style.WARNING(
                            f'   ⚠️  Reward skin not found: {challenge_data["reward_skin_slug"]}'
                        )
                    )
            
            if self.dry_run:
                if existing_challenge:
                    self.stdout.write(f'   🔄 Would update: {challenge_data["name"]}')
                else:
                    self.stdout.write(f'   ✨ Would create: {challenge_data["name"]}')
                
                if reward_skin:
                    self.stdout.write(f'      🎁 Rewards: {reward_skin.name}')
                
                return 'dry_run'
            
            # Create or update challenge
            if existing_challenge:
                # Update existing - but ensure challenge_id matches our intended one
                if existing_challenge.challenge_id != challenge_data['challenge_id']:
                    # Update challenge_id to match our intended challenge_id
                    existing_challenge.challenge_id = challenge_data['challenge_id']
                
                existing_challenge.name = challenge_data['name']
                existing_challenge.description = challenge_data['description']
                existing_challenge.type = challenge_data['type']
                existing_challenge.tier = challenge_data['tier']
                existing_challenge.xp_reward = challenge_data['xp_reward']
                existing_challenge.criteria = challenge_data['criteria']
                existing_challenge.icon_name = challenge_data.get('icon_name', '')
                existing_challenge.primary_color = challenge_data.get('primary_color', '#000000')
                existing_challenge.background_color = challenge_data.get('background_color', '#FFFFFF')
                existing_challenge.reward_skin = reward_skin
                existing_challenge.is_secret = challenge_data.get('is_secret', False)
                existing_challenge.save()
                
                # Update the skin's achievement reference if skin exists
                if reward_skin and not reward_skin.achievement:
                    reward_skin.achievement = existing_challenge
                    reward_skin.save(update_fields=['achievement'])
                
                self.stdout.write(f'   🔄 Updated: {challenge_data["name"]}')
                return 'updated'
            else:
                # Create new - but double-check for duplicates one more time
                from django.db import models
                final_check = Achievement.objects.filter(
                    models.Q(challenge_id=challenge_data['challenge_id']) | 
                    models.Q(name__iexact=challenge_data['name'])
                ).first()
                
                if final_check:
                    self.stdout.write(
                        self.style.WARNING(
                            f'   ⚠️  Duplicate detected during creation, updating existing: {final_check.name}'
                        )
                    )
                    # Update the found duplicate instead
                    final_check.challenge_id = challenge_data['challenge_id']
                    final_check.name = challenge_data['name']
                    final_check.description = challenge_data['description']
                    final_check.type = challenge_data['type']
                    final_check.tier = challenge_data['tier']
                    final_check.xp_reward = challenge_data['xp_reward']
                    final_check.criteria = challenge_data['criteria']
                    final_check.icon_name = challenge_data.get('icon_name', '')
                    final_check.primary_color = challenge_data.get('primary_color', '#000000')
                    final_check.background_color = challenge_data.get('background_color', '#FFFFFF')
                    final_check.reward_skin = reward_skin
                    final_check.is_secret = challenge_data.get('is_secret', False)
                    final_check.save()
                    
                    # Link the skin to this achievement if skin exists
                    if reward_skin and not reward_skin.achievement:
                        reward_skin.achievement = final_check
                        reward_skin.save(update_fields=['achievement'])
                    
                    self.stdout.write(f'   🔄 Updated (duplicate): {challenge_data["name"]}')
                    if reward_skin:
                        self.stdout.write(f'      🎁 Rewards: {reward_skin.name}')
                        self.stdout.write(f'      🔗 Linked skin to achievement')
                    return 'updated'
                
                # Create new achievement
                new_achievement = Achievement.objects.create(
                    challenge_id=challenge_data['challenge_id'],
                    name=challenge_data['name'],
                    description=challenge_data['description'],
                    type=challenge_data['type'],
                    tier=challenge_data['tier'],
                    xp_reward=challenge_data['xp_reward'],
                    criteria=challenge_data['criteria'],
                    icon_name=challenge_data.get('icon_name', ''),
                    primary_color=challenge_data.get('primary_color', '#000000'),
                    background_color=challenge_data.get('background_color', '#FFFFFF'),
                    reward_skin=reward_skin,
                    is_secret=challenge_data.get('is_secret', False)
                )
                
                # Link the skin to this achievement if skin exists
                if reward_skin and not reward_skin.achievement:
                    reward_skin.achievement = new_achievement
                    reward_skin.save(update_fields=['achievement'])
                
                self.stdout.write(f'   ✨ Created: {challenge_data["name"]}')
                if reward_skin:
                    self.stdout.write(f'      🎁 Rewards: {reward_skin.name}')
                    self.stdout.write(f'      🔗 Linked skin to achievement')
                
                return 'created'
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ Error processing {challenge_data["name"]}: {str(e)}')
            )
            return None

    def initialize_user_achievements(self):
        """Initialize achievement tracking for all existing users"""
        self.stdout.write('\n📊 Initializing achievements for existing users...')
        
        # Get all non-secret achievements
        achievements = Achievement.objects.filter(is_secret=False)
        users = User.objects.all()
        
        initialized_count = 0
        
        with transaction.atomic():
            for user in users:
                for achievement in achievements:
                    user_achievement, created = UserAchievement.objects.get_or_create(
                        user=user,
                        achievement=achievement,
                        defaults={'progress': {}}
                    )
                    if created:
                        initialized_count += 1
        
        self.stdout.write(f'   ✓ Initialized {initialized_count} user-achievement pairs')
        
        # Check for users who might already qualify for First Drop
        from pins.models import Pin
        users_with_pins = User.objects.filter(pins__isnull=False).distinct()
        
        first_drop_achievement = Achievement.objects.filter(
            challenge_id='first_steps'
        ).first()
        
        if first_drop_achievement:
            completed_count = 0
            for user in users_with_pins:
                user_achievement = UserAchievement.objects.filter(
                    user=user,
                    achievement=first_drop_achievement
                ).first()
                
                if user_achievement and not user_achievement.completed_at:
                    # Mark as completed
                    user_achievement.completed_at = timezone.now()
                    user_achievement.progress = {'total_pins': user.pins.count()}
                    user_achievement.save()
                    
                    # Award the skin
                    if first_drop_achievement.reward_skin:
                        UserSkin.objects.get_or_create(
                            user=user,
                            skin=first_drop_achievement.reward_skin
                        )
                        
                        # Send notification
                        try:
                            notify_achievement_unlocked(
                                user=user,
                                achievement_name=first_drop_achievement.name,
                                achievement_description=first_drop_achievement.description
                            )
                            notify_skin_unlocked(
                                user=user,
                                skin_name=first_drop_achievement.reward_skin.name,
                                unlock_reason=f"Completed {first_drop_achievement.name}"
                            )
                        except Exception as e:
                            self.stdout.write(
                                self.style.WARNING(f'      ⚠️  Failed to send notification: {str(e)}')
                            )
                    
                    completed_count += 1
            
            if completed_count > 0:
                self.stdout.write(
                    f'   ✓ Awarded "First Drop" to {completed_count} existing users'
                ) 

    def cleanup_duplicate_achievements(self):
        """Clean up duplicate achievements based on challenge_id or name"""
        try:
            from django.db.models import Count
            
            # Find duplicate challenge_ids
            duplicate_challenge_ids = Achievement.objects.values('challenge_id').annotate(
                count=Count('id')
            ).filter(count__gt=1)
            
            duplicates_removed = 0
            
            for duplicate in duplicate_challenge_ids:
                challenge_id = duplicate['challenge_id']
                achievements = Achievement.objects.filter(challenge_id=challenge_id).order_by('created_at')
                
                # Keep the first (oldest) one, delete the rest
                achievements_to_delete = achievements[1:]
                for achievement in achievements_to_delete:
                    self.stdout.write(f'   ❌ Removing duplicate achievement: {achievement.name} (ID: {achievement.id})')
                    # Clean up related UserAchievements first
                    UserAchievement.objects.filter(achievement=achievement).delete()
                    achievement.delete()
                    duplicates_removed += 1
            
            # Find duplicate names (case-insensitive)
            duplicate_names = Achievement.objects.values('name').annotate(
                count=Count('id')
            ).filter(count__gt=1)
            
            for duplicate in duplicate_names:
                name = duplicate['name']
                achievements = Achievement.objects.filter(name__iexact=name).order_by('created_at')
                
                if achievements.count() > 1:
                    # Keep the first (oldest) one, delete the rest
                    achievements_to_delete = achievements[1:]
                    for achievement in achievements_to_delete:
                        self.stdout.write(f'   ❌ Removing duplicate achievement by name: {achievement.name} (ID: {achievement.id})')
                        # Clean up related UserAchievements first
                        UserAchievement.objects.filter(achievement=achievement).delete()
                        achievement.delete()
                        duplicates_removed += 1
            
            if duplicates_removed > 0:
                self.stdout.write(f'   ✓ Removed {duplicates_removed} duplicate achievements')
            else:
                self.stdout.write('   ✓ No duplicate achievements found')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ❌ Error cleaning up duplicates: {str(e)}')) 