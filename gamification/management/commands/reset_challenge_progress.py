from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from gamification.models import UserAchievement, Achievement
from users.models import User
import json

class Command(BaseCommand):
    help = 'Reset all completed challenges for testing (keeps challenge definitions)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm that you want to reset all challenge progress',
        )
        parser.add_argument(
            '--backup',
            action='store_true',
            help='Create a backup before resetting',
            default=True,
        )
        parser.add_argument(
            '--user',
            type=str,
            help='Reset only for specific user (username)',
        )
        parser.add_argument(
            '--category',
            type=str,
            choices=['location', 'artist', 'genre', 'social'],
            help='Reset only specific category (location, artist, genre, social)',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    '⚠️  This will reset ALL challenge progress for ALL users!\n'
                    'Add --confirm to proceed.\n'
                    'Example: python manage.py reset_challenge_progress --confirm'
                )
            )
            return

        self.stdout.write(self.style.SUCCESS('🔄 Starting challenge progress reset...'))

        with transaction.atomic():
            # Create backup if requested
            if options['backup']:
                self.create_backup()

            # Get user filter
            user_filter = {}
            achievement_filter = {}
            
            if options['user']:
                try:
                    user = User.objects.get(username=options['user'])
                    user_filter['user'] = user
                    self.stdout.write(f'📍 Resetting only for user: {user.username}')
                except User.DoesNotExist:
                    self.stdout.write(
                        self.style.ERROR(f'❌ User "{options["user"]}" not found')
                    )
                    return
            
            if options['category']:
                achievement_filter['achievement__type'] = options['category']
                self.stdout.write(f'🎯 Resetting only {options["category"].upper()} challenges')

            # Reset completed challenges
            completed_count = UserAchievement.objects.filter(
                completed_at__isnull=False,
                **user_filter,
                **achievement_filter
            ).count()

            in_progress_count = UserAchievement.objects.filter(
                completed_at__isnull=True,
                **user_filter,
                **achievement_filter
            ).count()

            self.stdout.write(f'📊 Found:')
            self.stdout.write(f'   • {completed_count} completed challenges')
            self.stdout.write(f'   • {in_progress_count} in-progress challenges')

            # Method 1: Reset completion status (keep progress records)
            reset_completed = UserAchievement.objects.filter(
                completed_at__isnull=False,
                **user_filter,
                **achievement_filter
            ).update(
                completed_at=None,
                progress={}
            )

            # Method 2: Reset in-progress challenges
            reset_progress = UserAchievement.objects.filter(
                completed_at__isnull=True,
                **user_filter,
                **achievement_filter
            ).update(
                progress={}
            )

            # Reset user XP (set total_xp to 0) - only if resetting all categories
            if not options['category']:  # Don't reset XP for category-specific resets
                if options['user']:
                    user.total_xp = 0
                    user.save()
                    self.stdout.write(f'🔄 Reset XP for user: {user.username}')
                else:
                    updated_users = User.objects.all().update(total_xp=0)
                    self.stdout.write(f'🔄 Reset XP for {updated_users} users')
            else:
                self.stdout.write(f'ℹ️  XP not reset (category-specific reset)')

            self.stdout.write(self.style.SUCCESS('✅ Challenge progress reset complete!'))
            self.stdout.write(f'   • Reset {reset_completed} completed challenges')
            self.stdout.write(f'   • Reset {reset_progress} in-progress challenges')
            self.stdout.write(f'   • All users\' XP reset to 0')
            self.stdout.write(f'   • Challenge definitions preserved')

            # Show current state
            self.show_current_state(user_filter, achievement_filter)

    def create_backup(self):
        """Create backup of current challenge progress"""
        self.stdout.write('💾 Creating backup...')
        
        backup_data = {
            'timestamp': timezone.now().isoformat(),
            'user_achievements': [],
            'user_xp': []
        }

        # Backup user achievements
        for ua in UserAchievement.objects.all().select_related('user', 'achievement'):
            backup_data['user_achievements'].append({
                'user_id': ua.user.id,
                'username': ua.user.username,
                'achievement_id': ua.achievement.id,
                'achievement_name': ua.achievement.name,
                'progress': ua.progress,
                'completed_at': ua.completed_at.isoformat() if ua.completed_at else None,
                'last_updated': ua.last_updated.isoformat()
            })

        # Backup user XP
        for user in User.objects.all():
            backup_data['user_xp'].append({
                'user_id': user.id,
                'username': user.username,
                'total_xp': user.total_xp
            })

        # Save backup
        backup_filename = f'challenge_progress_backup_{timezone.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(backup_filename, 'w') as f:
            json.dump(backup_data, f, indent=2)

        self.stdout.write(f'💾 Backup saved to: {backup_filename}')

    def show_current_state(self, user_filter, achievement_filter=None):
        """Show current state after reset"""
        self.stdout.write('\n📊 Current State:')
        
        if achievement_filter:
            # Category-specific state
            category = achievement_filter['achievement__type']
            total_achievements = Achievement.objects.filter(type=category).count()
            completed_after = UserAchievement.objects.filter(
                completed_at__isnull=False,
                **user_filter,
                **achievement_filter
            ).count()
            in_progress_after = UserAchievement.objects.filter(
                completed_at__isnull=True,
                **user_filter,
                **achievement_filter
            ).count()
            
            self.stdout.write(f'   • {total_achievements} total {category.upper()} challenges')
            self.stdout.write(f'   • {completed_after} completed {category} challenges (should be 0)')
            self.stdout.write(f'   • {in_progress_after} in-progress {category} challenges')
        else:
            # All categories state
            total_achievements = Achievement.objects.count()
            
            # Fix user count calculation
            if user_filter and 'user' in user_filter:
                total_users = 1  # Single user
                user_info = f' for user: {user_filter["user"].username}'
            else:
                total_users = User.objects.count()
                user_info = ''
            
            completed_after = UserAchievement.objects.filter(
                completed_at__isnull=False,
                **user_filter
            ).count()
            
            in_progress_after = UserAchievement.objects.filter(
                completed_at__isnull=True,
                **user_filter
            ).count()

            self.stdout.write(f'   • {total_achievements} total challenges available')
            self.stdout.write(f'   • {total_users} user(s) affected{user_info}')
            self.stdout.write(f'   • {completed_after} completed challenges (should be 0)')
            self.stdout.write(f'   • {in_progress_after} in-progress challenges')
        
        completed_after = UserAchievement.objects.filter(
            completed_at__isnull=False,
            **user_filter,
            **(achievement_filter or {})
        ).count()
        
        if completed_after == 0:
            self.stdout.write(self.style.SUCCESS('✅ All challenges successfully reset!'))
        else:
            self.stdout.write(self.style.WARNING(f'⚠️  {completed_after} challenges still marked as completed')) 