from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from gamification.models import Achievement, UserAchievement
from gamification.services.xp_calculator import XPCalculator
from rankings.models import UserRanking
from django.utils import timezone

User = get_user_model()

class Command(BaseCommand):
    help = 'Set a user\'s XP to a specific value for testing'

    def add_arguments(self, parser):
        parser.add_argument('username', type=str, help='Username to modify')
        parser.add_argument('xp', type=int, help='New XP value')

    def handle(self, *args, **options):
        username = options['username']
        target_xp = options['xp']

        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'❌ User {username} not found'))
            return

        self.stdout.write(f'🎯 Setting XP for {username}...')
        self.stdout.write(f'   Current XP: {XPCalculator.get_user_total_xp(user)}')

        with transaction.atomic():
            # Create a test achievement if needed
            test_achievement, created = Achievement.objects.get_or_create(
                name='Test XP Achievement',
                defaults={
                    'description': 'Achievement for testing XP',
                    'type': 'social',
                    'xp_reward': target_xp,
                    'criteria': {'test': True}
                }
            )
            
            # Update XP reward if achievement already existed
            if not created:
                test_achievement.xp_reward = target_xp
                test_achievement.save(update_fields=['xp_reward'])

            # Reset existing achievements
            UserAchievement.objects.filter(user=user).delete()

            # Create new achievement with target XP
            UserAchievement.objects.create(
                user=user,
                achievement=test_achievement,
                completed_at=timezone.now(),
                progress={'test': True}
            )

            # Update rankings
            ranking, _ = UserRanking.objects.get_or_create(user=user)
            ranking.xp = target_xp
            ranking.achievement_score = target_xp  # Store XP directly
            ranking.total_score = target_xp  # Store XP directly
            ranking.calculate_level()
            ranking.save()

        # Verify the change
        new_xp = XPCalculator.get_user_total_xp(user)
        level_info = XPCalculator.get_user_level_info(user)

        self.stdout.write(self.style.SUCCESS(f'✅ XP set successfully!'))
        self.stdout.write(f'   New XP: {new_xp}')
        self.stdout.write(f'   Level: {level_info["current_level"]["level"]} ({level_info["current_level"]["name"]})')
        self.stdout.write(f'   Progress: {level_info["progress_percentage"]}% to next level') 