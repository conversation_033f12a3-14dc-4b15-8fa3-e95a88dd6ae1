from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth import get_user_model
from gamification.services.xp_calculator import XPCalculator
from rankings.models import UserRanking

User = get_user_model()

class Command(BaseCommand):
    help = 'Sync XP system to use only challenge-based XP from gamification app'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Preview changes without applying them'
        )
        parser.add_argument(
            '--reset-rankings',
            action='store_true',
            help='Reset all rankings to use only challenge XP'
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        reset_rankings = options['reset_rankings']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('🔍 DRY RUN MODE - No changes will be made'))
        
        self.stdout.write('🎯 Analyzing XP System Migration...\n')
        
        # Analyze current state
        self.analyze_current_state()
        
        if not dry_run:
            self.stdout.write('\n🔄 Starting XP system migration...')
            
            with transaction.atomic():
                if reset_rankings:
                    self.reset_rankings_system()
                else:
                    self.update_rankings_system()
                
                self.create_challenge_bridge()
                
            self.stdout.write(self.style.SUCCESS('\n✅ XP system migration complete!'))
            self.show_migration_results()
        else:
            self.stdout.write(self.style.WARNING('\n🔍 Dry run complete. Use --no-dry-run to apply changes.'))

    def analyze_current_state(self):
        """Analyze the current fragmented XP state"""
        self.stdout.write('📊 Current XP System Analysis:')
        
        # Count users with achievements
        users_with_achievements = User.objects.filter(
            userachievement__completed_at__isnull=False
        ).distinct().count()
        
        # Count users with rankings
        users_with_rankings = UserRanking.objects.count()
        
        # Calculate total XP from both systems
        total_challenge_xp = 0
        total_ranking_xp = 0
        
        for user in User.objects.filter(userachievement__completed_at__isnull=False).distinct():
            challenge_xp = XPCalculator.get_user_total_xp(user)
            total_challenge_xp += challenge_xp
            
            try:
                ranking = UserRanking.objects.get(user=user)
                ranking_xp = ranking.total_score * 10  # Current conversion
                total_ranking_xp += ranking_xp
            except UserRanking.DoesNotExist:
                pass
        
        self.stdout.write(f'  👥 Users with completed challenges: {users_with_achievements}')
        self.stdout.write(f'  👥 Users with rankings: {users_with_rankings}')
        self.stdout.write(f'  🎯 Total Challenge XP: {total_challenge_xp:,}')
        self.stdout.write(f'  📊 Total Ranking XP: {total_ranking_xp:,}')
        
        # Show discrepancies
        if total_challenge_xp != total_ranking_xp:
            diff = abs(total_challenge_xp - total_ranking_xp)
            self.stdout.write(f'  ⚠️  XP Discrepancy: {diff:,} XP difference between systems')
        else:
            self.stdout.write('  ✅ XP systems are in sync')

    def reset_rankings_system(self):
        """Reset rankings to use only challenge-based XP"""
        self.stdout.write('🔄 Resetting rankings system...')
        
        # Clear existing score components
        UserRanking.objects.update(
            challenge_score=0,
            pin_score=0,
            social_score=0,
            achievement_score=0,
            total_score=0
        )
        
        # Update from challenges only
        XPCalculator.update_rankings_from_challenges()
        
        # Update ranks based on new scores
        rankings = UserRanking.objects.all().order_by('-total_score')
        for rank, ranking in enumerate(rankings, 1):
            ranking.current_rank = rank
            ranking.save(update_fields=['current_rank'])
        
        self.stdout.write('  ✅ Rankings reset to challenge-based XP only')

    def update_rankings_system(self):
        """Update rankings to include challenge-based XP"""
        self.stdout.write('🔄 Updating rankings system...')
        
        users_updated = 0
        
        for user in User.objects.filter(userachievement__completed_at__isnull=False).distinct():
            challenge_xp = XPCalculator.get_user_total_xp(user)
            level_info = XPCalculator.get_user_level_info(user)
            
            ranking, created = UserRanking.objects.get_or_create(
                user=user,
                defaults={
                    'total_score': challenge_xp // 10,
                    'achievement_score': challenge_xp // 10,
                    'level': level_info['current_level']['level'],
                    'xp': challenge_xp,
                    'badge_name': level_info['current_level']['name']
                }
            )
            
            if not created:
                # Add challenge XP to existing ranking
                ranking.achievement_score = challenge_xp // 10
                ranking.xp = challenge_xp
                ranking.level = level_info['current_level']['level']
                ranking.badge_name = level_info['current_level']['name']
                ranking.calculate_total_score()
                ranking.save()
            
            users_updated += 1
        
        self.stdout.write(f'  ✅ Updated {users_updated} user rankings')

    def create_challenge_bridge(self):
        """Create bridge between weekly challenges and achievement challenges"""
        self.stdout.write('🌉 Creating challenge bridge...')
        
        # Import here to avoid circular imports
        from challenges.models import ChallengeParticipation
        from gamification.models import Achievement, UserAchievement
        
        # Create achievements for weekly challenge participation if they don't exist
        weekly_challenge_achievement, created = Achievement.objects.get_or_create(
            challenge_id='weekly_participation',
            defaults={
                'name': 'Weekly Challenger',
                'description': 'Participate in weekly challenges',
                'type': 'social',
                'tier': 'T1',
                'xp_reward': 50,
                'criteria': {'weekly_participations': 1}
            }
        )
        
        if created:
            self.stdout.write('  ✅ Created weekly challenge achievement')
        
        # Bridge existing weekly challenge participations
        participations = ChallengeParticipation.objects.all()
        bridged_count = 0
        
        for participation in participations:
            # Create or update user achievement for weekly participation
            user_achievement, created = UserAchievement.objects.get_or_create(
                user=participation.user,
                achievement=weekly_challenge_achievement,
                defaults={
                    'progress': {'weekly_participations': 1},
                    'completed_at': participation.created_at
                }
            )
            
            if not created:
                # Update progress
                current_count = user_achievement.progress.get('weekly_participations', 0)
                user_achievement.progress['weekly_participations'] = current_count + 1
                if not user_achievement.completed_at:
                    user_achievement.completed_at = participation.created_at
                user_achievement.save()
            
            bridged_count += 1
        
        self.stdout.write(f'  ✅ Bridged {bridged_count} weekly challenge participations')

    def show_migration_results(self):
        """Show results of the migration"""
        self.stdout.write('\n📈 Migration Results:')
        
        # Get updated stats
        users_with_xp = User.objects.filter(
            userachievement__completed_at__isnull=False
        ).distinct().count()
        
        total_xp = sum(
            XPCalculator.get_user_total_xp(user)
            for user in User.objects.filter(userachievement__completed_at__isnull=False).distinct()
        )
        
        # Top users by XP
        leaderboard = XPCalculator.get_leaderboard_data(5)
        
        self.stdout.write(f'  👥 Users with XP: {users_with_xp}')
        self.stdout.write(f'  🎯 Total System XP: {total_xp:,}')
        self.stdout.write('\n  🏆 Top 5 Users by Challenge XP:')
        
        for entry in leaderboard:
            self.stdout.write(
                f'    {entry["rank"]}. {entry["username"]} - '
                f'{entry["total_xp"]:,} XP (Level {entry["level"]} {entry["badge_name"]})'
            )
        
        self.stdout.write('\n🎯 New Unified XP System Active!')
        self.stdout.write('  • All XP now comes from completed challenges only')
        self.stdout.write('  • Rankings sync with gamification system')
        self.stdout.write('  • Weekly challenges bridge to achievement system')
        self.stdout.write('  • Consistent XP calculation across all endpoints') 