# Generated by Django 4.2.7 on 2025-04-07 16:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("gamification", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="userachievement",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="achievements",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="achievement",
            name="reward_skin",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="achievement_rewards",
                to="gamification.pinskin",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="userachievement",
            unique_together={("user", "achievement")},
        ),
    ]
