# Generated by Django 4.2.7 on 2025-06-16 02:15

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("gamification", "0002_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="achievement",
            name="challenge_id",
            field=models.CharField(blank=True, max_length=50, null=True, unique=True),
        ),
        migrations.AddField(
            model_name="achievement",
            name="is_secret",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="achievement",
            name="tier",
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name="achievement",
            name="type",
            field=models.CharField(
                choices=[
                    ("achievement", "Achievement"),
                    ("location", "Location Challenge"),
                    ("artist", "Artist Challenge"),
                    ("genre", "Genre Challenge"),
                    ("social", "Social Challenge"),
                ],
                default="achievement",
                max_length=20,
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="achievement",
            name="xp_reward",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="userachievement",
            name="last_updated",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name="achievement",
            name="criteria",
            field=models.JSONField(help_text="JSON criteria for completion"),
        ),
        migrations.AlterField(
            model_name="userachievement",
            name="completed_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="userachievement",
            name="progress",
            field=models.JSONField(
                default=dict, help_text="Current progress towards completion"
            ),
        ),
        migrations.AddIndex(
            model_name="achievement",
            index=models.Index(fields=["type"], name="gamificatio_type_2a30ec_idx"),
        ),
        migrations.AddIndex(
            model_name="achievement",
            index=models.Index(
                fields=["challenge_id"], name="gamificatio_challen_829c6b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="achievement",
            index=models.Index(fields=["tier"], name="gamificatio_tier_8c5723_idx"),
        ),
        migrations.AddIndex(
            model_name="userachievement",
            index=models.Index(
                fields=["user", "achievement"], name="gamificatio_user_id_4b2f15_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="userachievement",
            index=models.Index(
                fields=["completed_at"], name="gamificatio_complet_82f63a_idx"
            ),
        ),
    ]
