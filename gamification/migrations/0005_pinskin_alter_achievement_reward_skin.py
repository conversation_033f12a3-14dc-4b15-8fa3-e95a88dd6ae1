# Generated by Django 4.2.7 on 2025-06-17 00:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("gamification", "0004_merge_20250617_0052"),
    ]

    operations = [
        migrations.CreateModel(
            name="PinSkin",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=50)),
                ("image", models.ImageField(upload_to="pin_skins/")),
                ("description", models.TextField(blank=True, null=True)),
                ("is_premium", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.AlterField(
            model_name="achievement",
            name="reward_skin",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="achievement_rewards",
                to="gamification.pinskin",
            ),
        ),
    ]
