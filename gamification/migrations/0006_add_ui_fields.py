# Generated by Django 4.2.7 on 2025-06-17 01:15

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('gamification', '0005_pinskin_alter_achievement_reward_skin'),
    ]

    operations = [
        migrations.AddField(
            model_name='achievement',
            name='icon_name',
            field=models.CharField(
                blank=True,
                help_text='Material icon name for Flutter',
                max_length=50,
                null=True
            ),
        ),
        migrations.AddField(
            model_name='achievement',
            name='background_color',
            field=models.CharField(
                default='#FFFFFF',
                help_text='Hex color code',
                max_length=7
            ),
        ),
        migrations.AddField(
            model_name='achievement',
            name='primary_color',
            field=models.Char<PERSON>ield(
                default='#000000',
                help_text='Hex color code',
                max_length=7
            ),
        ),
        migrations.AddField(
            model_name='achievement',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddIndex(
            model_name='achievement',
            index=models.Index(fields=['created_at'], name='gamificatio_created_123abc_idx'),
        ),
    ] 