from django.db import models
from django.utils import timezone

# Create your models here.

class Achievement(models.Model):
    """
    Base model for achievements and challenges
    """
    TYPES = [
        ('achievement', 'Achievement'),
        ('location', 'Location Challenge'),
        ('artist', 'Artist Challenge'),
        ('genre', 'Genre Challenge'),
        ('social', 'Social Challenge')
    ]
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    icon = models.ImageField(upload_to='achievements/', null=True, blank=True)
    icon_name = models.CharField(max_length=50, help_text="Material icon name for Flutter", null=True, blank=True)
    criteria = models.JSONField(help_text="JSON criteria for completion")
    type = models.CharField(max_length=20, choices=TYPES, default='achievement')
    
    # Challenge-specific fields
    challenge_id = models.CharField(max_length=50, null=True, blank=True, unique=True)
    tier = models.CharField(max_length=10, null=True, blank=True)  # T0-T7
    xp_reward = models.Integer<PERSON><PERSON>(default=0)
    is_secret = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    
    # Visual customization
    background_color = models.CharField(max_length=7, default="#FFFFFF", help_text="Hex color code")
    primary_color = models.CharField(max_length=7, default="#000000", help_text="Hex color code")
    
    # Optional reward
    reward_skin = models.ForeignKey(
        'pins.PinSkin', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='achievement_rewards'
    )
    
    class Meta:
        indexes = [
            models.Index(fields=['type']),
            models.Index(fields=['challenge_id']),
            models.Index(fields=['tier']),
            models.Index(fields=['created_at'])
        ]
    
    def __str__(self):
        return self.name
    
    @property
    def is_challenge(self):
        return self.type != 'achievement'
    
    @property
    def category(self):
        """Return category name for Flutter UI"""
        return self.get_type_display()
    
    @property
    def icon_data(self):
        """Get icon data for Flutter"""
        return {
            'name': self.icon_name or 'emoji_events',  # Default icon
            'color': self.primary_color
        }


class UserAchievement(models.Model):
    """
    Model for tracking user's progress on achievements
    """
    user = models.ForeignKey('users.User', on_delete=models.CASCADE)
    achievement = models.ForeignKey(Achievement, on_delete=models.CASCADE)
    progress = models.JSONField(default=dict, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['user', 'achievement']
        indexes = [
            models.Index(fields=['user', 'completed_at']),
            models.Index(fields=['achievement'])
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.achievement.name}"
    
    def check_completion(self):
        """
        Check if achievement is completed based on criteria
        """
        if self.completed_at:
            return True
            
        criteria = self.achievement.criteria
        progress = self.progress or {}
        
        # Handle manual completion flag
        if progress.get('completed', False):
            self.completed_at = timezone.now()
            self.save()
            return True
        
        # Check all criteria - ALL must be met for completion
        completion_checks = []
        
        # === BASIC COUNT CRITERIA ===
        if 'required_count' in criteria:
            current = progress.get('current_count', 0)
            required = criteria['required_count']
            completion_checks.append(current >= required)
        
        if 'total_pins' in criteria:
            current = progress.get('total_pins', 0)
            required = criteria['total_pins']
            completion_checks.append(current >= required)
        
        # === GEOGRAPHIC CRITERIA ===
        if 'pins_in_radius' in criteria:
            current = progress.get('pins_in_radius', 0)
            required = criteria['pins_in_radius']
            completion_checks.append(current >= required)
        
        if 'different_cities' in criteria:
            current = progress.get('different_cities', 0)
            required = criteria['different_cities']
            completion_checks.append(current >= required)
        
        if 'different_states' in criteria:
            current = progress.get('different_states', 0)
            required = criteria['different_states']
            completion_checks.append(current >= required)
        
        if 'different_countries' in criteria:
            current = progress.get('different_countries', 0)
            required = criteria['different_countries']
            completion_checks.append(current >= required)
        
        if 'different_continents' in criteria:
            current = progress.get('different_continents', 0)
            required = criteria['different_continents']
            completion_checks.append(current >= required)
        
        if 'continents_required' in criteria:
            visited_continents = set(progress.get('visited_continents', []))
            required_continents = set(criteria['continents_required'])
            completion_checks.append(required_continents.issubset(visited_continents))
        
        if 'districts' in criteria:
            current = progress.get('districts', 0)
            required = criteria['districts']
            completion_checks.append(current >= required)
        
        if 'no_prior_pins' in criteria:
            # This is a special case - should be checked when pin is created
            completion_checks.append(progress.get('no_prior_pins_verified', False))
        
        # === ARTIST CRITERIA ===
        if 'unique_artists' in criteria:
            current = progress.get('unique_artists', 0)
            required = criteria['unique_artists']
            completion_checks.append(current >= required)
        
        if 'same_artist_pins' in criteria:
            current = progress.get('same_artist_pins', 0)
            required = criteria['same_artist_pins']
            completion_checks.append(current >= required)
        
        if 'unique_albums' in criteria:
            current = progress.get('unique_albums', 0)
            required = criteria['unique_albums']
            completion_checks.append(current >= required)
        
        if 'unique_artists_in_city' in criteria:
            current = progress.get('unique_artists_in_city', 0)
            required = criteria['unique_artists_in_city']
            completion_checks.append(current >= required)
        
        if 'first_artist_in_city' in criteria:
            is_first = progress.get('first_artist_in_city', False)
            completion_checks.append(is_first)
        
        if 'chart_topping_artists' in criteria:
            current = progress.get('chart_topping_artists', 0)
            required = criteria['chart_topping_artists']
            completion_checks.append(current >= required)
        
        if 'artists_per_genre' in criteria and 'unique_genres' in criteria:
            # Genre Hopper: 5 artists in 5 genres
            genres_with_enough_artists = progress.get('genres_with_enough_artists', 0)
            required_genres = criteria['unique_genres']
            completion_checks.append(genres_with_enough_artists >= required_genres)
        
        # === GENRE CRITERIA ===
        if 'unique_genres' in criteria and 'artists_per_genre' not in criteria:
            current = progress.get('unique_genres', 0)
            required = criteria['unique_genres']
            completion_checks.append(current >= required)
        
        if 'same_genre_pins' in criteria:
            current = progress.get('same_genre_pins', 0)
            required = criteria['same_genre_pins']
            completion_checks.append(current >= required)
        
        if 'unique_genres_in_city' in criteria:
            current = progress.get('unique_genres_in_city', 0)
            required = criteria['unique_genres_in_city']
            completion_checks.append(current >= required)
        
        if 'same_genre_different_countries' in criteria:
            current = progress.get('same_genre_different_countries', 0)
            required = criteria['same_genre_different_countries']
            completion_checks.append(current >= required)
        
        if 'different_genres_same_day' in criteria and 'different_days' in criteria:
            # Daily Genre Mix: 2 different genres on same day for 3 different days
            days_with_different_genres = progress.get('days_with_different_genres', 0)
            required_days = criteria['different_days']
            completion_checks.append(days_with_different_genres >= required_days)
        
        if 'unique_genres_weekend' in criteria:
            current = progress.get('unique_genres_weekend', 0)
            required = criteria['unique_genres_weekend']
            completion_checks.append(current >= required)
        
        if 'genre_patterns' in criteria:
            # Electronic Explorer: genre contains specific patterns
            completion_checks.append(progress.get('genre_pattern_matched', False))
        
        # === SOCIAL CRITERIA ===
        if 'reactions_given' in criteria:
            current = progress.get('reactions_given', 0)
            required = criteria['reactions_given']
            completion_checks.append(current >= required)
        
        if 'reactions_received' in criteria:
            current = progress.get('reactions_received', 0)
            required = criteria['reactions_received']
            completion_checks.append(current >= required)
        
        if 'unique_reactors' in criteria:
            current = progress.get('unique_reactors', 0)
            required = criteria['unique_reactors']
            completion_checks.append(current >= required)
        
        if 'unique_shares' in criteria:
            current = progress.get('unique_shares', 0)
            required = criteria['unique_shares']
            completion_checks.append(current >= required)
        
        if 'total_replays' in criteria:
            current = progress.get('total_replays', 0)
            required = criteria['total_replays']
            completion_checks.append(current >= required)
        
        if 'successful_referrals' in criteria:
            current = progress.get('successful_referrals', 0)
            required = criteria['successful_referrals']
            completion_checks.append(current >= required)
        
        if 'pins_with_reactions' in criteria and 'reactions_per_pin' in criteria:
            # Multi-Hit Maker: 3 pins with ≥ 25 reactions each
            current = progress.get('pins_with_enough_reactions', 0)
            required = criteria['pins_with_reactions']
            completion_checks.append(current >= required)
        
        if 'legend_rank_reaction' in criteria:
            completion_checks.append(progress.get('legend_rank_reaction_received', False))
        
        # === COMMENT CRITERIA ===
        if 'comments_given' in criteria:
            current = progress.get('comments_given', 0)
            required = criteria['comments_given']
            completion_checks.append(current >= required)
        
        if 'comments_received' in criteria:
            current = progress.get('comments_received', 0)
            required = criteria['comments_received']
            completion_checks.append(current >= required)
        
        if 'unique_commenters' in criteria:
            current = progress.get('unique_commenters', 0)
            required = criteria['unique_commenters']
            completion_checks.append(current >= required)
        
        # === WEEKLY CHALLENGE CRITERIA ===
        if 'weekly_challenges_completed' in criteria:
            current = progress.get('weekly_challenges_completed', 0)
            required = criteria['weekly_challenges_completed']
            completion_checks.append(current >= required)
        
        # === TIME-BASED CRITERIA ===
        if 'consecutive_days' in criteria:
            current_streak = progress.get('current_streak', 0)
            max_streak = progress.get('max_streak', 0)
            required = criteria['consecutive_days']
            completion_checks.append(max(current_streak, max_streak) >= required)
        
        if 'different_days' in criteria:
            current = progress.get('different_days', 0)
            required = criteria['different_days']
            completion_checks.append(current >= required)
        
        if 'time_range' in criteria:
            # Midnight Bop: pins between specific hours
            completion_checks.append(progress.get('time_range_met', False))
        
        if 'time_window_hours' in criteria:
            # Cross-Town Sound: 2 pins 10km+ apart in < 24h
            completion_checks.append(progress.get('time_window_met', False))
        
        if 'time_window_days' in criteria:
            # Jet-Set Sound: 2 countries within 7 days
            completion_checks.append(progress.get('time_window_met', False))
        
        if 'days_since_release' in criteria:
            # Early Adopter: pin within 7 days of release
            completion_checks.append(progress.get('early_adopter_verified', False))
        
        # === COMPLEX COMBINATIONS ===
        # Handle cases where multiple criteria must be met together
        
        # City Sampler: 3 pins on 3 different days in the same city
        if 'pins_in_city' in criteria and 'different_days' in criteria:
            pins_count = progress.get('pins_in_city', 0)
            days_count = progress.get('different_days', 0)
            completion_checks.append(
                pins_count >= criteria['pins_in_city'] and 
                days_count >= criteria['different_days']
            )
        
        # Artist Evangelist: 15 pins, same artist across ≥ 3 countries
        if 'same_artist_pins' in criteria and 'different_countries' in criteria:
            pins_count = progress.get('same_artist_pins', 0)
            countries_count = progress.get('different_countries', 0)
            completion_checks.append(
                pins_count >= criteria['same_artist_pins'] and 
                countries_count >= criteria['different_countries']
            )
        
        # Atlas Mode: 10 cities in ≥ 5 countries plus 25 total pins
        if 'different_cities' in criteria and 'min_countries' in criteria and 'total_pins' in criteria:
            cities = progress.get('different_cities', 0)
            countries = progress.get('different_countries', 0)  # Use different_countries for min_countries
            total_pins = progress.get('total_pins', 0)
            completion_checks.append(
                cities >= criteria['different_cities'] and
                countries >= criteria['min_countries'] and
                total_pins >= criteria['total_pins']
            )
        
        # Globe Trotter: 10 cities across ≥ 5 countries
        if 'different_cities' in criteria and 'min_countries' in criteria and 'total_pins' not in criteria:
            cities = progress.get('different_cities', 0)
            countries = progress.get('different_countries', 0)
            completion_checks.append(
                cities >= criteria['different_cities'] and
                countries >= criteria['min_countries']
            )
        
        # Pinfluence Peak: One pin hits 25 reactions & 50 replays within 30 days
        if 'single_pin_reactions' in criteria and 'single_pin_replays' in criteria:
            completion_checks.append(progress.get('viral_pin_achieved', False))
        
        # Social Icon: 350 reactions received + 75 unique supporters
        if 'reactions_received' in criteria and 'unique_reactors' in criteria:
            reactions = progress.get('reactions_received', 0)
            reactors = progress.get('unique_reactors', 0)
            completion_checks.append(
                reactions >= criteria['reactions_received'] and
                reactors >= criteria['unique_reactors']
            )
        
        # Consistency King: React ≥ 1/day 15 consecutive days
        if 'consecutive_days' in criteria and 'daily_reactions' in criteria:
            streak = progress.get('max_streak', 0)
            completion_checks.append(streak >= criteria['consecutive_days'])
        
        # === SPECIAL CONDITIONS ===
        if 'api_verified' in criteria:
            completion_checks.append(progress.get('api_verified', False))
        
        if 'auto_reveal' in criteria:
            # Secret challenges with auto-reveal
            completion_checks.append(progress.get('auto_revealed', False))
        
        if 'public_only' in criteria:
            # Most challenges require public pins - this is enforced during progress tracking
            pass  # No additional check needed here
        
        # === POPULATION/LOCATION SPECIFIC ===
        if 'max_population' in criteria:
            # Backroads Bopper: 3 pins in towns < 100k population
            current = progress.get('small_town_pins', 0)
            required = criteria.get('pins_count', 3)  # Default to 3 if not specified
            completion_checks.append(current >= required)
        
        if 'min_distance_km' in criteria:
            # Cross-Town Sound, Metro Mover: distance requirements
            completion_checks.append(progress.get('distance_requirement_met', False))
        
        # === NIGHT/SPECIAL TIME CRITERIA ===
        if 'night_pins' in criteria:
            current = progress.get('night_pins', 0)
            required = criteria['night_pins']
            completion_checks.append(current >= required)
        
        # === LOCATION CRITERIA ===
        if 'total_pins' in criteria:
            current = progress.get('total_pins', 0)
            required = criteria['total_pins']
            completion_checks.append(current >= required)
        
        if 'different_cities' in criteria:
            current = progress.get('different_cities', 0)
            required = criteria['different_cities']
            completion_checks.append(current >= required)
        
        if 'different_states' in criteria:
            current = progress.get('different_states', 0)
            required = criteria['different_states']
            completion_checks.append(current >= required)
        
        if 'different_countries' in criteria:
            current = progress.get('different_countries', 0)
            required = criteria['different_countries']
            completion_checks.append(current >= required)
        
        if 'different_continents' in criteria:
            current = progress.get('different_continents', 0)
            required = criteria['different_continents']
            completion_checks.append(current >= required)
        
        if 'different_districts' in criteria:
            current = progress.get('different_districts', 0)
            required = criteria['different_districts']
            completion_checks.append(current >= required)
        
        if 'pins_in_radius' in criteria:
            current = progress.get('pins_in_radius', 0)
            required = criteria['pins_in_radius']
            completion_checks.append(current >= required)
        
        if 'max_distance_km' in criteria:
            current = progress.get('max_distance_km', 0)
            required = criteria['max_distance_km']
            completion_checks.append(current >= required)
        
        if 'small_town_pins' in criteria:
            current = progress.get('small_town_pins', 0)
            required = criteria['small_town_pins']
            completion_checks.append(current >= required)
        
        if 'big_city_pins' in criteria:
            current = progress.get('big_city_pins', 0)
            required = criteria['big_city_pins']
            completion_checks.append(current >= required)
        
        if 'midnight_pins' in criteria:
            current = progress.get('midnight_pins', 0)
            required = criteria['midnight_pins']
            completion_checks.append(current >= required)
        
        if 'weekend_pins' in criteria:
            current = progress.get('weekend_pins', 0)
            required = criteria['weekend_pins']
            completion_checks.append(current >= required)
        
        # Check if ALL criteria are met
        if completion_checks and all(completion_checks):
            self.completed_at = timezone.now()
            self.save()
            return True
            
        return False
    
    def update_progress(self, new_data):
        """
        Update progress with new data
        """
        current_data = self.progress or {}
        
        # Update data
        for key, value in new_data.items():
            current_data[key] = value
        
        self.progress = current_data
        self.save()
        
        # Check completion
        self.check_completion()
