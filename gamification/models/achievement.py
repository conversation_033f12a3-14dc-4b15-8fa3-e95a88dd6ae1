from django.db import models

class Achievement(models.Model):
    TYPES = [
        ('artist', 'Artist'),
        ('genre', 'Genre'),
        ('location', 'Location'),
        ('social', 'Social'),
    ]
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    type = models.CharField(max_length=20, choices=TYPES)
    criteria = models.JSONField()
    xp_reward = models.IntegerField(default=0)
    is_secret = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    icon_data = models.CharField(max_length=50, blank=True, null=True)  # For frontend icon
    primary_color = models.CharField(max_length=7, default="#000000")  # For UI theming
    
    class Meta:
        ordering = ['id']
    
    def __str__(self):
        return self.name 