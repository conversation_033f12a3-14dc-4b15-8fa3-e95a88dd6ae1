from django.db import models
from django.utils import timezone

class User<PERSON><PERSON>enge<PERSON>rogress(models.Model):
    """
    Tracks user's progress in challenges (separate from achievements)
    """
    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='challenge_progress'
    )
    challenge_type = models.CharField(max_length=50)  # 'location', 'artist', etc.
    challenge_id = models.CharField(max_length=50)    # From frontend
    progress_data = models.JSONField(default=dict)    # Current progress
    completed_at = models.DateTimeField(null=True)
    last_updated = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('user', 'challenge_type', 'challenge_id')
        indexes = [
            models.Index(fields=['user', 'challenge_type']),
            models.Index(fields=['challenge_id']),
            models.Index(fields=['completed_at'])
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.challenge_type} - {self.challenge_id}" 