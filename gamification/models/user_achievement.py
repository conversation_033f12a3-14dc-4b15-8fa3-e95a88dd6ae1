from django.db import models
from django.utils import timezone
from .achievement import Achievement

class UserAchievement(models.Model):
    """
    Model tracking user progress in achievements and challenges
    """
    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='achievements'
    )
    achievement = models.ForeignKey(
        Achievement,
        on_delete=models.CASCADE,
        related_name='completions'
    )
    completed_at = models.DateTimeField(null=True, blank=True)
    progress = models.JSONField(default=dict, help_text="Current progress towards completion")
    last_updated = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('user', 'achievement')
        indexes = [
            models.Index(fields=['user', 'achievement']),
            models.Index(fields=['completed_at'])
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.achievement.name}"
    
    def update_progress(self, new_data):
        """
        Update progress with new data
        """
        current_data = self.progress or {}
        
        # Merge new data with existing
        for key, value in new_data.items():
            if key in current_data and isinstance(value, (int, float)):
                current_data[key] = current_data[key] + value
            else:
                current_data[key] = value
        
        self.progress = current_data
        self.save()
        
        # Check completion
        self.check_completion()
    
    def check_completion(self):
        """Check if the achievement criteria have been met"""
        if self.completed_at:
            return True
            
        criteria = self.achievement.criteria
        progress = self.progress or {}
        
        # Location-based challenges
        if self.achievement.type == 'location':
            # Basic pin counting
            if 'total_pins' in criteria:
                if progress.get('total_pins', 0) < criteria['total_pins']:
                    return False
            
            # Pins within radius
            if 'pins_in_radius' in criteria:
                if progress.get('pins_in_radius', 0) < criteria['pins_in_radius']:
                    return False
            
            # City-based challenges
            if 'pins_in_city' in criteria and 'different_days' in criteria:
                cities = progress.get('cities', {})
                if not cities:
                    return False
                
                # Check if any city has enough pins on different days
                for city_dates in cities.values():
                    unique_days = len(set(date[:10] for date in city_dates))
                    if unique_days >= criteria['different_days']:
                        break
                else:  # No city met the criteria
                    return False
            
            # Different cities
            if 'different_cities' in criteria:
                if progress.get('different_cities', 0) < criteria['different_cities']:
                    return False
            
            # Different states
            if 'different_states' in criteria:
                if progress.get('different_states', 0) < criteria['different_states']:
                    return False
            
            # Different countries
            if 'different_countries' in criteria:
                if progress.get('different_countries', 0) < criteria['different_countries']:
                    return False
            
            # Different continents
            if 'different_continents' in criteria:
                if progress.get('different_continents', 0) < criteria['different_continents']:
                    return False
            
            # Pioneer pin
            if 'no_prior_pins' in criteria:
                if not progress.get('is_pioneer'):
                    return False
        
        # Social challenges
        else:
            # Basic reaction counting
            if 'reactions_given' in criteria:
                if progress.get('reactions_given', 0) < criteria['reactions_given']:
                    return False
            
            if 'reactions_received' in criteria:
                if progress.get('reactions_received', 0) < criteria['reactions_received']:
                    return False
            
            # Unique reactors check
            if 'unique_reactors' in criteria:
                if progress.get('unique_reactors', 0) < criteria['unique_reactors']:
                    return False
            
            # Reaction streak check
            if 'consecutive_days' in criteria:
                if progress.get('max_streak', 0) < criteria['consecutive_days']:
                    return False
                    
                # Check daily reaction requirement
                if criteria.get('daily_reactions') and progress.get('reactions_given', 0) < criteria['consecutive_days']:
                    return False
            
            # Share tracking
            if 'unique_shares' in criteria:
                if progress.get('unique_shares', 0) < criteria['unique_shares']:
                    return False
            
            # Replay tracking
            if 'total_replays' in criteria:
                if progress.get('total_replays', 0) < criteria['total_replays']:
                    return False
            
            # Single pin requirements
            if 'single_pin_reactions' in criteria or 'single_pin_replays' in criteria:
                pin_reactions = progress.get('pin_reactions', {})
                
                # Check for pins meeting both reaction and replay thresholds
                required_reactions = criteria.get('single_pin_reactions', 0)
                required_replays = criteria.get('single_pin_replays', 0)
                
                qualifying_pins = sum(
                    1 for pin in pin_reactions.values()
                    if pin['reactions'] >= required_reactions and pin['replays'] >= required_replays
                )
                
                # For viral pins, also check time window
                if 'time_window_days' in criteria:
                    if not progress.get('has_viral_pin'):
                        return False
                else:
                    # For non-viral challenges, check if we have enough qualifying pins
                    pins_needed = criteria.get('pins_with_reactions', 1)  # Default to 1 if not specified
                    if qualifying_pins < pins_needed:
                        return False
            
            # Referral tracking
            if 'successful_referrals' in criteria:
                if progress.get('successful_referrals', 0) < criteria['successful_referrals']:
                    return False
            
            # Legend rank reaction check
            if 'legend_rank_reaction' in criteria:
                if not progress.get('has_legend_reaction'):
                    return False
        
        # If we've made it here, all criteria are met
        self.completed_at = timezone.now()
        self.save()
        
        return True 