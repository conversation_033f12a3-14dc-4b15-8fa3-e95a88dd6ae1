from django.utils import timezone
from ..models.challenge_progress import UserChallengeProgress

class ChallengeEngine:
    """
    Core challenge validation engine
    """
    def validate_challenge(self, user, challenge_type, challenge_id, data):
        """
        Validates if a challenge is completed based on the data
        """
        # Get or create progress record
        progress, _ = UserChallengeProgress.objects.get_or_create(
            user=user,
            challenge_type=challenge_type,
            challenge_id=challenge_id,
            defaults={'progress_data': {}}
        )
        
        # Skip if already completed
        if progress.completed_at:
            return {'status': 'already_completed'}
            
        # Update progress
        self._update_progress(progress, data)
        
        # Check completion
        is_completed = self._check_completion(progress)
        if is_completed:
            progress.completed_at = timezone.now()
            progress.save()
            
        return {
            'status': 'completed' if is_completed else 'in_progress',
            'progress': progress.progress_data
        }
    
    def _update_progress(self, progress, new_data):
        """
        Update progress with new data
        """
        current_data = progress.progress_data
        
        # Merge new data with existing
        for key, value in new_data.items():
            if key in current_data:
                if isinstance(value, (int, float)):
                    current_data[key] = current_data[key] + value
                else:
                    current_data[key] = value
            else:
                current_data[key] = value
        
        progress.progress_data = current_data
        progress.save()
    
    def _check_completion(self, progress):
        """
        Check if challenge is completed based on type
        To be implemented by specific validators
        """
        return False  # Base implementation 