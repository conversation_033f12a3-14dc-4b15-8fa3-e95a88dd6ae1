from django.db.models import Sum
from django.contrib.auth import get_user_model
from ..models import UserAchievement

User = get_user_model()

class XPCalculator:
    """
    Centralized XP calculation service that ONLY uses challenge-based XP
    from the gamification system. This replaces the fragmented XP systems.
    """
    
    @staticmethod
    def get_user_total_xp(user):
        """
        Calculate total XP for a user based ONLY on completed challenges
        from the gamification system.
        
        Args:
            user: User instance
            
        Returns:
            int: Total XP from completed challenges only
        """
        total_xp = UserAchievement.objects.filter(
            user=user,
            completed_at__isnull=False  # Only completed challenges
        ).aggregate(
            total_xp=Sum('achievement__xp_reward')
        )['total_xp'] or 0
        
        return total_xp
    
    @staticmethod
    def update_user_xp_and_level(user):
        """
        Update user's XP and level based on completed achievements.
        Triggers level-up notifications if level changed.
        
        Args:
            user: User instance
            
        Returns:
            dict: Information about XP and level changes
        """
        # Calculate new total XP from completed achievements
        new_total_xp = XPCalculator.get_user_total_xp(user)
        old_total_xp = user.total_xp
        old_level = user.current_level
        
        # Get new level information
        level_info = XPCalculator.get_user_level_info(user)
        new_level = level_info['current_level']['level']
        new_badge_name = level_info['current_level']['name']
        
        # Update user's XP and level
        user.total_xp = new_total_xp
        user.current_level = new_level
        user.save(update_fields=['total_xp', 'current_level'])
        
        # Check if level changed
        level_changed = new_level > old_level
        
        # Trigger level-up notification if needed
        if level_changed:
            try:
                from notifications.utils import notify_level_up
                notify_level_up(
                    user=user,
                    new_level=new_level,
                    badge_name=new_badge_name
                )
            except ImportError:
                pass  # Notifications app not available
        
        return {
            'xp_gained': new_total_xp - old_total_xp,
            'old_level': old_level,
            'new_level': new_level,
            'level_changed': level_changed,
            'badge_name': new_badge_name,
            'total_xp': new_total_xp
        }
    
    @staticmethod
    def get_user_xp_breakdown(user):
        """
        Get detailed XP breakdown by challenge type
        
        Args:
            user: User instance
            
        Returns:
            dict: XP breakdown by challenge category
        """
        from ..models import Achievement
        
        breakdown = {}
        
        for type_code, type_name in Achievement.TYPES:
            type_xp = UserAchievement.objects.filter(
                user=user,
                completed_at__isnull=False,
                achievement__type=type_code
            ).aggregate(
                xp=Sum('achievement__xp_reward')
            )['xp'] or 0
            
            breakdown[type_code] = {
                'name': type_name,
                'xp': type_xp
            }
        
        return breakdown
    
    @staticmethod 
    def get_user_level_info(user):
        """
        Calculate user level and badge based on challenge XP only
        
        Args:
            user: User instance
            
        Returns:
            dict: Level information including next level requirements
        """
        total_xp = XPCalculator.get_user_total_xp(user)
        
        # Level thresholds based on XP (not score conversion)
        level_thresholds = [
            {'level': 1, 'name': 'Basement Bopper', 'required_xp': 0},
            {'level': 2, 'name': 'Selector', 'required_xp': 500},
            {'level': 3, 'name': 'Tastemaker', 'required_xp': 1500},
            {'level': 4, 'name': 'Trendsetter', 'required_xp': 3500},
            {'level': 5, 'name': 'Icon', 'required_xp': 7000},
            {'level': 6, 'name': 'Architect', 'required_xp': 12000},
            {'level': 7, 'name': 'Legend', 'required_xp': 20000},
        ]
        
        # Find current level
        current_level = level_thresholds[0]
        next_level = None
        
        for i, threshold in enumerate(level_thresholds):
            if total_xp >= threshold['required_xp']:
                current_level = threshold
                if i + 1 < len(level_thresholds):
                    next_level = level_thresholds[i + 1]
            else:
                if current_level == level_thresholds[0]:
                    current_level = level_thresholds[0]
                next_level = threshold
                break
        
        # Calculate progress to next level
        if next_level:
            xp_for_current = current_level['required_xp']
            xp_for_next = next_level['required_xp']
            progress_xp = total_xp - xp_for_current
            required_xp = xp_for_next - xp_for_current
            progress_percentage = (progress_xp / required_xp) * 100 if required_xp > 0 else 100
        else:
            progress_percentage = 100
            progress_xp = 0
            required_xp = 0
        
        return {
            'total_xp': total_xp,
            'current_level': current_level,
            'next_level': next_level,
            'progress_xp': progress_xp,
            'required_xp': required_xp,
            'progress_percentage': min(100, progress_percentage)
        }
    
    @staticmethod
    def get_leaderboard_data(limit=50):
        """
        Get leaderboard based on challenge XP only
        
        Args:
            limit: Number of users to return
            
        Returns:
            list: Ordered list of users with XP data
        """
        # Get users with completed achievements
        users_with_xp = User.objects.filter(
            userachievement__completed_at__isnull=False
        ).annotate(
            calculated_xp=Sum('userachievement__achievement__xp_reward')  # Changed from total_xp to calculated_xp
        ).distinct().order_by('-calculated_xp')[:limit]
        
        leaderboard = []
        for rank, user in enumerate(users_with_xp, 1):
            level_info = XPCalculator.get_user_level_info(user)
            leaderboard.append({
                'rank': rank,
                'user_id': user.id,
                'username': user.username,
                'total_xp': level_info['total_xp'],
                'level': level_info['current_level']['level'],
                'badge_name': level_info['current_level']['name']
            })
        
        return leaderboard
    
    @staticmethod
    def update_rankings_from_challenges():
        """
        Sync the rankings system to use challenge-based XP only.
        This replaces the fragmented score system.
        """
        from rankings.models import UserRanking
        
        # Get all users with achievements
        users_with_achievements = User.objects.filter(
            userachievement__completed_at__isnull=False
        ).distinct()
        
        for user in users_with_achievements:
            total_xp = XPCalculator.get_user_total_xp(user)
            level_info = XPCalculator.get_user_level_info(user)
            
            # Update or create ranking based on challenge XP only
            ranking, created = UserRanking.objects.get_or_create(
                user=user,
                defaults={
                    'total_score': total_xp,  # Store XP directly
                    'achievement_score': total_xp,  # Store XP directly
                    'challenge_score': 0,  # Weekly challenges are now part of achievements
                    'pin_score': 0,  # Pins give XP through challenges
                    'social_score': 0,  # Social activities give XP through challenges
                    'level': level_info['current_level']['level'],
                    'xp': total_xp,
                    'badge_name': level_info['current_level']['name']
                }
            )
            
            if not created:
                # Update existing ranking to use challenge-based XP
                ranking.total_score = total_xp  # Store XP directly
                ranking.achievement_score = total_xp  # Store XP directly
                ranking.challenge_score = 0
                ranking.pin_score = 0  
                ranking.social_score = 0
                ranking.level = level_info['current_level']['level']
                ranking.xp = total_xp
                ranking.badge_name = level_info['current_level']['name']
                ranking.save() 