from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone
from asgiref.sync import async_to_sync
from .models import Achievement, UserAchievement
from pins.models import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
from votes.models import Vote
from comments.models import Comment
from .utils import check_achievement_progress
from .services.xp_calculator import XPCalculator
from .services.progress_tracker import ProgressTracker
from pins.services import GeocodingService
from .consumers import AchievementConsumer
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

@receiver(post_save, sender=UserAchievement)
def handle_achievement_completion(sender, instance, created, **kwargs):
    """
    Handle when an achievement is completed - update user XP and level
    Send WebSocket notification for real-time updates
    """
    if not created and instance.completed_at and not hasattr(instance, '_xp_updated'):
        # Mark as updated to prevent recursive calls
        instance._xp_updated = True
        
        # Update user's XP and level, trigger notifications if level changed
        result = XPCalculator.update_user_xp_and_level(instance.user)
        
        # Check if achievement has a reward skin
        if instance.achievement.reward_skin:
            _unlock_achievement_skin(instance.user, instance.achievement)
        
        # Prepare WebSocket notification data
        notification_data = {
            'type': 'achievement_completed',
            'achievement': {
                'name': instance.achievement.name,
                'description': instance.achievement.description,
                'xp_earned': instance.achievement.xp_reward,
                'icon': instance.achievement.icon_data
            },
            'xp_gained': instance.achievement.xp_reward,
            'new_total_xp': result['total_xp']
        }

        # Add level up data if level changed
        if result['level_changed']:
            notification_data.update({
                'type': 'level_up',
                'old_level': result['old_level'],
                'new_level': result['new_level'],
                'badge_name': result['badge_name']
            })

        # Send WebSocket notification
        async_to_sync(AchievementConsumer.notify_achievement)(
            instance.user.id,
            notification_data
        )

        # Log the achievement completion
        logger.info(
            f"Achievement completed: {instance.user.username} - {instance.achievement.name}"
        )
        if result['level_changed']:
            logger.info(
                f"Level up: {result['old_level']} -> {result['new_level']} "
                f"({result['badge_name']})"
            )
            
            # Check for rank-based skin unlocks
            _unlock_rank_skins_for_level(instance.user, result['new_level'])


def _unlock_achievement_skin(user, achievement):
    """Unlock a skin rewarded by completing an achievement"""
    try:
        skin = achievement.reward_skin
        if not skin:
            return
            
        # Check if user already has this skin
        user_skin, created = UserSkin.objects.get_or_create(
            user=user,
            skin=skin
        )
        
        if created:
            # Send notification about skin unlock
            try:
                from notifications.utils import notify_skin_unlocked
                notify_skin_unlocked(
                    user=user,
                    skin_name=skin.name,
                    unlock_reason=f"Completed '{achievement.name}' challenge"
                )
                logger.info(f"Unlocked achievement skin {skin.name} for {user.username}")
            except Exception as e:
                logger.error(f"Failed to send skin unlock notification: {str(e)}")
                
    except Exception as e:
        logger.error(f"Error unlocking achievement skin: {str(e)}")


def _unlock_rank_skins_for_level(user, new_level):
    """Unlock rank-based skins when user reaches appropriate level"""
    try:
        # Get rank skins that user is now eligible for
        eligible_skins = PinSkin.objects.filter(
            metadata__unlock_type='RANK',
            metadata__required_rank__lte=new_level
        )
        
        for skin in eligible_skins:
            # Check if user already has this skin
            user_skin, created = UserSkin.objects.get_or_create(
                user=user,
                skin=skin
            )
            
            if created:
                # Send notification about skin unlock
                try:
                    from notifications.utils import notify_skin_unlocked
                    notify_skin_unlocked(
                        user=user,
                        skin_name=skin.name,
                        unlock_reason=f"Reached {skin.metadata.get('rank_name', 'Rank ' + str(new_level))}"
                    )
                    logger.info(f"Unlocked rank skin {skin.name} for {user.username}")
                except Exception as e:
                    logger.error(f"Failed to send skin unlock notification: {str(e)}")
                    
    except Exception as e:
        logger.error(f"Error unlocking rank skins for level {new_level}: {str(e)}")


@receiver(post_save, sender=User)
def initialize_user_achievements(sender, instance, created, **kwargs):
    """Initialize achievement tracking for new users"""
    if created:
        # Get all non-secret achievements/challenges
        achievements = Achievement.objects.filter(is_secret=False)
        
        # Create progress records
        UserAchievement.objects.bulk_create([
            UserAchievement(
                user=instance,
                achievement=achievement,
                progress={}
            ) for achievement in achievements
        ])

@receiver(post_save, sender=Pin)
def handle_pin_creation(sender, instance, created, **kwargs):
    """Handle pin creation for location, artist, and genre challenges"""
    if created:  # Only process new pins
        user = instance.owner  # Use 'owner' not 'user' based on Pin model
        
        # Extract geographic information from coordinates
        latitude = instance.location.y
        longitude = instance.location.x
        
        # Get enriched geographic data via reverse geocoding
        geo_data = GeocodingService.reverse_geocode(
            latitude, longitude, location_name=instance.location_name
        )
        
        # Comprehensive pin data for progress tracking
        pin_data = {
            'current_pin_id': instance.id,
            'latitude': latitude,
            'longitude': longitude,
            'city': geo_data.get('city'),
            'state': geo_data.get('state'),
            'country': geo_data.get('country'),
            'continent': geo_data.get('continent'),
            'district': geo_data.get('district'),
            'population': geo_data.get('population'),
            'location_name': instance.location_name,
            'artist_id': None,  # Could be added later if needed
            'artist_name': instance.track_artist,
            'album_id': None,   # Could be added later if needed
            'album_name': instance.album,
            'genre': instance.genre,
            'service': instance.service,
            'created_at': instance.created_at,
            'time_of_day': instance.created_at.hour if instance.created_at else None
        }
        
        # Update all relevant progress types
        ProgressTracker.update_pin_progress(user, pin_data)
        
        logger.info(f"Updated gamification progress for pin {instance.id} by {user.username} in {geo_data.get('city', 'Unknown')}, {geo_data.get('country', 'Unknown')}")

# === VOTE TRACKING FOR SOCIAL CHALLENGES ===

@receiver(post_save, sender=Vote)
def handle_vote_given(sender, instance, created, **kwargs):
    """Handle when a user gives a vote (upvote/downvote) for social challenges"""
    if created:  # Only process new votes
        voter = instance.user  # User who gave the vote
        pin_owner = instance.pin.owner  # User who receives the vote
        
        # Track reactions GIVEN (for the voter)
        ProgressTracker.update_social_progress(
            voter,
            'reaction_given',
            {
                'vote_value': instance.value,
                'pin_id': instance.pin.id,
                'pin_owner_id': pin_owner.id
            }
        )
        
        # Track reactions RECEIVED (for the pin owner)
        # Get the pin owner's current level/rank for legend detection
        pin_owner_level = getattr(pin_owner, 'level', 0)
        voter_rank = 'legend' if pin_owner_level >= 7 else 'regular'
        
        ProgressTracker.update_social_progress(
            pin_owner,
            'reaction_received',
            {
                'vote_value': instance.value,
                'reactor_user_id': voter.id,
                'reactor_rank': voter_rank,
                'pin_id': instance.pin.id
            }
        )
        
        logger.info(f"Vote tracked: {voter.username} voted {instance.value} on {pin_owner.username}'s pin")

@receiver(post_delete, sender=Vote)
def handle_vote_removed(sender, instance, **kwargs):
    """Handle when a vote is removed"""
    voter = instance.user
    pin_owner = instance.pin.owner
    
    # Track vote removal for both users
    ProgressTracker.update_social_progress(
        voter,
        'reaction_removed',
        {
            'vote_value': instance.value,
            'pin_id': instance.pin.id
        }
    )
    
    ProgressTracker.update_social_progress(
        pin_owner,
        'reaction_lost',
        {
            'vote_value': instance.value,
            'reactor_user_id': voter.id,
            'pin_id': instance.pin.id
        }
    )

# === COMMENT TRACKING FOR SOCIAL CHALLENGES ===

@receiver(post_save, sender=Comment)
def handle_comment_created(sender, instance, created, **kwargs):
    """Handle when a user creates a comment for social challenges"""
    if created:  # Only process new comments
        commenter = instance.user  # User who wrote the comment
        pin_owner = instance.pin.owner  # User who owns the pin
        
        # Track comments GIVEN (for the commenter)
        ProgressTracker.update_social_progress(
            commenter,
            'comment_given',
            {
                'pin_id': instance.pin.id,
                'pin_owner_id': pin_owner.id,
                'comment_id': instance.id
            }
        )
        
        # Track comments RECEIVED (for the pin owner)
        ProgressTracker.update_social_progress(
            pin_owner,
            'comment_received',
            {
                'commenter_user_id': commenter.id,
                'pin_id': instance.pin.id,
                'comment_id': instance.id
            }
        )
        
        logger.info(f"Comment tracked: {commenter.username} commented on {pin_owner.username}'s pin")

@receiver(post_delete, sender=Comment)
def handle_comment_removed(sender, instance, **kwargs):
    """Handle when a comment is removed"""
    commenter = instance.user
    pin_owner = instance.pin.owner
    
    # Track comment removal
    ProgressTracker.update_social_progress(
        commenter,
        'comment_removed',
        {
            'pin_id': instance.pin.id,
            'comment_id': instance.id
        }
    )
    
    ProgressTracker.update_social_progress(
        pin_owner,
        'comment_lost',
        {
            'commenter_user_id': commenter.id,
            'pin_id': instance.pin.id,
            'comment_id': instance.id
        }
    )

# === INTERACTION TRACKING FOR SOCIAL CHALLENGES ===

@receiver(post_save, sender=PinInteraction)
def handle_pin_interaction(sender, instance, created, **kwargs):
    """Handle pin interactions (like, share, collect, view) for social challenges"""
    if created:  # Only process new interactions
        interactor = instance.user  # User who performed the interaction
        pin_owner = instance.pin.owner  # User who owns the pin
        
        # Handle different interaction types
        if instance.interaction_type == 'like':
            # Track likes GIVEN (for the interactor)
            ProgressTracker.update_social_progress(
                interactor,
                'reaction_given',
                {
                    'interaction_type': 'like',
                    'pin_id': instance.pin.id,
                    'pin_owner_id': pin_owner.id
                }
            )
            
            # Track likes RECEIVED (for the pin owner)
            pin_owner_level = getattr(pin_owner, 'level', 0)
            interactor_rank = 'legend' if pin_owner_level >= 7 else 'regular'
            
            ProgressTracker.update_social_progress(
                pin_owner,
                'reaction_received',
                {
                    'interaction_type': 'like',
                    'reactor_user_id': interactor.id,
                    'reactor_rank': interactor_rank,
                    'pin_id': instance.pin.id
                }
            )
        
        # Don't track other interaction types (collect, share, view) for now
        # since they may not be actively used in the frontend
        
        logger.info(f"Interaction tracked: {interactor.username} {instance.interaction_type} {pin_owner.username}'s pin")

@receiver(post_delete, sender=PinInteraction)
def handle_pin_interaction_removed(sender, instance, **kwargs):
    """Handle removal of pin interactions"""
    interactor = instance.user
    pin_owner = instance.pin.owner
    
    # Only track like removals
    if instance.interaction_type == 'like':
        ProgressTracker.update_social_progress(
            interactor,
            'reaction_removed',
            {
                'pin_id': instance.pin.id,
                'pin_owner_id': pin_owner.id
            }
        )
        
        ProgressTracker.update_social_progress(
            pin_owner,
            'reaction_lost',
            {
                'reactor_user_id': interactor.id,
                'pin_id': instance.pin.id
            }
        ) 