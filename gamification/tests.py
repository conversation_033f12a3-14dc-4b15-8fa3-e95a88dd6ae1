from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient, APITestCase
from rest_framework import status
from users.models import User
from .models import Achievement, UserAchievement
from django.utils import timezone

class GamificationTests(APITestCase):
    def setUp(self):
        """Set up test data"""
        # Clear any existing achievements and user achievements
        Achievement.objects.all().delete()
        UserAchievement.objects.all().delete()
        User.objects.all().delete()
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test achievements for each category
        self.artist_achievement = Achievement.objects.create(
            name='Artist Pioneer',
            description='Add songs from 5 different artists',
            type='artist',
            criteria={'required_count': 5},
            xp_reward=100,
            is_secret=False,
            primary_color='#9C27B0'
        )
        
        self.genre_achievement = Achievement.objects.create(
            name='Genre Explorer',
            description='Add songs from 3 different genres',
            type='genre',
            criteria={'required_count': 3},
            xp_reward=150,
            is_secret=False,
            primary_color='#2196F3'
        )
        
        self.location_achievement = Achievement.objects.create(
            name='City Mapper',
            description='Add pins in 3 different cities',
            type='location',
            criteria={'required_count': 3},
            xp_reward=200,
            is_secret=False,
            primary_color='#4CAF50'
        )
        
        self.social_achievement = Achievement.objects.create(
            name='Social Butterfly',
            description='Make 5 friends',
            type='social',
            criteria={'required_count': 5},
            xp_reward=250,
            is_secret=False,
            primary_color='#F44336'
        )
        
        # Create some user achievements
        self.user_achievements = {
            'artist': UserAchievement.objects.create(
                user=self.user,
                achievement=self.artist_achievement,
                progress={'current_count': 2}
            ),
            'genre': UserAchievement.objects.create(
                user=self.user,
                achievement=self.genre_achievement,
                progress={'current_count': 3}
            ),
            'location': UserAchievement.objects.create(
                user=self.user,
                achievement=self.location_achievement,
                progress={'current_count': 1}
            ),
            'social': UserAchievement.objects.create(
                user=self.user,
                achievement=self.social_achievement,
                progress={'current_count': 4}
            )
        }
        
        # Complete the genre achievement
        self.user_achievements['genre'].completed_at = timezone.now()
        self.user_achievements['genre'].save()
        
        # Set up the client
        self.client.force_authenticate(user=self.user)
        
        # Base URLs
        self.achievements_url = reverse('gamification:achievements-list')
    
    def test_list_all_achievements(self):
        """Test listing all achievements"""
        response = self.client.get(self.achievements_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 4)
    
    def test_filter_by_type_parameter(self):
        """Test filtering achievements using type query parameter"""
        # Test each category
        categories = ['artist', 'genre', 'location', 'social']
        for category in categories:
            response = self.client.get(f"{self.achievements_url}?type={category}")
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(len(response.data), 1)
            self.assertEqual(response.data[0]['type'], category)
    
    def test_category_specific_endpoints(self):
        """Test dedicated category endpoints"""
        # Test each category endpoint
        categories = ['artist', 'genre', 'location', 'social']
        for category in categories:
            response = self.client.get(f"{self.achievements_url}{category}/")
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(len(response.data), 1)
            self.assertEqual(response.data[0]['type'], category)
    
    def test_by_category_endpoint(self):
        """Test the by-category endpoint that groups achievements"""
        response = self.client.get(f"{self.achievements_url}by-category/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that all categories are present
        categories = ['artist', 'genre', 'location', 'social']
        for category in categories:
            self.assertIn(category, response.data)
            self.assertEqual(len(response.data[category]['achievements']), 1)
            self.assertEqual(response.data[category]['achievements'][0]['type'], category)
    
    def test_achievement_progress(self):
        """Test that achievement progress is correctly included"""
        response = self.client.get(self.achievements_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check progress for each achievement
        for achievement in response.data:
            user_achievement = self.user_achievements[achievement['type']]
            progress = achievement['progress']
            
            self.assertIn('current_count', progress)
            self.assertIn('progress_percentage', progress)
            
            # Verify progress values
            self.assertEqual(
                progress['current_count'],
                user_achievement.progress['current_count']
            )
    
    def test_completed_achievements(self):
        """Test filtering completed achievements"""
        response = self.client.get(f"{self.achievements_url}completed/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Only genre achievement should be completed
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['type'], 'genre')
    
    def test_in_progress_achievements(self):
        """Test filtering in-progress achievements"""
        response = self.client.get(f"{self.achievements_url}in_progress/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # All except genre achievement should be in progress
        self.assertEqual(len(response.data), 3)
        achievement_types = [a['type'] for a in response.data]
        self.assertIn('artist', achievement_types)
        self.assertIn('location', achievement_types)
        self.assertIn('social', achievement_types)
        self.assertNotIn('genre', achievement_types)
