from django.test import TestCase
from django.utils import timezone
from datetime import datetime, timedelta
from users.models import User
from gamification.models import Achievement, UserAchievement
from gamification.services.progress_tracker import ProgressTracker


class ComplexChallengeTests(TestCase):
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='challenger',
            email='<EMAIL>',
            password='password123'
        )
        
        # Create test challenges with complex criteria
        self.location_challenge = Achievement.objects.create(
            name='World Tour',
            description='≥ 1 city on every continent except Antarctica',
            type='location',
            challenge_id='world_tour',
            tier='T7',
            xp_reward=1000,
            criteria={
                'continents_required': ['NA', 'SA', 'EU', 'AF', 'AS', 'OC'],
                'public_only': True
            }
        )
        
        self.artist_challenge = Achievement.objects.create(
            name='Artist Evangelist',
            description='15 pins, same artist across ≥ 3 countries',
            type='artist',
            challenge_id='artist_evangelist',
            tier='T6',
            xp_reward=600,
            criteria={
                'same_artist_pins': 15,
                'different_countries': 3
            }
        )
        
        self.genre_challenge = Achievement.objects.create(
            name='Genre Hopper',
            description='5 artists in 5 genres',
            type='artist',
            challenge_id='genre_hopper',
            tier='T4',
            xp_reward=325,
            criteria={
                'artists_per_genre': 5,
                'unique_genres': 5
            }
        )
        
        self.time_challenge = Achievement.objects.create(
            name='Midnight Bop',
            description='1 pin between 00:00–03:00 local',
            type='location',
            challenge_id='after_hours',
            tier='T1',
            xp_reward=75,
            criteria={
                'time_range': {'start': '00:00', 'end': '03:00'},
                'pins_count': 1,
                'public_only': True
            }
        )
        
        self.social_challenge = Achievement.objects.create(
            name='Social Icon',
            description='350 reactions received + 75 unique supporters',
            type='social',
            challenge_id='react_350_received',
            tier='T7',
            xp_reward=800,
            criteria={
                'reactions_received': 350,
                'unique_reactors': 75
            }
        )
        
        self.complex_location_challenge = Achievement.objects.create(
            name='Atlas Mode',
            description='10 cities in ≥ 5 countries plus 25 total pins',
            type='location',
            challenge_id='atlas_mode',
            tier='T7',
            xp_reward=800,
            criteria={
                'different_cities': 10,
                'min_countries': 5,
                'total_pins': 25,
                'public_only': True
            }
        )
    
    def test_geographic_continent_completion(self):
        """Test World Tour challenge with continent requirements"""
        # Create pin data for each required continent
        continents = ['NA', 'SA', 'EU', 'AF', 'AS', 'OC']
        
        for i, continent in enumerate(continents):
            pin_data = {
                'latitude': 40.7128 + i,
                'longitude': -74.0060 + i,
                'city': f'City{i}',
                'country': f'Country{i}',
                'continent': continent,
                'artist_name': 'Test Artist',
                'genre': 'pop',
                'is_public': True
            }
            ProgressTracker.update_pin_progress(self.user, pin_data)
        
        # Check completion
        user_achievement = UserAchievement.objects.get(
            user=self.user,
            achievement=self.location_challenge
        )
        
        self.assertIsNotNone(user_achievement.completed_at)
        self.assertEqual(user_achievement.progress['different_continents'], 6)
        self.assertEqual(set(user_achievement.progress['visited_continents']), set(continents))
    
    def test_artist_evangelist_complex_criteria(self):
        """Test Artist Evangelist: 15 pins, same artist across 3+ countries"""
        # Create 15 pins of the same artist across 3 countries
        countries = ['USA', 'Canada', 'Mexico']
        artist_name = 'Taylor Swift'
        
        for i in range(15):
            country = countries[i % 3]  # Cycle through countries
            pin_data = {
                'latitude': 40.0 + i,
                'longitude': -74.0 + i,
                'city': f'City{i}',
                'country': country,
                'continent': 'NA',
                'artist_name': artist_name,
                'genre': 'pop',
                'is_public': True
            }
            ProgressTracker.update_pin_progress(self.user, pin_data)
        
        user_achievement = UserAchievement.objects.get(
            user=self.user,
            achievement=self.artist_challenge
        )
        
        self.assertIsNotNone(user_achievement.completed_at)
        self.assertEqual(user_achievement.progress['same_artist_pins'], 15)
        self.assertEqual(user_achievement.progress['different_countries'], 3)
    
    def test_genre_hopper_artists_per_genre(self):
        """Test Genre Hopper: 5 artists in 5 genres"""
        genres = ['pop', 'rock', 'jazz', 'classical', 'hip-hop']
        
        for genre in genres:
            # Add 5 different artists for each genre
            for i in range(5):
                pin_data = {
                    'latitude': 40.0,
                    'longitude': -74.0,
                    'city': 'Test City',
                    'country': 'USA',
                    'continent': 'NA',
                    'artist_name': f'{genre}_artist_{i}',
                    'genre': genre,
                    'is_public': True
                }
                ProgressTracker.update_pin_progress(self.user, pin_data)
        
        user_achievement = UserAchievement.objects.get(
            user=self.user,
            achievement=self.genre_challenge
        )
        
        self.assertIsNotNone(user_achievement.completed_at)
        self.assertEqual(user_achievement.progress['genres_with_enough_artists'], 5)
        self.assertEqual(user_achievement.progress['unique_genres'], 5)
    
    def test_midnight_bop_time_range(self):
        """Test Midnight Bop: pin between 00:00-03:00"""
        # Create pin at 2:30 AM
        midnight_time = timezone.now().replace(hour=2, minute=30, second=0)
        
        pin_data = {
            'latitude': 40.7128,
            'longitude': -74.0060,
            'city': 'New York',
            'country': 'USA',
            'continent': 'NA',
            'artist_name': 'Night Owl',
            'genre': 'electronic',
            'created_at': midnight_time,
            'is_public': True
        }
        
        ProgressTracker.update_pin_progress(self.user, pin_data)
        
        user_achievement = UserAchievement.objects.get(
            user=self.user,
            achievement=self.time_challenge
        )
        
        self.assertIsNotNone(user_achievement.completed_at)
        self.assertTrue(user_achievement.progress['time_range_met'])
    
    def test_social_icon_dual_criteria(self):
        """Test Social Icon: 350 reactions + 75 unique supporters"""
        # Simulate receiving reactions from 75 unique users
        for reactor_id in range(1, 76):
            # Each user gives 5 reactions (75 * 5 = 375 total)
            for _ in range(5):
                ProgressTracker.update_social_progress(
                    self.user,
                    'reaction_received',
                    {'reactor_user_id': reactor_id}
                )
        
        user_achievement = UserAchievement.objects.get(
            user=self.user,
            achievement=self.social_challenge
        )
        
        self.assertIsNotNone(user_achievement.completed_at)
        self.assertEqual(user_achievement.progress['reactions_received'], 375)
        self.assertEqual(user_achievement.progress['unique_reactors'], 75)
    
    def test_atlas_mode_triple_criteria(self):
        """Test Atlas Mode: 10 cities + 5 countries + 25 total pins"""
        countries = ['USA', 'Canada', 'Mexico', 'Brazil', 'France']  # 5 countries
        cities = [
            'New York', 'Los Angeles', 'Toronto', 'Vancouver',  # 4 cities
            'Cancun', 'Mexico City', 'São Paulo', 'Rio',       # 4 cities  
            'Paris', 'Lyon'                                     # 2 cities = 10 total
        ]
        
        # Create 25 pins across 10 cities in 5 countries
        for i in range(25):
            country = countries[i % 5]
            city = cities[i % 10]
            
            pin_data = {
                'latitude': 40.0 + i * 0.1,
                'longitude': -74.0 + i * 0.1,
                'city': city,
                'country': country,
                'continent': 'NA' if country in ['USA', 'Canada', 'Mexico'] else 'SA' if country == 'Brazil' else 'EU',
                'artist_name': f'Artist{i}',
                'genre': 'pop',
                'is_public': True
            }
            ProgressTracker.update_pin_progress(self.user, pin_data)
        
        user_achievement = UserAchievement.objects.get(
            user=self.user,
            achievement=self.complex_location_challenge
        )
        
        self.assertIsNotNone(user_achievement.completed_at)
        self.assertEqual(user_achievement.progress['total_pins'], 25)
        self.assertEqual(user_achievement.progress['different_cities'], 10)
        self.assertEqual(user_achievement.progress['different_countries'], 5)
    
    def test_incomplete_complex_criteria(self):
        """Test that complex criteria don't complete when only partially met"""
        # Only meet 2 of 3 criteria for Atlas Mode (cities + countries, but not enough pins)
        countries = ['USA', 'Canada', 'Mexico', 'Brazil', 'France']
        cities = [
            'New York', 'Los Angeles', 'Toronto', 'Vancouver',
            'Cancun', 'Mexico City', 'São Paulo', 'Rio',
            'Paris', 'Lyon'
        ]
        
        # Create only 10 pins (need 25)
        for i in range(10):
            country = countries[i % 5]
            city = cities[i % 10]
            
            pin_data = {
                'latitude': 40.0 + i * 0.1,
                'longitude': -74.0 + i * 0.1,
                'city': city,
                'country': country,
                'continent': 'NA',
                'artist_name': f'Artist{i}',
                'genre': 'pop',
                'is_public': True
            }
            ProgressTracker.update_pin_progress(self.user, pin_data)
        
        user_achievement = UserAchievement.objects.get(
            user=self.user,
            achievement=self.complex_location_challenge
        )
        
        # Should not be completed because total_pins is only 10 (need 25)
        self.assertIsNone(user_achievement.completed_at)
        self.assertEqual(user_achievement.progress['total_pins'], 10)
        self.assertEqual(user_achievement.progress['different_cities'], 10)
        self.assertEqual(user_achievement.progress['different_countries'], 5)
    
    def test_progress_percentage_calculation(self):
        """Test that progress percentage calculation works with complex criteria"""
        from gamification.api.serializers import UserAchievementSerializer
        
        # Create partial progress for Artist Evangelist
        for i in range(8):  # 8 out of 15 needed pins
            pin_data = {
                'latitude': 40.0 + i,
                'longitude': -74.0 + i,
                'city': f'City{i}',
                'country': 'USA',  # Only 1 country (need 3)
                'continent': 'NA',
                'artist_name': 'Taylor Swift',
                'genre': 'pop',
                'is_public': True
            }
            ProgressTracker.update_pin_progress(self.user, pin_data)
        
        user_achievement = UserAchievement.objects.get(
            user=self.user,
            achievement=self.artist_challenge
        )
        
        serializer = UserAchievementSerializer(user_achievement)
        
        # Should not be completed yet
        self.assertIsNone(user_achievement.completed_at)
        
        # Progress should reflect partial completion
        self.assertEqual(user_achievement.progress['same_artist_pins'], 8)
        self.assertEqual(user_achievement.progress['different_countries'], 1) 