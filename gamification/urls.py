from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from .api.views import AchievementViewSet, UserAchievementViewSet

router = DefaultRouter()
router.register(r'achievements', AchievementViewSet, basename='achievements')
router.register(r'user-achievements', UserAchievementViewSet, basename='user-achievements')

# The router will automatically generate URLs for:
# /achievements/
# /achievements/featured/
# /achievements/by_category/
# /achievements/check_completions/
# /achievements/live_progress/
# /achievements/rank_badge_data/
# /achievements/quick_progress/
# /achievements/action_response/
# /user-achievements/
# /user-achievements/stats/
# /user-achievements/rank_info/
# /user-achievements/leaderboard/

urlpatterns = [
    path('', include(router.urls)),
]

app_name = 'gamification' 