from django.utils import timezone
from django.contrib.gis.measure import D
from django.contrib.gis.geos import Point
from .models import Achievement, UserAchievement
import logging

logger = logging.getLogger('bopmaps')

def check_achievement_progress(user, achievement_type, data):
    """
    Check and update progress towards achievements and challenges
    
    Args:
        user: User object
        achievement_type: Type of achievement/challenge
        data: Dictionary with relevant data for checking progress
    
    Returns:
        List of completed achievements/challenges
    """
    # Find all achievements/challenges that match this type
    achievements = Achievement.objects.filter(type=achievement_type)
    completed = []
    
    for achievement in achievements:
        # Get or create progress record
        progress, created = UserAchievement.objects.get_or_create(
            user=user,
            achievement=achievement,
            defaults={'progress': {}}
        )
        
        # Skip if already completed
        if progress.completed_at:
            continue
            
        # Process based on type
        if achievement_type == 'location':
            _process_location_challenge(progress, data)
        elif achievement_type == 'artist':
            _process_artist_challenge(progress, data)
        elif achievement_type == 'genre':
            _process_genre_challenge(progress, data)
        elif achievement_type == 'social':
            _process_social_challenge(progress, data)
        else:
            # Handle regular achievements
            _process_achievement(progress, data)
        
        # Check if completed after processing
        if progress.completed_at:
            completed.append(achievement)
    
    return completed

def _process_location_challenge(progress, data):
    """Process location-based challenges"""
    if not data.get('location'):
        return
        
    pin_location = Point(
        float(data['location']['longitude']), 
        float(data['location']['latitude'])
    )
    
    criteria = progress.achievement.criteria
    current = progress.progress or {}
    
    # Handle different location challenge types
    if 'pins_in_radius' in criteria:
        # Check pins within radius
        radius_km = criteria['radius_km']
        required_pins = criteria['pins_in_radius']
        
        # Count pins within radius
        pins_in_radius = data.get('pins_in_radius', 0)
        current['pins_in_radius'] = current.get('pins_in_radius', 0) + pins_in_radius
        
    elif 'different_cities' in criteria:
        # Track different cities
        city = data.get('city')
        if city:
            cities = current.get('cities', [])
            if city not in cities:
                cities.append(city)
                current['cities'] = cities
                current['different_cities'] = len(cities)
    
    # Update progress
    progress.progress = current
    progress.save()
    progress.check_completion()

def _process_artist_challenge(progress, data):
    """Process artist-based challenges"""
    if not data.get('artist'):
        return
        
    artist = data['artist']
    criteria = progress.achievement.criteria
    current = progress.progress or {}
    
    if 'unique_artists' in criteria:
        # Track unique artists
        artists = current.get('artists', [])
        if artist not in artists:
            artists.append(artist)
            current['artists'] = artists
            current['unique_artists'] = len(artists)
    
    elif 'same_artist_pins' in criteria:
        # Track pins from same artist
        if artist == criteria.get('target_artist'):
            current['same_artist_pins'] = current.get('same_artist_pins', 0) + 1
    
    # Update progress
    progress.progress = current
    progress.save()
    progress.check_completion()

def _process_genre_challenge(progress, data):
    """Process genre-based challenges"""
    if not data.get('genre'):
        return
        
    genre = data['genre']
    criteria = progress.achievement.criteria
    current = progress.progress or {}
    
    if 'unique_genres' in criteria:
        # Track unique genres
        genres = current.get('genres', [])
        if genre not in genres:
            genres.append(genre)
            current['genres'] = genres
            current['unique_genres'] = len(genres)
    
    elif 'same_genre_pins' in criteria:
        # Track pins from same genre
        if genre == criteria.get('target_genre'):
            current['same_genre_pins'] = current.get('same_genre_pins', 0) + 1
    
    # Update progress
    progress.progress = current
    progress.save()
    progress.check_completion()

def _process_social_challenge(progress, data):
    """Process social interaction challenges"""
    criteria = progress.achievement.criteria
    current = progress.progress or {}
    
    if 'reactions_received' in criteria:
        # Track reactions received
        reactions = data.get('reactions', 0)
        current['reactions_received'] = current.get('reactions_received', 0) + reactions
    
    elif 'unique_reactors' in criteria:
        # Track unique users who reacted
        reactor_id = data.get('reactor_id')
        if reactor_id:
            reactors = current.get('reactors', [])
            if reactor_id not in reactors:
                reactors.append(reactor_id)
                current['reactors'] = reactors
                current['unique_reactors'] = len(reactors)
    
    # Update progress
    progress.progress = current
    progress.save()
    progress.check_completion()

def _process_achievement(progress, data):
    """Process regular achievement progress"""
    achievement_type = data.get('type')
    
    if achievement_type == 'pin_collection':
        count = data.get('count', 0)
        current = progress.progress.get('current_count', 0)
        required = progress.achievement.criteria.get('required_count', 0)
        
        # Update progress
        progress.progress['current_count'] = current + count
        
        # Check completion
        if progress.progress['current_count'] >= required:
            progress.progress['completed'] = True
            progress.completed_at = timezone.now()
            
    elif achievement_type == 'pin_count':
        count = data.get('count', 0)
        current = progress.progress.get('current_count', 0)
        required = progress.achievement.criteria.get('required_count', 0)
        
        # Update progress
        progress.progress['current_count'] = current + count
        
        # Check completion
        if progress.progress['current_count'] >= required:
            progress.progress['completed'] = True
            progress.completed_at = timezone.now()
    
    # Save progress
    progress.save() 
