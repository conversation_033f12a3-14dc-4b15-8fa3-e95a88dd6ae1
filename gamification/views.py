from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q
from django.utils import timezone
from .models import Achievement, UserAchievement
from .api.serializers import (
    AchievementSerializer,
    UserAchievementSerializer,
    UserAchievementListSerializer
)

class AchievementViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing achievements and challenges
    """
    serializer_class = AchievementSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter queryset based on type and completion status"""
        queryset = Achievement.objects.all()
        
        # Filter by type if specified
        achievement_type = self.request.query_params.get('type')
        if achievement_type:
            queryset = queryset.filter(type=achievement_type)
            
        return queryset
    
    @action(detail=False)
    def artist(self, request):
        """Get artist achievements"""
        achievements = self.get_queryset().filter(type='artist')
        serializer = self.get_serializer(achievements, many=True)
        return Response(serializer.data)
    
    @action(detail=False)
    def genre(self, request):
        """Get genre achievements"""
        achievements = self.get_queryset().filter(type='genre')
        serializer = self.get_serializer(achievements, many=True)
        return Response(serializer.data)
    
    @action(detail=False)
    def location(self, request):
        """Get location achievements"""
        achievements = self.get_queryset().filter(type='location')
        serializer = self.get_serializer(achievements, many=True)
        return Response(serializer.data)
    
    @action(detail=False)
    def social(self, request):
        """Get social achievements"""
        achievements = self.get_queryset().filter(type='social')
        serializer = self.get_serializer(achievements, many=True)
        return Response(serializer.data)
    
    @action(detail=False)
    def completed(self, request):
        """List completed achievements for the user"""
        user_achievements = UserAchievement.objects.filter(
            user=request.user,
            completed_at__isnull=False
        ).select_related('achievement')
        
        achievements = [ua.achievement for ua in user_achievements]
        serializer = self.get_serializer(achievements, many=True)
        return Response(serializer.data)
    
    @action(detail=False)
    def in_progress(self, request):
        """List in-progress achievements for the user"""
        user_achievements = UserAchievement.objects.filter(
            user=request.user,
            completed_at__isnull=True
        ).select_related('achievement')
        
        achievements = [ua.achievement for ua in user_achievements]
        serializer = self.get_serializer(achievements, many=True)
        return Response(serializer.data)
    
    @action(detail=False)
    def by_category(self, request):
        """Get achievements grouped by category"""
        categories = {}
        for type_code, type_name in Achievement.TYPES:
            achievements = self.get_queryset().filter(type=type_code)
            if achievements.exists():
                serializer = self.get_serializer(achievements, many=True)
                categories[type_code] = {
                    'name': type_name,
                    'achievements': serializer.data
                }
        
        return Response(categories)

class UserAchievementViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing user achievement progress
    """
    serializer_class = UserAchievementSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter queryset to only show user's achievements"""
        return UserAchievement.objects.filter(
            user=self.request.user
        ).select_related('achievement')
    
    @action(detail=True, methods=['post'])
    def update_progress(self, request, pk=None):
        """Update achievement progress"""
        user_achievement = self.get_object()
        
        # Update progress
        progress_data = {'current_count': request.data.get('current_count', 0)}
        user_achievement.update_progress(progress_data)
        
        serializer = self.get_serializer(user_achievement)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get achievement/challenge statistics"""
        # Get counts
        total = UserAchievement.objects.filter(user=request.user).count()
        completed = UserAchievement.objects.filter(
            user=request.user,
            completed_at__isnull=False
        ).count()
        
        # Get completion rate by type
        completion_by_type = {}
        for type_choice in Achievement.TYPES:
            type_code = type_choice[0]
            type_total = UserAchievement.objects.filter(
                user=request.user,
                achievement__type=type_code
            ).count()
            
            type_completed = UserAchievement.objects.filter(
                user=request.user,
                achievement__type=type_code,
                completed_at__isnull=False
            ).count()
            
            if type_total > 0:
                completion_rate = (type_completed / type_total) * 100
            else:
                completion_rate = 0
                
            completion_by_type[type_code] = {
                'total': type_total,
                'completed': type_completed,
                'completion_rate': round(completion_rate, 2)
            }
        
        # Get recent completions
        recent_completions = UserAchievement.objects.filter(
            user=request.user,
            completed_at__isnull=False
        ).order_by('-completed_at')[:5]
        
        recent_serializer = UserAchievementListSerializer(recent_completions, many=True)
        
        return Response({
            'total_achievements': total,
            'completed_achievements': completed,
            'completion_rate': round((completed / total * 100), 2) if total > 0 else 0,
            'completion_by_type': completion_by_type,
            'recent_completions': recent_serializer.data
        }) 
