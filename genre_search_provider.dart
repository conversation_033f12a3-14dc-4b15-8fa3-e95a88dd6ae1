/// AI-Powered, Personalized Genre Search Provider
///
/// This provider is a complete overhaul of the original genre search,
/// inspired by the architecture of `ai_search_provider.dart`. It focuses on:
/// 1.  **Dynamic Artist Discovery**: No more hardcoded lists. It finds artists
///     based on user's listening history and expands that with Last.fm.
/// 2.  **Personalization (80%)**: 80% of content comes from artists you
///     love and artists similar to them.
/// 3.  **Discovery (20%)**: 20% of content comes from curated genre
///     playlists on Spotify to help you discover new things.
/// 4.  **Infinite & Fast Scrolling**: Uses parallel fetching, caching, and
///     anti-repetition logic to create a seamless, endless experience.
/// 5.  **Superior International Support**: Properly uses market codes and
///     specialized queries to get the best results for international genres.

import 'dart:async';
import 'dart:math' as math;

import 'package:bop_maps/models/music_track.dart';
import 'package:bop_maps/providers/pin_provider.dart';
import 'package:bop_maps/providers/spotify_provider.dart';
import 'package:bop_maps/providers/user_provider.dart';
import 'package:bop_maps/services/music/lastfm_service.dart';
import 'package:bop_maps/services/music/musicbrainz_service.dart';
import 'package:bop_maps/services/music/spotify_genre_service.dart';
import 'package:bop_maps/services/music/spotify_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class GenreSearchProvider with ChangeNotifier {
  // ===== Context-aware genre validation settings =====
  // No more audio sampling - using intelligent text analysis and genre family checking

  // Services
  final SpotifyService _spotifyService = SpotifyService();
  final LastFmService _lastFmService = LastFmService();

  // State
  String? _selectedGenre;
  List<MusicTrack> _currentTracks = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _isLoadingBackground = false; // New: Background loading state
  bool _hasMore = true;
  String _statusMessage = 'Select a genre to start';

  // Pagination & Anti-Repetition Tracking
  int _currentPage = 0;
  final int _pageSize = 30; // Increased page size for a richer feed
  final Set<String> _allLoadedTrackIds = {};
  final Set<String> _usedQueries = {};
  final Set<String> _usedArtistsForRecs = {};
  DateTime? _lastResetTime;

  // Background loading control
  final Set<String> _backgroundExpandedGenres = {}; // Track which genres have been background-expanded
  Timer? _backgroundLoadingTimer;

  // 🎯 EFFICIENT ARTIST GENRE CACHING SYSTEM (Instance-based to prevent cross-contamination)
  final Map<String, List<String>> _artistGenreCache = {}; // artist -> [genres]
  final Map<String, DateTime> _artistGenreCacheTimestamps = {};
  static const Duration _artistGenreCacheExpiry = Duration(hours: 4);
  static const int _maxArtistGenreCache = 1000;
  
  // Batch processing for efficiency  
  final Set<String> _pendingArtistGenreRequests = {};
  Timer? _batchProcessTimer;
  static const Duration _batchDelay = Duration(milliseconds: 500);

  // Cache for genre-specific artists and tracks
  final Map<String, List<MusicTrack>> _genreTrackCache = {};
  final Map<String, List<String>> _genreArtistCache = {};
  final Map<String, DateTime> _cacheTimes = {};
  static const Duration _cacheExpiry = Duration(minutes: 30);

  // Cache for valid genre artists from validation (Instance-based to prevent cross-contamination)
  final Map<String, Set<String>> _validGenreArtistsCache = {};
  final Map<String, DateTime> _validArtistsCacheTimestamps = {};
  static const Duration _validArtistsCacheExpiry = Duration(hours: 2);

  // Getters
  String? get selectedGenre => _selectedGenre;
  List<MusicTrack> get currentTracks => _currentTracks;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  bool get isLoadingBackground => _isLoadingBackground; // New: Background loading getter
  bool get hasMore => _hasMore;
  String get statusMessage => _statusMessage;

  /// Load tracks for a new genre.
  Future<void> loadGenre(String genre, BuildContext context) async {
    if (_isLoading) return;

    print('🎯 [Genre] Loading new genre: $genre');
    
    // Clear previous state FIRST (before any cache checks)
    _clearStateForNewGenre();
    
    _selectedGenre = genre;
    _isLoading = true;
    _statusMessage = 'Discovering $genre music...';
    notifyListeners();

    // Check cache first
    if (_isCacheValid(genre)) {
      _currentTracks = List.from(_genreTrackCache[genre]!);
      _allLoadedTrackIds.addAll(_currentTracks.map((t) => t.id));
      _statusMessage =
          'Loaded ${_currentTracks.length} $genre tracks from cache';
      _isLoading = false;
      _hasMore = true; // Always infinite
      notifyListeners();
      print('📦 [Genre] Loaded $genre from cache.');
      return;
    }

    await _fetchTracks(genre, context);

    _isLoading = false;
    notifyListeners();
  }

  /// Load more tracks for the current genre (pagination).
  Future<void> loadMore(BuildContext context) async {
    if (_isLoadingMore || _selectedGenre == null) return;

    _isLoadingMore = true;
    notifyListeners();

    _currentPage++;
    _statusMessage = 'Loading more $_selectedGenre tracks...';

    // Periodically reset tracking to ensure "infinite" feel
    if (_currentPage > 0 && _currentPage % 4 == 0) {
      _resetAntiRepetitionTracking();
    }

    await _fetchTracks(_selectedGenre!, context, isPaginating: true);

    _isLoadingMore = false;
    notifyListeners();
  }

  /// Fetches a single page of tracks (personalized + discovery) without mutating state.
  Future<List<MusicTrack>> _fetchTracksForPage(
      String genre, BuildContext context, int page) async {
    final results = await Future.wait([
      _getPersonalizedTracksWithExpansion(genre, context, 20, page * 20),
      _getDiscoveryTracks(genre, 10, page * 10),
    ]);

    final combined = [...results[0], ...results[1]];
    combined.shuffle();

    // Basic de-duplication within this batch
    final seen = <String>{};
    return combined.where((track) {
      if (seen.contains(track.id)) return false;
      seen.add(track.id);
      return true;
    }).cast<MusicTrack>().toList();
  }

  /// The core logic for fetching and combining personalized and discovery tracks.
  Future<void> _fetchTracks(String genre, BuildContext context,
      {bool isPaginating = false}) async {
    try {
      final offset = _currentPage * _pageSize;
      final personalizedTarget = (_pageSize * 0.8).round();
      final discoveryTarget = (_pageSize * 0.2).round();

      // For initial load, get immediate tracks from validated seeds and playlists
      if (!isPaginating) {
        await _loadImmediateTracks(genre, context, personalizedTarget, discoveryTarget);
        
        // Start background expansion if not already done for this genre (skip for niche genres)
        if (!_backgroundExpandedGenres.contains(genre) && !_isNicheGenre(genre)) {
          _startBackgroundExpansion(genre, context);
        }
        return;
      }

      // For pagination - check if niche genre should use playlist-only approach
      List<MusicTrack> personalizedTracks;
      List<MusicTrack> discoveryTracks;
      
      if (_isNicheGenre(genre)) {
        print('🎯 [Genre] Pagination for niche genre $genre - using playlist-only approach');
        personalizedTracks = [];
        discoveryTracks = await _getDiscoveryTracks(genre, personalizedTarget + discoveryTarget, offset);
      } else {
        // Standard approach for mainstream genres
        final results = await Future.wait([
          _getPersonalizedTracksWithExpansion(genre, context, personalizedTarget, offset),
          _getDiscoveryTracks(genre, discoveryTarget, offset),
        ]);
        personalizedTracks = results[0];
        discoveryTracks = results[1];
      }

      print(
          '🎶 [Genre] Fetched: ${personalizedTracks.length} personalized, ${discoveryTracks.length} discovery.');

      final allNewTracks = [...personalizedTracks, ...discoveryTracks];
      allNewTracks.shuffle();

      // Filter out tracks we've already loaded in this session
      final uniqueNewTracks = allNewTracks
          .where((track) => !_allLoadedTrackIds.contains(track.id))
          .toList();

      // Further deduplicate by title/artist to avoid slight variations
      final seenTitleArtist = <String>{};
      final trulyUniqueTracks = <MusicTrack>[];
      for (final track in uniqueNewTracks) {
        final key =
            '${track.title.toLowerCase()}_${track.artist.toLowerCase()}';
        if (!seenTitleArtist.contains(key)) {
          seenTitleArtist.add(key);
          trulyUniqueTracks.add(track);
        }
      }

      if (!isPaginating && trulyUniqueTracks.isEmpty) {
        // Try alternative methods if no tracks found initially
        final alternativeTracks = await _getAlternativeTracks(genre, context, 0);
        if (alternativeTracks.isNotEmpty) {
          _currentTracks.addAll(alternativeTracks);
          _allLoadedTrackIds.addAll(alternativeTracks.map((t) => t.id));
          _statusMessage = 'Showing ${_currentTracks.length} $genre tracks';
          _hasMore = true;
        } else {
          _statusMessage = 'Could not find tracks for $genre. Try another!';
          _hasMore = true; // Keep infinite flag
        }
      } else if (isPaginating && trulyUniqueTracks.isEmpty) {
        // Try alternatives for pagination too
        final alternativeTracks = await _getAlternativeTracks(genre, context, _currentPage);
        if (alternativeTracks.isNotEmpty) {
          _currentTracks.addAll(alternativeTracks);
          _allLoadedTrackIds.addAll(alternativeTracks.map((t) => t.id));
          _statusMessage = 'Showing ${_currentTracks.length} $genre tracks';
        }
        _hasMore = true; // Always infinite
      } else {
        _currentTracks.addAll(trulyUniqueTracks);
        _allLoadedTrackIds.addAll(trulyUniqueTracks.map((t) => t.id));
        _statusMessage = 'Showing ${_currentTracks.length} $genre tracks';
        _hasMore = true; // Always infinite
      }

      // Cache the initial page load
      if (!isPaginating) {
        _genreTrackCache[genre] = List.from(_currentTracks);
        _cacheTimes[genre] = DateTime.now();
      }
    } catch (e) {
      print('❌ Error fetching tracks for $genre: $e');
      _statusMessage = 'Error loading $genre. Please try again.';
      _hasMore = true; // Keep infinite flag
    }
  }

  /// Load immediate tracks from validated seed artists and playlists (no Last.fm expansion yet)
  Future<void> _loadImmediateTracks(String genre, BuildContext context, int personalizedTarget, int discoveryTarget) async {
    print('⚡ [Genre] Loading immediate tracks for $genre...');
    
    // Check if this is a niche genre that should use playlist-only approach
    if (_isNicheGenre(genre)) {
      print('🎯 [Genre] $genre is a niche genre - using playlist-only approach');
      final playlistTracks = await _getDiscoveryTracks(genre, personalizedTarget + discoveryTarget, 0);
      
      print('⚡ [Genre] Playlist-only results: ${playlistTracks.length} tracks');
      final allNewTracks = [...playlistTracks];
      allNewTracks.shuffle();
      
      // Continue with existing logic...
      final uniqueNewTracks = allNewTracks
          .where((track) => !_allLoadedTrackIds.contains(track.id))
          .toList();

      final seenTitleArtist = <String>{};
      final trulyUniqueTracks = <MusicTrack>[];
      for (final track in uniqueNewTracks) {
        final key = '${track.title.toLowerCase()}_${track.artist.toLowerCase()}';
        if (!seenTitleArtist.contains(key)) {
          seenTitleArtist.add(key);
          trulyUniqueTracks.add(track);
        }
      }

      _currentTracks.addAll(trulyUniqueTracks);
      _allLoadedTrackIds.addAll(trulyUniqueTracks.map((t) => t.id));
      
      if (_currentTracks.isNotEmpty) {
        _statusMessage = 'Showing ${_currentTracks.length} $genre tracks (curated playlists)';
      } else {
        _statusMessage = 'Finding more $genre tracks...';
      }
      
      _hasMore = true;
      
      // Cache the initial results
      _genreTrackCache[genre] = List.from(_currentTracks);
      _cacheTimes[genre] = DateTime.now();
      
      notifyListeners();
      print('✅ [Genre] Playlist-only tracks loaded: ${_currentTracks.length}');
      return;
    }
    
    // Standard approach for mainstream genres
    final results = await Future.wait([
      _getPersonalizedTracksFromSeeds(genre, context, personalizedTarget, 0),
      _getDiscoveryTracks(genre, discoveryTarget, 0),
    ]);

    final personalizedTracks = results[0];
    final discoveryTracks = results[1];

    print('⚡ [Genre] Immediate results: ${personalizedTracks.length} personalized, ${discoveryTracks.length} discovery');

    final allNewTracks = [...personalizedTracks, ...discoveryTracks];
    allNewTracks.shuffle();

    // Filter and deduplicate
    final uniqueNewTracks = allNewTracks
        .where((track) => !_allLoadedTrackIds.contains(track.id))
        .toList();

    final seenTitleArtist = <String>{};
    final trulyUniqueTracks = <MusicTrack>[];
    for (final track in uniqueNewTracks) {
      final key = '${track.title.toLowerCase()}_${track.artist.toLowerCase()}';
      if (!seenTitleArtist.contains(key)) {
        seenTitleArtist.add(key);
        trulyUniqueTracks.add(track);
      }
    }

    // Add to current tracks
    _currentTracks.addAll(trulyUniqueTracks);
    _allLoadedTrackIds.addAll(trulyUniqueTracks.map((t) => t.id));
    
    if (_currentTracks.isNotEmpty) {
      _statusMessage = 'Showing ${_currentTracks.length} $genre tracks';
    } else {
      _statusMessage = 'Finding more $genre tracks...';
    }
    
    _hasMore = true;
    
    // Cache the initial results
    _genreTrackCache[genre] = List.from(_currentTracks);
    _cacheTimes[genre] = DateTime.now();
    
    notifyListeners();
    print('✅ [Genre] Immediate tracks loaded: ${_currentTracks.length}');
  }

  /// Start background expansion with Last.fm similar artists
  Future<void> _startBackgroundExpansion(String genre, BuildContext context) async {
    // Prevent multiple expansions for the same genre
    if (_backgroundExpandedGenres.contains(genre) || _isLoadingBackground) {
      print('⚠️ [Genre] Background expansion already running or completed for $genre');
      return;
    }
    
    print('🔄 [Genre] Starting background expansion for $genre...');
    _backgroundExpandedGenres.add(genre);
    _isLoadingBackground = true;
    notifyListeners();

    try {
      // Get seed artists that we already validated
      final seedArtists = await _getValidatedSeedArtists(genre, context);
      if (seedArtists.isEmpty) {
        print('⚠️ [Genre] No seed artists for background expansion');
        _isLoadingBackground = false;
        notifyListeners();
        return;
      }

      // Expand with Last.fm in background
      final similarArtists = await _lastFmService.getWeightedSimilarArtists(
        seedArtists: seedArtists.take(5).toList(),
        limit: 30,
      );

      if (similarArtists.isNotEmpty) {
        print('🔥 [Genre] Background expansion found ${similarArtists.length} similar artists');
        
        // Update the genre artist cache with expanded list
        final cacheKey = 'artists_$genre';
        final existingArtists = _genreArtistCache[cacheKey] ?? [];
        final expandedArtists = [...existingArtists, ...similarArtists];
        _genreArtistCache[cacheKey] = expandedArtists.toSet().toList(); // Remove duplicates
        _cacheTimes[cacheKey] = DateTime.now();

        // Load tracks from the new similar artists
        await _loadBackgroundTracks(genre, context, similarArtists);
      }
    } catch (e) {
      print('❌ [Genre] Background expansion error: $e');
    } finally {
      _isLoadingBackground = false;
      notifyListeners();
    }
  }

  /// Load additional tracks from background-discovered artists
  Future<void> _loadBackgroundTracks(String genre, BuildContext context, List<String> newArtists) async {
    try {
      // RANDOMIZATION: Shuffle new artists and select random subset
      final shuffledArtists = List<String>.from(newArtists)..shuffle();
      final selectedArtists = shuffledArtists.take(5).toList();

      final trackFutures = selectedArtists.map((artist) {
        // RANDOMIZATION: Add random offset for background artist tracks
        final random = math.Random();
        final randomOffset = random.nextInt(15); // Random offset 0-14 for background tracks
        
        final query = 'artist:"$artist"';
        return _spotifyService.searchTracks(
          query,
          limit: 3, // Fewer tracks per artist for background loading
          offset: randomOffset,
          market: _getMarketForGenre(genre),
        );
      });

      final results = await Future.wait(trackFutures);
      final newTracks = results.expand((tracks) => tracks).toList();
      
      // RANDOMIZATION: Shuffle new tracks before filtering
      newTracks.shuffle();

      if (newTracks.isNotEmpty) {
        // Filter out already loaded tracks
        final uniqueNewTracks = newTracks
            .where((track) => !_allLoadedTrackIds.contains(track.id))
            .toList();

        // Further deduplicate by title/artist
        final seenTitleArtist = _currentTracks
            .map((t) => '${t.title.toLowerCase()}_${t.artist.toLowerCase()}')
            .toSet();
        
        final trulyUniqueTracks = <MusicTrack>[];
        for (final track in uniqueNewTracks) {
          final key = '${track.title.toLowerCase()}_${track.artist.toLowerCase()}';
          if (!seenTitleArtist.contains(key)) {
            seenTitleArtist.add(key);
            trulyUniqueTracks.add(track);
          }
        }

        if (trulyUniqueTracks.isNotEmpty) {
          // RANDOMIZATION: Shuffle before adding to maintain variety
          trulyUniqueTracks.shuffle();
          
          _currentTracks.addAll(trulyUniqueTracks);
          _allLoadedTrackIds.addAll(trulyUniqueTracks.map((t) => t.id));
          _statusMessage = 'Found ${trulyUniqueTracks.length} more $genre tracks! Total: ${_currentTracks.length}';
          
          // Update cache
          _genreTrackCache[genre] = List.from(_currentTracks);
          
          notifyListeners();
          print('✅ [Genre] Added ${trulyUniqueTracks.length} background tracks');
        }
      }
    } catch (e) {
      print('❌ [Genre] Error loading background tracks: $e');
    }
  }

  /// Get personalized tracks from validated seed artists only (no Last.fm expansion)
  Future<List<MusicTrack>> _getPersonalizedTracksFromSeeds(
      String genre, BuildContext context, int limit, int offset) async {
    final seedArtists = await _getValidatedSeedArtists(genre, context);
    if (seedArtists.isEmpty) {
      print('🕵️‍♂️ [DEBUG] _getPersonalizedTracksFromSeeds: No seed artists found for $genre');
      return [];
    }

    // RANDOMIZATION: Shuffle seed artists for variety
    final shuffledSeedArtists = List<String>.from(seedArtists)..shuffle();

    // Select artists for this request, avoiding ones we just used
    final availableArtists =
        shuffledSeedArtists.where((a) => !_usedArtistsForRecs.contains(a)).toList();
    final artistsToSearch =
        (availableArtists.isNotEmpty ? availableArtists : shuffledSeedArtists)
            .take(5)
            .toList();
    _usedArtistsForRecs.addAll(artistsToSearch);
    print('🕵️‍♂️ [DEBUG] _getPersonalizedTracksFromSeeds: Using artists for search: $artistsToSearch');

    final trackFutures = artistsToSearch.map((artist) {
      // RANDOMIZATION: Add random offset for artist tracks to get variety
      final random = math.Random();
      final randomOffset = random.nextInt(10); // Random offset 0-9 for artist tracks
      
      final query = 'artist:"$artist"';
      _usedQueries.add(query);
      return _spotifyService.searchTracks(
        query,
        limit: (limit / artistsToSearch.length).ceil(),
        offset: ((offset / artistsToSearch.length).floor() + randomOffset).clamp(0, 50),
        market: _getMarketForGenre(genre),
      );
    });

    final results = await Future.wait(trackFutures);
    final allTracks = results.expand((tracks) => tracks).toList();
    
    // RANDOMIZATION: Shuffle final track list for variety
    allTracks.shuffle();
    
    print('🕵️‍♂️ [DEBUG] _getPersonalizedTracksFromSeeds: Found ${allTracks.length} tracks from seed artists');
    return allTracks;
  }

  /// Gets the 80% personalized tracks based on user's artists + similar artists (with expansion).
  Future<List<MusicTrack>> _getPersonalizedTracksWithExpansion(
      String genre, BuildContext context, int limit, int offset) async {
    final artists = await _getGenreSpecificArtists(genre, context);
    if (artists.isEmpty) {
      print(
          '🕵️‍♂️ [DEBUG] _getPersonalizedTracksWithExpansion: No artists found for personalization in $genre. Returning empty list.');
      return [];
    }

    // Select artists for this page, avoiding ones we just used
    final availableArtists =
        artists.where((a) => !_usedArtistsForRecs.contains(a)).toList();
    final artistsToSearch =
        (availableArtists.isNotEmpty ? availableArtists : artists)
            .take(5)
            .toList();
    _usedArtistsForRecs.addAll(artistsToSearch);
    print(
        '🕵️‍♂️ [DEBUG] _getPersonalizedTracksWithExpansion: Using artists for search: $artistsToSearch');

    final trackFutures = artistsToSearch.map((artist) {
      final query = 'artist:"$artist"';
      _usedQueries.add(query);
      return _spotifyService.searchTracks(
        query,
        limit: (limit / artistsToSearch.length).ceil(), // Distribute limit
        offset: (offset / artistsToSearch.length).floor(),
        market: _getMarketForGenre(genre),
      );
    });

    final results = await Future.wait(trackFutures);
    final allTracks = results.expand((tracks) => tracks).toList();
    print(
        '🕵️‍♂️ [DEBUG] _getPersonalizedTracksWithExpansion: Found ${allTracks.length} tracks from personalized search.');
    return allTracks;
  }

  /// Get validated seed artists without Last.fm expansion
  Future<List<String>> _getValidatedSeedArtists(String genre, BuildContext context) async {
    final artists = <String>{};

    // 1. PRIMARY SOURCE: Extract artists from genre-specific playlists (most accurate)
    final playlistArtists = await _getArtistsFromGenrePlaylists(genre);
    artists.addAll(playlistArtists);
    print('🎵 [Genre] Found ${playlistArtists.length} artists from playlists for $genre.');

    // 2. SECONDARY SOURCE: Get user's personal artists with STRICT validation
    final userArtists = await _getUserArtistsForGenreStrict(genre, context);
    artists.addAll(userArtists);
    print('👤 [Genre] Found ${userArtists.length} validated user artists for $genre.');

    // 3. FALLBACK: If still no artists, use Spotify search with strict validation
    if (artists.isEmpty) {
      print('🤔 [Genre] No playlist/user artists found. Using Spotify search as fallback...');
      final searchResults =
          await _spotifyService.searchArtists('genre:"$genre"', limit: 10);
      final artistNames =
          searchResults.map((a) => a['name'] as String).toList();

      final validatedSpotifyArtists =
          await _validateArtistsStrict(artistNames, genre);
      artists.addAll(validatedSpotifyArtists);
      print('✅ [Genre] Found ${validatedSpotifyArtists.length} validated artists from Spotify search.');
    }

    return artists.toList();
  }

  /// Extract artists from genre-specific playlists with frequency analysis
  Future<List<String>> _getArtistsFromGenrePlaylists(String genre) async {
    try {
      final market = _getMarketForGenre(genre);
      
      // Get genre aliases for better playlist search
      List<String> genreAliases;
      try {
        genreAliases = SpotifyGenreService.getGenreAliases(genre);
      } catch (e) {
        genreAliases = [genre];
      }
      
      if (genreAliases.isEmpty) {
        genreAliases = [genre];
      }

      // IMPROVED: More specific and targeted playlist search queries
      final playlistQueries = [
        // Primary queries - most specific
        '${genreAliases.first} playlist',
        '${genreAliases.first} music',
        
        // Secondary queries - broader but still targeted
        'best ${genreAliases.first}',
        '${genreAliases.first} hits',
        
        // Tertiary queries - alternative phrasings
        if (genreAliases.length > 1) '${genreAliases[1]} playlist',
        'top ${genreAliases.first}',
        '${genreAliases.first} essentials',
      ];

      final artistFrequency = <String, int>{};
      final processedPlaylists = <String>{};
      final validatedPlaylists = <Map<String, dynamic>>[];

      print('🔍 [Genre] Searching with ${playlistQueries.length} targeted queries for $genre');

      // Search multiple playlist queries with validation
      for (final query in playlistQueries) {
        try {
          final playlists = await _spotifyService.searchPlaylists(
            query,
            limit: 8, // Get more options for better filtering
          );

          // ROBUST VALIDATION: Filter playlists before using them
          final genreRelevantPlaylists = await _validatePlaylistsForGenre(playlists, genre, genreAliases);
          validatedPlaylists.addAll(genreRelevantPlaylists);
          
          print('🎵 [Genre] Query "$query" found ${playlists.length} playlists, ${genreRelevantPlaylists.length} validated as relevant');
        } catch (e) {
          print('⚠️ [Playlist] Error searching playlists for "$query": $e');
        }
      }

      // Remove duplicate playlists
      final uniquePlaylists = <String, Map<String, dynamic>>{};
      for (final playlist in validatedPlaylists) {
        final id = playlist['id'] as String?;
        if (id != null && !uniquePlaylists.containsKey(id)) {
          uniquePlaylists[id] = playlist;
        }
      }

      print('🎯 [Genre] Found ${uniquePlaylists.length} unique validated playlists for $genre');

      // Randomize playlist selection for variety
      final shuffledPlaylists = uniquePlaylists.values.toList()..shuffle();

      // Process validated playlists
      for (final playlist in shuffledPlaylists.take(6)) { // Use up to 6 validated playlists
        final playlistId = playlist['id'] as String?;
        final playlistName = playlist['name'] as String? ?? '';
        
        if (playlistId == null || processedPlaylists.contains(playlistId)) {
          continue;
        }
        
        processedPlaylists.add(playlistId);
        print('🎵 [Playlist] Analyzing validated playlist "$playlistName" for $genre artists');

        // RANDOMIZATION: Use random offset for playlist tracks
        final random = math.Random();
        final randomOffset = random.nextInt(20); // Random offset 0-19

        // Get tracks from playlist with random starting point
        final playlistData = await _spotifyService.getPlaylistTracks(
          playlistId,
          limit: 50, // Get substantial sample
          offset: randomOffset,
        );

        final tracks = playlistData['tracks'] as List<MusicTrack>? ?? [];
        
        // ADDITIONAL VALIDATION: Sample tracks to ensure genre relevance
        final validTracks = await _validateTracksForGenre(tracks, genre);
        final validationScore = validTracks.length / math.max(tracks.length, 1);
        
        if (validationScore < 0.3) { // If less than 30% of tracks are genre-relevant
          print('⚠️ [Playlist] Skipping playlist "$playlistName" - low genre relevance (${(validationScore * 100).round()}%)');
          continue;
        }
        
        print('✅ [Playlist] Playlist "$playlistName" validated with ${(validationScore * 100).round()}% genre relevance');
        
        // Shuffle tracks within playlist for more randomness
        final shuffledTracks = List.from(validTracks)..shuffle();
        
        // Count artist frequency from validated tracks only
        for (final track in shuffledTracks) {
          final artist = track.artist;
          if (artist.isNotEmpty) {
            artistFrequency[artist] = (artistFrequency[artist] ?? 0) + 1;
          }
        }
      }

      // Sort artists by frequency (most frequent = most authentic to genre)
      final sortedArtists = artistFrequency.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      // Take artists that appear in multiple playlists or frequently
      final authenticArtists = sortedArtists
          .where((entry) => entry.value >= 2) // Must appear at least twice
          .take(25) // Increased to 25 for more variety
          .map((entry) => entry.key)
          .toList();

      // RANDOMIZATION: Shuffle the final artist list while keeping top artists prominent
      final topArtists = authenticArtists.take(5).toList(); // Keep top 5 stable
      final remainingArtists = authenticArtists.skip(5).toList()..shuffle();
      final finalArtists = [...topArtists, ...remainingArtists].take(20).toList();

      print('🎯 [Genre] Extracted ${finalArtists.length} authentic artists from ${processedPlaylists.length} validated playlists');
      print('🎯 [Genre] Top artists: ${finalArtists.take(5).join(', ')}');
      
      return finalArtists;
    } catch (e) {
      print('❌ [Genre] Error extracting artists from playlists: $e');
      return [];
    }
  }

  /// Validate playlists for genre relevance using strict criteria
  Future<List<Map<String, dynamic>>> _validatePlaylistsForGenre(
    List<Map<String, dynamic>> playlists, 
    String genre, 
    List<String> genreAliases
  ) async {
    final valid = <Map<String, dynamic>>[];
    final genreLower = genre.toLowerCase();
    final aliasSet = genreAliases.map((e) => e.toLowerCase()).toSet();

    for (final playlist in playlists) {
      final id = playlist['id'] as String?;
      if (id == null) continue;

      final title = (playlist['name'] as String? ?? '').toLowerCase();
      final desc  = (playlist['description'] as String? ?? '').toLowerCase();
      final owner = (playlist['owner']?['display_name'] as String? ?? '').toLowerCase();
      final combinedText = '$title $desc';

      // Quick text pre-filter – reject obvious spam / emoji-only playlists
      if (!_quickTextFilter(title, combinedText, genreLower)) {
        print('❌ [Validation] "$title" rejected: quick text filter');
        continue;
      }

      // No longer sampling tracks - using text-based validation only

      bool accepted = false;
      String reason = '';

      // NEW: Context-aware genre family validation
      final genreValidation = _validatePlaylistGenreContext(combinedText, genre, genreAliases);
      
      if (genreValidation['accepted'] == true) {
        accepted = true;
        reason = genreValidation['reason'] as String;
      }

      // additional rejection for obvious artist-only lists unless artist matches genre
      if (accepted && _isArtistSpecificPlaylist(title, genre)) {
        accepted = false;
        reason = 'artist playlist';
      }

      if (accepted) {
        valid.add(playlist);
        print('✅ [Validation] "$title" validated: $reason');
      } else {
        print('❌ [Validation] "$title" rejected');
      }
    }

    return valid;
  }

  // ---------- Niche Genre Detection ----------
  
  /// Check if a genre is niche/specific and should use playlist-only approach
  bool _isNicheGenre(String genre) {
    final genreLower = genre.toLowerCase();
    
    // South African house/electronic genres (very specific regional sound)
    if (genreLower.contains('amapiano') || 
        genreLower.contains('gqom') ||
        genreLower.contains('kwaito') ||
        genreLower.contains('afro house')) {
      return true;
    }
    
    // Nigerian/West African pop genres (different from South African)
    if (genreLower.contains('afrobeats') ||
        genreLower.contains('afrobeat') ||
        genreLower.contains('afropop') ||
        genreLower.contains('nigerian')) {
      return true;
    }
    
    // Very specific electronic subgenres
    if (genreLower.contains('phonk') ||
        genreLower.contains('hyperpop') ||
        genreLower.contains('breakcore') ||
        genreLower.contains('gabber') ||
        genreLower.contains('hardstyle') ||
        genreLower.contains('psytrance') ||
        genreLower.contains('neurofunk') ||
        genreLower.contains('liquid dnb') ||
        genreLower.contains('jump up') ||
        genreLower.contains('riddim')) {
      return true;
    }
    
    // Regional drill variations (very location-specific)
    if (genreLower.contains('uk drill') ||
        genreLower.contains('chicago drill') ||
        genreLower.contains('ny drill') ||
        genreLower.contains('bronx drill')) {
      return true;
    }
    
    // Asian pop (unless user specifically listens to them)
    if (genreLower.contains('j-pop') ||
        genreLower.contains('j-rock') ||
        genreLower.contains('j-hip') ||
        genreLower.contains('k-pop') ||
        genreLower.contains('k-rock') ||
        genreLower.contains('k-hip') ||
        genreLower.contains('c-pop') ||
        genreLower.contains('chinese pop') ||
        genreLower.contains('mandarin') ||
        genreLower.contains('cantopop') ||
        genreLower.contains('bollywood')) {
      return true;
    }
    
    // Underground/scene-specific genres
    if (genreLower.contains('darksynth') ||
        genreLower.contains('synthwave') ||
        genreLower.contains('vaporwave') ||
        genreLower.contains('lo-fi') ||
        genreLower.contains('lofi') ||
        genreLower.contains('chillhop') ||
        genreLower.contains('bedroom pop') ||
        genreLower.contains('shoegaze') ||
        genreLower.contains('post-rock') ||
        genreLower.contains('math rock')) {
      return true;
    }
    
    // Latin subgenres (very region/culture specific)
    if (genreLower.contains('reggaeton') ||
        genreLower.contains('bachata') ||
        genreLower.contains('merengue') ||
        genreLower.contains('cumbia') ||
        genreLower.contains('urbano latino') ||
        genreLower.contains('trap latino')) {
      return true;
    }
    
    // Metal subgenres (very specific sounds)
    if (genreLower.contains('black metal') ||
        genreLower.contains('death metal') ||
        genreLower.contains('doom metal') ||
        genreLower.contains('sludge metal') ||
        genreLower.contains('post-metal') ||
        genreLower.contains('djent') ||
        genreLower.contains('mathcore') ||
        genreLower.contains('grindcore')) {
      return true;
    }
    
    return false;
  }
  
  /// Check if mixing different genre families is acceptable for regional contexts
  bool _isAcceptableRegionalMixing(Set<String> genreFamilies, String targetFamily) {
    // Asian music: Allow mixing pop, rock, hip_hop within Asian context
    if (genreFamilies.contains('asian')) {
      final compatibleWithAsian = {'pop', 'rock', 'hip_hop', 'electronic'};
      final nonAsianFamilies = genreFamilies.where((f) => f != 'asian').toSet();
      if (compatibleWithAsian.containsAll(nonAsianFamilies)) {
        return true;
      }
    }
    
    // Afrobeats (Nigerian pop): Allow mixing with pop, rnb_soul, hip_hop
    if (genreFamilies.contains('afrobeats')) {
      final compatibleWithAfrobeats = {'pop', 'rnb_soul', 'hip_hop'};
      final nonAfrobeatsFamilies = genreFamilies.where((f) => f != 'afrobeats').toSet();
      if (compatibleWithAfrobeats.containsAll(nonAfrobeatsFamilies)) {
        return true;
      }
    }
    
    // Amapiano (South African house): Allow mixing with electronic, house
    if (genreFamilies.contains('amapiano')) {
      final compatibleWithAmapiano = {'electronic', 'house'};
      final nonAmpianoFamilies = genreFamilies.where((f) => f != 'amapiano').toSet();
      if (compatibleWithAmapiano.containsAll(nonAmpianoFamilies)) {
        return true;
      }
    }
    
    // STRICT: Do NOT allow afrobeats + amapiano mixing - they are different genres
    if (genreFamilies.contains('afrobeats') && genreFamilies.contains('amapiano')) {
      return false; // Explicitly reject this combination
    }
    
    // Electronic music: Allow mixing with world music for dance genres
    if (genreFamilies.contains('electronic') && genreFamilies.contains('world')) {
      final onlyElectronicAndWorld = genreFamilies.difference({'electronic', 'world'}).isEmpty;
      if (onlyElectronicAndWorld) {
        return true;
      }
    }
    
    // Allow pop + rock mixing for broad international playlists
    final popRockSet = {'pop', 'rock'};
    if (genreFamilies.length == 2 && 
        genreFamilies.containsAll(popRockSet) && 
        popRockSet.containsAll(genreFamilies)) {
      return true;
    }
    
    return false;
  }

  // ---------- Context-aware Genre Validation ----------

  bool _quickTextFilter(String title, String fullText, String genreLower) {
    // Only reject if title is too short or appears to be spam/emoji-only
    if (title.replaceAll(RegExp(r'[^a-z]'), '').length < 3) return false; // too short / emoji-only
    
    // Allow through - genre validation happens in main validation method
    return true;
  }

  // Removed: _playlistGenreScore method - no longer using audio sampling validation

  /// Context-aware genre validation using genre family compatibility
  Map<String, dynamic> _validatePlaylistGenreContext(String text, String targetGenre, List<String> genreAliases) {
    print('🔍 [Context Validation] Target: $targetGenre, Text: "$text"');
    
    // Extract all genre terms from the text
    final extractedGenres = _extractGenreTermsFromText(text);
    
    if (extractedGenres.isEmpty) {
      print('❌ [Context Validation] No genres extracted, falling back to text check');
      final mentionsTarget = genreAliases.any((alias) => text.toLowerCase().contains(alias.toLowerCase()));
      return {
        'accepted': mentionsTarget,
        'reason': mentionsTarget ? 'target genre mentioned (fallback)' : 'no genre terms found'
      };
    }

    // Get the target genre family
    final targetCanonical = SpotifyGenreService.getCanonicalGenre(targetGenre);
    final targetFamily = SpotifyGenreService.getGenreFamily(targetCanonical);
    
    print('🎯 [Context Validation] Target canonical: $targetCanonical, family: $targetFamily');
    
    if (targetFamily == null) {
      // If target genre has no family, fall back to simple text check
      final mentionsTarget = genreAliases.any((alias) => text.toLowerCase().contains(alias.toLowerCase()));
      print('⚠️ [Context Validation] Target has no family, using text check: $mentionsTarget');
      return {
        'accepted': mentionsTarget,
        'reason': mentionsTarget ? 'target genre mentioned (no family)' : 'target genre not mentioned'
      };
    }

    // Check if all extracted genres belong to the same family
    final genreFamilies = <String>{};
    final genreToFamilyMap = <String, String>{};
    
    for (final genre in extractedGenres) {
      final canonical = SpotifyGenreService.getCanonicalGenre(genre);
      final family = SpotifyGenreService.getGenreFamily(canonical);
      print('🧬 [Context Validation] Genre: $genre -> $canonical -> family: $family');
      
      if (family != null) {
        genreFamilies.add(family);
        genreToFamilyMap[genre] = family;
      }
    }

    print('🔬 [Context Validation] Found families: $genreFamilies');
    print('🎪 [Context Validation] Genre->Family map: $genreToFamilyMap');

    // If only one family found, check if it matches target
    if (genreFamilies.length == 1) {
      final singleFamily = genreFamilies.first;
      if (singleFamily == targetFamily) {
        print('✅ [Context Validation] Single family match: $singleFamily');
        return {'accepted': true, 'reason': 'single genre family match ($singleFamily)'};
      } else {
        print('❌ [Context Validation] Wrong family: $singleFamily vs $targetFamily');
        return {'accepted': false, 'reason': 'wrong genre family ($singleFamily vs $targetFamily)'};
      }
    } 
    // Multiple families found - check if it's acceptable regional mixing
    else if (genreFamilies.length > 1) {
      print('🚨 [Context Validation] Multiple families detected: $genreFamilies');
      
      // SPECIAL CASE: Allow mixing within regional contexts for niche genres
      if (_isNicheGenre(targetGenre) && _isAcceptableRegionalMixing(genreFamilies, targetFamily)) {
        print('✅ [Context Validation] Acceptable regional mixing for niche genre');
        return {'accepted': true, 'reason': 'acceptable regional mixing: ${genreFamilies.join(", ")}'};
      }
      
      // Otherwise reject mixed genre playlists
      return {'accepted': false, 'reason': 'mixed genre families: ${genreFamilies.join(", ")}'};
    } 
    // No families identified - fall back to text matching
    else {
      print('⚠️ [Context Validation] No families identified, using text fallback');
      final mentionsTarget = genreAliases.any((alias) => text.toLowerCase().contains(alias.toLowerCase()));
      return {
        'accepted': mentionsTarget,
        'reason': mentionsTarget ? 'text mention fallback' : 'no genre context found'
      };
    }
  }

  /// Extract genre terms from playlist text using known genres
  List<String> _extractGenreTermsFromText(String text) {
    final textLower = text.toLowerCase()
        .replaceAll(RegExp(r'[^\w\s&+/|,-]'), ' ') // Replace special chars with spaces except separators
        .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
        .trim();
    
    final extractedGenres = <String>[];
    
    print('🔍 [Genre Extract] Analyzing text: "$textLower"');
    
    // First, check for common genre patterns with word boundaries
    final commonGenrePatterns = {
      r'\b(r\s*&\s*b|r\&b|rnb|r n b)\b': 'r-n-b',
      r'\b(hip\s*hop|hip-hop)\b': 'hip-hop', 
      r'\b(cloud\s*rap|cloud-rap)\b': 'cloud-rap',
      r'\b(underground\s*rap|underground-rap)\b': 'underground-hip-hop',
      r'\b(uk drill|uk-drill)\b': 'uk drill',
      r'\b(chicago drill|chicago-drill)\b': 'chicago drill',
      r'\b(drill)\b': 'uk drill', // Default to UK drill for generic drill
      r'\b(trap)\b': 'trap',
      r'\b(rap)\b': 'rap',
      r'\b(pop)\b': 'pop',
      r'\b(rock)\b': 'rock',
      r'\b(electronic|edm)\b': 'electronic',
      r'\b(house)\b': 'house',
      r'\b(techno)\b': 'techno',
      r'\b(jazz)\b': 'jazz',
      r'\b(blues)\b': 'blues',
      r'\b(country)\b': 'country',
      r'\b(folk)\b': 'folk',
      r'\b(reggae)\b': 'reggae',
      r'\b(metal)\b': 'metal',
      
      // Asian genres - with flexible matching
      r'\b(j-pop|j pop|jpop|japanese pop)\b': 'j-pop',
      r'\b(j-rock|j rock|jrock|japanese rock)\b': 'j-rock',
      r'\b(k-pop|k pop|kpop|korean pop)\b': 'k-pop', 
      r'\b(k-rock|k rock|krock|korean rock)\b': 'k-rock',
      r'\b(c-pop|c pop|cpop|chinese pop|mandarin pop|mandopop|cantopop)\b': 'c-pop',
      
      // South African house/electronic genres
      r'\b(amapiano)\b': 'amapiano',
      r'\b(gqom)\b': 'gqom',
      r'\b(kwaito)\b': 'kwaito',
      r'\b(afro house|afrohouse)\b': 'afro house',
      
      // Nigerian/West African pop genres (different family)
      r'\b(afrobeats|afro beats)\b': 'afrobeats',
      r'\b(afrobeat|afro beat)\b': 'afrobeat',
      r'\b(afropop|afro pop)\b': 'afropop',
      
      // Additional regional terms
      r'\b(japanese|japan)\b': 'japanese',
      r'\b(korean|korea)\b': 'korean', 
      r'\b(chinese|china|mandarin)\b': 'chinese',
      r'\b(african|africa)\b': 'african',
    };
    
    for (final entry in commonGenrePatterns.entries) {
      final regex = RegExp(entry.key, caseSensitive: false);
      if (regex.hasMatch(textLower)) {
        final canonicalGenre = entry.value;
        if (!extractedGenres.contains(canonicalGenre)) {
          extractedGenres.add(canonicalGenre);
          print('🎯 [Genre Extract] Found: ${entry.key} -> $canonicalGenre');
        }
      }
    }
    
    // Also check against canonical genres with exact matching
    for (final genre in SpotifyGenreService.canonicalGenres) {
      final genreLower = genre.toLowerCase();
      
      // Use word boundary matching for better accuracy
      final genrePattern = RegExp(r'\b' + RegExp.escape(genreLower) + r'\b');
      if (genrePattern.hasMatch(textLower)) {
        if (!extractedGenres.contains(genre)) {
          extractedGenres.add(genre);
          print('🎯 [Genre Extract] Found canonical: $genre');
        }
      }
    }
    
    print('🎯 [Genre Extract] Final extracted genres: $extractedGenres');
    return extractedGenres;
  }

  /// Check for mixed genre indicators that should immediately disqualify a playlist
  bool _containsMixedGenreIndicators(String text, String genre) {
    final genreLower = genre.toLowerCase();
    
    // Look for multiple genre mentions that aren't the target genre
    final mixedGenrePatterns = [
      // Multiple genres separated by &, x, +, /
      r'(\w+)\s*[&x+/]\s*(\w+)',
      // Lists with commas
      r'(\w+),\s*(\w+),\s*(\w+)',
      // "and" separators
      r'(\w+)\s+and\s+(\w+)',
    ];
    
    for (final pattern in mixedGenrePatterns) {
      final regex = RegExp(pattern, caseSensitive: false);
      final matches = regex.allMatches(text);
      
      for (final match in matches) {
        // Check if this is a mixed genre combination involving our target genre
        final fullMatch = match.group(0)?.toLowerCase() ?? '';
        if (fullMatch.contains(genreLower)) {
          // If our genre is mentioned with other genres, it's mixed
          final otherGenres = ['rap', 'trap', 'pop', 'edm', 'house', 'techno', 'dubstep', 'rock', 'metal', 'country', 'jazz', 'classical'];
          final conflictingGenres = otherGenres.where((g) => g != genreLower && fullMatch.contains(g));
          if (conflictingGenres.isNotEmpty) {
            return true;
          }
        }
      }
    }
    
    // Specific mixed genre red flags
    final mixedGenreRedFlags = [
      'gaming music', 'workout mix', 'party mix', 'club mix',
      'best of edm', 'electronic mix', 'bass music', 'dance mix',
      'rap & ', ' & rap', 'hip hop &', '& hip hop',
      'pop &', '& pop', 'rock &', '& rock',
      'multiple genres', 'various genres', 'mixed genres',
    ];
    
    return mixedGenreRedFlags.any((flag) => text.contains(flag));
  }

  /// Check if playlist is generic/compilation rather than genre-specific
  bool _isGenericPlaylist(String text, String genre) {
    final genericIndicators = [
      // Gaming/activity playlists
      'gaming music', 'gaming playlist', 'game music', 'gamer music',
      'workout music', 'gym music', 'fitness music', 'running music',
      
      // Broad compilation indicators
      'top hits', 'chart hits', 'billboard', 'radio hits',
      'greatest hits', 'best songs', 'all time hits',
      'compilation', 'collection', 'anthology',
      
      // Multi-genre indicators
      'various artists', 'mixed', 'assorted', 'diverse',
      'electronic music', 'dance music', 'bass music',
      'party music', 'club music', 'festival music',
      
      // Mood/activity rather than genre
      'chill vibes', 'study music', 'focus music', 'relaxing',
      'aggressive music', 'hype music', 'motivation',
      
      // Time-based compilations
      '2024 hits', '2025 hits', 'monthly', 'weekly',
      'new releases', 'latest hits', 'trending',
    ];
    
    return genericIndicators.any((indicator) => text.contains(indicator));
  }

  /// Check if playlist is artist-specific rather than genre-specific
  bool _isArtistSpecificPlaylist(String name, String genre) {
    // Common patterns for artist-specific playlists
    final artistPatterns = [
      r'\b\w+\s+essentials\b',  // "Drake Essentials", "Lee Drilly Essentials"
      r'\b\w+\s+greatest\s+hits\b',
      r'\b\w+\s+discography\b',
      r'\b\w+\s+collection\b',
      r'\bbest\s+of\s+\w+\b',
      r'\b\w+\s+playlist\b', // Only if it's clearly an artist name format
    ];
    
    for (final pattern in artistPatterns) {
      final regex = RegExp(pattern, caseSensitive: false);
      if (regex.hasMatch(name)) {
        // Additional check: if it contains the genre name, it might still be valid
        final genreLower = genre.toLowerCase();
        if (!name.toLowerCase().contains(genreLower)) {
          return true; // Artist-specific without genre mention
        }
      }
    }
    
    return false;
  }

  /// Check if playlist is a pure genre playlist
  bool _isPureGenrePlaylist(String text, String genre, Set<String> aliasesLower) {
    final genreLower = genre.toLowerCase();
    
    // Must contain the genre or its aliases
    final containsGenre = aliasesLower.any((alias) => text.contains(alias));
    if (!containsGenre) return false;
    
    // Pure genre indicators (positive signals)
    final pureGenrePatterns = [
      // Quality/curation indicators
      'best $genreLower', 'top $genreLower', '$genreLower hits',
      '$genreLower classics', '$genreLower essentials', '$genreLower bangers',
      '$genreLower music', '$genreLower playlist', '$genreLower vibes',
      
      // Geographic/style specific (still pure)
      'uk $genreLower', 'us $genreLower', 'chicago $genreLower', 'ny $genreLower',
      'underground $genreLower', 'old school $genreLower', 'new $genreLower',
      
      // Time-based but genre-specific
      '$genreLower 2024', '$genreLower 2025', 'modern $genreLower',
      
      // Quality descriptors
      'pure $genreLower', 'real $genreLower', 'authentic $genreLower',
    ];
    
    // Check for pure genre patterns
    final hasPurePattern = pureGenrePatterns.any((pattern) => text.contains(pattern));
    
    // Additional validation: check title structure
    final titleWords = text.split(RegExp(r'\s+'));
    final genreWordIndex = titleWords.indexWhere((word) => 
        aliasesLower.any((alias) => word.contains(alias)));
    
    if (genreWordIndex != -1) {
      // Genre word found, check surrounding context
      final contextWords = <String>[];
      
      // Get 2 words before and after genre word
      for (int i = math.max(0, genreWordIndex - 2); 
           i <= math.min(titleWords.length - 1, genreWordIndex + 2); 
           i++) {
        contextWords.add(titleWords[i]);
      }
      
      final context = contextWords.join(' ');
      
      // Check if context suggests pure genre focus
      final pureContextIndicators = [
        'best', 'top', 'hits', 'classics', 'essentials', 'bangers',
        'music', 'playlist', 'collection', 'vibes', 'sounds',
        'all time', 'greatest', 'ultimate', 'definitive',
      ];
      
      final hasPureContext = pureContextIndicators.any((indicator) => 
          context.contains(indicator));
      
      return hasPurePattern || hasPureContext;
    }
    
    return hasPurePattern;
  }

  /// Check if playlist is a curated genre collection by a specialist
  bool _isCuratedGenreCollection(String text, String ownerName, String genre) {
    final genreLower = genre.toLowerCase();
    
    // Check if owner is genre-specific
    final genreSpecificOwners = {
      'uk drill': ['uk drill', 'british drill', 'london drill', 'drill music', 'drill central', 'drill nation'],
      'chicago drill': ['chicago drill', 'chicago drill music', 'drill music', 'drill central', 'drill nation'],
      'reggaeton': ['reggaeton', 'urbano', 'latino', 'latin music', 'perreo', 'reggaeton nation'],
      'trap': ['trap', 'trap nation', 'trap music', 'trap city', 'trap sounds'],
      'future bass': ['future bass', 'melodic bass', 'emotional music', 'future sounds'],
      'phonk': ['phonk', 'memphis', 'drift music', 'phonk house'],
      'hyperpop': ['hyperpop', 'pc music', 'digital hardcore', 'glitchcore'],
      'k-pop': ['kpop', 'korean music', 'hallyu', 'k-pop central'],
      'j-pop': ['jpop', 'japanese music', 'anime music', 'j-music'],
      'c-pop': ['cpop', 'chinese music', 'mandopop', 'cantopop'],
    };
    
    final specialists = genreSpecificOwners[genreLower] ?? [];
    final isSpecialistOwner = specialists.any((specialist) => 
        ownerName.contains(specialist));
    
    if (isSpecialistOwner) {
      // Even specialist owners need genre-relevant titles
      return text.contains(genreLower) || 
             genreSpecificOwners[genreLower]?.any((term) => text.contains(term)) == true;
    }
    
    // Check for official/verified accounts
    final officialIndicators = ['official', 'verified', 'records', 'label', 'music'];
    final isOfficial = officialIndicators.any((indicator) => ownerName.contains(indicator));
    
    if (isOfficial && text.contains(genreLower)) {
      // Official accounts with genre in title/description
      return !_containsMixedGenreIndicators(text, genre);
    }
    
    return false;
  }



  /// Validate tracks within a playlist for genre relevance
  Future<List<MusicTrack>> _validateTracksForGenre(List<MusicTrack> tracks, String genre) async {
    final validTracks = <MusicTrack>[];
    final targetCanonical = SpotifyGenreService.getCanonicalGenre(genre);
    
    for (final track in tracks) {
      bool isValid = false;
      
      // Check track genres if available
      if (track.genres.isNotEmpty) {
        for (final trackGenre in track.genres) {
          final trackCanonical = SpotifyGenreService.getCanonicalGenre(trackGenre);
          if (SpotifyGenreService.areGenresRelated(trackCanonical, targetCanonical)) {
            isValid = true;
            break;
          }
        }
      } else {
        // If no genre data, assume valid (will be filtered later by artist validation)
        isValid = true;
      }
      
      if (isValid) {
        validTracks.add(track);
      }
    }
    
    return validTracks;
  }

  /// Get user's artists with STRICT genre validation (only exact matches)
  /// PRIORITIZES USER PROFILE DATA (90%+) over service data (10% max)
  Future<List<String>> _getUserArtistsForGenreStrict(String genre, BuildContext context) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    final pinProvider = Provider.of<PinProvider>(context, listen: false);

    // PRIMARY SOURCE: Get artists from user profile (90%+ priority)
    final profileArtists = userProvider.currentUser?.topArtists ?? [];
    print('🎵 PRIMARY: ${profileArtists.length} artists from user profile for genre validation');

    // Use profile artists as the main source
    final rawArtists = <String>{};
    rawArtists.addAll(profileArtists);

    // MINIMAL SERVICE DATA: Only add a small amount (max 10%) for variety if profile has data
    if (profileArtists.isNotEmpty) {
      final maxServiceArtists = (profileArtists.length * 0.1).ceil().clamp(1, 5);
      print('🎵 SUPPLEMENTAL: Adding max $maxServiceArtists artists from service (10% rule)');

      if (spotifyProvider.isConnected) {
        try {
          final topArtists = await _spotifyService.getTopArtists(limit: maxServiceArtists);
          final serviceArtists = topArtists.map((a) => a['name'] as String).toList();
          
          // Only add artists that aren't already in profile
          final newServiceArtists = serviceArtists
              .where((artist) => !profileArtists.contains(artist))
              .take(maxServiceArtists)
              .toList();
              
          rawArtists.addAll(newServiceArtists);
          print('🎵 Added ${newServiceArtists.length} supplemental Spotify artists');
        } catch (e) {
          print('⚠️ Error getting minimal Spotify artists: $e');
        }
      }
    } else {
      // FALLBACK: Only when profile is empty, use minimal service/pin data
      print('⚠️ No user profile artists found - using minimal fallback data');
      
      if (spotifyProvider.isConnected) {
        try {
          final topArtists = await _spotifyService.getTopArtists(limit: 10);
          rawArtists.addAll(topArtists.map((a) => a['name'] as String));
          print('🎵 FALLBACK: Using ${rawArtists.length} Spotify artists');
        } catch (e) {
          print('❌ Error loading Spotify fallback artists: $e');
          // Final fallback to pins
          rawArtists.addAll(pinProvider.pins.map((pin) => pin.trackArtist));
          print('🎵 FINAL FALLBACK: Using ${rawArtists.length} artists from pins');
        }
      } else {
        // Use artists from pins as absolute fallback
        rawArtists.addAll(pinProvider.pins.map((pin) => pin.trackArtist));
        print('🎵 FALLBACK: Using ${rawArtists.length} artists from pins');
      }
    }

    print('🎵 FINAL: ${rawArtists.length} total artists (${profileArtists.length} from profile, ${rawArtists.length - profileArtists.length} from service/pins)');
    print('🕵️‍♂️ [DEBUG] Applying strict validation for genre: $genre');
    
    if (rawArtists.isEmpty) return [];

    return await _validateArtistsStrict(rawArtists.toList(), genre);
  }

  /// STRICT artist validation - only accepts exact genre matches or direct children
  Future<List<String>> _validateArtistsStrict(List<String> artists, String genre) async {
    final validated = <String>[];

    try {
      final targetCanonical = SpotifyGenreService.getCanonicalGenre(genre);
      
      for (final artist in artists) {
        final artistGenres = await _getArtistGenres(artist);
        if (artistGenres.isEmpty) continue;

        bool isValid = false;
        
        for (final artistGenre in artistGenres) {
          final artistCanonical = SpotifyGenreService.getCanonicalGenre(artistGenre);
          
          // STRICT RULE 1: Exact canonical match
          if (artistCanonical == targetCanonical) {
            isValid = true;
            break;
          }
          
          // STRICT RULE 2: Artist genre is a child of target genre (not parent)
          // Example: drill artist is valid for "rap", but rap artist is NOT valid for "drill"
          final artistRelated = SpotifyGenreService.getRelatedGenres(artistCanonical);
          if (artistRelated.contains(targetCanonical)) {
            isValid = true;
            break;
          }
          
          // STRICT RULE 3: Check aliases for exact match
          final genreAliases = SpotifyGenreService.getGenreAliases(targetCanonical);
          if (genreAliases.any((alias) => 
              alias.toLowerCase() == artistGenre.toLowerCase())) {
            isValid = true;
            break;
          }
        }

        if (isValid) {
          validated.add(artist);
        }
      }

      print('✅ [Strict Validation] Validated ${validated.length}/${artists.length} artists for genre: $genre');
      if (validated.isNotEmpty) {
        print('✅ [Strict Validation] Valid artists: ${validated.take(5).join(', ')}${validated.length > 5 ? '...' : ''}');
      }
      
      return validated;
    } catch (e) {
      print('❌ [Strict Validation] Error validating artists: $e');
      return artists.take(5).toList(); // Conservative fallback
    }
  }

  /// Gets the 20% discovery tracks from curated genre playlists.
  Future<List<MusicTrack>> _getDiscoveryTracks(
      String genre, int limit, int offset) async {
    final market = _getMarketForGenre(genre);
    
    try {
      // Safely get genre aliases with fallback
      List<String> genreAliases;
      try {
        genreAliases = SpotifyGenreService.getGenreAliases(genre);
      } catch (e) {
        print('⚠️ No aliases found for $genre, using original name');
        genreAliases = [genre];
      }
      
      if (genreAliases.isEmpty) {
        genreAliases = [genre]; // Fallback to original genre name
      }
      
      // IMPROVED: Use more targeted search queries
      final playlistQueries = [
        '${genreAliases.first} playlist',
        '${genreAliases.first} hits',
        'best ${genreAliases.first}',
      ];

      final allValidatedPlaylists = <Map<String, dynamic>>[];

      // Search with multiple queries and validate each set
      for (final query in playlistQueries) {
        final playlists = await _spotifyService.searchPlaylists(
          query,
          limit: 6, // Get more options for validation
          offset: _currentPage,
        );
        
        // ROBUST VALIDATION: Use the same validation system
        final validatedPlaylists = await _validatePlaylistsForGenre(playlists, genre, genreAliases);
        allValidatedPlaylists.addAll(validatedPlaylists);
      }

      // Remove duplicates
      final uniquePlaylists = <String, Map<String, dynamic>>{};
      for (final playlist in allValidatedPlaylists) {
        final id = playlist['id'] as String?;
        if (id != null && !uniquePlaylists.containsKey(id)) {
          uniquePlaylists[id] = playlist;
      }
      }

      if (uniquePlaylists.isEmpty) {
        print('⚠️ [Discovery] No validated playlists found for $genre');
        return [];
      }

      // RANDOMIZATION: Shuffle playlists and pick random ones for variety
      final shuffledPlaylists = uniquePlaylists.values.toList()..shuffle();
      final selectedPlaylists = shuffledPlaylists.take(3).toList(); // Use 3 random validated playlists

      final allTracks = <MusicTrack>[];
      
      for (final playlist in selectedPlaylists) {
      final playlistId = playlist['id'] as String?;
        final playlistName = playlist['name'] as String? ?? '';
        if (playlistId == null) continue;

        print('🎵 [Discovery] Using validated playlist "$playlistName"');

        // RANDOMIZATION: Use random offset for each playlist
        final random = math.Random();
        final randomOffset = random.nextInt(30); // Random offset 0-29

      final playlistData = await _spotifyService.getPlaylistTracks(
        playlistId,
          limit: (limit * 2 / selectedPlaylists.length).ceil(), // Distribute limit across playlists
          offset: randomOffset,
      );

      final tracks = playlistData['tracks'] as List<MusicTrack>? ?? [];
        
        // ADDITIONAL VALIDATION: Validate tracks for genre relevance
        final validTracks = await _validateTracksForGenre(tracks, genre);
        final validationScore = validTracks.length / math.max(tracks.length, 1);
        
        print('✅ [Discovery] Playlist "$playlistName" track validation: ${(validationScore * 100).round()}% relevant');
        
        allTracks.addAll(validTracks); // Use only validated tracks
      }

      print('🕵️‍♂️ [DEBUG] _getDiscoveryTracks: Found ${allTracks.length} validated tracks from ${selectedPlaylists.length} playlists.');

      // RANDOMIZATION: Shuffle validated tracks for final variety
      final shuffledValidated = List<MusicTrack>.from(allTracks)..shuffle();

      print('🕵️‍♂️ [DEBUG] _getDiscoveryTracks: Returning ${shuffledValidated.length} tracks after validation.');
      return shuffledValidated.take(limit).toList();
    } catch (e) {
      print('❌ Error getting discovery tracks for $genre: $e');
      return [];
    }
  }

  /// 🎯 EFFICIENT BATCH ARTIST GENRE VALIDATION
  /// Get cached artist genres, batching requests for efficiency
  Future<List<String>> _getArtistGenres(String artistName) async {
    final artistKey = artistName.toLowerCase().trim();
    final now = DateTime.now();
    
    // Check cache first
    if (_artistGenreCache.containsKey(artistKey) &&
        _artistGenreCacheTimestamps.containsKey(artistKey)) {
      final cacheTime = _artistGenreCacheTimestamps[artistKey]!;
      if (now.difference(cacheTime) < _artistGenreCacheExpiry) {
        return _artistGenreCache[artistKey]!;
      }
    }
    
    // Add to batch request queue
    _pendingArtistGenreRequests.add(artistKey);
    
    // Start batch timer if not already running
    _batchProcessTimer?.cancel();
    _batchProcessTimer = Timer(_batchDelay, _processBatchArtistGenreRequests);
    
    // For now, return cached value or empty list
    return _artistGenreCache[artistKey] ?? [];
  }

  /// Process batch requests for artist genres
  Future<void> _processBatchArtistGenreRequests() async {
    if (_pendingArtistGenreRequests.isEmpty) return;
    
    final artistsToFetch = _pendingArtistGenreRequests.toList();
    _pendingArtistGenreRequests.clear();
    
    try {
      print('🎯 [Batch Genre] Fetching genres for ${artistsToFetch.length} artists');
      
      // Batch search for artists using individual searches in parallel
      final artistGenreFutures = artistsToFetch.map((artistName) async {
        try {
          final searchResults = await _spotifyService.searchArtists(
            'artist:"$artistName"',
            limit: 1,
          );
          
          if (searchResults.isNotEmpty) {
            final artistData = searchResults.first;
            return {
              'name': artistName,
              'genres': artistData['genres'] ?? [],
            };
          }
          
          return {'name': artistName, 'genres': <String>[]};
        } catch (e) {
          print('❌ [Batch Genre] Error fetching genres for $artistName: $e');
          return {'name': artistName, 'genres': <String>[]};
        }
      }).toList();
      
      final artistGenres = await Future.wait(artistGenreFutures);
      
      final now = DateTime.now();
      for (final artistData in artistGenres) {
        final artistName = (artistData['name'] as String).toLowerCase().trim();
        final genres = List<String>.from(artistData['genres'] ?? []);
        
        _artistGenreCache[artistName] = genres;
        _artistGenreCacheTimestamps[artistName] = now;
      }
      
      // Cleanup cache if too large
      if (_artistGenreCache.length > _maxArtistGenreCache) {
        _cleanupArtistGenreCache();
      }
      
      print('✅ [Batch Genre] Cached genres for ${artistGenres.length} artists');
    } catch (e) {
      print('❌ [Batch Genre] Error fetching artist genres: $e');
    }
  }

  /// Clean up old entries from artist genre cache
  void _cleanupArtistGenreCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _artistGenreCacheTimestamps.entries) {
      if (now.difference(entry.value) >= _artistGenreCacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }
    
    // Remove expired entries
    for (final key in expiredKeys) {
      _artistGenreCache.remove(key);
      _artistGenreCacheTimestamps.remove(key);
    }
    
    // If still too large, remove oldest entries
    if (_artistGenreCache.length > _maxArtistGenreCache) {
      final sortedEntries = _artistGenreCacheTimestamps.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));
      
      final toRemove = sortedEntries.length - (_maxArtistGenreCache ~/ 2);
      for (int i = 0; i < toRemove; i++) {
        final key = sortedEntries[i].key;
        _artistGenreCache.remove(key);
        _artistGenreCacheTimestamps.remove(key);
      }
    }
  }

  // --- Utility and State Management Methods ---

  void _clearStateForNewGenre() {
    print('🧹 [Genre] Clearing state for new genre');
    
    // Clear track state
    _currentTracks.clear();
    _allLoadedTrackIds.clear();
    _usedQueries.clear();
    _usedArtistsForRecs.clear();
    _currentPage = 0;
    _hasMore = true;
    _lastResetTime = DateTime.now();
    
    // Clear background loading state
    _isLoadingBackground = false;
    _backgroundLoadingTimer?.cancel();
    
    // Clear background expansion tracking (force fresh expansion for new genre)
    _backgroundExpandedGenres.clear();
    
    // Clear loading states
    _isLoadingMore = false;
    
    print('✅ [Genre] State cleared successfully');
  }

  bool _isCacheValid(String key) {
    final cacheTime = _cacheTimes[key];
    if (cacheTime == null) return false;
    return DateTime.now().difference(cacheTime) < _cacheExpiry;
  }

  void _resetAntiRepetitionTracking() {
    final now = DateTime.now();
    if (_lastResetTime == null ||
        now.difference(_lastResetTime!).inMinutes > 15) {
      print('🔄 [Genre] Resetting anti-repetition tracking.');
      _usedQueries.clear();
      _usedArtistsForRecs.clear();
      _lastResetTime = now;
      // Note: Removed clearSearchCache() to prevent interference with AI search provider
    }
  }

  /// Check if a genre is highly specific (like J-pop, K-pop) and should prioritize discovery
  bool _isSpecificInternationalGenre(String genre) {
    final genreLower = genre.toLowerCase();
    
    return genreLower.contains('j-pop') ||
           genreLower.contains('j-rock') ||
           genreLower.contains('japanese') ||
           genreLower.contains('k-pop') ||
           genreLower.contains('korean') ||
           genreLower.contains('c-pop') ||
           genreLower.contains('chinese') ||
           genreLower.contains('gufeng') ||
           genreLower.contains('mandopop') ||
           genreLower.contains('cantopop') ||
           genreLower.contains('bollywood') ||
           genreLower.contains('indian') ||
           genreLower.contains('brazilian') ||
           genreLower.contains('samba') ||
           genreLower.contains('reggaeton') ||
           genreLower.contains('latin');
  }

  String? _getMarketForGenre(String genre) {
    final genreLower = genre.toLowerCase();

    if (genreLower.contains('c-pop') ||
        genreLower.contains('chinese') ||
        genreLower.contains('gufeng') ||
        genreLower.contains('mandopop') ||
        genreLower.contains('cantopop')) return 'HK';
    if (genreLower.contains('k-pop') || genreLower.contains('korean'))
      return 'KR';
    if (genreLower.contains('j-pop') ||
        genreLower.contains('j-rock') ||
        genreLower.contains('japanese')) return 'JP';
    if (genreLower.contains('latin') || genreLower.contains('reggaeton'))
      return 'MX';
    if (genreLower.contains('brazilian') || genreLower.contains('samba'))
      return 'BR';
    if (genreLower.contains('indian') || genreLower.contains('bollywood'))
      return 'IN';

    return 'US'; // Default fallback
  }

  void clearAll() {
    print('🧹 [Genre] Clearing all provider state.');
    _selectedGenre = null;
    _currentTracks.clear();
    _allLoadedTrackIds.clear();
    _usedQueries.clear();
    _usedArtistsForRecs.clear();
    _currentPage = 0;
    _isLoading = false;
    _isLoadingMore = false;
    _isLoadingBackground = false;
    _hasMore = true;
    _statusMessage = 'Select a genre to start';

    _genreTrackCache.clear();
    _genreArtistCache.clear();
    _cacheTimes.clear();
    _backgroundExpandedGenres.clear();
    _backgroundLoadingTimer?.cancel();
    
    // Clear instance-based caches to prevent cross-contamination
    _artistGenreCache.clear();
    _artistGenreCacheTimestamps.clear();
    _validGenreArtistsCache.clear();
    _validArtistsCacheTimestamps.clear();

    notifyListeners();
  }

  @override
  void dispose() {
    _backgroundLoadingTimer?.cancel();
    clearAll();
    super.dispose();
  }

  /// Fallback method: Direct search for genre tracks
  Future<List<MusicTrack>> _tryDirectGenreSearch(String genre) async {
    try {
      // Try searching for tracks with the genre directly
      final tracks = await _spotifyService.searchTracks(
        'genre:"$genre"',
        limit: 20,
      );
      
      if (tracks.isNotEmpty) {
        print('🎯 [Fallback] Found ${tracks.length} tracks with direct genre search');
        return tracks;
      }

      // If that fails, try searching for the genre name as a query
      final queryTracks = await _spotifyService.searchTracks(
        '$genre music',
        limit: 20,
      );
      
      if (queryTracks.isNotEmpty) {
        print('🎯 [Fallback] Found ${queryTracks.length} tracks with genre query search');
        return queryTracks;
      }

      return [];
    } catch (e) {
      print('❌ [Fallback] Direct genre search failed: $e');
      return [];
    }
  }

  /// Generate helpful error message for failed genre loading
  String _getHelpfulErrorMessage(String genre, String? error) {
    final suggestions = <String>[];
    
    // Check if it's a specific international genre
    if (_isSpecificInternationalGenre(genre)) {
      suggestions.add('Try connecting to Spotify for better international music access');
    }
    
    // Check if it might be a typo or uncommon genre
    if (genre.length < 3) {
      suggestions.add('Try a more specific genre name');
    }
    
    // Check for common misspellings or variations
    final commonGenres = ['pop', 'rock', 'hip-hop', 'electronic', 'jazz', 'blues', 'country', 'folk', 'metal'];
    final similarGenre = commonGenres.firstWhere(
      (g) => g.toLowerCase().contains(genre.toLowerCase()) || genre.toLowerCase().contains(g.toLowerCase()),
      orElse: () => '',
    );
    
    if (similarGenre.isNotEmpty && similarGenre != genre) {
      suggestions.add('Did you mean "$similarGenre"?');
    }
    
    if (suggestions.isEmpty) {
      suggestions.add('Try a different genre or check your internet connection');
    }
    
    return 'No $genre tracks found. ${suggestions.first}';
  }

  /// Strategy 2: Alternative track loading methods
  Future<List<MusicTrack>> _getAlternativeTracks(String genre, BuildContext context, int page) async {
    final allTracks = <MusicTrack>[];
    
    try {
      // Alternative 1: Search with different page offsets
      final offsetTracks = await _getDiscoveryTracks(genre, 10, (page + 5) * 10);
      allTracks.addAll(offsetTracks);
      
      // Alternative 2: Use broader search terms
      final broadTracks = await _getBroadGenreTracks(genre, 10);
      allTracks.addAll(broadTracks);
      
      // Alternative 3: Related genre tracks
      final relatedTracks = await _getRelatedGenreTracks(genre, 10);
      allTracks.addAll(relatedTracks);
      
      print('🔄 [Alternative Tracks] Got ${allTracks.length} tracks from alternative methods');
      return allTracks;
    } catch (e) {
      print('❌ [Alternative Tracks] Error: $e');
      return [];
    }
  }

  /// Strategy 3: Get tracks after resetting restrictions
  Future<List<MusicTrack>> _getTracksAfterReset(String genre, BuildContext context) async {
    try {
      // After reset, try the primary methods again with fresh state
      final resetTracks = await _getDiscoveryTracks(genre, 20, 0);
      print('🔄 [Reset Tracks] Got ${resetTracks.length} tracks after reset');
      return resetTracks;
    } catch (e) {
      print('❌ [Reset Tracks] Error: $e');
      return [];
    }
  }

  /// Strategy 4: Force generate content using broad searches
  Future<List<MusicTrack>> _forceGenerateContent(String genre, BuildContext context) async {
    final allTracks = <MusicTrack>[];
    
    try {
      // Force 1: Very broad music search
      final broadTracks = await _spotifyService.searchTracks('music', limit: 10);
      allTracks.addAll(broadTracks);
      
      // Force 2: Popular tracks
      final popularTracks = await _spotifyService.searchTracks('popular songs', limit: 10);
      allTracks.addAll(popularTracks);
      
      // Force 3: Current year tracks
      final currentYear = DateTime.now().year;
      final yearTracks = await _spotifyService.searchTracks('$currentYear music', limit: 10);
      allTracks.addAll(yearTracks);
      
      print('🚨 [Force Generate] Generated ${allTracks.length} tracks as fallback content');
      return allTracks;
    } catch (e) {
      print('❌ [Force Generate] Error: $e');
      return [];
    }
  }

  /// Get broader genre tracks by searching with more general terms
  Future<List<MusicTrack>> _getBroadGenreTracks(String genre, int limit) async {
    try {
      // Remove specific qualifiers and search more broadly
      final broadGenre = genre.replaceAll('-', ' ').split(' ').first;
      final tracks = await _spotifyService.searchTracks('$broadGenre music', limit: limit);
      print('🎯 [Broad Genre] Got ${tracks.length} tracks for broad term: $broadGenre');
      return tracks;
    } catch (e) {
      print('❌ [Broad Genre] Error: $e');
      return [];
    }
  }

  /// Get related genre tracks
  Future<List<MusicTrack>> _getRelatedGenreTracks(String genre, int limit) async {
    try {
      // Use SpotifyGenreService to get related genres
      final relatedGenres = SpotifyGenreService.getRelatedGenres(genre);
      if (relatedGenres.isEmpty) return [];
      
      final relatedGenre = relatedGenres.first;
      final tracks = await _spotifyService.searchTracks('$relatedGenre music', limit: limit);
      print('🔗 [Related Genre] Got ${tracks.length} tracks for related genre: $relatedGenre');
      return tracks;
    } catch (e) {
      print('❌ [Related Genre] Error: $e');
      return [];
    }
  }

  /// Get a pool of artists relevant to the user for a specific genre.
  Future<List<String>> _getGenreSpecificArtists(
      String genre, BuildContext context) async {
    final cacheKey = 'artists_$genre';
    if (_isCacheValid(cacheKey) && _genreArtistCache.containsKey(cacheKey)) {
      print('📦 [Genre] Using cached artists for $genre.');
      return _genreArtistCache[cacheKey]!;
    }

    final artists = <String>{};

    // 1. Get user's personal artists for the genre, strictly validated.
    final userArtists = await _getUserArtistsForGenre(genre, context);
    artists.addAll(userArtists);
    print(
        '🎤 [Genre] Found ${userArtists.length} validated personal artists for $genre.');

    // 2. Fallback: If no personal artists, search Spotify for seeds.
    if (artists.isEmpty) {
      print(
          '🤔 [Genre] No personal artists found. Searching Spotify for seeds...');
      final searchResults =
          await _spotifyService.searchArtists('genre:"$genre"', limit: 20);
      final artistNames =
          searchResults.map((a) => a['name'] as String).toList();

      print(
          'Found ${artistNames.length} artists from Spotify search. Validating...');
      final validatedSpotifyArtists =
          await _validateArtists(artistNames, genre);
      artists.addAll(validatedSpotifyArtists);
      print(
          '✅ [Genre] Found ${validatedSpotifyArtists.length} validated artists from Spotify search.');
    }

    // 3. Expand with Last.fm if we have seed artists
    if (artists.isNotEmpty) {
      try {
        final similarArtists = await _lastFmService.getWeightedSimilarArtists(
          seedArtists: artists.take(5).toList(),
          limit: 30,
        );
        artists.addAll(similarArtists);
        print(
            '🔥 [Genre] Expanded with ${similarArtists.length} similar artists from Last.fm.');
      } catch (e) {
        print('⚠️ Error getting similar artists from Last.fm: $e');
      }
    }

    final result = artists.toList();
    _genreArtistCache[cacheKey] = result;
    _cacheTimes[cacheKey] = DateTime.now();

    print(
        '✅ [Genre] Final artist pool for $genre has ${result.length} artists.');
    return result;
  }

  /// Gets a user's top artists from various sources and validates them for a specific genre.
  /// PRIORITIZES USER PROFILE DATA (90%+) over service data (10% max)
  Future<List<String>> _getUserArtistsForGenre(
      String genre, BuildContext context) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);
    final pinProvider = Provider.of<PinProvider>(context, listen: false);

    // PRIMARY SOURCE: Get artists from user profile (90%+ priority)
    final profileArtists = userProvider.currentUser?.topArtists ?? [];
    print('🎵 PRIMARY: ${profileArtists.length} artists from user profile for genre: $genre');

    // Use profile artists as the main source
    final rawArtists = <String>{};
    rawArtists.addAll(profileArtists);

    // MINIMAL SERVICE DATA: Only add a small amount (max 10%) for variety if profile has data
    if (profileArtists.isNotEmpty) {
      final maxServiceArtists = (profileArtists.length * 0.1).ceil().clamp(1, 5);
      print('🎵 SUPPLEMENTAL: Adding max $maxServiceArtists artists from service (10% rule)');

      if (spotifyProvider.isConnected) {
        try {
          final topArtists = await _spotifyService.getTopArtists(limit: maxServiceArtists);
          final serviceArtists = topArtists.map((a) => a['name'] as String).toList();
          
          // Only add artists that aren't already in profile
          final newServiceArtists = serviceArtists
              .where((artist) => !profileArtists.contains(artist))
              .take(maxServiceArtists)
              .toList();
              
          rawArtists.addAll(newServiceArtists);
          print('🎵 Added ${newServiceArtists.length} supplemental Spotify artists');
        } catch (e) {
          print('⚠️ Error getting minimal Spotify artists: $e');
        }
      }
    } else {
      // FALLBACK: Only when profile is empty, use minimal service/pin data
      print('⚠️ No user profile artists found - using minimal fallback data');
      
      if (spotifyProvider.isConnected) {
        try {
          final topArtists = await _spotifyService.getTopArtists(limit: 10);
          rawArtists.addAll(topArtists.map((a) => a['name'] as String));
          print('🎵 FALLBACK: Using ${rawArtists.length} Spotify artists');
        } catch (e) {
          print('❌ Error loading Spotify fallback artists: $e');
          // Final fallback to pins
          rawArtists.addAll(pinProvider.pins.map((pin) => pin.trackArtist));
          print('🎵 FINAL FALLBACK: Using ${rawArtists.length} artists from pins');
        }
      } else {
        // Use artists from pins as absolute fallback
        rawArtists.addAll(pinProvider.pins.map((pin) => pin.trackArtist));
        print('🎵 FALLBACK: Using ${rawArtists.length} artists from pins');
      }
    }

    print('🎵 FINAL: ${rawArtists.length} total artists (${profileArtists.length} from profile, ${rawArtists.length - profileArtists.length} from service/pins)');
    print('🕵️‍♂️ [DEBUG] Validating for genre: $genre');
    
    if (rawArtists.isEmpty) return [];

    return await _validateArtists(rawArtists.toList(), genre);
  }

  /// Validates a list of artists against a genre using efficient batch caching.
  Future<List<String>> _validateArtists(List<String> artists, String genre) async {
    final validated = <String>[];

    try {
      // First, try to get cached genre information for all artists
      for (final artist in artists) {
        final artistGenres = await _getArtistGenres(artist);
        if (artistGenres.isNotEmpty) {
          // Check if any of the artist's genres match or are related to target genre
          final targetCanonical = SpotifyGenreService.getCanonicalGenre(genre);
          
          final isValid = artistGenres.any((artistGenre) {
            final artistCanonical = SpotifyGenreService.getCanonicalGenre(artistGenre);
            return SpotifyGenreService.areGenresRelated(artistCanonical, targetCanonical) ||
                   SpotifyGenreService.validateArtistForGenre([artistGenre], genre);
          });
          
          if (isValid) {
            validated.add(artist);
          }
        }
      }

      // If we don't have enough validated artists, use Spotify's genre filtering as fallback
      if (validated.length < 5) {
        try {
          final spotifyValidated = await _spotifyService.filterArtistsByGenre(artists, genre);
          // Add any new artists from Spotify validation
          for (final artist in spotifyValidated) {
            if (!validated.contains(artist)) {
              validated.add(artist);
            }
          }
        } catch (e) {
          print('⚠️ [Validation] Spotify validation failed: $e');
        }
      }

      // Final fallback: use MusicBrainz validation for higher accuracy
      if (validated.length < 3) {
        try {
          final mbzArtists = await MusicBrainzService.searchArtistsByTag(genre, limit: 100, minScore: 75);
          final mbzArtistNames = mbzArtists.map((a) => a['name'].toString().toLowerCase()).toSet();

          // Keep artists that exist in MusicBrainz results
          final finalArtists = artists.where((artist) {
            final lowerArtist = artist.toLowerCase();
            return validated.contains(artist) || mbzArtistNames.contains(lowerArtist);
          }).toList();

          print('🧠 [Validation] MusicBrainz check complete. Final validated count: ${finalArtists.length}');
          return finalArtists;
        } catch (e) {
          print('⚠️ [Validation] MusicBrainz validation failed: $e. Using existing validation.');
        }
      }

      print('✅ [Validation] Validated ${validated.length} artists for genre: $genre');
      return validated;
    } catch (e) {
      print('❌ [Validation] Error validating artists: $e');
      return artists.take(10).toList(); // Return first 10 as fallback
    }
  }
}
