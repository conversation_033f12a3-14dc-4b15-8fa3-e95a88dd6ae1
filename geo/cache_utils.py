"""
Cache utilities for geo data and map tiles
Provides caching functionality for vector data and map tiles
"""

from django.core.cache import cache
from django.conf import settings
import hashlib
import json
from typing import Optional, Dict, Any


# Cache timeout configurations (in seconds)
CACHE_TIMEOUTS = {
    'tile': 60 * 60 * 24,  # 24 hours
    'vector_data': 60 * 60 * 4,  # 4 hours  
    'map_style': 60 * 60 * 12,  # 12 hours
    'metadata': 60 * 30,  # 30 minutes
    'user_settings': 60 * 60 * 2,  # 2 hours
    'trending_data': 60 * 15,  # 15 minutes
}


class MapCache:
    """
    Main caching interface for map data and tiles
    Provides static methods for caching various types of map data
    """
    
    @staticmethod
    def _generate_cache_key(prefix: str, **kwargs) -> str:
        """
        Generate a consistent cache key from parameters
        
        Args:
            prefix: Cache key prefix
            **kwargs: Parameters to include in the key
            
        Returns:
            Generated cache key
        """
        # Sort kwargs for consistent key generation
        sorted_params = sorted(kwargs.items())
        params_str = "&".join(f"{k}={v}" for k, v in sorted_params if v is not None)
        
        # Create hash for long parameter strings
        if len(params_str) > 100:
            params_hash = hashlib.md5(params_str.encode()).hexdigest()[:16]
            return f"{prefix}:{params_hash}"
        else:
            return f"{prefix}:{params_str}"
    
    @staticmethod
    def get_tile(z: int, x: int, y: int) -> Optional[bytes]:
        """
        Get a map tile from cache
        
        Args:
            z: Zoom level
            x: X coordinate  
            y: Y coordinate
            
        Returns:
            Tile data as bytes or None if not in cache
        """
        cache_key = f"osm_tile:{z}:{x}:{y}"
        return cache.get(cache_key)
    
    @staticmethod
    def set_tile(z: int, x: int, y: int, data: bytes) -> None:
        """
        Store a map tile in cache
        
        Args:
            z: Zoom level
            x: X coordinate
            y: Y coordinate  
            data: Tile image data
        """
        cache_key = f"osm_tile:{z}:{x}:{y}"
        cache.set(cache_key, data, timeout=CACHE_TIMEOUTS['tile'])
    
    @staticmethod
    def get_tile_metadata(cache_key: str, metadata_key: str) -> Optional[str]:
        """
        Get metadata for a tile (like ETags)
        
        Args:
            cache_key: The tile cache key
            metadata_key: The specific metadata key to retrieve
            
        Returns:
            Metadata value or None if not in cache
        """
        # Extract z, x, y from the cache_key if it's in the format "tile_z_x_y"
        parts = cache_key.split("_")
        if len(parts) == 4 and parts[0] == "tile":
            try:
                z, x, y = int(parts[1]), int(parts[2]), int(parts[3])
                metadata_cache_key = f"osm_tile:{z}:{x}:{y}:metadata:{metadata_key}"
                return cache.get(metadata_cache_key)
            except ValueError:
                pass
        
        # Fallback to the original format
        metadata_cache_key = f"{cache_key}:metadata:{metadata_key}"
        return cache.get(metadata_cache_key)
    
    @staticmethod
    def set_tile_metadata(cache_key: str, metadata_key: str, value: str) -> None:
        """
        Set metadata for a tile
        
        Args:
            cache_key: The tile cache key
            metadata_key: The specific metadata key
            value: The metadata value to store
        """
        metadata_cache_key = f"{cache_key}:metadata:{metadata_key}"
        cache.set(metadata_cache_key, value, timeout=CACHE_TIMEOUTS['metadata'])
    
    @staticmethod
    def get_vector_data(data_type: str, lat: float, lng: float, zoom: int, 
                       additional_params: Optional[Dict[str, Any]] = None) -> Optional[Dict]:
        """
        Get cached vector data
        
        Args:
            data_type: Type of vector data (roads, buildings, parks, trending)
            lat: Latitude
            lng: Longitude  
            zoom: Zoom level
            additional_params: Additional parameters for cache key
            
        Returns:
            Cached data or None if not in cache
        """
        params = {
            'lat': round(lat, 6),
            'lng': round(lng, 6), 
            'zoom': zoom
        }
        
        if additional_params:
            params.update(additional_params)
            
        cache_key = MapCache._generate_cache_key(f"vector_{data_type}", **params)
        return cache.get(cache_key)
    
    @staticmethod
    def set_vector_data(data_type: str, lat: float, lng: float, zoom: int,
                       data: Dict, additional_params: Optional[Dict[str, Any]] = None) -> None:
        """
        Cache vector data
        
        Args:
            data_type: Type of vector data (roads, buildings, parks, trending)
            lat: Latitude
            lng: Longitude
            zoom: Zoom level
            data: Data to cache
            additional_params: Additional parameters for cache key
        """
        params = {
            'lat': round(lat, 6),
            'lng': round(lng, 6),
            'zoom': zoom  
        }
        
        if additional_params:
            params.update(additional_params)
            
        cache_key = MapCache._generate_cache_key(f"vector_{data_type}", **params)
        cache.set(cache_key, data, timeout=CACHE_TIMEOUTS['vector_data'])
    
    @staticmethod
    def get_map_style(style_name: str) -> Optional[Dict]:
        """
        Get cached map style
        
        Args:
            style_name: Name of the map style
            
        Returns:
            Cached style data or None if not in cache
        """
        cache_key = f"map_style:{style_name}"
        return cache.get(cache_key)
    
    @staticmethod
    def set_map_style(style_name: str, style_data: Dict) -> None:
        """
        Cache map style data
        
        Args:
            style_name: Name of the map style
            style_data: Style data to cache
        """
        cache_key = f"map_style:{style_name}"
        cache.set(cache_key, style_data, timeout=CACHE_TIMEOUTS['map_style'])
    
    @staticmethod
    def get_user_settings(user_id: int) -> Optional[Dict]:
        """
        Get cached user map settings
        
        Args:
            user_id: User ID
            
        Returns:
            Cached settings or None if not in cache
        """
        cache_key = f"user_settings:{user_id}"
        return cache.get(cache_key)
    
    @staticmethod
    def set_user_settings(user_id: int, settings: Dict) -> None:
        """
        Cache user map settings
        
        Args:
            user_id: User ID
            settings: Settings data to cache
        """
        cache_key = f"user_settings:{user_id}"
        cache.set(cache_key, settings, timeout=CACHE_TIMEOUTS['user_settings'])
    
    @staticmethod
    def invalidate_user_cache(user_id: int) -> None:
        """
        Invalidate all cached data for a user
        
        Args:
            user_id: User ID
        """
        cache.delete(f"user_settings:{user_id}")
        # Could add more user-specific cache invalidation here
    
    @staticmethod
    def clear_vector_cache(data_type: str) -> None:
        """
        Clear cached vector data of a specific type
        Note: This is a simplified version - Django's cache doesn't support pattern deletion
        
        Args:
            data_type: Type of vector data to clear
        """
        # In a production environment, you might want to use Redis with pattern deletion
        # For now, this is a placeholder that could be implemented with cache versioning
        pass
    
    @staticmethod
    def get_cache_stats() -> Dict[str, Any]:
        """
        Get cache statistics (implementation depends on cache backend)
        
        Returns:
            Dictionary with cache statistics
        """
        # This would need to be implemented based on your cache backend
        # For Redis, you could use INFO commands
        # For Memcached, you could use stats commands
        # For Django's default cache, this is limited
        
        return {
            'backend': getattr(settings, 'CACHES', {}).get('default', {}).get('BACKEND', 'unknown'),
            'note': 'Detailed stats depend on cache backend'
        }


class CacheMiddleware:
    """
    Cache middleware for geo data
    Provides automatic caching for common geo queries
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Pre-processing: Check for cached geo data
        if request.path.startswith('/api/geo/'):
            # Add cache headers for geo endpoints
            response = self.get_response(request)
            
            if response.status_code == 200:
                # Add appropriate cache headers
                if 'buildings' in request.path or 'roads' in request.path or 'parks' in request.path:
                    response['Cache-Control'] = 'public, max-age=14400'  # 4 hours
                elif 'trending' in request.path:
                    response['Cache-Control'] = 'public, max-age=900'   # 15 minutes
                else:
                    response['Cache-Control'] = 'public, max-age=3600'  # 1 hour
            
            return response
        
        return self.get_response(request)


# Utility functions for cache management
def warm_cache_for_area(north: float, south: float, east: float, west: float, zoom: int):
    """
    Pre-warm cache for a specific geographical area
    
    Args:
        north, south, east, west: Bounding box coordinates
        zoom: Zoom level
    """
    # This would implement cache warming logic
    # Could be used with Celery tasks for background cache warming
    pass


def clear_area_cache(north: float, south: float, east: float, west: float):
    """
    Clear cached data for a specific geographical area
    
    Args:
        north, south, east, west: Bounding box coordinates
    """
    # This would implement area-specific cache clearing
    # Useful when data is updated for a specific region
    pass 