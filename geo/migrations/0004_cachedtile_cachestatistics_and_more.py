# Generated by Django 4.2.7 on 2025-05-05 00:23

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("geo", "0003_usermapsettings_road_park_cachedregion_building"),
    ]

    operations = [
        migrations.CreateModel(
            name="CachedTile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("z", models.IntegerField()),
                ("x", models.IntegerField()),
                ("y", models.IntegerField()),
                ("data", models.BinaryField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("last_accessed", models.DateTimeField(auto_now=True)),
                ("access_count", models.IntegerField(default=0)),
                ("size_bytes", models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name="CacheStatistics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("total_tiles", models.IntegerField()),
                ("total_regions", models.IntegerField()),
                ("total_size_bytes", models.BigIntegerField()),
                ("cleanup_runs", models.IntegerField(default=0)),
                ("tiles_cleaned", models.IntegerField(default=0)),
                ("regions_cleaned", models.IntegerField(default=0)),
                ("space_reclaimed_bytes", models.BigIntegerField(default=0)),
            ],
            options={
                "get_latest_by": "timestamp",
            },
        ),
        migrations.RemoveIndex(
            model_name="cachedregion",
            name="region_bounds_idx",
        ),
        migrations.RenameField(
            model_name="cachedregion",
            old_name="updated_at",
            new_name="last_accessed",
        ),
        migrations.RenameField(
            model_name="cachedregion",
            old_name="size_kb",
            new_name="size_bytes",
        ),
        migrations.RemoveField(
            model_name="cachedregion",
            name="bounds",
        ),
        migrations.AddField(
            model_name="cachedregion",
            name="access_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AddIndex(
            model_name="cachedregion",
            index=models.Index(
                fields=["last_accessed"], name="geo_cachedr_last_ac_9980bf_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="cachedregion",
            index=models.Index(
                fields=["access_count"], name="geo_cachedr_access__17f256_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="cachedtile",
            index=models.Index(fields=["z", "x", "y"], name="geo_cachedt_z_bfa847_idx"),
        ),
        migrations.AddIndex(
            model_name="cachedtile",
            index=models.Index(
                fields=["last_accessed"], name="geo_cachedt_last_ac_3d1f6a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="cachedtile",
            index=models.Index(
                fields=["access_count"], name="geo_cachedt_access__226b77_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="cachedtile",
            unique_together={("z", "x", "y")},
        ),
    ]
