from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Q
from django.utils import timezone
from .models import LastFmCacheEntry, LastFmApiUsage, LastFmRateLimit


@admin.register(LastFmCacheEntry)
class LastFmCacheEntryAdmin(admin.ModelAdmin):
    """Admin interface for Last.fm cache entries"""
    
    list_display = ['cache_key_short', 'method', 'hit_count', 'created_at', 'expires_at', 'is_expired_display', 'last_accessed']
    list_filter = ['method', 'created_at', 'expires_at']
    search_fields = ['cache_key', 'method', 'query_hash']
    readonly_fields = ['cache_key', 'method', 'query_hash', 'created_at', 'updated_at', 'last_accessed', 'is_expired']
    ordering = ['-last_accessed']
    
    fieldsets = (
        ('Cache Information', {
            'fields': ('cache_key', 'method', 'query_hash')
        }),
        ('Usage Statistics', {
            'fields': ('hit_count', 'last_accessed')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'expires_at', 'is_expired')
        }),
        ('Response Data', {
            'fields': ('response_data',),
            'classes': ('collapse',),
        }),
    )
    
    def cache_key_short(self, obj):
        """Display shortened cache key"""
        return obj.cache_key[:50] + '...' if len(obj.cache_key) > 50 else obj.cache_key
    cache_key_short.short_description = 'Cache Key'
    
    def is_expired_display(self, obj):
        """Display expiration status with color"""
        if obj.is_expired:
            return format_html('<span style="color: red;">Expired</span>')
        else:
            return format_html('<span style="color: green;">Active</span>')
    is_expired_display.short_description = 'Status'
    
    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related()
    
    actions = ['delete_expired_entries', 'clear_hit_counts']
    
    def delete_expired_entries(self, request, queryset):
        """Delete expired cache entries"""
        expired_entries = queryset.filter(expires_at__lt=timezone.now())
        count = expired_entries.count()
        expired_entries.delete()
        self.message_user(request, f'Deleted {count} expired cache entries.')
    delete_expired_entries.short_description = 'Delete expired entries'
    
    def clear_hit_counts(self, request, queryset):
        """Reset hit counts to zero"""
        count = queryset.update(hit_count=0)
        self.message_user(request, f'Reset hit counts for {count} cache entries.')
    clear_hit_counts.short_description = 'Clear hit counts'


@admin.register(LastFmApiUsage)
class LastFmApiUsageAdmin(admin.ModelAdmin):
    """Admin interface for Last.fm API usage statistics"""
    
    list_display = ['date', 'method', 'total_requests', 'cache_hits', 'cache_misses', 'cache_hit_rate_display', 
                   'api_calls_made', 'average_response_time']
    list_filter = ['date', 'method']
    search_fields = ['method']
    readonly_fields = ['date', 'method', 'cache_hit_rate']
    ordering = ['-date', 'method']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('date', 'method')
        }),
        ('Request Statistics', {
            'fields': ('total_requests', 'cache_hits', 'cache_misses', 'cache_hit_rate')
        }),
        ('API Performance', {
            'fields': ('api_calls_made', 'average_response_time')
        }),
    )
    
    def cache_hit_rate_display(self, obj):
        """Display cache hit rate with color coding"""
        rate = obj.cache_hit_rate
        if rate >= 80:
            color = 'green'
        elif rate >= 60:
            color = 'orange'
        else:
            color = 'red'
        return format_html(f'<span style="color: {color};">{rate:.1f}%</span>')
    cache_hit_rate_display.short_description = 'Cache Hit Rate'
    
    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related()


@admin.register(LastFmRateLimit)
class LastFmRateLimitAdmin(admin.ModelAdmin):
    """Admin interface for Last.fm rate limiting"""
    
    list_display = ['window_start', 'method', 'requests_made', 'is_current_window']
    list_filter = ['window_start', 'method']
    search_fields = ['method']
    readonly_fields = ['window_start', 'method', 'requests_made']
    ordering = ['-window_start']
    
    def is_current_window(self, obj):
        """Check if this is the current rate limit window"""
        now = timezone.now()
        current_window = now.replace(second=0, microsecond=0)
        return obj.window_start == current_window
    is_current_window.boolean = True
    is_current_window.short_description = 'Current Window'
    
    def get_queryset(self, request):
        """Optimize queryset and limit to recent windows"""
        from datetime import timedelta
        recent_time = timezone.now() - timedelta(hours=24)
        return super().get_queryset(request).filter(
            window_start__gte=recent_time
        ).select_related()
    
    def has_add_permission(self, request):
        """Disable manual addition of rate limit records"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Disable manual editing of rate limit records"""
        return False


# Customizations for the admin site
admin.site.site_header = "BOPMaps Last.fm Cache Administration"
admin.site.site_title = "Last.fm Cache Admin"
admin.site.index_title = "Welcome to Last.fm Cache Administration"
