# Generated by Django 4.2.7 on 2025-06-25 21:29

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="LastFmRateLimit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("window_start", models.DateTimeField(db_index=True)),
                ("requests_made", models.PositiveIntegerField(default=0)),
                ("method", models.CharField(blank=True, max_length=100)),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["window_start", "method"],
                        name="lastfm_last_window__e6b827_idx",
                    )
                ],
            },
        ),
        migrations.CreateModel(
            name="LastFmCacheEntry",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "cache_key",
                    models.Char<PERSON>ield(db_index=True, max_length=255, unique=True),
                ),
                ("method", models.CharField(db_index=True, max_length=100)),
                ("query_hash", models.CharField(db_index=True, max_length=64)),
                ("response_data", models.JSONField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("expires_at", models.DateTimeField(db_index=True)),
                ("hit_count", models.PositiveIntegerField(default=0)),
                ("last_accessed", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["-last_accessed"],
                "indexes": [
                    models.Index(
                        fields=["method", "expires_at"],
                        name="lastfm_last_method_61e475_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="lastfm_last_created_ecf17d_idx"
                    ),
                    models.Index(
                        fields=["last_accessed"], name="lastfm_last_last_ac_f6818c_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="LastFmApiUsage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(auto_now_add=True, db_index=True)),
                ("method", models.CharField(db_index=True, max_length=100)),
                ("total_requests", models.PositiveIntegerField(default=0)),
                ("cache_hits", models.PositiveIntegerField(default=0)),
                ("cache_misses", models.PositiveIntegerField(default=0)),
                ("api_calls_made", models.PositiveIntegerField(default=0)),
                ("average_response_time", models.FloatField(default=0.0)),
            ],
            options={
                "ordering": ["-date", "method"],
                "unique_together": {("date", "method")},
            },
        ),
    ]
