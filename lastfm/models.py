from django.db import models
from django.utils import timezone
import hashlib


class LastFmCacheEntry(models.Model):
    """Cache entry for Last.fm API responses"""
    
    cache_key = models.CharField(max_length=255, unique=True, db_index=True)
    method = models.CharField(max_length=100, db_index=True)
    query_hash = models.CharField(max_length=64, db_index=True)
    response_data = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(db_index=True)
    hit_count = models.PositiveIntegerField(default=0)
    last_accessed = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['method', 'expires_at']),
            models.Index(fields=['created_at']),
            models.Index(fields=['last_accessed']),
        ]
        ordering = ['-last_accessed']
    
    def __str__(self):
        return f"LastFm Cache: {self.method} - {self.cache_key[:50]}"
    
    @property
    def is_expired(self):
        return timezone.now() > self.expires_at
    
    def increment_hit_count(self):
        self.hit_count += 1
        self.last_accessed = timezone.now()
        self.save(update_fields=['hit_count', 'last_accessed'])


class LastFmApiUsage(models.Model):
    """Track API usage statistics"""
    
    date = models.DateField(auto_now_add=True, db_index=True)
    method = models.CharField(max_length=100, db_index=True)
    total_requests = models.PositiveIntegerField(default=0)
    cache_hits = models.PositiveIntegerField(default=0)
    cache_misses = models.PositiveIntegerField(default=0)
    api_calls_made = models.PositiveIntegerField(default=0)
    average_response_time = models.FloatField(default=0.0)
    
    class Meta:
        unique_together = ('date', 'method')
        ordering = ['-date', 'method']
    
    def __str__(self):
        return f"LastFm Usage: {self.date} - {self.method}"
    
    @property
    def cache_hit_rate(self):
        if self.total_requests > 0:
            return (self.cache_hits / self.total_requests) * 100
        return 0.0


class LastFmRateLimit(models.Model):
    """Track rate limiting for Last.fm API"""
    
    window_start = models.DateTimeField(db_index=True)
    requests_made = models.PositiveIntegerField(default=0)
    method = models.CharField(max_length=100, blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['window_start', 'method']),
        ]
    
    def __str__(self):
        return f"Rate Limit: {self.window_start} - {self.requests_made} requests"
