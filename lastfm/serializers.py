from rest_framework import serializers
from .models import LastFmCacheE<PERSON>ry, LastFmApiUsage


class LastFmRequestSerializer(serializers.Serializer):
    """Serializer for Last.fm API request parameters"""
    
    method = serializers.Char<PERSON>ield(max_length=100, help_text="Last.fm API method (e.g., artist.getSimilar)")
    artist = serializers.CharField(max_length=255, required=False, help_text="Artist name")
    album = serializers.Char<PERSON>ield(max_length=255, required=False, help_text="Album name")
    track = serializers.Char<PERSON>ield(max_length=255, required=False, help_text="Track name")
    mbid = serializers.CharField(max_length=36, required=False, help_text="MusicBrainz ID")
    limit = serializers.IntegerField(min_value=1, max_value=200, default=20, required=False, 
                                   help_text="Number of results to return")
    page = serializers.IntegerField(min_value=1, default=1, required=False, 
                                  help_text="Page number for pagination")
    
    # Additional parameters that may be sent by clients but should be ignored
    api_key = serializers.CharField(max_length=255, required=False, help_text="API key (ignored - server uses its own)")
    format = serializers.CharField(max_length=10, required=False, help_text="Response format (ignored - always JSON)")
    
    def validate_method(self, value):
        """Validate that the method is supported"""
        supported_methods = [
            'artist.getSimilar',
            'artist.getInfo',
            'artist.getTopTracks',
            'artist.getTopAlbums',
            'album.getInfo',
            'track.getInfo',
            'track.getSimilar',
        ]
        
        if value not in supported_methods:
            raise serializers.ValidationError(
                f"Method '{value}' is not supported. Supported methods: {', '.join(supported_methods)}"
            )
        
        return value
    
    def validate(self, data):
        """Validate that required parameters are provided for each method"""
        method = data.get('method')
        
        if method and method.startswith('artist.'):
            if not data.get('artist') and not data.get('mbid'):
                raise serializers.ValidationError("Either 'artist' or 'mbid' is required for artist methods")
        
        elif method and method.startswith('album.'):
            if not data.get('album') and not data.get('mbid'):
                if not (data.get('artist') and data.get('album')):
                    raise serializers.ValidationError("Either 'mbid' or both 'artist' and 'album' are required for album methods")
        
        elif method and method.startswith('track.'):
            if not data.get('track') and not data.get('mbid'):
                if not (data.get('artist') and data.get('track')):
                    raise serializers.ValidationError("Either 'mbid' or both 'artist' and 'track' are required for track methods")
        
        return data


class LastFmCacheEntrySerializer(serializers.ModelSerializer):
    """Serializer for cache entry information"""
    
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = LastFmCacheEntry
        fields = [
            'id', 'cache_key', 'method', 'created_at', 'updated_at', 
            'expires_at', 'hit_count', 'last_accessed', 'is_expired'
        ]
        read_only_fields = ['id', 'cache_key', 'method', 'created_at', 'updated_at', 
                           'expires_at', 'hit_count', 'last_accessed']


class LastFmApiUsageSerializer(serializers.ModelSerializer):
    """Serializer for API usage statistics"""
    
    cache_hit_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = LastFmApiUsage
        fields = [
            'id', 'date', 'method', 'total_requests', 'cache_hits', 
            'cache_misses', 'api_calls_made', 'average_response_time', 'cache_hit_rate'
        ]
        read_only_fields = ['id', 'date', 'method', 'total_requests', 'cache_hits', 
                           'cache_misses', 'api_calls_made', 'average_response_time']


class CacheStatsSerializer(serializers.Serializer):
    """Serializer for cache statistics"""
    
    today_stats = serializers.DictField(read_only=True)
    total_cache_entries = serializers.IntegerField(read_only=True)
    expired_entries = serializers.IntegerField(read_only=True)
    active_entries = serializers.IntegerField(read_only=True)


class ErrorResponseSerializer(serializers.Serializer):
    """Serializer for error responses"""
    
    error = serializers.IntegerField()
    message = serializers.CharField()
    links = serializers.ListField(child=serializers.CharField(), required=False) 