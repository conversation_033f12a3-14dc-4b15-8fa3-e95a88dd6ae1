import hashlib
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from urllib.parse import urlencode

import requests
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from django.db import transaction
from django.db import models

from .models import LastFmCacheEntry, LastFmApiUsage, LastFmRateLimit

logger = logging.getLogger(__name__)


class LastFmService:
    """Service for handling Last.fm API requests with caching and rate limiting"""
    
    BASE_URL = 'https://ws.audioscrobbler.com/2.0/'
    CACHE_TTL = 60 * 60 * 6  # 6 hours
    RATE_LIMIT_WINDOW = 60  # 1 minute
    MAX_REQUESTS_PER_WINDOW = 300  # 5 requests per second average
    REQUEST_TIMEOUT = 10
    
    def __init__(self):
        self.api_key = getattr(settings, 'LASTFM_API_KEY', '')
        if not self.api_key:
            logger.warning("LASTFM_API_KEY not configured in settings")
    
    def _generate_cache_key(self, method: str, params: Dict[str, Any]) -> str:
        """Generate a cache key for the request"""
        # Sort params for consistent hashing
        sorted_params = sorted(params.items())
        query_string = urlencode(sorted_params)
        query_hash = hashlib.sha1(query_string.encode()).hexdigest()
        return f"lastfm:{method}:{query_hash}"
    
    def _generate_query_hash(self, params: Dict[str, Any]) -> str:
        """Generate a query hash for database storage"""
        sorted_params = sorted(params.items())
        query_string = urlencode(sorted_params)
        return hashlib.sha1(query_string.encode()).hexdigest()
    
    def _check_rate_limit(self, method: str = '') -> bool:
        """Check if we're within rate limits"""
        now = timezone.now()
        window_start = now.replace(second=0, microsecond=0)
        
        # Check current window
        rate_limit, created = LastFmRateLimit.objects.get_or_create(
            window_start=window_start,
            method=method,
            defaults={'requests_made': 0}
        )
        
        if rate_limit.requests_made >= self.MAX_REQUESTS_PER_WINDOW:
            logger.warning(f"Rate limit exceeded for method {method} in window {window_start}")
            return False
        
        return True
    
    def _increment_rate_limit(self, method: str = ''):
        """Increment rate limit counter"""
        now = timezone.now()
        window_start = now.replace(second=0, microsecond=0)
        
        rate_limit, created = LastFmRateLimit.objects.get_or_create(
            window_start=window_start,
            method=method,
            defaults={'requests_made': 1}
        )
        
        if not created:
            rate_limit.requests_made = models.F('requests_made') + 1
            rate_limit.save(update_fields=['requests_made'])
    
    def _get_from_cache(self, cache_key: str, method: str) -> Optional[Dict[str, Any]]:
        """Get data from cache (Redis first, then database)"""
        # Try Redis first
        cached_data = cache.get(cache_key)
        if cached_data:
            self._update_usage_stats(method, cache_hit=True)
            logger.info(f"Cache hit (Redis) for {cache_key}")
            return cached_data
        
        # Try database cache
        try:
            cache_entry = LastFmCacheEntry.objects.get(
                cache_key=cache_key,
                expires_at__gt=timezone.now()
            )
            cache_entry.increment_hit_count()
            
            # Store back in Redis for faster access
            cache.set(cache_key, cache_entry.response_data, self.CACHE_TTL)
            
            self._update_usage_stats(method, cache_hit=True)
            logger.info(f"Cache hit (Database) for {cache_key}")
            return cache_entry.response_data
            
        except LastFmCacheEntry.DoesNotExist:
            return None
    
    def _store_in_cache(self, cache_key: str, method: str, query_hash: str, data: Dict[str, Any]):
        """Store data in both Redis and database cache"""
        expires_at = timezone.now() + timedelta(seconds=self.CACHE_TTL)
        
        # Store in Redis
        cache.set(cache_key, data, self.CACHE_TTL)
        
        # Store in database
        try:
            with transaction.atomic():
                LastFmCacheEntry.objects.update_or_create(
                    cache_key=cache_key,
                    defaults={
                        'method': method,
                        'query_hash': query_hash,
                        'response_data': data,
                        'expires_at': expires_at,
                        'hit_count': 0,
                    }
                )
            logger.info(f"Stored in cache: {cache_key}")
        except Exception as e:
            logger.error(f"Error storing cache entry: {e}")
    
    def _update_usage_stats(self, method: str, cache_hit: bool = False, 
                           api_call: bool = False, response_time: float = 0.0):
        """Update usage statistics"""
        today = timezone.now().date()
        
        try:
            usage, created = LastFmApiUsage.objects.get_or_create(
                date=today,
                method=method,
                defaults={
                    'total_requests': 0,
                    'cache_hits': 0,
                    'cache_misses': 0,
                    'api_calls_made': 0,
                    'average_response_time': 0.0,
                }
            )
            
            usage.total_requests += 1
            
            if cache_hit:
                usage.cache_hits += 1
            else:
                usage.cache_misses += 1
            
            if api_call:
                usage.api_calls_made += 1
                # Update average response time
                if usage.api_calls_made == 1:
                    usage.average_response_time = response_time
                else:
                    usage.average_response_time = (
                        (usage.average_response_time * (usage.api_calls_made - 1) + response_time) 
                        / usage.api_calls_made
                    )
            
            usage.save()
            
        except Exception as e:
            logger.error(f"Error updating usage stats: {e}")
    
    def _make_api_request(self, method: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Make request to Last.fm API"""
        if not self.api_key:
            logger.error("Last.fm API key not configured")
            return None
        
        # Check rate limit
        if not self._check_rate_limit(method):
            logger.warning("Rate limit exceeded, skipping API request")
            return None
        
        # Prepare request parameters
        request_params = {
            'method': method,
            'api_key': self.api_key,
            'format': 'json',
            **params
        }
        
        start_time = time.time()
        
        try:
            response = requests.get(
                self.BASE_URL,
                params=request_params,
                timeout=self.REQUEST_TIMEOUT
            )
            
            response_time = time.time() - start_time
            
            # Increment rate limit counter
            self._increment_rate_limit(method)
            
            if response.status_code == 200:
                data = response.json()
                
                # Check for Last.fm API errors
                if 'error' in data:
                    logger.error(f"Last.fm API error: {data.get('message', 'Unknown error')}")
                    return None
                
                self._update_usage_stats(method, api_call=True, response_time=response_time)
                logger.info(f"Successful API request for {method}")
                return data
            
            else:
                logger.error(f"HTTP error {response.status_code}: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            logger.error(f"Timeout making request to Last.fm API for method {method}")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"Error making request to Last.fm API: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON response: {e}")
            return None
    
    def get_data(self, method: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get data from Last.fm API with caching"""
        # Generate cache key
        cache_key = self._generate_cache_key(method, params)
        query_hash = self._generate_query_hash(params)
        
        logger.info(f"Processing request for method: {method}")
        
        # Try to get from cache first
        cached_data = self._get_from_cache(cache_key, method)
        if cached_data:
            return cached_data
        
        # Make API request
        data = self._make_api_request(method, params)
        if data:
            # Store in cache
            self._store_in_cache(cache_key, method, query_hash, data)
            self._update_usage_stats(method, cache_hit=False)
            return data
        
        return None
    
    def get_similar_artists(self, artist: str, limit: int = 20) -> Optional[Dict[str, Any]]:
        """Get similar artists for a given artist"""
        params = {
            'artist': artist,
            'limit': str(limit)
        }
        return self.get_data('artist.getSimilar', params)
    
    def get_artist_info(self, artist: str, mbid: str = None) -> Optional[Dict[str, Any]]:
        """Get artist information"""
        params = {'artist': artist}
        if mbid:
            params['mbid'] = mbid
        return self.get_data('artist.getInfo', params)
    
    def get_top_tracks(self, artist: str, limit: int = 20) -> Optional[Dict[str, Any]]:
        """Get top tracks for an artist"""
        params = {
            'artist': artist,
            'limit': str(limit)
        }
        return self.get_data('artist.getTopTracks', params)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        today = timezone.now().date()
        
        # Get today's stats
        stats = LastFmApiUsage.objects.filter(date=today).aggregate(
            total_requests=models.Sum('total_requests'),
            total_cache_hits=models.Sum('cache_hits'),
            total_cache_misses=models.Sum('cache_misses'),
            total_api_calls=models.Sum('api_calls_made'),
        )
        
        # Get cache entry counts
        total_entries = LastFmCacheEntry.objects.count()
        expired_entries = LastFmCacheEntry.objects.filter(
            expires_at__lt=timezone.now()
        ).count()
        
        return {
            'today_stats': stats,
            'total_cache_entries': total_entries,
            'expired_entries': expired_entries,
            'active_entries': total_entries - expired_entries,
        }
    
    def cleanup_expired_cache(self):
        """Clean up expired cache entries"""
        expired_count = LastFmCacheEntry.objects.filter(
            expires_at__lt=timezone.now()
        ).delete()[0]
        
        logger.info(f"Cleaned up {expired_count} expired cache entries")
        return expired_count 