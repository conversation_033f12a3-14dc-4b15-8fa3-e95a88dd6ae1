from django.urls import path
from .views import (
    LastFmAPIView,
    get_similar_artists,
    CacheStatsView,
    CacheManagementView,
    CacheEntriesView,
    UsageStatsView,
)

app_name = 'lastfm'

urlpatterns = [
    # Main Last.fm API proxy endpoint
    path('', LastFmAPIView.as_view(), name='api'),
    
    # Convenience endpoints
    path('similar-artists/', get_similar_artists, name='similar-artists'),
    
    # Admin/monitoring endpoints
    path('admin/stats/', CacheStatsView.as_view(), name='cache-stats'),
    path('admin/cleanup/', CacheManagementView.as_view(), name='cache-cleanup'),
    path('admin/entries/', CacheEntriesView.as_view(), name='cache-entries'),
    path('admin/usage/', UsageStatsView.as_view(), name='usage-stats'),
] 