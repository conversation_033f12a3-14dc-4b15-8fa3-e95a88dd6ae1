from django.shortcuts import render
import logging
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.throttling import AnonRateThrottle, UserRateThrottle
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes
from django.utils import timezone
from django.core.cache import cache as django_cache
import hashlib
from urllib.parse import urlencode

from .services import LastFmService
from .serializers import (
    LastFmRequestSerializer, 
    LastFmCacheEntrySerializer, 
    LastFmApiUsageSerializer,
    CacheStatsSerializer,
    ErrorResponseSerializer
)
from .models import LastFmCacheEntry, LastFmApiUsage

logger = logging.getLogger(__name__)


class LastFmCacheAwareThrottle(AnonRateThrottle):
    """Custom rate throttle that checks cache before applying rate limiting"""
    scope = 'lastfm'
    rate = '60/min'  # 60 requests per minute per IP
    
    def allow_request(self, request, view):
        """Check if request should be allowed - skip rate limiting for cached requests"""
        # Generate cache key to check if request would be served from cache
        if request.method == 'GET':
            query_params = dict(request.query_params)
            method = query_params.get('method', '')
            
            if method:
                # Sort params for consistent hashing
                sorted_params = sorted(query_params.items())
                query_string = urlencode(sorted_params)
                query_hash = hashlib.sha1(query_string.encode()).hexdigest()
                cache_key = f"lastfm:{method}:{query_hash}"
                
                # Check if data exists in cache
                if django_cache.get(cache_key) is not None:
                    # Data is cached, allow request without rate limiting
                    return True
                
                # Also check database cache
                try:
                    LastFmCacheEntry.objects.get(
                        cache_key=cache_key,
                        expires_at__gt=timezone.now()
                    )
                    # Data is in database cache, allow request
                    return True
                except LastFmCacheEntry.DoesNotExist:
                    pass
        
        # Not cached, apply normal rate limiting
        return super().allow_request(request, view)


class LastFmAPIView(APIView):
    """Main API view for Last.fm requests"""
    
    permission_classes = [AllowAny]  # Can be changed to require authentication
    throttle_classes = [LastFmCacheAwareThrottle]  # Use cache-aware throttle
    
    @extend_schema(
        summary="Last.fm API Proxy",
        description="Proxy requests to Last.fm API with caching. Returns the same JSON structure as Last.fm.",
        parameters=[
            OpenApiParameter(
                name='method',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=True,
                description='Last.fm API method (e.g., artist.getSimilar)',
                enum=['artist.getSimilar', 'artist.getInfo', 'artist.getTopTracks', 'artist.getTopAlbums']
            ),
            OpenApiParameter(
                name='artist',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Artist name'
            ),
            OpenApiParameter(
                name='limit',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Number of results to return (default: 20, max: 200)'
            ),
            OpenApiParameter(
                name='mbid',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='MusicBrainz ID'
            ),
        ],
        responses={
            200: OpenApiTypes.OBJECT,
            400: ErrorResponseSerializer,
            429: ErrorResponseSerializer,
            502: ErrorResponseSerializer,
        },
        tags=['Last.fm Cache API']
    )
    def get(self, request):
        """Handle GET request for Last.fm API proxy"""
        
        # Validate request parameters
        serializer = LastFmRequestSerializer(data=request.query_params)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 400,
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        method = validated_data.pop('method')
        
        # Remove client-sent parameters that should not be passed to the service
        validated_data.pop('api_key', None)  # Server uses its own API key
        validated_data.pop('format', None)   # Server always uses JSON format
        
        # Remove None values from params
        params = {k: v for k, v in validated_data.items() if v is not None}
        
        try:
            # Initialize service and get data
            service = LastFmService()
            data = service.get_data(method, params)
            
            if data is None:
                return Response(
                    {
                        'error': 502,
                        'message': 'Bad Gateway – Last.fm proxy failed',
                        'links': []
                    },
                    status=status.HTTP_502_BAD_GATEWAY
                )
            
            # Check if Last.fm returned an error
            if 'error' in data:
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            # Return successful response
            return Response(data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error in LastFm API view: {e}")
            return Response(
                {
                    'error': 500,
                    'message': 'Internal server error',
                    'links': []
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@extend_schema(
    summary="Last.fm Similar Artists",
    description="Get similar artists for a given artist (convenience endpoint)",
    parameters=[
        OpenApiParameter(
            name='artist',
            type=OpenApiTypes.STR,
            location=OpenApiParameter.QUERY,
            required=True,
            description='Artist name'
        ),
        OpenApiParameter(
            name='limit',
            type=OpenApiTypes.INT,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Number of similar artists to return (default: 20)'
        ),
    ],
    responses={
        200: OpenApiTypes.OBJECT,
        400: ErrorResponseSerializer,
        502: ErrorResponseSerializer,
    },
    tags=['Last.fm Cache API']
)
@api_view(['GET'])
@permission_classes([AllowAny])
def get_similar_artists(request):
    """Get similar artists for a given artist"""
    
    artist = request.query_params.get('artist')
    if not artist:
        return Response(
            {
                'error': 400,
                'message': 'Artist parameter is required',
                'links': []
            },
            status=status.HTTP_400_BAD_REQUEST
        )
    
    limit = request.query_params.get('limit', 20)
    try:
        limit = int(limit)
        if limit < 1 or limit > 200:
            limit = 20
    except (ValueError, TypeError):
        limit = 20
    
    try:
        service = LastFmService()
        data = service.get_similar_artists(artist, limit)
        
        if data is None:
            return Response(
                {
                    'error': 502,
                    'message': 'Failed to get similar artists',
                    'links': []
                },
                status=status.HTTP_502_BAD_GATEWAY
            )
        
        return Response(data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error getting similar artists: {e}")
        return Response(
            {
                'error': 500,
                'message': 'Internal server error',
                'links': []
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


class CacheStatsView(APIView):
    """View for cache statistics"""
    
    permission_classes = [AllowAny]  # Can be restricted to admin users
    
    @extend_schema(
        summary="Get Cache Statistics",
        description="Get statistics about the Last.fm cache performance",
        responses={
            200: CacheStatsSerializer,
        },
        tags=['Last.fm Cache Admin']
    )
    def get(self, request):
        """Get cache statistics"""
        try:
            service = LastFmService()
            stats = service.get_cache_stats()
            
            serializer = CacheStatsSerializer(stats)
            return Response(serializer.data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return Response(
                {
                    'error': 500,
                    'message': 'Error retrieving cache statistics'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CacheManagementView(APIView):
    """View for cache management operations"""
    
    permission_classes = [AllowAny]  # Should be restricted to admin users in production
    
    @extend_schema(
        summary="Clean Expired Cache",
        description="Remove expired cache entries from the database",
        responses={
            200: OpenApiTypes.OBJECT,
            500: ErrorResponseSerializer,
        },
        tags=['Last.fm Cache Admin']
    )
    def delete(self, request):
        """Clean up expired cache entries"""
        try:
            service = LastFmService()
            deleted_count = service.cleanup_expired_cache()
            
            return Response(
                {
                    'message': f'Successfully cleaned up {deleted_count} expired cache entries',
                    'deleted_count': deleted_count
                },
                status=status.HTTP_200_OK
            )
            
        except Exception as e:
            logger.error(f"Error cleaning cache: {e}")
            return Response(
                {
                    'error': 500,
                    'message': 'Error cleaning expired cache entries'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CacheEntriesView(APIView):
    """View for browsing cache entries"""
    
    permission_classes = [AllowAny]  # Should be restricted to admin users in production
    
    @extend_schema(
        summary="List Cache Entries",
        description="Get a list of cache entries with pagination",
        parameters=[
            OpenApiParameter(
                name='method',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Filter by Last.fm method'
            ),
            OpenApiParameter(
                name='expired',
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Filter by expiration status'
            ),
        ],
        responses={
            200: LastFmCacheEntrySerializer(many=True),
        },
        tags=['Last.fm Cache Admin']
    )
    def get(self, request):
        """Get cache entries with optional filtering"""
        try:
            queryset = LastFmCacheEntry.objects.all()
            
            # Filter by method if provided
            method = request.query_params.get('method')
            if method:
                queryset = queryset.filter(method=method)
            
            # Filter by expiration status if provided
            expired = request.query_params.get('expired')
            if expired is not None:
                if expired.lower() in ['true', '1']:
                    queryset = queryset.filter(expires_at__lt=timezone.now())
                elif expired.lower() in ['false', '0']:
                    queryset = queryset.filter(expires_at__gte=timezone.now())
            
            # Order by last accessed
            queryset = queryset.order_by('-last_accessed')[:100]  # Limit to 100 entries
            
            serializer = LastFmCacheEntrySerializer(queryset, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error getting cache entries: {e}")
            return Response(
                {
                    'error': 500,
                    'message': 'Error retrieving cache entries'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UsageStatsView(APIView):
    """View for API usage statistics"""
    
    permission_classes = [AllowAny]  # Should be restricted to admin users in production
    
    @extend_schema(
        summary="Get Usage Statistics",
        description="Get API usage statistics by date and method",
        parameters=[
            OpenApiParameter(
                name='days',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Number of days to include (default: 7)'
            ),
        ],
        responses={
            200: LastFmApiUsageSerializer(many=True),
        },
        tags=['Last.fm Cache Admin']
    )
    def get(self, request):
        """Get usage statistics"""
        try:
            days = request.query_params.get('days', 7)
            try:
                days = int(days)
                if days < 1 or days > 90:
                    days = 7
            except (ValueError, TypeError):
                days = 7
            
            from datetime import date, timedelta
            start_date = date.today() - timedelta(days=days-1)
            
            queryset = LastFmApiUsage.objects.filter(
                date__gte=start_date
            ).order_by('-date', 'method')
            
            serializer = LastFmApiUsageSerializer(queryset, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error getting usage stats: {e}")
            return Response(
                {
                    'error': 500,
                    'message': 'Error retrieving usage statistics'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
