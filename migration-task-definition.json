{"family": "bopmaps-prod-app-migration", "taskRoleArn": "arn:aws:iam::296028253727:role/bopmaps-prod-ecs-task", "executionRoleArn": "arn:aws:iam::296028253727:role/bopmaps-prod-ecs-task-execution", "networkMode": "awsvpc", "containerDefinitions": [{"name": "app", "image": "296028253727.dkr.ecr.us-east-1.amazonaws.com/bopmaps-prod:latest", "cpu": 0, "portMappings": [{"containerPort": 8000, "hostPort": 8000, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DEBUG", "value": "False"}, {"name": "ENVIRONMENT", "value": "prod"}, {"name": "DJANGO_SETTINGS_MODULE", "value": "bopmaps.settings"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DATABASE_URL", "valueFrom": "arn:aws:secretsmanager:us-east-1:296028253727:secret:bopmaps-prod-database-url-lw24tu-hG2Ill"}, {"name": "SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:296028253727:secret:bopmaps-prod-django-secret-b2exfo-sE34Ve"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/bopmaps-prod/app", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8000/health/ || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}, "systemControls": [], "command": ["python", "manage.py", "migrate"]}], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048"}