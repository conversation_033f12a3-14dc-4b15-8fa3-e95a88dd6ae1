from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import UserBlock, Report, ReportType, ModerationAction, AppealRequest


@admin.register(ReportType)
class ReportTypeAdmin(admin.ModelAdmin):
    """Admin interface for ReportType model"""
    list_display = ['name', 'severity_level', 'is_active']
    list_filter = ['is_active', 'severity_level']
    search_fields = ['name', 'description']
    ordering = ['severity_level', 'name']
    
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'severity_level', 'is_active')
        }),
    )


@admin.register(UserBlock)
class UserBlockAdmin(admin.ModelAdmin):
    """Admin interface for UserBlock model"""
    list_display = ['blocker_link', 'blocked_link', 'reason_preview', 'created_at']
    list_filter = ['created_at']
    search_fields = ['blocker__username', 'blocked__username', 'reason']
    readonly_fields = ['created_at', 'updated_at']
    raw_id_fields = ['blocker', 'blocked']
    
    fieldsets = (
        (None, {
            'fields': ('blocker', 'blocked', 'reason')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def blocker_link(self, obj):
        """Link to blocker user"""
        url = reverse('admin:users_user_change', args=[obj.blocker.pk])
        return format_html('<a href="{}">{}</a>', url, obj.blocker.username)
    blocker_link.short_description = 'Blocker'
    
    def blocked_link(self, obj):
        """Link to blocked user"""
        url = reverse('admin:users_user_change', args=[obj.blocked.pk])
        return format_html('<a href="{}">{}</a>', url, obj.blocked.username)
    blocked_link.short_description = 'Blocked User'
    
    def reason_preview(self, obj):
        """Short preview of reason"""
        if obj.reason:
            return obj.reason[:50] + '...' if len(obj.reason) > 50 else obj.reason
        return '-'
    reason_preview.short_description = 'Reason'


@admin.register(Report)
class ReportAdmin(admin.ModelAdmin):
    """Admin interface for Report model"""
    list_display = [
        'id', 'reporter_link', 'reported_user_link', 'report_type', 
        'status', 'assigned_moderator_link', 'created_at'
    ]
    list_filter = ['status', 'report_type', 'created_at', 'resolved_at']
    search_fields = [
        'reporter__username', 'reported_user__username', 
        'description', 'moderator_notes'
    ]
    readonly_fields = ['created_at', 'updated_at', 'ip_address', 'user_agent']
    raw_id_fields = ['reporter', 'reported_user', 'assigned_moderator']
    list_select_related = ['reporter', 'reported_user', 'report_type', 'assigned_moderator']
    
    fieldsets = (
        ('Report Details', {
            'fields': ('reporter', 'reported_user', 'report_type', 'description')
        }),
        ('Status', {
            'fields': ('status', 'assigned_moderator', 'moderator_notes', 'resolution_notes')
        }),
        ('Technical Info', {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'resolved_at'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['assign_to_self', 'mark_under_review', 'mark_resolved']
    
    def reporter_link(self, obj):
        """Link to reporter user"""
        url = reverse('admin:users_user_change', args=[obj.reporter.pk])
        return format_html('<a href="{}">{}</a>', url, obj.reporter.username)
    reporter_link.short_description = 'Reporter'
    
    def reported_user_link(self, obj):
        """Link to reported user"""
        if obj.reported_user:
            url = reverse('admin:users_user_change', args=[obj.reported_user.pk])
            return format_html('<a href="{}">{}</a>', url, obj.reported_user.username)
        return '-'
    reported_user_link.short_description = 'Reported User'
    
    def assigned_moderator_link(self, obj):
        """Link to assigned moderator"""
        if obj.assigned_moderator:
            url = reverse('admin:users_user_change', args=[obj.assigned_moderator.pk])
            return format_html('<a href="{}">{}</a>', url, obj.assigned_moderator.username)
        return '-'
    assigned_moderator_link.short_description = 'Assigned Moderator'
    
    def assign_to_self(self, request, queryset):
        """Assign selected reports to current user"""
        updated = queryset.filter(assigned_moderator__isnull=True).update(
            assigned_moderator=request.user,
            status='under_review'
        )
        self.message_user(request, f'{updated} reports assigned to you.')
    assign_to_self.short_description = 'Assign to me'
    
    def mark_under_review(self, request, queryset):
        """Mark selected reports as under review"""
        updated = queryset.filter(status='pending').update(status='under_review')
        self.message_user(request, f'{updated} reports marked as under review.')
    mark_under_review.short_description = 'Mark as under review'
    
    def mark_resolved(self, request, queryset):
        """Mark selected reports as resolved"""
        updated = queryset.exclude(status='resolved').update(
            status='resolved',
            resolved_at=timezone.now()
        )
        self.message_user(request, f'{updated} reports marked as resolved.')
    mark_resolved.short_description = 'Mark as resolved'


@admin.register(ModerationAction)
class ModerationActionAdmin(admin.ModelAdmin):
    """Admin interface for ModerationAction model"""
    list_display = [
        'id', 'target_user_link', 'action_type', 'moderator_link',
        'duration_days', 'is_active', 'expires_at', 'created_at'
    ]
    list_filter = ['action_type', 'is_active', 'created_at', 'expires_at']
    search_fields = ['target_user__username', 'moderator__username', 'reason', 'notes']
    readonly_fields = ['created_at', 'updated_at']
    raw_id_fields = ['moderator', 'target_user', 'report']
    list_select_related = ['moderator', 'target_user', 'report']
    
    fieldsets = (
        ('Action Details', {
            'fields': ('report', 'moderator', 'target_user', 'action_type')
        }),
        ('Duration & Expiry', {
            'fields': ('duration_days', 'expires_at', 'is_active')
        }),
        ('Details', {
            'fields': ('reason', 'notes')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def target_user_link(self, obj):
        """Link to target user"""
        url = reverse('admin:users_user_change', args=[obj.target_user.pk])
        return format_html('<a href="{}">{}</a>', url, obj.target_user.username)
    target_user_link.short_description = 'Target User'
    
    def moderator_link(self, obj):
        """Link to moderator"""
        url = reverse('admin:users_user_change', args=[obj.moderator.pk])
        return format_html('<a href="{}">{}</a>', url, obj.moderator.username)
    moderator_link.short_description = 'Moderator'


@admin.register(AppealRequest)
class AppealRequestAdmin(admin.ModelAdmin):
    """Admin interface for AppealRequest model"""
    list_display = [
        'id', 'user_link', 'moderation_action_preview',
        'status', 'reviewed_by_link', 'created_at'
    ]
    list_filter = ['status', 'created_at', 'reviewed_at']
    search_fields = ['user__username', 'appeal_reason', 'review_notes']
    readonly_fields = ['created_at', 'updated_at']
    raw_id_fields = ['user', 'moderation_action', 'reviewed_by']
    list_select_related = ['user', 'moderation_action', 'reviewed_by']
    
    fieldsets = (
        ('Appeal Details', {
            'fields': ('user', 'moderation_action', 'appeal_reason')
        }),
        ('Review', {
            'fields': ('status', 'reviewed_by', 'review_notes', 'reviewed_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['approve_appeals', 'deny_appeals']
    
    def user_link(self, obj):
        """Link to user"""
        url = reverse('admin:users_user_change', args=[obj.user.pk])
        return format_html('<a href="{}">{}</a>', url, obj.user.username)
    user_link.short_description = 'User'
    
    def moderation_action_preview(self, obj):
        """Preview of moderation action"""
        return f"{obj.moderation_action.action_type} for {obj.moderation_action.target_user.username}"
    moderation_action_preview.short_description = 'Moderation Action'
    
    def reviewed_by_link(self, obj):
        """Link to reviewer"""
        if obj.reviewed_by:
            url = reverse('admin:users_user_change', args=[obj.reviewed_by.pk])
            return format_html('<a href="{}">{}</a>', url, obj.reviewed_by.username)
        return '-'
    reviewed_by_link.short_description = 'Reviewed By'
    
    def approve_appeals(self, request, queryset):
        """Approve selected appeals"""
        updated = 0
        for appeal in queryset.filter(status='pending'):
            appeal.status = 'approved'
            appeal.reviewed_by = request.user
            appeal.reviewed_at = timezone.now()
            appeal.save()
            
            # Deactivate the moderation action
            appeal.moderation_action.is_active = False
            appeal.moderation_action.save()
            updated += 1
        
        self.message_user(request, f'{updated} appeals approved.')
    approve_appeals.short_description = 'Approve selected appeals'
    
    def deny_appeals(self, request, queryset):
        """Deny selected appeals"""
        updated = queryset.filter(status='pending').update(
            status='denied',
            reviewed_by=request.user,
            reviewed_at=timezone.now()
        )
        self.message_user(request, f'{updated} appeals denied.')
    deny_appeals.short_description = 'Deny selected appeals'
