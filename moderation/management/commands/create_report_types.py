from django.core.management.base import BaseCommand
from moderation.models import ReportType


class Command(BaseCommand):
    help = 'Create default report types for the moderation system'
    
    def handle(self, *args, **options):
        report_types = [
            {
                'name': 'Harassment',
                'description': 'User is harassing, bullying, or intimidating others',
                'severity_level': 5
            },
            {
                'name': 'Hate Speech',
                'description': 'Content contains hate speech or discrimination',
                'severity_level': 5
            },
            {
                'name': 'Spam',
                'description': 'User is posting spam or repetitive content',
                'severity_level': 2
            },
            {
                'name': 'Inappropriate Content',
                'description': 'Content is inappropriate, vulgar, or offensive',
                'severity_level': 3
            },
            {
                'name': 'Fake Account',
                'description': 'This appears to be a fake or impersonation account',
                'severity_level': 4
            },
            {
                'name': 'Copyright Violation',
                'description': 'User is posting copyrighted content without permission',
                'severity_level': 3
            },
            {
                'name': 'Personal Information',
                'description': 'User is sharing personal information without consent',
                'severity_level': 4
            },
            {
                'name': 'Scam or Fraud',
                'description': 'User is involved in scamming or fraudulent activities',
                'severity_level': 5
            },
            {
                'name': 'Self-Harm',
                'description': 'Content promotes or depicts self-harm',
                'severity_level': 5
            },
            {
                'name': 'Other',
                'description': 'Other violations not covered by the above categories',
                'severity_level': 1
            }
        ]
        
        created_count = 0
        
        for report_type_data in report_types:
            report_type, created = ReportType.objects.get_or_create(
                name=report_type_data['name'],
                defaults={
                    'description': report_type_data['description'],
                    'severity_level': report_type_data['severity_level'],
                    'is_active': True
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Created report type: {report_type.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'⚠️  Report type already exists: {report_type.name}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'\n🎉 Created {created_count} new report types!')
        )
        
        total_count = ReportType.objects.count()
        self.stdout.write(
            self.style.SUCCESS(f'📊 Total report types: {total_count}')
        ) 