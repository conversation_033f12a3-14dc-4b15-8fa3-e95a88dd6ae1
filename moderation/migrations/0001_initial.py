# Generated by Django 4.2.7 on 2025-06-25 21:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("contenttypes", "0002_remove_content_type_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="ReportType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField()),
                ("is_active", models.<PERSON>oleanField(default=True)),
                ("severity_level", models.PositiveIntegerField(default=1)),
            ],
            options={
                "ordering": ["severity_level", "name"],
            },
        ),
        migrations.CreateModel(
            name="Report",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("object_id", models.PositiveIntegerField(blank=True, null=True)),
                ("description", models.TextField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("under_review", "Under Review"),
                            ("resolved", "Resolved"),
                            ("dismissed", "Dismissed"),
                            ("escalated", "Escalated"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("moderator_notes", models.TextField(blank=True)),
                ("resolution_notes", models.TextField(blank=True)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                (
                    "assigned_moderator",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "content_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "report_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="moderation.reporttype",
                    ),
                ),
                (
                    "reported_user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reports_received",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "reporter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reports_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ModerationAction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("warning", "Warning Issued"),
                            ("temporary_ban", "Temporary Ban"),
                            ("permanent_ban", "Permanent Ban"),
                            ("content_removal", "Content Removed"),
                            ("account_suspension", "Account Suspended"),
                            ("no_action", "No Action Taken"),
                        ],
                        max_length=20,
                    ),
                ),
                ("duration_days", models.PositiveIntegerField(blank=True, null=True)),
                ("reason", models.TextField()),
                ("notes", models.TextField(blank=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "moderator",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="moderation_actions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "report",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="actions",
                        to="moderation.report",
                    ),
                ),
                (
                    "target_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="moderation_actions_received",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="AppealRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("appeal_reason", models.TextField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("under_review", "Under Review"),
                            ("approved", "Approved"),
                            ("denied", "Denied"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("review_notes", models.TextField(blank=True)),
                ("reviewed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "moderation_action",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="appeals",
                        to="moderation.moderationaction",
                    ),
                ),
                (
                    "reviewed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="reviewed_appeals",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="appeal_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="UserBlock",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("reason", models.TextField(blank=True, null=True)),
                (
                    "blocked",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="blocks_received",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "blocker",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="blocks_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["blocker", "created_at"],
                        name="moderation__blocker_4d9a0a_idx",
                    ),
                    models.Index(
                        fields=["blocked", "created_at"],
                        name="moderation__blocked_526b16_idx",
                    ),
                ],
                "unique_together": {("blocker", "blocked")},
            },
        ),
        migrations.AddIndex(
            model_name="report",
            index=models.Index(
                fields=["reporter", "created_at"], name="moderation__reporte_d53858_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="report",
            index=models.Index(
                fields=["reported_user", "status"],
                name="moderation__reporte_1240a6_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="report",
            index=models.Index(
                fields=["status", "created_at"], name="moderation__status_2a84ce_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="report",
            index=models.Index(
                fields=["report_type", "status"], name="moderation__report__7ab5dc_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="report",
            index=models.Index(
                fields=["assigned_moderator", "status"],
                name="moderation__assigne_c484c9_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="report",
            unique_together={
                ("reporter", "reported_user", "content_type", "object_id")
            },
        ),
        migrations.AddIndex(
            model_name="moderationaction",
            index=models.Index(
                fields=["target_user", "action_type"],
                name="moderation__target__1dad74_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="moderationaction",
            index=models.Index(
                fields=["moderator", "created_at"],
                name="moderation__moderat_b59e8d_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="moderationaction",
            index=models.Index(
                fields=["is_active", "expires_at"],
                name="moderation__is_acti_2b3037_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="appealrequest",
            index=models.Index(
                fields=["user", "status"], name="moderation__user_id_5f03c5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="appealrequest",
            index=models.Index(
                fields=["status", "created_at"], name="moderation__status_9a56b1_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="appealrequest",
            unique_together={("user", "moderation_action")},
        ),
    ]
