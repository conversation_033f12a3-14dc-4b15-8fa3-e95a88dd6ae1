from django.db import models
from django.conf import settings
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from bopmaps.models import TimeStampedModel
import logging

logger = logging.getLogger('bopmaps')


class UserBlock(TimeStampedModel):
    """
    Model for blocking relationships between users
    """
    blocker = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='blocks_made'
    )
    blocked = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='blocks_received'
    )
    reason = models.TextField(blank=True, null=True)
    
    class Meta:
        unique_together = ('blocker', 'blocked')
        indexes = [
            models.Index(fields=['blocker', 'created_at']),
            models.Index(fields=['blocked', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.blocker.username} blocked {self.blocked.username}"
    
    def save(self, *args, **kwargs):
        # Prevent self-blocking
        if self.blocker == self.blocked:
            raise ValueError("Users cannot block themselves")
        super().save(*args, **kwargs)


class ReportType(models.Model):
    """
    Different types of reports that can be made
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    is_active = models.BooleanField(default=True)
    severity_level = models.PositiveIntegerField(default=1)  # 1-5, higher is more severe
    
    class Meta:
        ordering = ['severity_level', 'name']
    
    def __str__(self):
        return self.name


class Report(TimeStampedModel):
    """
    Model for reporting users or content
    """
    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('under_review', 'Under Review'),
        ('resolved', 'Resolved'),
        ('dismissed', 'Dismissed'),
        ('escalated', 'Escalated'),
    ]
    
    reporter = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='reports_made'
    )
    reported_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='reports_received',
        null=True,
        blank=True
    )
    
    # Generic relation for reporting any content
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    report_type = models.ForeignKey(
        ReportType,
        on_delete=models.CASCADE
    )
    description = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Admin fields
    assigned_moderator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='assigned_reports',
        null=True,
        blank=True
    )
    moderator_notes = models.TextField(blank=True)
    resolution_notes = models.TextField(blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    
    # System fields
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['reporter', 'created_at']),
            models.Index(fields=['reported_user', 'status']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['report_type', 'status']),
            models.Index(fields=['assigned_moderator', 'status']),
        ]
        unique_together = ('reporter', 'reported_user', 'content_type', 'object_id')
    
    def __str__(self):
        if self.reported_user:
            return f"Report against {self.reported_user.username} by {self.reporter.username}"
        return f"Content report by {self.reporter.username}"
    
    def save(self, *args, **kwargs):
        # Set resolved_at when status changes to resolved
        if self.status == 'resolved' and not self.resolved_at:
            self.resolved_at = timezone.now()
        
        # Prevent self-reporting
        if self.reported_user and self.reporter == self.reported_user:
            raise ValueError("Users cannot report themselves")
            
        super().save(*args, **kwargs)
    
    def resolve(self, moderator, resolution_notes=''):
        """Mark report as resolved"""
        self.status = 'resolved'
        self.assigned_moderator = moderator
        self.resolution_notes = resolution_notes
        self.resolved_at = timezone.now()
        self.save()
        logger.info(f"Report {self.id} resolved by {moderator.username}")
    
    def dismiss(self, moderator, resolution_notes=''):
        """Dismiss report as invalid"""
        self.status = 'dismissed'
        self.assigned_moderator = moderator
        self.resolution_notes = resolution_notes
        self.resolved_at = timezone.now()
        self.save()
        logger.info(f"Report {self.id} dismissed by {moderator.username}")


class ModerationAction(TimeStampedModel):
    """
    Track moderation actions taken as a result of reports
    """
    ACTION_TYPES = [
        ('warning', 'Warning Issued'),
        ('temporary_ban', 'Temporary Ban'),
        ('permanent_ban', 'Permanent Ban'),
        ('content_removal', 'Content Removed'),
        ('account_suspension', 'Account Suspended'),
        ('no_action', 'No Action Taken'),
    ]
    
    report = models.ForeignKey(
        Report,
        on_delete=models.CASCADE,
        related_name='actions'
    )
    moderator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='moderation_actions'
    )
    target_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='moderation_actions_received'
    )
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    duration_days = models.PositiveIntegerField(null=True, blank=True)
    reason = models.TextField()
    notes = models.TextField(blank=True)
    
    # For tracking temporary actions
    expires_at = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['target_user', 'action_type']),
            models.Index(fields=['moderator', 'created_at']),
            models.Index(fields=['is_active', 'expires_at']),
        ]
    
    def __str__(self):
        return f"{self.action_type} for {self.target_user.username} by {self.moderator.username}"
    
    def save(self, *args, **kwargs):
        # Set expiration for temporary actions
        if self.action_type == 'temporary_ban' and self.duration_days:
            self.expires_at = timezone.now() + timezone.timedelta(days=self.duration_days)
        super().save(*args, **kwargs)


class AppealRequest(TimeStampedModel):
    """
    Model for users to appeal moderation actions
    """
    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('under_review', 'Under Review'),
        ('approved', 'Approved'),
        ('denied', 'Denied'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='appeal_requests'
    )
    moderation_action = models.ForeignKey(
        ModerationAction,
        on_delete=models.CASCADE,
        related_name='appeals'
    )
    appeal_reason = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Review fields
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='reviewed_appeals',
        null=True,
        blank=True
    )
    review_notes = models.TextField(blank=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        unique_together = ('user', 'moderation_action')
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['status', 'created_at']),
        ]
    
    def __str__(self):
        return f"Appeal by {self.user.username} for {self.moderation_action.action_type}"
