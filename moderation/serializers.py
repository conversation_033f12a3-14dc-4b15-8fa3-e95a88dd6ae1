from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from .models import UserBlock, Report, ReportType, ModerationAction, AppealRequest
from users.serializers import UserPublicProfileSerializer
from bopmaps.serializers import BaseSerializer

User = get_user_model()


class UserBlockSerializer(BaseSerializer):
    """Serializer for UserBlock model"""
    blocker = UserPublicProfileSerializer(read_only=True)
    blocked = UserPublicProfileSerializer(read_only=True)
    blocked_user_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = UserBlock
        fields = ['id', 'blocker', 'blocked', 'blocked_user_id', 'reason', 'created_at']
        read_only_fields = ['id', 'blocker', 'created_at']
    
    def validate_blocked_user_id(self, value):
        """Validate the blocked user exists"""
        try:
            user = User.objects.get(id=value)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found.")
    
    def validate(self, data):
        """Validate block creation"""
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            blocked_user_id = data.get('blocked_user_id')
            if request.user.id == blocked_user_id:
                raise serializers.ValidationError("You cannot block yourself.")
        return data
    
    def create(self, validated_data):
        """Create a new block"""
        request = self.context.get('request')
        blocked_user_id = validated_data.pop('blocked_user_id')
        blocked_user = User.objects.get(id=blocked_user_id)
        
        return UserBlock.objects.create(
            blocker=request.user,
            blocked=blocked_user,
            **validated_data
        )


class ReportTypeSerializer(serializers.ModelSerializer):
    """Serializer for ReportType model"""
    
    class Meta:
        model = ReportType
        fields = ['id', 'name', 'description', 'severity_level']


class ReportSerializer(BaseSerializer):
    """Serializer for Report model"""
    reporter = UserPublicProfileSerializer(read_only=True)
    reported_user = UserPublicProfileSerializer(read_only=True)
    report_type = ReportTypeSerializer(read_only=True)
    assigned_moderator = UserPublicProfileSerializer(read_only=True)
    
    # Write-only fields for creating reports
    reported_user_id = serializers.IntegerField(write_only=True, required=False)
    report_type_id = serializers.IntegerField(write_only=True)
    content_type_id = serializers.IntegerField(write_only=True, required=False)
    object_id = serializers.IntegerField(write_only=True, required=False)
    
    class Meta:
        model = Report
        fields = [
            'id', 'reporter', 'reported_user', 'reported_user_id', 
            'content_type', 'object_id', 'content_type_id', 'report_type', 
            'report_type_id', 'description', 'status', 'assigned_moderator',
            'moderator_notes', 'resolution_notes', 'resolved_at', 'created_at'
        ]
        read_only_fields = [
            'id', 'reporter', 'status', 'assigned_moderator', 
            'moderator_notes', 'resolution_notes', 'resolved_at', 'created_at'
        ]
    
    def validate_report_type_id(self, value):
        """Validate report type exists and is active"""
        try:
            report_type = ReportType.objects.get(id=value, is_active=True)
            return value
        except ReportType.DoesNotExist:
            raise serializers.ValidationError("Invalid or inactive report type.")
    
    def validate_reported_user_id(self, value):
        """Validate reported user exists"""
        if value:
            try:
                User.objects.get(id=value)
                return value
            except User.DoesNotExist:
                raise serializers.ValidationError("Reported user not found.")
        return value
    
    def validate(self, data):
        """Validate report creation"""
        request = self.context.get('request')
        
        # Must report either a user or content, not both
        has_user = data.get('reported_user_id')
        has_content = data.get('content_type_id') and data.get('object_id')
        
        if not has_user and not has_content:
            raise serializers.ValidationError(
                "Must specify either a user to report or content to report."
            )
        
        if has_user and has_content:
            raise serializers.ValidationError(
                "Cannot report both a user and content in the same report."
            )
        
        # Prevent self-reporting
        if request and hasattr(request, 'user'):
            if has_user and request.user.id == data.get('reported_user_id'):
                raise serializers.ValidationError("You cannot report yourself.")
        
        return data
    
    def create(self, validated_data):
        """Create a new report"""
        request = self.context.get('request')
        
        # Extract IDs and get objects
        reported_user_id = validated_data.pop('reported_user_id', None)
        report_type_id = validated_data.pop('report_type_id')
        content_type_id = validated_data.pop('content_type_id', None)
        
        reported_user = None
        if reported_user_id:
            reported_user = User.objects.get(id=reported_user_id)
        
        report_type = ReportType.objects.get(id=report_type_id)
        
        content_type = None
        if content_type_id:
            content_type = ContentType.objects.get(id=content_type_id)
        
        # Get client IP and user agent
        ip_address = None
        user_agent = ''
        if request:
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip_address = x_forwarded_for.split(',')[0]
            else:
                ip_address = request.META.get('REMOTE_ADDR')
            user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        return Report.objects.create(
            reporter=request.user,
            reported_user=reported_user,
            report_type=report_type,
            content_type=content_type,
            ip_address=ip_address,
            user_agent=user_agent,
            **validated_data
        )


class ModerationActionSerializer(BaseSerializer):
    """Serializer for ModerationAction model"""
    moderator = UserPublicProfileSerializer(read_only=True)
    target_user = UserPublicProfileSerializer(read_only=True)
    report = ReportSerializer(read_only=True)
    
    class Meta:
        model = ModerationAction
        fields = [
            'id', 'report', 'moderator', 'target_user', 'action_type',
            'duration_days', 'reason', 'notes', 'expires_at', 'is_active',
            'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class AppealRequestSerializer(BaseSerializer):
    """Serializer for AppealRequest model"""
    user = UserPublicProfileSerializer(read_only=True)
    moderation_action = ModerationActionSerializer(read_only=True)
    reviewed_by = UserPublicProfileSerializer(read_only=True)
    moderation_action_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = AppealRequest
        fields = [
            'id', 'user', 'moderation_action', 'moderation_action_id',
            'appeal_reason', 'status', 'reviewed_by', 'review_notes',
            'reviewed_at', 'created_at'
        ]
        read_only_fields = [
            'id', 'user', 'status', 'reviewed_by', 'review_notes',
            'reviewed_at', 'created_at'
        ]
    
    def validate_moderation_action_id(self, value):
        """Validate moderation action exists"""
        try:
            action = ModerationAction.objects.get(id=value)
            # Only allow appeals for actions against the current user
            request = self.context.get('request')
            if request and hasattr(request, 'user'):
                if action.target_user != request.user:
                    raise serializers.ValidationError(
                        "You can only appeal actions taken against you."
                    )
            return value
        except ModerationAction.DoesNotExist:
            raise serializers.ValidationError("Moderation action not found.")
    
    def create(self, validated_data):
        """Create a new appeal request"""
        request = self.context.get('request')
        moderation_action_id = validated_data.pop('moderation_action_id')
        moderation_action = ModerationAction.objects.get(id=moderation_action_id)
        
        return AppealRequest.objects.create(
            user=request.user,
            moderation_action=moderation_action,
            **validated_data
        )


# Simple serializers for quick operations
class BlockUserSerializer(serializers.Serializer):
    """Simple serializer for blocking a user"""
    user_id = serializers.IntegerField()
    reason = serializers.CharField(max_length=500, required=False, allow_blank=True)
    
    def validate_user_id(self, value):
        try:
            User.objects.get(id=value)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found.")


class ReportUserSerializer(serializers.Serializer):
    """Simple serializer for reporting a user"""
    user_id = serializers.IntegerField()
    report_type_id = serializers.IntegerField()
    description = serializers.CharField(max_length=1000)
    
    def validate_user_id(self, value):
        try:
            User.objects.get(id=value)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found.")
    
    def validate_report_type_id(self, value):
        try:
            ReportType.objects.get(id=value, is_active=True)
            return value
        except ReportType.DoesNotExist:
            raise serializers.ValidationError("Invalid report type.") 