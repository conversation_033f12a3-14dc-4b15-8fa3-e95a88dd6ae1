from django.urls import path, include
from rest_framework.routers import De<PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

# Create router for regular user endpoints
router = DefaultRouter()
router.register(r'blocks', views.UserBlockViewSet, basename='userblock')
router.register(r'report-types', views.ReportTypeViewSet, basename='reporttype')
router.register(r'reports', views.ReportViewSet, basename='report')
router.register(r'moderation-actions', views.ModerationActionViewSet, basename='moderationaction')
router.register(r'appeals', views.AppealRequestViewSet, basename='appealrequest')

# Create router for admin endpoints
admin_router = DefaultRouter()
admin_router.register(r'reports', views.AdminReportViewSet, basename='admin-report')
admin_router.register(r'moderation-actions', views.AdminModerationActionViewSet, basename='admin-moderationaction')
admin_router.register(r'appeals', views.AdminAppealViewSet, basename='admin-appeal')

urlpatterns = [
    # Regular user endpoints
    path('', include(router.urls)),
    
    # Admin endpoints
    path('admin/', include(admin_router.urls)),
] 