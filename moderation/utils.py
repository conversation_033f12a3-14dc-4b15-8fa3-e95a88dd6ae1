from django.contrib.auth import get_user_model
from django.db.models import Q
from .models import UserBlock

User = get_user_model()


def is_user_blocked(blocker, blocked):
    """
    Check if one user has blocked another user
    
    Args:
        blocker: User object who might have blocked
        blocked: User object who might be blocked
        
    Returns:
        bool: True if blocked, False otherwise
    """
    return UserBlock.objects.filter(blocker=blocker, blocked=blocked).exists()


def is_mutually_blocked(user1, user2):
    """
    Check if two users have blocked each other
    
    Args:
        user1: First user object
        user2: Second user object
        
    Returns:
        bool: True if either user has blocked the other
    """
    return UserBlock.objects.filter(
        Q(blocker=user1, blocked=user2) |
        Q(blocker=user2, blocked=user1)
    ).exists()


def get_blocked_users(user):
    """
    Get all users blocked by a specific user
    
    Args:
        user: User object
        
    Returns:
        QuerySet: Users that have been blocked by the given user
    """
    return User.objects.filter(blocks_received__blocker=user)


def get_users_who_blocked(user):
    """
    Get all users who have blocked a specific user
    
    Args:
        user: User object
        
    Returns:
        QuerySet: Users who have blocked the given user
    """
    return User.objects.filter(blocks_made__blocked=user)


def filter_blocked_users(queryset, user):
    """
    Filter out users who have been blocked by or have blocked the given user
    
    Args:
        queryset: QuerySet of users to filter
        user: User object to check blocks against
        
    Returns:
        QuerySet: Filtered queryset excluding blocked users
    """
    # Get IDs of users who are blocked or have blocked the current user
    blocked_user_ids = UserBlock.objects.filter(
        Q(blocker=user) | Q(blocked=user)
    ).values_list('blocked_id', 'blocker_id')
    
    # Flatten the list of IDs
    excluded_ids = set()
    for blocked_id, blocker_id in blocked_user_ids:
        if blocker_id == user.id:
            excluded_ids.add(blocked_id)
        elif blocked_id == user.id:
            excluded_ids.add(blocker_id)
    
    return queryset.exclude(id__in=excluded_ids)


def can_interact_with_user(user1, user2):
    """
    Check if two users can interact (not blocked by each other)
    
    Args:
        user1: First user object
        user2: Second user object
        
    Returns:
        bool: True if users can interact, False if blocked
    """
    return not is_mutually_blocked(user1, user2)


class BlockingMixin:
    """
    Mixin to add blocking functionality to viewsets
    """
    
    def filter_queryset_for_blocking(self, queryset):
        """
        Filter queryset to exclude blocked users
        """
        if hasattr(self.request, 'user') and self.request.user.is_authenticated:
            return filter_blocked_users(queryset, self.request.user)
        return queryset
    
    def check_user_can_access(self, target_user):
        """
        Check if current user can access target user's content
        
        Args:
            target_user: User object to check access for
            
        Returns:
            bool: True if access allowed, False if blocked
        """
        if not hasattr(self.request, 'user') or not self.request.user.is_authenticated:
            return True
            
        return can_interact_with_user(self.request.user, target_user) 