from django.shortcuts import render
from rest_framework import viewsets, status, permissions, mixins
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from django.contrib.auth import get_user_model
from django.db.models import Q, Count
from django.db import transaction
from django.utils import timezone
from datetime import timedelta
import logging

from .models import UserBlock, Report, ReportType, ModerationAction, AppealRequest
from .serializers import (
    UserBlockSerializer, ReportSerializer, ReportTypeSerializer,
    ModerationActionSerializer, AppealRequestSerializer,
    BlockUserSerializer, ReportUserSerializer
)
from friends.models import Friend
from bopmaps.views import BaseModelViewSet
from bopmaps.utils import create_error_response
from bopmaps.permissions import IsOwner

logger = logging.getLogger('bopmaps')
User = get_user_model()


class UserBlockViewSet(BaseModelViewSet):
    """
    ViewSet for managing user blocks
    """
    serializer_class = UserBlockSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Return blocks created by the current user"""
        return UserBlock.objects.filter(
            blocker=self.request.user
        ).select_related('blocker', 'blocked').order_by('-created_at')
    
    def create(self, request, *args, **kwargs):
        """Block a user"""
        try:
            with transaction.atomic():
                # Create the block
                response = super().create(request, *args, **kwargs)
                
                if response.status_code == status.HTTP_201_CREATED:
                    block_data = response.data
                    blocked_user_id = request.data.get('blocked_user_id')
                    
                    # Remove any existing friendship
                    Friend.objects.filter(
                        Q(requester=request.user, recipient_id=blocked_user_id) |
                        Q(requester_id=blocked_user_id, recipient=request.user)
                    ).delete()
                    
                    logger.info(f"User {request.user.username} blocked user {blocked_user_id}")
                
                return response
                
        except Exception as e:
            logger.error(f"Error blocking user: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def destroy(self, request, *args, **kwargs):
        """Unblock a user"""
        try:
            block = self.get_object()
            blocked_user = block.blocked
            
            response = super().destroy(request, *args, **kwargs)
            
            if response.status_code == status.HTTP_204_NO_CONTENT:
                logger.info(f"User {request.user.username} unblocked user {blocked_user.username}")
            
            return response
            
        except Exception as e:
            logger.error(f"Error unblocking user: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def block_user(self, request):
        """Quick endpoint to block a user"""
        try:
            serializer = BlockUserSerializer(data=request.data)
            if serializer.is_valid():
                user_id = serializer.validated_data['user_id']
                reason = serializer.validated_data.get('reason', '')
                
                # Check if already blocked
                if UserBlock.objects.filter(blocker=request.user, blocked_id=user_id).exists():
                    return create_error_response("User is already blocked", status.HTTP_400_BAD_REQUEST)
                
                # Prevent self-blocking
                if request.user.id == user_id:
                    return create_error_response("You cannot block yourself", status.HTTP_400_BAD_REQUEST)
                
                with transaction.atomic():
                    # Create block
                    blocked_user = User.objects.get(id=user_id)
                    block = UserBlock.objects.create(
                        blocker=request.user,
                        blocked=blocked_user,
                        reason=reason
                    )
                    
                    # Remove any existing friendship
                    Friend.objects.filter(
                        Q(requester=request.user, recipient=blocked_user) |
                        Q(requester=blocked_user, recipient=request.user)
                    ).delete()
                    
                    logger.info(f"User {request.user.username} blocked user {blocked_user.username}")
                    
                    return Response({
                        "success": True,
                        "message": f"User {blocked_user.username} has been blocked"
                    })
            
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        except User.DoesNotExist:
            return create_error_response("User not found", status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error blocking user: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def unblock_user(self, request):
        """Quick endpoint to unblock a user"""
        try:
            user_id = request.data.get('user_id')
            if not user_id:
                return create_error_response("user_id is required", status.HTTP_400_BAD_REQUEST)
            
            block = UserBlock.objects.filter(
                blocker=request.user,
                blocked_id=user_id
            ).first()
            
            if not block:
                return create_error_response("User is not blocked", status.HTTP_400_BAD_REQUEST)
            
            blocked_user = block.blocked
            block.delete()
            
            logger.info(f"User {request.user.username} unblocked user {blocked_user.username}")
            
            return Response({
                "success": True,
                "message": f"User {blocked_user.username} has been unblocked"
            })
            
        except Exception as e:
            logger.error(f"Error unblocking user: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def is_blocked(self, request):
        """Check if a user is blocked"""
        try:
            user_id = request.query_params.get('user_id')
            if not user_id:
                return create_error_response("user_id parameter is required", status.HTTP_400_BAD_REQUEST)
            
            is_blocked = UserBlock.objects.filter(
                blocker=request.user,
                blocked_id=user_id
            ).exists()
            
            return Response({"is_blocked": is_blocked})
            
        except Exception as e:
            logger.error(f"Error checking block status: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)


class ReportTypeViewSet(mixins.ListModelMixin, viewsets.GenericViewSet):
    """
    ViewSet for listing available report types
    """
    queryset = ReportType.objects.filter(is_active=True).order_by('severity_level', 'name')
    serializer_class = ReportTypeSerializer
    permission_classes = [IsAuthenticated]


class ReportViewSet(BaseModelViewSet):
    """
    ViewSet for managing reports
    """
    serializer_class = ReportSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Return reports created by the current user"""
        return Report.objects.filter(
            reporter=self.request.user
        ).select_related(
            'reporter', 'reported_user', 'report_type', 'assigned_moderator'
        ).order_by('-created_at')
    
    def get_permissions(self):
        """
        Instantiate and return the list of permissions that this view requires.
        """
        if self.action in ['list', 'retrieve', 'create', 'report_user']:
            permission_classes = [IsAuthenticated]
        else:
            permission_classes = [IsAdminUser]  # Only admins can modify reports
        return [permission() for permission in permission_classes]
    
    @action(detail=False, methods=['post'])
    def report_user(self, request):
        """Quick endpoint to report a user"""
        try:
            serializer = ReportUserSerializer(data=request.data)
            if serializer.is_valid():
                user_id = serializer.validated_data['user_id']
                report_type_id = serializer.validated_data['report_type_id']
                description = serializer.validated_data['description']
                
                # Prevent self-reporting
                if request.user.id == user_id:
                    logger.info(f"User {request.user.username} cannot report themselves")
                    return create_error_response("You cannot report yourself", status.HTTP_400_BAD_REQUEST)
                
                # Check if already reported
                existing_report = Report.objects.filter(
                    reporter=request.user,
                    reported_user_id=user_id,
                    report_type_id=report_type_id
                ).first()
                
                if existing_report:
                    logger.info(f"User {request.user.username} has already reported user {user_id} for {report_type.name}")
                    return create_error_response(
                        "You have already reported this user for this reason", 
                        status.HTTP_400_BAD_REQUEST
                    )
                
                # Create report
                reported_user = User.objects.get(id=user_id)
                report_type = ReportType.objects.get(id=report_type_id)
                
                # Get client IP and user agent
                ip_address = None
                user_agent = ''
                x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
                if x_forwarded_for:
                    ip_address = x_forwarded_for.split(',')[0]
                else:
                    ip_address = request.META.get('REMOTE_ADDR')
                user_agent = request.META.get('HTTP_USER_AGENT', '')
                
                report = Report.objects.create(
                    reporter=request.user,
                    reported_user=reported_user,
                    report_type=report_type,
                    description=description,
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                
                logger.info(f"User {request.user.username} reported user {reported_user.username} for {report_type.name}")
                
                return Response({
                    "success": True,
                    "message": "Report submitted successfully",
                    "report_id": report.id
                })
            
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        except User.DoesNotExist:
            return create_error_response("User not found", status.HTTP_404_NOT_FOUND)
        except ReportType.DoesNotExist:
            return create_error_response("Report type not found", status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error reporting user: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)


class ModerationActionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing moderation actions (read-only for users)
    """
    serializer_class = ModerationActionSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Return moderation actions for the current user"""
        return ModerationAction.objects.filter(
            target_user=self.request.user
        ).select_related(
            'moderator', 'target_user', 'report'
        ).order_by('-created_at')


class AppealRequestViewSet(BaseModelViewSet):
    """
    ViewSet for managing appeal requests
    """
    serializer_class = AppealRequestSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Return appeal requests created by the current user"""
        return AppealRequest.objects.filter(
            user=self.request.user
        ).select_related(
            'user', 'moderation_action', 'reviewed_by'
        ).order_by('-created_at')
    
    def get_permissions(self):
        """
        Users can create and view their own appeals.
        Only admins can update appeal status.
        """
        if self.action in ['list', 'retrieve', 'create']:
            permission_classes = [IsAuthenticated]
        else:
            permission_classes = [IsAdminUser]
        return [permission() for permission in permission_classes]


# Admin-only views for moderation management
class AdminReportViewSet(viewsets.ModelViewSet):
    """
    Admin viewset for managing all reports
    """
    queryset = Report.objects.all().select_related(
        'reporter', 'reported_user', 'report_type', 'assigned_moderator'
    ).order_by('-created_at')
    serializer_class = ReportSerializer
    permission_classes = [IsAdminUser]
    
    @action(detail=True, methods=['post'])
    def assign(self, request, pk=None):
        """Assign a report to a moderator"""
        try:
            report = self.get_object()
            report.assigned_moderator = request.user
            report.status = 'under_review'
            report.save()
            
            logger.info(f"Report {report.id} assigned to {request.user.username}")
            
            return Response({
                "success": True,
                "message": "Report assigned successfully"
            })
            
        except Exception as e:
            logger.error(f"Error assigning report: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        """Resolve a report"""
        try:
            report = self.get_object()
            resolution_notes = request.data.get('resolution_notes', '')
            
            report.resolve(request.user, resolution_notes)
            
            return Response({
                "success": True,
                "message": "Report resolved successfully"
            })
            
        except Exception as e:
            logger.error(f"Error resolving report: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def dismiss(self, request, pk=None):
        """Dismiss a report"""
        try:
            report = self.get_object()
            resolution_notes = request.data.get('resolution_notes', '')
            
            report.dismiss(request.user, resolution_notes)
            
            return Response({
                "success": True,
                "message": "Report dismissed successfully"
            })
            
        except Exception as e:
            logger.error(f"Error dismissing report: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)


class AdminModerationActionViewSet(viewsets.ModelViewSet):
    """
    Admin viewset for managing moderation actions
    """
    queryset = ModerationAction.objects.all().select_related(
        'moderator', 'target_user', 'report'
    ).order_by('-created_at')
    serializer_class = ModerationActionSerializer
    permission_classes = [IsAdminUser]


class AdminAppealViewSet(viewsets.ModelViewSet):
    """
    Admin viewset for managing appeals
    """
    queryset = AppealRequest.objects.all().select_related(
        'user', 'moderation_action', 'reviewed_by'
    ).order_by('-created_at')
    serializer_class = AppealRequestSerializer
    permission_classes = [IsAdminUser]
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve an appeal"""
        try:
            appeal = self.get_object()
            review_notes = request.data.get('review_notes', '')
            
            with transaction.atomic():
                appeal.status = 'approved'
                appeal.reviewed_by = request.user
                appeal.review_notes = review_notes
                appeal.reviewed_at = timezone.now()
                appeal.save()
                
                # Deactivate the moderation action
                appeal.moderation_action.is_active = False
                appeal.moderation_action.save()
                
                logger.info(f"Appeal {appeal.id} approved by {request.user.username}")
            
            return Response({
                "success": True,
                "message": "Appeal approved successfully"
            })
            
        except Exception as e:
            logger.error(f"Error approving appeal: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def deny(self, request, pk=None):
        """Deny an appeal"""
        try:
            appeal = self.get_object()
            review_notes = request.data.get('review_notes', '')
            
            appeal.status = 'denied'
            appeal.reviewed_by = request.user
            appeal.review_notes = review_notes
            appeal.reviewed_at = timezone.now()
            appeal.save()
            
            logger.info(f"Appeal {appeal.id} denied by {request.user.username}")
            
            return Response({
                "success": True,
                "message": "Appeal denied"
            })
            
        except Exception as e:
            logger.error(f"Error denying appeal: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
