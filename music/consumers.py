import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.utils import timezone

logger = logging.getLogger('bopmaps')

class MusicChatConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for real-time music chat
    """
    
    async def connect(self):
        """Connect to WebSocket and join user's music chat room"""
        self.user = self.scope['user']
        
        if self.user.is_anonymous:
            await self.close()
            return
        
        # Join user's personal music chat room
        self.room_group_name = f'music_chat_user_{self.user.id}'
        
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        logger.info(f"User {self.user.username} connected to music chat WebSocket")
    
    async def disconnect(self, close_code):
        """Disconnect from WebSocket"""
        if hasattr(self, 'room_group_name'):
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )
        
        logger.info(f"User {self.user.username} disconnected from music chat WebSocket")
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'send_message':
                await self.handle_send_message(data)
            elif message_type == 'typing':
                await self.handle_typing(data)
            elif message_type == 'mark_read':
                await self.handle_mark_read(data)
            elif message_type == 'react':
                await self.handle_reaction(data)
            else:
                await self.send_error('Unknown message type')
                
        except json.JSONDecodeError:
            await self.send_error('Invalid JSON')
        except Exception as e:
            logger.error(f"Error in music chat WebSocket receive: {str(e)}")
            await self.send_error('Internal error')
    
    async def handle_send_message(self, data):
        """Handle sending a music chat message"""
        try:
            conversation_id = data.get('conversation_id')
            message_type = data.get('message_type', 'text')
            text_content = data.get('text_content', '')
            
            # Music data for track recommendations
            track_data = {
                'track_id': data.get('track_id'),
                'track_title': data.get('track_title'),
                'track_artist': data.get('track_artist'),
                'track_album': data.get('track_album'),
                'album_art_url': data.get('album_art_url'),
                'preview_url': data.get('preview_url'),
                'track_url': data.get('track_url'),
                'music_service': data.get('music_service'),
                'duration_ms': data.get('duration_ms'),
            }
            
            # Create message in database
            message = await self.create_message(
                conversation_id, message_type, text_content, track_data
            )
            
            if message:
                # Get the other participant
                other_participant = await self.get_other_participant(conversation_id)
                
                if other_participant:
                    # Send to other participant's room
                    await self.channel_layer.group_send(
                        f'music_chat_user_{other_participant.id}',
                        {
                            'type': 'music_message',
                            'message': await self.serialize_message(message)
                        }
                    )
                    
                    # Send confirmation to sender
                    await self.send(text_data=json.dumps({
                        'type': 'message_sent',
                        'message': await self.serialize_message(message)
                    }))
                    
                    # Send push notification
                    await self.send_push_notification(other_participant, message)
                
        except Exception as e:
            logger.error(f"Error handling send_message: {str(e)}")
            await self.send_error('Failed to send message')
    
    async def handle_typing(self, data):
        """Handle typing indicators"""
        try:
            conversation_id = data.get('conversation_id')
            is_typing = data.get('is_typing', False)
            
            other_participant = await self.get_other_participant(conversation_id)
            
            if other_participant:
                await self.channel_layer.group_send(
                    f'music_chat_user_{other_participant.id}',
                    {
                        'type': 'typing_indicator',
                        'conversation_id': conversation_id,
                        'user_id': self.user.id,
                        'username': self.user.username,
                        'is_typing': is_typing
                    }
                )
                
        except Exception as e:
            logger.error(f"Error handling typing: {str(e)}")
    
    async def handle_mark_read(self, data):
        """Handle marking messages as read"""
        try:
            conversation_id = data.get('conversation_id')
            count = await self.mark_messages_read(conversation_id)
            
            await self.send(text_data=json.dumps({
                'type': 'messages_marked_read',
                'conversation_id': conversation_id,
                'count': count
            }))
            
        except Exception as e:
            logger.error(f"Error handling mark_read: {str(e)}")
    
    async def handle_reaction(self, data):
        """Handle message reactions"""
        try:
            message_id = data.get('message_id')
            reaction = data.get('reaction')
            action = data.get('action', 'add')  # 'add' or 'remove'
            
            if action == 'add':
                reaction_obj = await self.add_reaction(message_id, reaction)
            else:
                await self.remove_reaction(message_id)
                reaction_obj = None
            
            # Get conversation and other participant
            conversation_id = await self.get_conversation_from_message(message_id)
            other_participant = await self.get_other_participant(conversation_id)
            
            if other_participant:
                await self.channel_layer.group_send(
                    f'music_chat_user_{other_participant.id}',
                    {
                        'type': 'message_reaction',
                        'message_id': message_id,
                        'user_id': self.user.id,
                        'username': self.user.username,
                        'reaction': reaction if action == 'add' else None,
                        'action': action
                    }
                )
            
            await self.send(text_data=json.dumps({
                'type': 'reaction_updated',
                'message_id': message_id,
                'action': action,
                'reaction': reaction if action == 'add' else None
            }))
            
        except Exception as e:
            logger.error(f"Error handling reaction: {str(e)}")
    
    # WebSocket message handlers
    
    async def music_message(self, event):
        """Send music message to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'new_message',
            'message': event['message']
        }))
    
    async def typing_indicator(self, event):
        """Send typing indicator to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'typing_indicator',
            'conversation_id': event['conversation_id'],
            'user_id': event['user_id'],
            'username': event['username'],
            'is_typing': event['is_typing']
        }))
    
    async def message_reaction(self, event):
        """Send message reaction to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'message_reaction',
            'message_id': event['message_id'],
            'user_id': event['user_id'],
            'username': event['username'],
            'reaction': event['reaction'],
            'action': event['action']
        }))
    
    # Helper methods
    
    async def send_error(self, error_message):
        """Send error message to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'error',
            'error': error_message
        }))
    
    @database_sync_to_async
    def create_message(self, conversation_id, message_type, text_content, track_data):
        """Create a new message in the database"""
        try:
            from .models import MusicChatConversation, MusicChatMessage
            
            conversation = MusicChatConversation.objects.get(
                id=conversation_id,
                participants=self.user
            )
            
            # Filter out None values from track_data
            filtered_track_data = {k: v for k, v in track_data.items() if v is not None}
            
            message = MusicChatMessage.objects.create(
                conversation=conversation,
                sender=self.user,
                message_type=message_type,
                text_content=text_content,
                **filtered_track_data
            )
            
            # Update conversation timestamp
            conversation.save()
            
            return message
            
        except Exception as e:
            logger.error(f"Error creating message: {str(e)}")
            return None
    
    @database_sync_to_async
    def get_other_participant(self, conversation_id):
        """Get the other participant in a conversation"""
        try:
            from .models import MusicChatConversation
            
            conversation = MusicChatConversation.objects.get(
                id=conversation_id,
                participants=self.user
            )
            
            return conversation.get_other_participant(self.user)
            
        except Exception as e:
            logger.error(f"Error getting other participant: {str(e)}")
            return None
    
    @database_sync_to_async
    def serialize_message(self, message):
        """Serialize message for JSON response"""
        from .serializers import MusicChatMessageSerializer
        from django.http import HttpRequest
        
        # Create a mock request for the serializer context
        request = HttpRequest()
        request.user = self.user
        
        serializer = MusicChatMessageSerializer(message, context={'request': request})
        return serializer.data
    
    @database_sync_to_async
    def mark_messages_read(self, conversation_id):
        """Mark messages as read"""
        try:
            from .models import MusicChatConversation
            
            conversation = MusicChatConversation.objects.get(
                id=conversation_id,
                participants=self.user
            )
            
            unread_messages = conversation.messages.filter(
                is_read=False
            ).exclude(sender=self.user)
            
            count = unread_messages.update(is_read=True)
            return count
            
        except Exception as e:
            logger.error(f"Error marking messages read: {str(e)}")
            return 0
    
    @database_sync_to_async
    def add_reaction(self, message_id, reaction):
        """Add reaction to a message"""
        try:
            from .models import MusicChatMessage, MusicChatReaction
            
            message = MusicChatMessage.objects.get(
                id=message_id,
                conversation__participants=self.user
            )
            
            reaction_obj, created = MusicChatReaction.objects.update_or_create(
                message=message,
                user=self.user,
                defaults={'reaction': reaction}
            )
            
            return reaction_obj
            
        except Exception as e:
            logger.error(f"Error adding reaction: {str(e)}")
            return None
    
    @database_sync_to_async
    def remove_reaction(self, message_id):
        """Remove reaction from a message"""
        try:
            from .models import MusicChatMessage, MusicChatReaction
            
            message = MusicChatMessage.objects.get(
                id=message_id,
                conversation__participants=self.user
            )
            
            MusicChatReaction.objects.filter(
                message=message,
                user=self.user
            ).delete()
            
            return True
            
        except Exception as e:
            logger.error(f"Error removing reaction: {str(e)}")
            return False
    
    @database_sync_to_async
    def get_conversation_from_message(self, message_id):
        """Get conversation ID from message ID"""
        try:
            from .models import MusicChatMessage
            
            message = MusicChatMessage.objects.get(
                id=message_id,
                conversation__participants=self.user
            )
            
            return message.conversation.id
            
        except Exception as e:
            logger.error(f"Error getting conversation from message: {str(e)}")
            return None
    
    @database_sync_to_async
    def send_push_notification(self, recipient, message):
        """Send push notification for new message"""
        try:
            from notifications.utils import notify_music_chat_message
            
            notify_music_chat_message(
                recipient=recipient,
                sender=self.user,
                message_preview=message.get_preview_text()
            )
            
        except Exception as e:
            logger.error(f"Error sending push notification: {str(e)}") 