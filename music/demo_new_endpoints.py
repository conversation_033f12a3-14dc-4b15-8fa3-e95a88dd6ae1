#!/usr/bin/env python3
"""
Demo script showing usage of new music authentication endpoints

This script demonstrates:
1. Apple Music user token sync endpoint
2. Spotify backend token refresh endpoint
"""

import json

# Base URL for your API (adjust as needed)
BASE_URL = "http://localhost:8000/api/music"

def demo_apple_music_sync():
    """
    Demonstrate Apple Music user token sync
    """
    print("\n=== Apple Music User Token Sync Demo ===")
    
    # Example payload
    sync_data = {
        "music_user_token": "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IldlYlBsYXlLaWQifQ.example_token"
    }
    
    # This would normally be called with proper authentication headers
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer YOUR_JWT_TOKEN_HERE"  # Replace with actual JWT
    }
    
    print(f"Endpoint: {BASE_URL}/auth/apple/sync_user_token/")
    print(f"Method: POST")
    print(f"Headers: {json.dumps(headers, indent=2)}")
    print(f"Payload: {json.dumps(sync_data, indent=2)}")
    
    print("\nExpected Response (200 OK):")
    expected_response = {
        "message": "Apple Music user token synced successfully",
        "user": {
            "id": 123,
            "username": "john_doe",
            "apple_music_connected": True
        },
        "service": {
            "service_type": "apple",
            "connected_at": "2024-01-01T12:00:00Z",
            "expires_at": "2024-07-01T12:00:00Z",
            "is_new": True
        }
    }
    print(json.dumps(expected_response, indent=2))
    
    print("\nPossible Error Responses:")
    print("400 Bad Request - Missing token:")
    print(json.dumps({"error": "music_user_token is required"}, indent=2))
    print("\n400 Bad Request - Invalid token:")
    print(json.dumps({"error": "Invalid Apple Music user token"}, indent=2))


def demo_spotify_refresh():
    """
    Demonstrate Spotify backend token refresh
    """
    print("\n\n=== Spotify Backend Token Refresh Demo ===")
    
    # This endpoint doesn't require a payload - it uses stored refresh tokens
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer YOUR_JWT_TOKEN_HERE"  # Replace with actual JWT
    }
    
    print(f"Endpoint: {BASE_URL}/auth/spotify/refresh/")
    print(f"Method: POST")
    print(f"Headers: {json.dumps(headers, indent=2)}")
    print("Payload: {} (empty - uses stored refresh token)")
    
    print("\nExpected Response (200 OK):")
    expected_response = {
        "access_token": "BQB_new_access_token_here",
        "refresh_token": "AQA_new_refresh_token_here",
        "expires_in": 3600,
        "token_type": "Bearer",
        "scope": "user-read-private user-read-email playlist-read-private"
    }
    print(json.dumps(expected_response, indent=2))
    
    print("\nPossible Error Responses:")
    print("400 Bad Request - Spotify not connected:")
    print(json.dumps({"error": "Spotify not connected"}, indent=2))
    print("\n400 Bad Request - No refresh token:")
    print(json.dumps({"error": "No refresh token available"}, indent=2))
    print("\n401 Unauthorized - Invalid refresh token:")
    print(json.dumps({"error": "Invalid refresh token - please re-authenticate"}, indent=2))


def demo_url_patterns():
    """
    Show the URL patterns for the new endpoints
    """
    print("\n\n=== URL Patterns ===")
    
    patterns = [
        {
            "name": "apple-music-user-token-sync",
            "path": "/api/music/auth/apple/sync_user_token/",
            "method": "POST",
            "auth_required": True,
            "description": "Sync Apple Music user token from mobile app"
        },
        {
            "name": "spotify-refresh-token", 
            "path": "/api/music/auth/spotify/refresh/",
            "method": "POST",
            "auth_required": True,
            "description": "Refresh Spotify access token using stored refresh token"
        },
        {
            "name": "spotify-pkce-token-exchange",
            "path": "/api/music/spotify/token/exchange/",
            "method": "POST", 
            "auth_required": False,
            "description": "Exchange authorization code for tokens using PKCE"
        },
        {
            "name": "spotify-pkce-token-refresh",
            "path": "/api/music/spotify/token/refresh/",
            "method": "POST",
            "auth_required": False,
            "description": "Refresh access token using refresh token (PKCE flow)"
        }
    ]
    
    for pattern in patterns:
        print(f"\nName: {pattern['name']}")
        print(f"Path: {pattern['path']}")
        print(f"Method: {pattern['method']}")
        print(f"Auth Required: {pattern['auth_required']}")
        print(f"Description: {pattern['description']}")


def demo_flutter_integration():
    """
    Show how Flutter app should integrate with these endpoints
    """
    print("\n\n=== Flutter Integration Guide ===")
    
    print("\n1. Apple Music Integration:")
    print("   - After getting userToken from MusicKit:")
    print("   - Call sendUserTokenToBackend(userToken) in AppleMusicService")
    print("   - This sends POST request to /api/music/auth/apple/sync_user_token/")
    print("   - Backend validates and stores the token")
    
    print("\n2. Spotify Token Refresh:")
    print("   - When access token expires (401 from Spotify API):")
    print("   - Call backend endpoint /api/music/auth/spotify/refresh/")
    print("   - Backend uses stored refresh token to get new access token")
    print("   - Returns new tokens to mobile app")
    print("   - App updates its stored tokens")
    
    print("\n3. Constants to add to Flutter app:")
    flutter_constants = """
// Add to lib/config/constants.dart
static const String appleMusicUserTokenSyncEndpoint = '/api/music/auth/apple/sync_user_token/';
static const String spotifyRefreshEndpoint = '/api/music/auth/spotify/refresh/';
"""
    print(flutter_constants)


if __name__ == "__main__":
    print("🎵 BOPMaps Music API - New Endpoints Demo")
    print("=" * 50)
    
    demo_apple_music_sync()
    demo_spotify_refresh()
    demo_url_patterns()
    demo_flutter_integration()
    
    print("\n\n=== Next Steps ===")
    print("1. Add the Apple Music constant to your Flutter constants.dart file")
    print("2. Test the endpoints with proper authentication")
    print("3. Update your Flutter app to use these new endpoints")
    print("4. Deploy and test the complete flow")
    
    print("\n✅ Demo complete!") 