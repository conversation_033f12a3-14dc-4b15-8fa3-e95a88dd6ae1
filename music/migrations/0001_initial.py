# Generated by Django 4.2.7 on 2025-04-07 16:22

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Genre",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name="MusicService",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "service_type",
                    models.CharField(
                        choices=[
                            ("spotify", "Spotify"),
                            ("apple", "Apple Music"),
                            ("soundcloud", "SoundCloud"),
                        ],
                        max_length=20,
                    ),
                ),
                ("access_token", models.CharField(max_length=1024)),
                (
                    "refresh_token",
                    models.Char<PERSON><PERSON>(blank=True, max_length=1024, null=True),
                ),
                ("expires_at", models.DateTimeField()),
            ],
        ),
        migrations.CreateModel(
            name="RecentTrack",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("track_id", models.CharField(max_length=255)),
                ("title", models.CharField(max_length=255)),
                ("artist", models.CharField(max_length=255)),
                ("album", models.CharField(blank=True, max_length=255, null=True)),
                ("album_art", models.URLField(blank=True, null=True)),
                (
                    "service",
                    models.CharField(
                        choices=[
                            ("spotify", "Spotify"),
                            ("apple", "Apple Music"),
                            ("soundcloud", "SoundCloud"),
                        ],
                        max_length=20,
                    ),
                ),
                ("played_at", models.DateTimeField()),
            ],
            options={
                "ordering": ["-played_at"],
            },
        ),
    ]
