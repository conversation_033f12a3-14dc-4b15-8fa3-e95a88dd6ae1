# Generated by Django 4.2.7 on 2025-06-16 21:14

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("music", "0002_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Artist",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=255, unique=True)),
                (
                    "spotify_id",
                    models.Char<PERSON>ield(
                        blank=True, max_length=255, null=True, unique=True
                    ),
                ),
                (
                    "apple_music_id",
                    models.Char<PERSON>ield(
                        blank=True, max_length=255, null=True, unique=True
                    ),
                ),
                (
                    "soundcloud_id",
                    models.Char<PERSON>ield(
                        blank=True, max_length=255, null=True, unique=True
                    ),
                ),
                ("image_url", models.URLField(blank=True, null=True)),
                ("created_at", models.DateTime<PERSON>ield(auto_now_add=True)),
            ],
        ),
    ]
