# Generated by Django 4.2.7 on 2025-06-13 23:33

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("music", "0002_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Track",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("spotify_id", models.Char<PERSON>ield(max_length=255, unique=True)),
                ("title", models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ("artist", models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ("album", models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ("duration_ms", models.IntegerField()),
                ("album_art", models.URLField(blank=True, null=True)),
                ("preview_url", models.URLField(blank=True, null=True)),
                ("created_at", models.DateTime<PERSON>ield(auto_now_add=True)),
            ],
        ),
    ]
