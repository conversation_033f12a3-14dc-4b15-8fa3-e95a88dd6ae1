# Generated by Django 4.2.7 on 2025-06-21 23:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("music", "0004_merge_0003_artist_0003_track"),
    ]

    operations = [
        migrations.CreateModel(
            name="MusicChatConversation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("last_message_at", models.DateTimeField(auto_now=True)),
                (
                    "participants",
                    models.ManyToManyField(
                        related_name="music_conversations", to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
            options={
                "ordering": ["-last_message_at"],
            },
        ),
        migrations.CreateModel(
            name="MusicChatMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "message_type",
                    models.CharField(
                        choices=[
                            ("text", "Text"),
                            ("track_recommendation", "Track Recommendation"),
                            ("playlist_share", "Playlist Share"),
                            ("album_share", "Album Share"),
                        ],
                        default="text",
                        max_length=20,
                    ),
                ),
                ("text_content", models.TextField(blank=True, null=True)),
                ("track_id", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "track_title",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "track_artist",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "track_album",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("album_art_url", models.URLField(blank=True, null=True)),
                ("preview_url", models.URLField(blank=True, null=True)),
                ("track_url", models.URLField(blank=True, null=True)),
                (
                    "music_service",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("spotify", "Spotify"),
                            ("apple", "Apple Music"),
                            ("soundcloud", "SoundCloud"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                ("duration_ms", models.IntegerField(blank=True, null=True)),
                ("is_read", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "conversation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="messages",
                        to="music.musicchatconversation",
                    ),
                ),
                (
                    "sender",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sent_music_messages",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="MusicChatReaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "reaction",
                    models.CharField(
                        choices=[
                            ("❤️", "Love"),
                            ("🔥", "Fire"),
                            ("🎵", "Music"),
                            ("👍", "Thumbs Up"),
                            ("👎", "Thumbs Down"),
                            ("😍", "Love Eyes"),
                            ("🤩", "Star Eyes"),
                            ("💯", "Hundred"),
                        ],
                        max_length=10,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "message",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reactions",
                        to="music.musicchatmessage",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="music_message_reactions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["message", "reaction"],
                        name="music_music_message_4def00_idx",
                    ),
                    models.Index(
                        fields=["user", "created_at"],
                        name="music_music_user_id_e35e06_idx",
                    ),
                ],
                "unique_together": {("message", "user")},
            },
        ),
        migrations.AddIndex(
            model_name="musicchatmessage",
            index=models.Index(
                fields=["conversation", "created_at"],
                name="music_music_convers_12e034_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="musicchatmessage",
            index=models.Index(
                fields=["sender", "created_at"], name="music_music_sender__3cd8d1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="musicchatmessage",
            index=models.Index(
                fields=["is_read"], name="music_music_is_read_35559a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="musicchatmessage",
            index=models.Index(
                fields=["-created_at"], name="music_music_created_955754_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="musicchatconversation",
            index=models.Index(
                fields=["-last_message_at"], name="music_music_last_me_716439_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="musicchatconversation",
            index=models.Index(
                fields=["created_at"], name="music_music_created_7482bf_idx"
            ),
        ),
    ]
