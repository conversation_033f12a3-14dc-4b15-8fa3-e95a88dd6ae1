from django.db import models
from django.conf import settings

# Create your models here.

class Artist(models.Model):
    """
    Model representing music artists
    """
    name = models.CharField(max_length=255, unique=True)
    spotify_id = models.Char<PERSON>ield(max_length=255, blank=True, null=True, unique=True)
    apple_music_id = models.CharField(max_length=255, blank=True, null=True, unique=True)
    soundcloud_id = models.CharField(max_length=255, blank=True, null=True, unique=True)
    image_url = models.URLField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.name


class Track(models.Model):
    """
    Model representing a music track
    """
    spotify_id = models.Char<PERSON>ield(max_length=255, unique=True)
    title = models.CharField(max_length=255)
    artist = models.Char<PERSON>ield(max_length=255)
    album = models.CharField(max_length=255, blank=True, null=True)
    duration_ms = models.IntegerField()
    album_art = models.URLField(blank=True, null=True)
    preview_url = models.URLField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.title} - {self.artist}"

class Genre(models.Model):
    """
    Model representing music genres
    """
    name = models.CharField(max_length=100, unique=True)
    
    def __str__(self):
        return self.name


class MusicService(models.Model):
    """
    Model to store user's connections to music services
    """
    SERVICE_TYPES = (
        ('spotify', 'Spotify'),
        ('apple', 'Apple Music'),
        ('soundcloud', 'SoundCloud')
    )
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='music_services'
    )
    service_type = models.CharField(max_length=20, choices=SERVICE_TYPES)
    access_token = models.CharField(max_length=1024)
    refresh_token = models.CharField(max_length=1024, blank=True, null=True)
    expires_at = models.DateTimeField()
    
    class Meta:
        unique_together = ('user', 'service_type')
    
    def __str__(self):
        return f"{self.user.username} - {self.service_type}"
        
        
class RecentTrack(models.Model):
    """
    Model to store tracks recently played by a user
    """
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='recent_tracks'
    )
    track_id = models.CharField(max_length=255)
    title = models.CharField(max_length=255)
    artist = models.CharField(max_length=255)
    album = models.CharField(max_length=255, blank=True, null=True)
    album_art = models.URLField(blank=True, null=True)
    service = models.CharField(max_length=20, choices=MusicService.SERVICE_TYPES)
    played_at = models.DateTimeField()
    
    class Meta:
        ordering = ['-played_at']
    
    def __str__(self):
        return f"{self.title} - {self.artist} (played by {self.user.username})"


# Music Chat Models

class MusicChatConversation(models.Model):
    """
    Chat conversation between two users about music
    """
    participants = models.ManyToManyField(
        settings.AUTH_USER_MODEL, 
        related_name='music_conversations'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    last_message_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-last_message_at']
        indexes = [
            models.Index(fields=['-last_message_at']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        participant_names = ", ".join([user.username for user in self.participants.all()[:2]])
        return f"Music Chat: {participant_names}"
    
    def get_other_participant(self, user):
        """Get the other participant in a 2-person conversation"""
        return self.participants.exclude(id=user.id).first()
    
    def get_unread_count(self, user):
        """Get unread message count for a specific user"""
        return self.messages.filter(is_read=False).exclude(sender=user).count()


class MusicChatMessage(models.Model):
    """
    Individual music chat message with support for text and music sharing
    """
    MESSAGE_TYPES = [
        ('text', 'Text'),
        ('track_recommendation', 'Track Recommendation'),
        ('playlist_share', 'Playlist Share'),
        ('album_share', 'Album Share'),
    ]
    
    conversation = models.ForeignKey(
        MusicChatConversation, 
        on_delete=models.CASCADE, 
        related_name='messages'
    )
    sender = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE,
        related_name='sent_music_messages'
    )
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPES, default='text')
    
    # Text content
    text_content = models.TextField(blank=True, null=True)
    
    # Music data (when sharing tracks/albums/playlists)
    track_id = models.CharField(max_length=255, blank=True, null=True)
    track_title = models.CharField(max_length=255, blank=True, null=True)  
    track_artist = models.CharField(max_length=255, blank=True, null=True)
    track_album = models.CharField(max_length=255, blank=True, null=True)
    album_art_url = models.URLField(blank=True, null=True)
    preview_url = models.URLField(blank=True, null=True)
    track_url = models.URLField(blank=True, null=True)
    music_service = models.CharField(max_length=20, choices=MusicService.SERVICE_TYPES, blank=True, null=True)
    duration_ms = models.IntegerField(blank=True, null=True)
    
    # Metadata
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['conversation', 'created_at']),
            models.Index(fields=['sender', 'created_at']),
            models.Index(fields=['is_read']),
            models.Index(fields=['-created_at']),
        ]
    
    def __str__(self):
        if self.message_type == 'text':
            preview = self.text_content[:50] + "..." if len(self.text_content) > 50 else self.text_content
            return f"{self.sender.username}: {preview}"
        else:
            return f"{self.sender.username} shared: {self.track_title} - {self.track_artist}"
    
    def mark_as_read(self):
        """Mark this message as read"""
        if not self.is_read:
            self.is_read = True
            self.save(update_fields=['is_read'])
    
    def get_preview_text(self):
        """Get a preview of the message for notifications"""
        if self.message_type == 'text':
            return self.text_content[:100] + "..." if len(self.text_content) > 100 else self.text_content
        elif self.message_type == 'track_recommendation':
            return f"🎵 {self.track_title} by {self.track_artist}"
        elif self.message_type == 'playlist_share':
            return f"📝 Shared a playlist"
        elif self.message_type == 'album_share':
            return f"💿 {self.track_album} by {self.track_artist}"
        return "Shared music"


class MusicChatReaction(models.Model):
    """
    Reactions to music chat messages (🔥, ❤️, 🎵, etc.)
    """
    REACTION_CHOICES = [
        ('❤️', 'Love'),
        ('🔥', 'Fire'),
        ('🎵', 'Music'),
        ('👍', 'Thumbs Up'),
        ('👎', 'Thumbs Down'),
        ('😍', 'Love Eyes'),
        ('🤩', 'Star Eyes'),
        ('💯', 'Hundred'),
    ]
    
    message = models.ForeignKey(
        MusicChatMessage, 
        on_delete=models.CASCADE, 
        related_name='reactions'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE,
        related_name='music_message_reactions'
    )
    reaction = models.CharField(max_length=10, choices=REACTION_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('message', 'user')
        indexes = [
            models.Index(fields=['message', 'reaction']),
            models.Index(fields=['user', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} reacted {self.reaction} to message {self.message.id}"
