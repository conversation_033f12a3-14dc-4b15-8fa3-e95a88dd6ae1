from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Track, MusicChatConversation, MusicChatMessage, MusicChatReaction

User = get_user_model()

class TrackSerializer(serializers.ModelSerializer):
    """Serializer for music tracks"""
    class Meta:
        model = Track
        fields = [
            'id',
            'spotify_id',
            'title',
            'artist',
            'album',
            'duration_ms',
            'album_art',
            'preview_url',
            'created_at'
        ]
        read_only_fields = ['created_at']


# Music Chat Serializers

class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user serializer for chat participants"""
    profile_pic_url = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'profile_pic_url']
    
    def get_profile_pic_url(self, obj):
        if obj.profile_pic:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.profile_pic)
            return obj.profile_pic
        return None


class MusicChatReactionSerializer(serializers.ModelSerializer):
    """Serializer for message reactions"""
    user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = MusicChatReaction
        fields = ['id', 'user', 'reaction', 'created_at']
        read_only_fields = ['created_at']


class MusicChatMessageSerializer(serializers.ModelSerializer):
    """Serializer for music chat messages"""
    sender = UserBasicSerializer(read_only=True)
    reactions = MusicChatReactionSerializer(many=True, read_only=True)
    
    class Meta:
        model = MusicChatMessage
        fields = [
            'id', 'sender', 'message_type', 'text_content',
            'track_id', 'track_title', 'track_artist', 'track_album',
            'album_art_url', 'preview_url', 'track_url', 'music_service',
            'duration_ms', 'is_read', 'created_at', 'reactions'
        ]
        read_only_fields = ['id', 'sender', 'created_at', 'reactions']
    
    def validate(self, data):
        """Validate message data based on message type"""
        message_type = data.get('message_type', 'text')
        
        if message_type == 'text':
            if not data.get('text_content'):
                raise serializers.ValidationError("Text content is required for text messages")
        
        elif message_type in ['track_recommendation', 'album_share']:
            required_fields = ['track_id', 'track_title', 'track_artist']
            for field in required_fields:
                if not data.get(field):
                    raise serializers.ValidationError(f"{field} is required for {message_type}")
        
        return data


class MusicChatMessageCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating music chat messages"""
    recipient_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = MusicChatMessage
        fields = [
            'recipient_id', 'message_type', 'text_content',
            'track_id', 'track_title', 'track_artist', 'track_album',
            'album_art_url', 'preview_url', 'track_url', 'music_service',
            'duration_ms'
        ]
    
    def validate_recipient_id(self, value):
        """Validate that recipient exists and is not the sender"""
        try:
            recipient = User.objects.get(id=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("Recipient does not exist")
        
        if recipient == self.context['request'].user:
            raise serializers.ValidationError("Cannot send message to yourself")
        
        return value
    
    def validate(self, data):
        """Validate message data based on message type"""
        message_type = data.get('message_type', 'text')
        
        if message_type == 'text':
            if not data.get('text_content'):
                raise serializers.ValidationError("Text content is required for text messages")
        
        elif message_type in ['track_recommendation', 'album_share']:
            required_fields = ['track_id', 'track_title', 'track_artist']
            for field in required_fields:
                if not data.get(field):
                    raise serializers.ValidationError(f"{field} is required for {message_type}")
        
        return data


class MusicChatConversationSerializer(serializers.ModelSerializer):
    """Serializer for music chat conversations"""
    participants = UserBasicSerializer(many=True, read_only=True)
    other_participant = serializers.SerializerMethodField()
    last_message = serializers.SerializerMethodField()
    unread_count = serializers.SerializerMethodField()
    
    class Meta:
        model = MusicChatConversation
        fields = [
            'id', 'participants', 'other_participant', 'last_message',
            'unread_count', 'created_at', 'last_message_at'
        ]
        read_only_fields = ['created_at', 'last_message_at']
    
    def get_other_participant(self, obj):
        """Get the other participant for 2-person conversations"""
        request = self.context.get('request')
        if request and request.user:
            other_user = obj.get_other_participant(request.user)
            if other_user:
                return UserBasicSerializer(other_user, context=self.context).data
        return None
    
    def get_last_message(self, obj):
        """Get the last message in the conversation"""
        last_message = obj.messages.last()
        if last_message:
            return {
                'id': last_message.id,
                'sender_id': last_message.sender.id,
                'message_type': last_message.message_type,
                'preview': last_message.get_preview_text(),
                'created_at': last_message.created_at,
                'is_read': last_message.is_read
            }
        return None
    
    def get_unread_count(self, obj):
        """Get unread message count for current user"""
        request = self.context.get('request')
        if request and request.user:
            return obj.get_unread_count(request.user)
        return 0


class MusicChatReactionCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating message reactions"""
    
    class Meta:
        model = MusicChatReaction
        fields = ['reaction']
    
    def validate_reaction(self, value):
        """Validate that reaction is one of the allowed choices"""
        allowed_reactions = [choice[0] for choice in MusicChatReaction.REACTION_CHOICES]
        if value not in allowed_reactions:
            raise serializers.ValidationError(f"Reaction must be one of: {', '.join(allowed_reactions)}")
        return value 