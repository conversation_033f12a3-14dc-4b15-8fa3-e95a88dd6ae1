{% extends "base.html" %}

{% block title %}Connect Music Services{% endblock %}

{% block content %}
<div class="container mt-5">
    <h1>Connect Your Music Services</h1>
    
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h4>Spotify</h4>
                </div>
                <div class="card-body">
                    <p>Connect your Spotify account to share tracks from your playlists and recently played songs.</p>
                    {% if user.spotify_connected %}
                        <p class="text-success">✅ Connected</p>
                        <a href="{% url 'music:services-disconnect-service' service_type='spotify' %}" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to disconnect Spotify?')">Disconnect</a>
                    {% else %}
                        <a href="{% url 'music:spotify-auth' %}" class="btn btn-success">Connect Spotify</a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4>Apple Music</h4>
                </div>
                <div class="card-body">
                    <p>Connect your Apple Music account to share tracks from your library and playlists.</p>
                    {% if user.apple_music_connected %}
                        <p class="text-success">✅ Connected</p>
                        <a href="{% url 'music:services-disconnect-service' service_type='apple' %}" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to disconnect Apple Music?')">Disconnect</a>
                    {% else %}
                        <div class="alert alert-info">
                            <strong>Note:</strong> Apple Music authentication happens through the mobile app. Use the token input below for testing purposes only.
                        </div>
                        <form action="{% url 'music:apple-music-auth' %}" method="post" class="mt-3">
                            {% csrf_token %}
                            <div class="form-group">
                                <label for="music_user_token">Music User Token:</label>
                                <input type="text" class="form-control" id="music_user_token" name="music_user_token" placeholder="Enter your Apple Music user token">
                            </div>
                            <button type="submit" class="btn btn-primary mt-2">Connect Apple Music</button>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4>Mobile App Integration</h4>
                </div>
                <div class="card-body">
                    <h5>API Endpoints for Mobile App:</h5>
                    <ul>
                        <li><strong>Spotify Auth URL:</strong> <code>{% url 'music:spotify-mobile-auth' %}</code></li>
                        <li><strong>Apple Music Token:</strong> <code>{% url 'music:apple-music-auth' %}</code></li>
                        <li><strong>Callback Handler:</strong> <code>{% url 'music:callback-handler' %}</code></li>
                    </ul>
                    
                    <h5 class="mt-4">Mobile Integration Examples:</h5>
                    <div class="accordion" id="codeExamples">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingSpotify">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSpotify" aria-expanded="false" aria-controls="collapseSpotify">
                                    Spotify Integration Code
                                </button>
                            </h2>
                            <div id="collapseSpotify" class="accordion-collapse collapse" aria-labelledby="headingSpotify">
                                <div class="accordion-body">
                                    <pre><code>
// Flutter code for Spotify integration
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

// Step 1: Get auth URL
Future&lt;String&gt; getSpotifyAuthUrl(String accessToken) async {
  final response = await http.get(
    Uri.parse('YOUR_BACKEND_URL/music/spotify/mobile/'),
    headers: {'Authorization': 'Bearer $accessToken'},
  );
  
  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    return data['auth_url'];
  } else {
    throw Exception('Failed to get auth URL');
  }
}

// Step 2: Open WebView for auth
void openSpotifyAuth(BuildContext context, String authUrl) {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => Scaffold(
        appBar: AppBar(title: Text('Connect Spotify')),
        body: WebView(
          initialUrl: authUrl,
          javascriptMode: JavascriptMode.unrestricted,
          navigationDelegate: (NavigationRequest request) {
            if (request.url.startsWith('bopmaps://callback')) {
              // Extract code from URL
              Uri uri = Uri.parse(request.url);
              String? code = uri.queryParameters['code'];
              
              if (code != null) {
                sendCodeToBackend(context, code);
              }
              
              Navigator.pop(context); // Close WebView
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      ),
    ),
  );
}

// Step 3: Send code to backend
Future&lt;void&gt; sendCodeToBackend(BuildContext context, String code) async {
  try {
    final response = await http.post(
      Uri.parse('YOUR_BACKEND_URL/music/auth/callback/'),
      headers: {
        'Authorization': 'Bearer YOUR_ACCESS_TOKEN',
        'Content-Type': 'application/json',
      },
      body: jsonEncode({'code': code}),
    );
    
    if (response.statusCode == 200) {
      // Success! Show a success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Spotify connected successfully!')),
      );
    } else {
      // Error handling
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to connect Spotify')),
      );
    }
  } catch (e) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Error: $e')),
    );
  }
}
                                    </code></pre>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingApple">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseApple" aria-expanded="false" aria-controls="collapseApple">
                                    Apple Music Integration Code
                                </button>
                            </h2>
                            <div id="collapseApple" class="accordion-collapse collapse" aria-labelledby="headingApple">
                                <div class="accordion-body">
                                    <pre><code>
// Flutter code for Apple Music integration
import 'package:flutter/material.dart';
import 'package:music_kit/music_kit.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class AppleMusicIntegration extends StatefulWidget {
  @override
  _AppleMusicIntegrationState createState() => _AppleMusicIntegrationState();
}

class _AppleMusicIntegrationState extends State&lt;AppleMusicIntegration&gt; {
  final MusicKit _musicKit = MusicKit();
  bool _isAuthorized = false;
  String? _userToken;

  @override
  void initState() {
    super.initState();
    _checkAuthorizationStatus();
  }

  Future&lt;void&gt; _checkAuthorizationStatus() async {
    final authStatus = await _musicKit.authorizationStatus;
    setState(() {
      _isAuthorized = authStatus == AuthorizationStatus.authorized;
    });
    
    if (_isAuthorized) {
      _getUserToken();
    }
  }

  Future&lt;void&gt; _authorize() async {
    try {
      final authStatus = await _musicKit.authorize();
      setState(() {
        _isAuthorized = authStatus == AuthorizationStatus.authorized;
      });
      
      if (_isAuthorized) {
        _getUserToken();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error authorizing Apple Music: $e')),
      );
    }
  }

  Future&lt;void&gt; _getUserToken() async {
    try {
      final token = await _musicKit.musicUserToken;
      setState(() {
        _userToken = token;
      });
      
      if (_userToken != null) {
        _sendTokenToBackend(_userToken!);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error getting user token: $e')),
      );
    }
  }

  Future&lt;void&gt; _sendTokenToBackend(String token) async {
    try {
      final response = await http.post(
        Uri.parse('YOUR_BACKEND_URL/music/auth/apple/token/'),
        headers: {
          'Authorization': 'Bearer YOUR_ACCESS_TOKEN',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({'music_user_token': token}),
      );
      
      if (response.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Apple Music connected successfully!')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to connect Apple Music to backend')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Apple Music Integration'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _isAuthorized 
                ? 'Apple Music Authorized' 
                : 'Connect to Apple Music',
              style: TextStyle(fontSize: 18),
            ),
            SizedBox(height: 20),
            if (!_isAuthorized)
              ElevatedButton(
                onPressed: _authorize,
                child: Text('Authorize Apple Music'),
              ),
            if (_isAuthorized && _userToken != null)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'User Token: ${_userToken!.substring(0, 20)}...',
                  style: TextStyle(fontSize: 12),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
                                    </code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 