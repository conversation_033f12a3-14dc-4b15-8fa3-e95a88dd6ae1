from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from .views import (
    spotify_auth, 
    spotify_callback, 
    connection_success,
    connect_services,
    MusicServiceViewSet,
    SpotifyViewSet,
    MusicTrackViewSet,
    callback_handler,
    spotify_mobile_auth,
    spotify_mobile_token_auth,
    apple_music_mobile_token_auth,
    apple_music_mobile_auth,
    apple_music_callback,
    apple_music_refresh_token,
    apple_music_sync_token,
    AppleMusicViewSet,
    spotify_pkce_token_exchange,
    spotify_pkce_token_refresh,
    spotify_refresh_token,
    MusicChatViewSet
)

router = DefaultRouter()
# Register viewsets
router.register(r'services', MusicServiceViewSet, basename='services')
router.register(r'spotify', SpotifyViewSet, basename='spotify')
router.register(r'apple', AppleMusicViewSet, basename='apple')
router.register(r'apple', AppleMusicViewSet, basename='apple')
router.register(r'tracks', MusicTrackViewSet, basename='tracks')
router.register(r'chat', MusicChatViewSet, basename='music-chat')

app_name = 'music'

app_name = 'music'

urlpatterns = [
    # Main view
    path('connect/', connect_services, name='connect-services'),
    
    # OAuth flow endpoints - Spotify
    path('auth/spotify/', spotify_mobile_token_auth, name='spotify-mobile-token-auth'),
    path('auth/spotify/mobile/', spotify_mobile_auth, name='spotify-mobile-auth'),
    path('auth/spotify/callback/', spotify_callback, name='spotify-callback'),
    path('auth/spotify/refresh/', spotify_refresh_token, name='spotify-refresh-token'),
    
    # OAuth flow endpoints - Apple Music (matching Spotify pattern)
    path('auth/apple/', apple_music_mobile_token_auth, name='apple-music-mobile-token-auth'),
    path('auth/apple/mobile/', apple_music_mobile_auth, name='apple-music-mobile-auth'),
    path('auth/apple/callback/', apple_music_callback, name='apple-music-callback'),
    path('auth/apple/refresh/', apple_music_refresh_token, name='apple-music-refresh-token'),
    path('auth/apple/sync-token/', apple_music_sync_token, name='apple-music-sync-token'),
    
    path('auth/success/', connection_success, name='connection-success'),
    
    # PKCE endpoints
    path('spotify/token/exchange/', spotify_pkce_token_exchange, name='spotify-pkce-token-exchange'),
    path('spotify/token/refresh/', spotify_pkce_token_refresh, name='spotify-pkce-token-refresh'),
    
    # Mobile app callback handler
    path('auth/callback/', callback_handler, name='callback-handler'),
    
    # API endpoints
    path('', include(router.urls)),
    path('', include(router.urls)),
] 