from django.shortcuts import render, redirect
from django.conf import settings
from django.http import JsonResponse
from django.utils import timezone
from django.contrib.auth.decorators import login_required
from django.urls import reverse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from rest_framework import viewsets, status, serializers
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.contrib.auth import get_user_model, login
from rest_framework_simplejwt.tokens import RefreshToken
from django.shortcuts import redirect
from django_ratelimit.decorators import ratelimit
import requests
import json
import logging
from datetime import datetime, timedelta
import uuid
import string
import random
from urllib.parse import urlencode

from .models import MusicService, RecentTrack
from .services import MusicServiceAuthMixin, SpotifyService, AppleMusicService
from .utils import (
    search_music, 
    get_recently_played_tracks, 
    get_user_playlists, 
    get_playlist_tracks,
    get_track_details,
    get_saved_tracks
)

User = get_user_model()
logger = logging.getLogger('bopmaps')

# First define all serializers
class SpotifyAuthSerializer(serializers.Serializer):
    """Serializer for Spotify auth endpoints"""
    auth_url = serializers.URLField()


class SpotifyCallbackSerializer(serializers.Serializer):
    """Serializer for Spotify callback request"""
    code = serializers.CharField(required=True)
    
    
class SpotifyCallbackResponseSerializer(serializers.Serializer):
    """Serializer for Spotify callback response"""
    message = serializers.CharField()
    user = serializers.DictField()
    service = serializers.DictField()


# Apple Music Serializers
class AppleMusicTokenSerializer(serializers.Serializer):
    """
    Serializer for Apple Music user token authentication
    Based on: https://developer.apple.com/documentation/applemusicapi/user-authentication-for-musickit
    """
    # Required: MusicKit user token
    music_user_token = serializers.CharField(required=False, help_text="MusicKit user token")
    userToken = serializers.CharField(required=False, help_text="Alternative name for user token")
    
    # Optional: Developer token for additional validation
    developer_token = serializers.CharField(required=False, help_text="Developer JWT token")
    developerToken = serializers.CharField(required=False, help_text="Alternative name for developer token")
    
    # Optional: User profile information
    user_id = serializers.CharField(required=False, help_text="User ID from Apple Music")
    id = serializers.CharField(required=False, help_text="Alternative name for user ID")
    display_name = serializers.CharField(required=False, help_text="User display name")
    displayName = serializers.CharField(required=False, help_text="Alternative name for display name")
    email = serializers.EmailField(required=False, help_text="User email address")
    country = serializers.CharField(required=False, help_text="User country code")
    
    def validate(self, data):
        # Ensure at least one of the user token fields is provided
        user_token = data.get('music_user_token') or data.get('userToken')
        if not user_token:
            raise serializers.ValidationError({
                'music_user_token': 'Either music_user_token or userToken is required'
            })
        return data


class AppleMusicResponseSerializer(serializers.Serializer):
    """Serializer for Apple Music connection response"""
    message = serializers.CharField()
    user = serializers.DictField()
    service = serializers.DictField()
    
    
class MusicServiceSerializer(serializers.Serializer):
    """Serializer for music service information"""
    service_type = serializers.CharField()
    connected_at = serializers.DateTimeField()
    is_active = serializers.BooleanField()


class SpotifyResponseSerializer(serializers.Serializer):
    """Generic serializer for Spotify API responses"""
    # This is a dynamic serializer that will adapt to various Spotify API responses
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Don't validate fields that aren't explicitly declared
        self.allow_unknown_fields = True
    
    def to_representation(self, instance):
        # Pass through the Spotify API response
        return instance


class MusicTrackSerializer(serializers.Serializer):
    """Serializer for music track data"""
    id = serializers.CharField()
    title = serializers.CharField()
    artist = serializers.CharField()
    album = serializers.CharField(required=False, allow_null=True)
    album_art = serializers.URLField(required=False, allow_null=True)
    url = serializers.URLField()
    service = serializers.CharField()
    preview_url = serializers.URLField(required=False, allow_null=True)


# PKCE Serializers
class SpotifyPKCETokenExchangeSerializer(serializers.Serializer):
    """Serializer for PKCE token exchange request"""
    code = serializers.CharField(required=True)
    code_verifier = serializers.CharField(required=True)
    redirect_uri = serializers.CharField(required=True)


class SpotifyPKCETokenRefreshSerializer(serializers.Serializer):
    """Serializer for PKCE token refresh request"""
    refresh_token = serializers.CharField(required=True)


class SpotifyPKCETokenResponseSerializer(serializers.Serializer):
    """Serializer for PKCE token response"""
    access_token = serializers.CharField()
    refresh_token = serializers.CharField(required=False, allow_null=True)
    expires_in = serializers.IntegerField()
    token_type = serializers.CharField()
    scope = serializers.CharField(required=False, allow_null=True)


# Helper function for email-based account linking
def find_or_create_user_by_email(email, service_type, user_data):
    """
    Find existing user by email or create new one if not found.
    This enables account linking across Spotify and Apple Music services.
    
    Args:
        email: User's email address
        service_type: 'spotify' or 'apple'
        user_data: Dictionary with user profile information
        
    Returns:
        tuple: (user, created) where created is True if user was newly created
    """
    user = None
    created = False
    
    # Try to find existing user by email
    if email:
        try:
            user = User.objects.get(email=email)
            logger.info(f"Found existing user by email {email} for {service_type} service")
            
            # Update the service connection flag
            if service_type == 'spotify':
                user.spotify_connected = True
            elif service_type == 'apple':
                user.apple_music_connected = True
            
            # Optionally update bio with new service info if not already present
            if user.bio:
                bio_parts = user.bio.split(' | ')
                service_info = f"{service_type.title()}: {user_data.get('premium_status', 'Connected')}"
                if not any(service_type.lower() in part.lower() for part in bio_parts):
                    bio_parts.append(service_info)
                    user.bio = ' | '.join(bio_parts)
            else:
                # Create bio with service info
                bio_parts = []
                if user_data.get('display_name'):
                    bio_parts.append(f"Name: {user_data['display_name']}")
                if user_data.get('country'):
                    bio_parts.append(f"Country: {user_data['country']}")
                bio_parts.append(f"{service_type.title()}: {user_data.get('premium_status', 'Connected')}")
                user.bio = ' | '.join(bio_parts)
            
            user.save()
            
        except User.DoesNotExist:
            # No user found with this email, create a new one
            user = None
    
    # Create new user if none found
    if not user:
        try:
            # Generate a unique username
            base_username = user_data.get('user_id') or f"{service_type}_user"
            username = base_username
            
            # Ensure username is unique
            counter = 1
            while User.objects.filter(username=username).exists():
                username = f"{base_username}_{counter}"
                counter += 1
            
            # Generate unique fallback email if no email provided
            if not email:
                # Generate a unique email using timestamp and random suffix to ensure uniqueness
                import time
                timestamp = int(time.time())
                random_suffix = ''.join(random.choices(string.digits, k=4))
                fallback_email = f"{username}_{timestamp}_{random_suffix}@{service_type}.local"
                
                # Double-check email uniqueness (very unlikely to collide but be safe)
                email_counter = 1
                while User.objects.filter(email=fallback_email).exists():
                    fallback_email = f"{username}_{timestamp}_{random_suffix}_{email_counter}@{service_type}.local"
                    email_counter += 1
                
                email_to_use = fallback_email
            else:
                email_to_use = email
            
            # Create the user
            user = User.objects.create_user(
                username=username,
                email=email_to_use,
                password=User.objects.make_random_password(),
            )
            
            # Set service connection flags
            if service_type == 'spotify':
                user.spotify_connected = True
            elif service_type == 'apple':
                user.apple_music_connected = True
            
            # Create bio with service info
            bio_parts = []
            if user_data.get('display_name'):
                bio_parts.append(f"Name: {user_data['display_name']}")
            if user_data.get('country'):
                bio_parts.append(f"Country: {user_data['country']}")
            bio_parts.append(f"{service_type.title()}: {user_data.get('premium_status', 'Connected')}")
            
            if bio_parts:
                user.bio = ' | '.join(bio_parts)
            
            user.save()
            created = True
            logger.info(f"Created new user {username} for {service_type} service with email {email}")
            
        except Exception as e:
            logger.error(f"Error creating user for {service_type}: {str(e)}")
            raise e
    
    return user, created


# Then define view functions
@login_required
def connect_services(request):
    """Page for connecting music services"""
    return render(request, 'music/connect_services.html')


def spotify_auth(request):
    """Start Spotify OAuth flow"""
    auth_url = SpotifyService.get_auth_url(request)
    return redirect(auth_url)


@api_view(['GET'])
@permission_classes([AllowAny])
def spotify_mobile_auth(request):
    """Start Spotify OAuth flow for mobile apps"""
    # Create a mock request to pass to get_auth_url with mobile redirect URI
    class MockRequest:
        def __init__(self):
            self.build_absolute_uri = lambda x: settings.SPOTIFY_MOBILE_REDIRECT_URI
    
    mock_request = MockRequest()
    auth_url = SpotifyService.get_auth_url(mock_request)
    
    # Use the serializer for response
    serializer = SpotifyAuthSerializer({"auth_url": auth_url})
    return Response(serializer.data)


def spotify_callback(request):
    """
    Handle Spotify OAuth callback
    This function can create a new user if one doesn't exist with the Spotify email
    """
    error = request.GET.get('error')
    if error:
        return JsonResponse({'error': error})
    
    code = request.GET.get('code')
    if not code:
        return JsonResponse({'error': 'No authorization code provided'})
    
    # Exchange code for tokens
    tokens_data = SpotifyService.exchange_code_for_tokens(request, code)
    
    if 'error' in tokens_data:
        return JsonResponse({'error': tokens_data['error']})
    
    # Create a temporary MusicService object to make API requests
    temp_service = MusicService(
        user=None,  # No user assigned yet
        service_type='spotify',
        access_token=tokens_data.get('access_token'),
        refresh_token=tokens_data.get('refresh_token', ''),
        expires_at=timezone.now() + timedelta(seconds=tokens_data.get('expires_in', 3600))
    )
    
    # Get user profile from Spotify
    user_profile = SpotifyService.make_api_request(temp_service, 'me')
    
    if 'error' in user_profile:
        return JsonResponse({'error': f"Failed to get Spotify profile: {user_profile['error']}"})
    
    # Check if we have a logged-in user
    if request.user.is_authenticated:
        # User is already logged in, just connect the service
        user = request.user
    else:
        # No logged-in user, check if a user with this email exists
        spotify_email = user_profile.get('email')
        
        if not spotify_email:
            return JsonResponse({'error': 'Spotify account does not have an email address'})
        
        try:
            # Try to find user with this email
            user = User.objects.get(email=spotify_email)
            # Auto-login the user
            login(request, user)
            logger.info(f"User {user.username} logged in via Spotify")
        except User.DoesNotExist:
            # User doesn't exist, create a new one
            try:
                # Generate a unique username based on Spotify display name
                display_name = user_profile.get('display_name', '')
                if not display_name:
                    display_name = "spotify_user"
                
                # Remove spaces and special characters, and make it lowercase
                base_username = ''.join(e for e in display_name if e.isalnum()).lower()
                
                # Check if username exists, if so, add random numbers
                if User.objects.filter(username=base_username).exists():
                    random_suffix = ''.join(random.choices(string.digits, k=4))
                    username = f"{base_username}_{random_suffix}"
                else:
                    username = base_username
                
                # Create the user with a random password
                random_password = ''.join(random.choices(string.ascii_letters + string.digits, k=16))
                
                # Get profile image if available
                profile_pic_url = None
                if user_profile.get('images') and len(user_profile['images']) > 0:
                    profile_pic_url = user_profile['images'][0].get('url')
                
                # Create user
                user = User.objects.create_user(
                    username=username,
                    email=spotify_email,
                    password=random_password,
                    # Don't set profile_pic here as it's a URL, not a file
                )
                
                # Set additional fields
                user.spotify_connected = True
                
                # Add Spotify profile info to user bio if available
                bio_parts = []
                if display_name:
                    bio_parts.append(f"Name: {display_name}")
                if user_profile.get('country'):
                    bio_parts.append(f"Country: {user_profile.get('country')}")
                if user_profile.get('product'):
                    product = user_profile.get('product').capitalize()
                    bio_parts.append(f"Spotify: {product}")
                
                if bio_parts:
                    user.bio = " | ".join(bio_parts)
                
                user.save()
                
                # Auto-login the user
                login(request, user)
                logger.info(f"New user {username} created and logged in via Spotify")
            except Exception as e:
                logger.error(f"Error creating user from Spotify: {str(e)}")
                return JsonResponse({'error': f"Failed to create user: {str(e)}"})
    
    # Save Spotify tokens to user's account
    user.spotify_connected = True
    user.save()
    MusicServiceAuthMixin.save_tokens(user, 'spotify', tokens_data)
    
    # Redirect to success page or frontend app
    return redirect('music:connection-success')


@api_view(['POST'])
@permission_classes([AllowAny])
def callback_handler(request):
    """
    Handle OAuth callback from mobile app
    Accepts a JSON payload with the authorization code and exchanges it for tokens.
    This is used with the fixed redirect URI workflow.
    """
    try:
        # Use the serializer for request validation
        serializer = SpotifyCallbackSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        # Get data from request
        code = serializer.validated_data.get('code')
        
        # Create a mock request to pass to exchange_code_for_tokens
        class MockRequest:
            def __init__(self):
                self.build_absolute_uri = lambda x: settings.SPOTIFY_MOBILE_REDIRECT_URI
        
        mock_request = MockRequest()
        
        # Exchange code for tokens
        tokens_data = SpotifyService.exchange_code_for_tokens(mock_request, code)
        
        if 'error' in tokens_data:
            logger.error(f"Error exchanging Spotify code: {tokens_data['error']}")
            return Response({'error': tokens_data['error']}, status=status.HTTP_400_BAD_REQUEST)
        
        # Create a temporary MusicService object to make API requests
        temp_service = MusicService(
            user=None,  # No user assigned yet
            service_type='spotify',
            access_token=tokens_data.get('access_token'),
            refresh_token=tokens_data.get('refresh_token', ''),
            expires_at=timezone.now() + timedelta(seconds=tokens_data.get('expires_in', 3600))
        )
        
        # Get user profile from Spotify
        user_profile = SpotifyService.make_api_request(temp_service, 'me')
        
        if 'error' in user_profile:
            logger.error(f"Error getting Spotify profile: {user_profile['error']}")
            return Response(
                {'error': f"Failed to get Spotify profile: {user_profile['error']}"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Use the authenticated user
        user = request.user
        
        # Save Spotify tokens to user's account
        user.spotify_connected = True
        user.save()
        service = MusicServiceAuthMixin.save_tokens(user, 'spotify', tokens_data)
        
        # Use the response serializer
        response_data = {
            'message': 'Spotify connected successfully',
            'user': {
                'id': user.id,
                'username': user.username,
                'spotify_connected': user.spotify_connected
            },
            'service': {
                'service_type': service.service_type,
                'expires_at': service.expires_at
            }
        }
        response_serializer = SpotifyCallbackResponseSerializer(response_data)
        return Response(response_serializer.data)
        
    except Exception as e:
        logger.error(f"Error in callback_handler: {str(e)}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@login_required
def connection_success(request):
    """Success page after connecting a music service"""
    return render(request, 'music/connection_success.html')


@api_view(['POST'])
@permission_classes([AllowAny])
def apple_music_mobile_token_auth(request):
    """
    Handle Apple Music authentication from mobile app using user token
    This endpoint receives the MusicKit user token from the mobile app
    and validates it according to Apple's authentication flow.
    
    Based on: https://developer.apple.com/documentation/applemusicapi/user-authentication-for-musickit
    """
    try:
        # Use serializer for validation
        serializer = AppleMusicTokenSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Apple Music auth validation failed: {serializer.errors}")
            return Response({
                'error': 'Invalid request data',
                'details': serializer.errors,
                'hint': 'Ensure you provide either music_user_token or userToken from MusicKit'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get validated data
        validated_data = serializer.validated_data
        music_user_token = validated_data.get('music_user_token') or validated_data.get('userToken')
        developer_token = validated_data.get('developer_token') or validated_data.get('developerToken')
        
        # User profile data (optional)
        user_id = validated_data.get('user_id') or validated_data.get('id')
        display_name = validated_data.get('display_name') or validated_data.get('displayName', 'Apple Music User')
        email = validated_data.get('email')
        country = validated_data.get('country')
        is_premium = validated_data.get('is_premium', True)  # Apple Music is a premium service
        
        # Log the request for debugging
        logger.info(f"Apple Music auth request: userToken={bool(music_user_token)}, developerToken={bool(developer_token)}")
        
        # Optional: Developer token can be used for additional validation
        if developer_token:
            logger.info("Developer token provided for additional validation")
        
        # Validate the user token with Apple Music API
        # Note: In a production environment, you would validate this against Apple's servers
        try:
            is_valid = AppleMusicService.validate_user_token(music_user_token)
            if not is_valid:
                logger.warning(f"Apple Music token validation failed for token: {music_user_token[:20]}...")
                return Response(
                    {'error': 'Invalid Apple Music user token'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            logger.error(f"Error validating Apple Music token: {str(e)}")
            return Response(
                {'error': 'Token validation failed'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Prepare user data for account linking
        user_data = {
            'user_id': user_id,
            'display_name': display_name,
            'country': country,
            'premium_status': 'Premium' if is_premium else 'Free'
        }
        
        # Find or create user by email (enables account linking)
        try:
            user, created = find_or_create_user_by_email(email, 'apple', user_data)
            if created:
                logger.info(f"Created new user for Apple Music: {user.username}")
            else:
                logger.info(f"Linked Apple Music to existing user: {user.username}")
        except Exception as e:
            logger.error(f"Error in find_or_create_user_by_email for Apple Music: {str(e)}")
            return Response(
                {'error': f'Failed to create or link user: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
        # Create or update MusicService record
        music_service, created = MusicService.objects.update_or_create(
            user=user,
            service_type='apple',
            defaults={
                'access_token': music_user_token,
                'refresh_token': '',  # Apple Music doesn't use refresh tokens
                'expires_at': timezone.now() + timedelta(days=180),  # Apple Music tokens are long-lived
            }
        )
        
        # Generate JWT token for the app
        refresh = RefreshToken.for_user(user)
        auth_token = str(refresh.access_token)
    
    # Return success response
        response_data = {
            'auth_token': auth_token,
            'user': {
                'id': user.id,
                'name': display_name or user.username,
                'display_name': display_name,
                'email': user.email,
                'bio': user.bio,
                'apple_music_connected': True,
                'is_premium': is_premium,
            }
        }
        
        logger.info(f"Successfully authenticated user {user.username} via Apple Music mobile")
        return Response(response_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in apple_music_mobile_token_auth: {str(e)}")
        logger.error(f"Request data keys: {list(request.data.keys()) if hasattr(request, 'data') else 'No data'}")
        return Response(
            {
                'error': f'Authentication failed: {str(e)}',
                'debug_info': {
                    'received_keys': list(request.data.keys()) if hasattr(request, 'data') else [],
                    'expected_keys': ['music_user_token', 'userToken', 'developer_token', 'email', 'display_name'],
                    'endpoint_docs': 'https://developer.apple.com/documentation/applemusicapi/user-authentication-for-musickit'
                }
            }, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([AllowAny])
def apple_music_mobile_auth(request):
    """
    Start Apple Music auth flow for mobile apps
    
    Based on: https://developer.apple.com/documentation/applemusicapi/user-authentication-for-musickit
    """
    # Apple Music uses MusicKit for authentication
    # This endpoint provides information for the mobile app to handle auth
    auth_info = {
        "auth_type": "musickit",
        "service": "apple_music",
        "flow": {
            "step1": "Initialize MusicKit in your app with your developer token",
            "step2": "Request user authorization using MusicKit.authorize()",
            "step3": "Send the obtained userToken to /api/music/auth/apple/",
            "documentation": "https://developer.apple.com/documentation/applemusicapi/user-authentication-for-musickit"
        },
        "required_tokens": {
            "developer_token": "Required for MusicKit initialization (generate server-side)",
            "user_token": "Obtained from MusicKit.authorize(), send to backend"
        },
        "scopes": ["read", "write"],
        "endpoints": {
            "authenticate": "/api/music/auth/apple/",
            "callback": "/api/music/auth/apple/callback/",
            "refresh": "/api/music/auth/apple/refresh/"
        }
    }
    
    return Response(auth_info)


@api_view(['POST'])
@permission_classes([AllowAny])
def apple_music_callback(request):
    """
    Handle Apple Music callback from mobile app
    This maintains consistency with Spotify structure but Apple Music doesn't use traditional OAuth
    """
    try:
        # Get user token from request
        music_user_token = request.data.get('music_user_token')
        
        if not music_user_token:
            return Response({'error': 'music_user_token is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate the token
        is_valid = AppleMusicService.validate_user_token(music_user_token)
        if not is_valid:
            return Response(
                {'error': 'Invalid Apple Music user token'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Use the authenticated user (this callback requires authentication)
        user = request.user
        
        # Update Apple Music connection status
        user.apple_music_connected = True
        user.save()
        
        # Create or update MusicService record
        music_service, created = MusicService.objects.update_or_create(
            user=user,
            service_type='apple',
            defaults={
                'access_token': music_user_token,
                'refresh_token': '',  # Apple Music doesn't use refresh tokens
                'expires_at': timezone.now() + timedelta(days=180),
            }
        )
        
        # Use response similar to Spotify callback
        response_data = {
        'message': 'Apple Music connected successfully',
        'user': {
                'id': user.id,
                'username': user.username,
                'apple_music_connected': user.apple_music_connected
        },
        'service': {
                'service_type': music_service.service_type,
                'expires_at': music_service.expires_at
            }
        }
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"Error in apple_music_callback: {str(e)}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def apple_music_refresh_token(request):
    """
    Refresh Apple Music token
    Apple Music tokens are long-lived but this maintains consistency with Spotify structure
    """
    try:
        # Get the user's Apple Music service
        try:
            apple_service = MusicService.objects.get(
                user=request.user, 
                service_type='apple'
            )
        except MusicService.DoesNotExist:
            return Response(
                {'error': 'Apple Music not connected'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if token is still valid (Apple Music tokens are long-lived)
        if apple_service.expires_at > timezone.now():
            return Response({
                'access_token': apple_service.access_token,
                'expires_in': int((apple_service.expires_at - timezone.now()).total_seconds()),
                'token_type': 'Bearer',
                'message': 'Token is still valid'
            }, status=status.HTTP_200_OK)
        else:
            # Token expired - user needs to re-authenticate
            apple_service.delete()
            request.user.apple_music_connected = False
            request.user.save()
            
            return Response(
                {'error': 'Apple Music token expired - please re-authenticate'}, 
                status=status.HTTP_401_UNAUTHORIZED
            )
                
    except Exception as e:
        logger.error(f"Unexpected error in Apple Music token refresh: {str(e)}")
        return Response(
            {'error': 'Internal server error during token refresh'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# Then define ViewSets
class MusicServiceViewSet(viewsets.ViewSet):
    """
    API endpoints for music service connections
    """
    permission_classes = [IsAuthenticated]
    serializer_class = MusicServiceSerializer  # Add serializer class
    
    @action(detail=False, methods=['GET'])
    def connected_services(self, request):
        """
        Get all connected music services for the user
        Shows how email-based account linking works across services
        """
        services = MusicService.objects.filter(user=request.user)
        service_data = []
        
        for service in services:
            service_data.append({
                'service_type': service.service_type,
                'connected_at': service.expires_at - timedelta(days=180),  # Approximate
                'is_active': service.expires_at > timezone.now(),
                'expires_at': service.expires_at
            })
        
        # Also include user flags
        user_data = {
            'user_id': request.user.id,
            'username': request.user.username,
            'email': request.user.email,
            'spotify_connected': request.user.spotify_connected,
            'apple_music_connected': request.user.apple_music_connected,
            'bio': request.user.bio,
            'services': service_data
        }
        
        return Response(user_data)
    
    @action(detail=False, methods=['DELETE'], url_path='disconnect/(?P<service_type>[^/.]+)')
    def disconnect_service(self, request, service_type=None):
        """Disconnect a music service"""
        if service_type not in ['spotify', 'apple', 'soundcloud']:
            return Response({'error': f'Invalid service type: {service_type}'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            service = MusicService.objects.get(user=request.user, service_type=service_type)
            service.delete()
            
            # Update user model flags
            if service_type == 'spotify':
                request.user.spotify_connected = False
            elif service_type == 'apple':
                request.user.apple_music_connected = False
            request.user.save()
            
            return Response({'message': f'{service_type} disconnected successfully'})
        except MusicService.DoesNotExist:
            return Response({'error': f'No {service_type} service connected'}, status=status.HTTP_404_NOT_FOUND)


class SpotifyViewSet(viewsets.ViewSet):
    """
    API endpoints for Spotify integration
    """
    permission_classes = [IsAuthenticated]
    serializer_class = SpotifyResponseSerializer  # Add serializer class
    
    def _get_spotify_service(self):
        """Helper to get the user's Spotify service or raise error"""
        try:
            return MusicService.objects.get(user=self.request.user, service_type='spotify')
        except MusicService.DoesNotExist:
            return None
    
    @action(detail=False, methods=['GET'])
    def playlists(self, request):
        """Get user's Spotify playlists"""
        spotify_service = self._get_spotify_service()
        if not spotify_service:
            return Response({"error": "Spotify not connected"}, status=status.HTTP_400_BAD_REQUEST)
        
        limit = request.query_params.get('limit', 50)
        offset = request.query_params.get('offset', 0)
        
        result = SpotifyService.get_user_playlists(spotify_service, limit, offset)
        if 'error' in result:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(result)
    
    @action(detail=True, methods=['GET'], url_path='playlist/(?P<playlist_id>[^/.]+)')
    def playlist(self, request, playlist_id=None):
        """Get a specific Spotify playlist"""
        spotify_service = self._get_spotify_service()
        if not spotify_service:
            return Response({"error": "Spotify not connected"}, status=status.HTTP_400_BAD_REQUEST)
        
        result = SpotifyService.get_playlist(spotify_service, playlist_id)
        if 'error' in result:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(result)
    
    @action(detail=True, methods=['GET'], url_path='playlist/(?P<playlist_id>[^/.]+)/tracks')
    def playlist_tracks(self, request, playlist_id=None):
        """Get tracks from a Spotify playlist"""
        spotify_service = self._get_spotify_service()
        if not spotify_service:
            return Response({"error": "Spotify not connected"}, status=status.HTTP_400_BAD_REQUEST)
        
        limit = request.query_params.get('limit', 100)
        offset = request.query_params.get('offset', 0)
        
        result = SpotifyService.get_playlist_tracks(spotify_service, playlist_id, limit, offset)
        if 'error' in result:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(result)
    
    @action(detail=True, methods=['GET'], url_path='track/(?P<track_id>[^/.]+)')
    def track(self, request, track_id=None):
        """Get details for a specific Spotify track"""
        spotify_service = self._get_spotify_service()
        if not spotify_service:
            return Response({"error": "Spotify not connected"}, status=status.HTTP_400_BAD_REQUEST)
        
        result = SpotifyService.get_track(spotify_service, track_id)
        if 'error' in result:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(result)
    
    @action(detail=False, methods=['GET'])
    def recently_played(self, request):
        """Get user's recently played tracks on Spotify"""
        spotify_service = self._get_spotify_service()
        if not spotify_service:
            return Response({"error": "Spotify not connected"}, status=status.HTTP_400_BAD_REQUEST)
        
        limit = request.query_params.get('limit', 50)
        
        result = SpotifyService.get_recently_played(spotify_service, limit)
        if 'error' in result:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
        
        # Optionally save to RecentTrack model
        if 'items' in result:
            for item in result['items']:
                track = item['track']
                RecentTrack.objects.update_or_create(
                    user=request.user,
                    track_id=track['id'],
                    service='spotify',
                    defaults={
                        'title': track['name'],
                        'artist': track['artists'][0]['name'],
                        'album': track['album']['name'],
                        'album_art': track['album']['images'][0]['url'] if track['album']['images'] else None,
                        'played_at': datetime.strptime(item['played_at'], "%Y-%m-%dT%H:%M:%S.%fZ")
                    }
                )
        
        return Response(result)
    
    @action(detail=False, methods=['GET'])
    def search(self, request):
        """Search for tracks on Spotify"""
        spotify_service = self._get_spotify_service()
        if not spotify_service:
            return Response({"error": "Spotify not connected"}, status=status.HTTP_400_BAD_REQUEST)
        
        query = request.query_params.get('q', '')
        if not query:
            return Response({"error": "Search query is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        limit = request.query_params.get('limit', 20)
        
        result = SpotifyService.search_tracks(spotify_service, query, limit)
        if 'error' in result:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(result)

    @action(detail=False, methods=['GET'])
    def saved_tracks(self, request):
        """Get user's saved/liked tracks on Spotify"""
        spotify_service = self._get_spotify_service()
        if not spotify_service:
            return Response({"error": "Spotify not connected"}, status=status.HTTP_400_BAD_REQUEST)
        
        limit = request.query_params.get('limit', 50)
        offset = request.query_params.get('offset', 0)
        
        result = SpotifyService.get_saved_tracks(spotify_service, limit, offset)
        if 'error' in result:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
        return Response(result)


class MusicTrackViewSet(viewsets.ViewSet):
    """
    API endpoints for selecting music tracks for pins
    """
    permission_classes = [IsAuthenticated]
    serializer_class = MusicTrackSerializer  # Add serializer class
    
    @action(detail=False, methods=['GET'])
    def search(self, request):
        """Search for music tracks"""
        query = request.query_params.get('q', '')
        service = request.query_params.get('service', None)
        limit = int(request.query_params.get('limit', 10))
        
        if not query:
            return Response(
                {"error": "Search query is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        results = search_music(request.user, query, service, limit)
        return Response(results)
    
    @action(detail=False, methods=['GET'])
    def recently_played(self, request):
        """Get user's recently played tracks"""
        service = request.query_params.get('service', None)
        limit = int(request.query_params.get('limit', 10))
        
        results = get_recently_played_tracks(request.user, service, limit)
        return Response(results)
    
    @action(detail=False, methods=['GET'])
    def saved_tracks(self, request):
        """Get user's saved/liked tracks"""
        service = request.query_params.get('service', None)
        limit = int(request.query_params.get('limit', 50))
        offset = int(request.query_params.get('offset', 0))
        
        results = get_saved_tracks(request.user, service, limit, offset)
        return Response(results)
    
    @action(detail=False, methods=['GET'])
    def playlists(self, request):
        """Get user's playlists"""
        service = request.query_params.get('service', None)
        limit = int(request.query_params.get('limit', 20))
        
        results = get_user_playlists(request.user, service, limit)
        return Response(results)
    
    @action(detail=False, methods=['GET'], url_path='playlist/(?P<service>[^/.]+)/(?P<playlist_id>[^/.]+)')
    def playlist_tracks(self, request, service=None, playlist_id=None):
        """Get tracks from a playlist"""
        if not service or not playlist_id:
            return Response(
                {"error": "Service and playlist ID are required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        limit = int(request.query_params.get('limit', 50))
        
        results = get_playlist_tracks(request.user, playlist_id, service, limit)
        if results is None:
            return Response(
                {"error": f"Unable to retrieve playlist tracks from {service}"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return Response(results)
    
    @action(detail=False, methods=['GET'], url_path='track/(?P<service>[^/.]+)/(?P<track_id>[^/.]+)')
    def track_details(self, request, service=None, track_id=None):
        """Get details for a specific track"""
        if not service or not track_id:
            return Response(
                {"error": "Service and track ID are required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        result = get_track_details(request.user, track_id, service)
        if result is None:
            return Response(
                {"error": f"Unable to retrieve track details from {service}"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return Response(result)


class AppleMusicViewSet(viewsets.ViewSet):
    """
    API endpoints for Apple Music integration
    """
    permission_classes = [IsAuthenticated]
    serializer_class = SpotifyResponseSerializer  # Reuse the Spotify response serializer for now
    
    def _get_apple_music_service(self):
        """Helper to get the user's Apple Music service or raise error"""
        try:
            return MusicService.objects.get(user=self.request.user, service_type='apple')
        except MusicService.DoesNotExist:
            return None
    
    @action(detail=False, methods=['GET'])
    def playlists(self, request):
        """Get user's Apple Music playlists"""
        music_service = self._get_apple_music_service()
        if not music_service:
            return Response({'error': 'Apple Music not connected'}, status=status.HTTP_400_BAD_REQUEST)
        
        limit = request.query_params.get('limit', '50')
        offset = request.query_params.get('offset', '0')
        
        result = AppleMusicService.get_user_playlists(music_service, limit, offset)
        if 'error' in result:
            return Response({'error': result['error']}, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(result)
    
    @action(detail=True, methods=['GET'], url_path='playlist/(?P<playlist_id>[^/.]+)')
    def playlist(self, request, playlist_id=None):
        """Get a specific Apple Music playlist"""
        music_service = self._get_apple_music_service()
        if not music_service:
            return Response({'error': 'Apple Music not connected'}, status=status.HTTP_400_BAD_REQUEST)
        
        result = AppleMusicService.get_playlist(music_service, playlist_id)
        if 'error' in result:
            return Response({'error': result['error']}, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(result)
    
    @action(detail=True, methods=['GET'], url_path='playlist/(?P<playlist_id>[^/.]+)/tracks')
    def playlist_tracks(self, request, playlist_id=None):
        """Get tracks from an Apple Music playlist"""
        music_service = self._get_apple_music_service()
        if not music_service:
            return Response({'error': 'Apple Music not connected'}, status=status.HTTP_400_BAD_REQUEST)
        
        limit = request.query_params.get('limit', '100')
        offset = request.query_params.get('offset', '0')
        
        result = AppleMusicService.get_playlist_tracks(music_service, playlist_id, limit, offset)
        if 'error' in result:
            return Response({'error': result['error']}, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(result)
    
    @action(detail=True, methods=['GET'], url_path='track/(?P<track_id>[^/.]+)')
    def track(self, request, track_id=None):
        """Get details for a specific Apple Music track"""
        music_service = self._get_apple_music_service()
        if not music_service:
            return Response({'error': 'Apple Music not connected'}, status=status.HTTP_400_BAD_REQUEST)
        
        result = AppleMusicService.get_track(music_service, track_id)
        if 'error' in result:
            return Response({'error': result['error']}, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(result)
    
    @action(detail=False, methods=['GET'])
    def recently_played(self, request):
        """Get user's recently played tracks from Apple Music"""
        music_service = self._get_apple_music_service()
        if not music_service:
            return Response({'error': 'Apple Music not connected'}, status=status.HTTP_400_BAD_REQUEST)
        
        limit = request.query_params.get('limit', '50')
        
        result = AppleMusicService.get_recently_played(music_service, limit)
        if 'error' in result:
            return Response({'error': result['error']}, status=status.HTTP_400_BAD_REQUEST)
        
        # Store recent tracks in our database for analytics/recommendations
        if 'data' in result:
            store_recent_tracks_apple(request.user, result['data'])
        
        return Response(result)
    
    @action(detail=False, methods=['GET'])
    def search(self, request):
        """Search for tracks on Apple Music"""
        music_service = self._get_apple_music_service()
        if not music_service:
            return Response({'error': 'Apple Music not connected'}, status=status.HTTP_400_BAD_REQUEST)
        
        query = request.query_params.get('q')
        if not query:
            return Response({'error': 'Search query is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        limit = request.query_params.get('limit', '20')
        
        result = AppleMusicService.search_tracks(music_service, query, limit)
        if 'error' in result:
            return Response({'error': result['error']}, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(result)
    
    @action(detail=False, methods=['GET'])
    def saved_tracks(self, request):
        """Get user's saved/liked tracks from Apple Music"""
        music_service = self._get_apple_music_service()
        if not music_service:
            return Response({'error': 'Apple Music not connected'}, status=status.HTTP_400_BAD_REQUEST)
        
        limit = request.query_params.get('limit', '50')
        offset = request.query_params.get('offset', '0')
        
        result = AppleMusicService.get_saved_tracks(music_service, limit, offset)
        if 'error' in result:
            return Response({'error': result['error']}, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(result)

# Helper function to store recently played tracks from Apple Music
def store_recent_tracks_apple(user, tracks_data):
    """Store recently played tracks from Apple Music in our database"""
    for item in tracks_data:
        try:
            # Parse the played_at date - format might vary
            played_at_str = item['attributes'].get('lastPlayedDate', '')
            try:
                played_at = datetime.fromisoformat(played_at_str.replace('Z', '+00:00'))
            except (ValueError, TypeError):
                played_at = timezone.now()
            
            # Create or update the RecentTrack
            RecentTrack.objects.update_or_create(
                user=user,
                track_id=item['id'],
                service='apple',
                defaults={
                    'title': item['attributes']['name'],
                    'artist': item['attributes']['artistName'],
                    'album': item['attributes'].get('albumName', ''),
                    'album_art': item['attributes'].get('artwork', {}).get('url', '').replace('{w}', '300').replace('{h}', '300'),
                    'played_at': played_at
                }
            )
        except Exception as e:
            logger.error(f"Error storing Apple Music recent track: {str(e)}")


@api_view(['POST'])
@permission_classes([AllowAny])
def spotify_mobile_token_auth(request):
    """
    Handle Spotify authentication from mobile app using access token
    This endpoint receives the access token directly from the mobile app
    """
    try:
        # Get data from request
        access_token = request.data.get('access_token')
        user_id = request.data.get('user_id')
        display_name = request.data.get('display_name', 'Spotify User')
        email = request.data.get('email')
        profile_image_url = request.data.get('profile_image_url')
        country = request.data.get('country')
        followers = request.data.get('followers', 0)
        is_premium = request.data.get('is_premium', False)
        
        if not access_token:
            return Response({'error': 'Access token is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Verify the token with Spotify API
        headers = {'Authorization': f'Bearer {access_token}'}
        try:
            spotify_response = requests.get(
                'https://api.spotify.com/v1/me',
                headers=headers,
                timeout=10
            )
            
            if spotify_response.status_code != 200:
                return Response(
                    {'error': 'Invalid Spotify access token'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            spotify_user_data = spotify_response.json()
            
        except requests.RequestException as e:
            logger.error(f"Error verifying Spotify token: {str(e)}")
            return Response(
                {'error': 'Failed to verify Spotify token'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Use the verified data from Spotify API
        verified_user_id = spotify_user_data.get('id', user_id)
        verified_email = spotify_user_data.get('email', email)
        verified_display_name = spotify_user_data.get('display_name', display_name)
        verified_country = spotify_user_data.get('country', country)
        verified_is_premium = spotify_user_data.get('product') == 'premium'
        
        # Get profile image
        verified_profile_image = None
        if spotify_user_data.get('images') and len(spotify_user_data['images']) > 0:
            verified_profile_image = spotify_user_data['images'][0].get('url')
        elif profile_image_url:
            verified_profile_image = profile_image_url
        
        # Prepare user data for account linking
        user_data = {
            'user_id': verified_user_id,
            'display_name': verified_display_name,
            'country': verified_country,
            'premium_status': 'Premium' if verified_is_premium else 'Free'
        }
        
        user_exists = False
        # Find or create user by email (enables account linking)
        try:
            user, created = find_or_create_user_by_email(verified_email, 'spotify', user_data)
            if created:
                logger.info(f"Created new user for Spotify: {user.username}")
                user_exists = False
            else:
                logger.info(f"Linked Spotify to existing user: {user.username}")
                user_exists = True
        except Exception as e:
            logger.error(f"Error in find_or_create_user_by_email for Spotify: {str(e)}")
            return Response(
                {'error': f'Failed to create or link user: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
        # Create or update MusicService record
        music_service, created = MusicService.objects.update_or_create(
            user=user,
            service_type='spotify',
            defaults={
                'access_token': access_token,
                'refresh_token': '',  # SDK handles refresh
                'expires_at': timezone.now() + timedelta(hours=1),  # Spotify tokens typically last 1 hour
            }
        )
        
        # Generate JWT token for the app
        refresh = RefreshToken.for_user(user)
        auth_token = str(refresh.access_token)
        
        # Return success response
        response_data = {
            'auth_token': auth_token,
            'user_exists': user_exists,
            'user': {
                'id': user.id,
                'name': verified_display_name or user.username,
                'display_name': verified_display_name,
                'email': user.email,
                'profile_image_url': verified_profile_image,
                'bio': user.bio,
                'spotify_connected': True,
                'is_premium': verified_is_premium,
            }
        }
        
        logger.info(f"Successfully authenticated user {user.username} via Spotify mobile")
        return Response(response_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in spotify_mobile_token_auth: {str(e)}")
        return Response(
            {'error': f'Authentication failed: {str(e)}'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# PKCE Authentication Endpoints
@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
@ratelimit(key='ip', rate='100/m', method='POST')
def spotify_pkce_token_exchange(request):
    """
    Exchange authorization code for access/refresh tokens using PKCE
    Endpoint: POST /api/spotify/token/exchange
    """
    try:
        # Validate request data
        serializer = SpotifyPKCETokenExchangeSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Invalid PKCE token exchange request: {serializer.errors}")
            return Response(
                {'error': 'Missing required parameters', 'details': serializer.errors}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        code = serializer.validated_data['code']
        code_verifier = serializer.validated_data['code_verifier']
        redirect_uri = serializer.validated_data['redirect_uri']
        
        # Validate redirect URI
        if redirect_uri not in settings.ALLOWED_REDIRECT_URIS:
            logger.warning(f"Invalid redirect URI attempted: {redirect_uri}")
            return Response(
                {'error': 'Invalid redirect URI'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Prepare token exchange request to Spotify
        token_data = {
            'grant_type': 'authorization_code',
            'code': code,
            'redirect_uri': redirect_uri,
            'client_id': settings.SPOTIFY_CLIENT_ID,
            'client_secret': settings.SPOTIFY_CLIENT_SECRET,
            'code_verifier': code_verifier
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        # Exchange code for tokens with Spotify
        response = requests.post(
            settings.SPOTIFY_TOKEN_URL,
            data=urlencode(token_data),
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            token_response = response.json()
            
            # Validate response with serializer
            response_serializer = SpotifyPKCETokenResponseSerializer(data=token_response)
            if response_serializer.is_valid():
                logger.info("Successful PKCE token exchange")
                return Response(response_serializer.validated_data, status=status.HTTP_200_OK)
            else:
                logger.error(f"Invalid token response from Spotify: {response_serializer.errors}")
                return Response(
                    {'error': 'Invalid response from Spotify'}, 
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        else:
            # Log the error but don't expose details to client
            error_details = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
            logger.error(f"Spotify token exchange failed: {response.status_code} - {error_details}")
            
            if response.status_code == 400:
                return Response(
                    {'error': 'Invalid authorization code or parameters'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            else:
                return Response(
                    {'error': 'Token exchange failed'}, 
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
                
    except requests.RequestException as e:
        logger.error(f"Network error during token exchange: {str(e)}")
        return Response(
            {'error': 'Network error during token exchange'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    except Exception as e:
        logger.error(f"Unexpected error in PKCE token exchange: {str(e)}")
        return Response(
            {'error': 'Internal server error during token exchange'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
@ratelimit(key='ip', rate='100/m', method='POST')
def spotify_pkce_token_refresh(request):
    """
    Refresh access token using refresh token
    Endpoint: POST /api/spotify/token/refresh
    """
    try:
        # Validate request data
        serializer = SpotifyPKCETokenRefreshSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Invalid token refresh request: {serializer.errors}")
            return Response(
                {'error': 'Missing refresh token', 'details': serializer.errors}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        refresh_token = serializer.validated_data['refresh_token']
        
        # Prepare token refresh request to Spotify
        token_data = {
            'grant_type': 'refresh_token',
            'refresh_token': refresh_token,
            'client_id': settings.SPOTIFY_CLIENT_ID,
            'client_secret': settings.SPOTIFY_CLIENT_SECRET
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        # Refresh token with Spotify
        response = requests.post(
            settings.SPOTIFY_TOKEN_URL,
            data=urlencode(token_data),
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            token_response = response.json()
            
            # Validate response with serializer
            response_serializer = SpotifyPKCETokenResponseSerializer(data=token_response)
            if response_serializer.is_valid():
                logger.info("Successful token refresh")
                return Response(response_serializer.validated_data, status=status.HTTP_200_OK)
            else:
                logger.error(f"Invalid refresh response from Spotify: {response_serializer.errors}")
                return Response(
                    {'error': 'Invalid response from Spotify'}, 
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        else:
            # Log the error but don't expose details to client
            error_details = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
            logger.error(f"Spotify token refresh failed: {response.status_code} - {error_details}")
            
            if response.status_code == 400:
                return Response(
                    {'error': 'Invalid refresh token'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            else:
                return Response(
                    {'error': 'Token refresh failed'}, 
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
                
    except requests.RequestException as e:
        logger.error(f"Network error during token refresh: {str(e)}")
        return Response(
            {'error': 'Network error during token refresh'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    except Exception as e:
        logger.error(f"Unexpected error in token refresh: {str(e)}")
        return Response(
            {'error': 'Internal server error during token refresh'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def spotify_refresh_token(request):
    """
    Refresh Spotify access token using stored refresh token
    This endpoint allows the mobile app to refresh tokens through the backend
    """
    try:
        # Get the user's Spotify service
        try:
            spotify_service = MusicService.objects.get(
                user=request.user, 
                service_type='spotify'
            )
        except MusicService.DoesNotExist:
            return Response(
                {'error': 'Spotify not connected'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if we have a refresh token
        if not spotify_service.refresh_token:
            return Response(
                {'error': 'No refresh token available'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Prepare token refresh request to Spotify
        token_data = {
            'grant_type': 'refresh_token',
            'refresh_token': spotify_service.refresh_token,
            'client_id': settings.SPOTIFY_CLIENT_ID,
            'client_secret': settings.SPOTIFY_CLIENT_SECRET
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        # Refresh token with Spotify
        response = requests.post(
            settings.SPOTIFY_TOKEN_URL,
            data=urlencode(token_data),
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            token_response = response.json()
            
            # Update the stored tokens
            spotify_service.access_token = token_response['access_token']
            if 'refresh_token' in token_response:
                spotify_service.refresh_token = token_response['refresh_token']
            spotify_service.expires_at = timezone.now() + timedelta(
                seconds=token_response.get('expires_in', 3600)
            )
            spotify_service.save()
            
            logger.info(f"Spotify token refreshed for user {request.user.username}")
            
            return Response({
                'access_token': token_response['access_token'],
                'expires_in': token_response.get('expires_in', 3600),
                'token_type': token_response.get('token_type', 'Bearer'),
                'scope': token_response.get('scope', ''),
                'refresh_token': token_response.get('refresh_token', spotify_service.refresh_token)
            }, status=status.HTTP_200_OK)
        else:
            error_details = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
            logger.error(f"Spotify token refresh failed: {response.status_code} - {error_details}")
            
            if response.status_code == 400:
                # Invalid refresh token - clear the service
                spotify_service.delete()
                request.user.spotify_connected = False
                request.user.save()
                
                return Response(
                    {'error': 'Invalid refresh token - please re-authenticate'}, 
                    status=status.HTTP_401_UNAUTHORIZED
                )
            else:
                return Response(
                    {'error': 'Token refresh failed'}, 
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
                
    except requests.RequestException as e:
        logger.error(f"Network error during token refresh: {str(e)}")
        return Response(
            {'error': 'Network error during token refresh'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    except Exception as e:
        logger.error(f"Unexpected error in token refresh: {str(e)}")
        return Response(
            {'error': 'Internal server error during token refresh'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# Music Chat Views

class MusicChatViewSet(viewsets.ViewSet):
    """
    API endpoints for music chat functionality
    """
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['GET'])
    def conversations(self, request):
        """Get user's music chat conversations"""
        from .models import MusicChatConversation
        from .serializers import MusicChatConversationSerializer
        
        conversations = MusicChatConversation.objects.filter(
            participants=request.user
        ).prefetch_related('participants', 'messages')
        
        serializer = MusicChatConversationSerializer(
            conversations, 
            many=True, 
            context={'request': request}
        )
        
        return Response(serializer.data)
    
    @action(detail=False, methods=['POST'])
    def send_message(self, request):
        """Send a music recommendation or text message"""
        from .models import MusicChatConversation, MusicChatMessage
        from .serializers import MusicChatMessageCreateSerializer, MusicChatMessageSerializer
        from friends.models import Friend
        from django.db.models import Q
        from django.db import transaction
        
        serializer = MusicChatMessageCreateSerializer(
            data=request.data, 
            context={'request': request}
        )
        
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        recipient_id = serializer.validated_data['recipient_id']
        
        try:
            recipient = User.objects.get(id=recipient_id)
        except User.DoesNotExist:
            return Response(
                {'error': 'Recipient not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Check if users are friends
        is_friend = Friend.objects.filter(
            Q(requester=request.user, recipient=recipient) |
            Q(requester=recipient, recipient=request.user),
            status='accepted'
        ).exists()
        
        if not is_friend:
            return Response(
                {'error': 'Can only send messages to friends'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        with transaction.atomic():
            # Get or create conversation
            conversation = MusicChatConversation.objects.filter(
                participants=request.user
            ).filter(
                participants=recipient
            ).first()
            
            if not conversation:
                conversation = MusicChatConversation.objects.create()
                conversation.participants.set([request.user, recipient])
            
            # Create message
            message_data = serializer.validated_data.copy()
            message_data.pop('recipient_id')
            
            message = MusicChatMessage.objects.create(
                conversation=conversation,
                sender=request.user,
                **message_data
            )
            
            # Update conversation timestamp
            conversation.save()  # This triggers auto_now on last_message_at
        
        # Send notification
        try:
            from notifications.utils import notify_music_chat_message
            notify_music_chat_message(
                recipient=recipient,
                sender=request.user, 
                message_preview=message.get_preview_text()
            )
        except Exception as e:
            logger.error(f"Failed to send music chat notification: {str(e)}")
        
        # Return the created message
        response_serializer = MusicChatMessageSerializer(
            message, 
            context={'request': request}
        )
        
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)
    
    @action(detail=True, methods=['GET'])
    def messages(self, request, pk=None):
        """Get messages in a conversation"""
        from .models import MusicChatConversation, MusicChatMessage
        from .serializers import MusicChatMessageSerializer
        
        try:
            conversation = MusicChatConversation.objects.get(
                id=pk,
                participants=request.user
            )
        except MusicChatConversation.DoesNotExist:
            return Response(
                {'error': 'Conversation not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        messages = conversation.messages.select_related('sender').prefetch_related(
            'reactions__user'
        ).order_by('created_at')
        
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 50)
        
        try:
            page = int(page)
            page_size = min(int(page_size), 100)  # Max 100 messages per page
        except ValueError:
            page = 1
            page_size = 50
        
        start = (page - 1) * page_size
        end = start + page_size
        
        paginated_messages = messages[start:end]
        
        serializer = MusicChatMessageSerializer(
            paginated_messages, 
            many=True, 
            context={'request': request}
        )
        
        return Response({
            'messages': serializer.data,
            'page': page,
            'page_size': page_size,
            'total_count': messages.count(),
            'has_next': end < messages.count()
        })
    
    @action(detail=True, methods=['POST'])
    def mark_read(self, request, pk=None):
        """Mark messages in a conversation as read"""
        from .models import MusicChatConversation
        
        try:
            conversation = MusicChatConversation.objects.get(
                id=pk,
                participants=request.user
            )
        except MusicChatConversation.DoesNotExist:
            return Response(
                {'error': 'Conversation not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Mark all unread messages from other participants as read
        unread_messages = conversation.messages.filter(
            is_read=False
        ).exclude(sender=request.user)
        
        count = unread_messages.update(is_read=True)
        
        return Response({
            'message': f'Marked {count} messages as read'
        })
    
    @action(detail=True, methods=['POST', 'DELETE'], url_path='messages/(?P<message_id>[^/.]+)/react')
    def react_to_message(self, request, pk=None, message_id=None):
        """React to a message with an emoji (POST) or remove reaction (DELETE)"""
        from .models import MusicChatConversation, MusicChatMessage, MusicChatReaction
        from .serializers import MusicChatReactionCreateSerializer, MusicChatReactionSerializer
        
        try:
            conversation = MusicChatConversation.objects.get(
                id=pk,
                participants=request.user
            )
            message = conversation.messages.get(id=message_id)
        except (MusicChatConversation.DoesNotExist, MusicChatMessage.DoesNotExist):
            return Response(
                {'error': 'Conversation or message not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        if request.method == 'POST':
            # Add or update reaction
            serializer = MusicChatReactionCreateSerializer(data=request.data)
            
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            reaction_emoji = serializer.validated_data['reaction']
            
            # Update or create reaction
            reaction, created = MusicChatReaction.objects.update_or_create(
                message=message,
                user=request.user,
                defaults={'reaction': reaction_emoji}
            )
            
            response_serializer = MusicChatReactionSerializer(
                reaction, 
                context={'request': request}
            )
            
            status_code = status.HTTP_201_CREATED if created else status.HTTP_200_OK
            return Response(response_serializer.data, status=status_code)
        
        elif request.method == 'DELETE':
            # Remove reaction
            try:
                reaction = MusicChatReaction.objects.get(
                    message=message,
                    user=request.user
                )
                reaction.delete()
                
                return Response(
                    {'message': 'Reaction removed successfully'}, 
                    status=status.HTTP_204_NO_CONTENT
                )
            except MusicChatReaction.DoesNotExist:
                return Response(
                    {'error': 'Reaction not found'}, 
                    status=status.HTTP_404_NOT_FOUND
                )
    
    # Remove the separate remove_reaction method since it's now handled above


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def apple_music_sync_token(request):
    """
    Sync and validate Apple Music token
    This endpoint checks the current token status and optionally updates token information
    """
    try:
        # Get the user's Apple Music service
        try:
            apple_service = MusicService.objects.get(
                user=request.user, 
                service_type='apple'
            )
        except MusicService.DoesNotExist:
            return Response(
                {'error': 'Apple Music not connected'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get optional new token from request
        new_user_token = request.data.get('music_user_token') or request.data.get('userToken')
        
        if new_user_token:
            # Validate the new token
            try:
                is_valid = AppleMusicService.validate_user_token(new_user_token)
                if not is_valid:
                    return Response(
                        {'error': 'Invalid Apple Music user token'}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                # Update the stored token
                apple_service.access_token = new_user_token
                apple_service.expires_at = timezone.now() + timedelta(days=180)
                apple_service.save()
                
                logger.info(f"Apple Music token synced for user {request.user.username}")
                
            except Exception as e:
                logger.error(f"Error validating new Apple Music token: {str(e)}")
                return Response(
                    {'error': 'Token validation failed'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # Check current token status
        is_expired = apple_service.expires_at <= timezone.now()
        
        response_data = {
            'service_type': 'apple',
            'is_connected': True,
            'is_expired': is_expired,
            'expires_at': apple_service.expires_at,
            'token_updated': bool(new_user_token),
            'user': {
                'id': request.user.id,
                'username': request.user.username,
                'apple_music_connected': request.user.apple_music_connected
            }
        }
        
        if is_expired:
            response_data['message'] = 'Token expired - please re-authenticate'
            return Response(response_data, status=status.HTTP_401_UNAUTHORIZED)
        else:
            response_data['message'] = 'Token is valid and synced'
            return Response(response_data, status=status.HTTP_200_OK)
            
    except Exception as e:
        logger.error(f"Error in apple_music_sync_token: {str(e)}")
        return Response(
            {'error': 'Internal server error during token sync'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )



