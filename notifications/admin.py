from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Count
from django.urls import reverse
from django.utils.safestring import mark_safe
import json

from .models import (
    Notification, NotificationTemplate, UserNotificationSettings,
    OneSignalPlayerID, NotificationBatch
)


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """
    Admin interface for Notification model
    """
    list_display = [
        'id', 'title_short', 'recipient_link', 'notification_type', 
        'category', 'priority', 'is_read', 'is_sent', 'delivery_status', 
        'created_at'
    ]
    list_filter = [
        'notification_type', 'category', 'priority', 'is_read', 
        'is_sent', 'delivery_status', 'created_at'
    ]
    search_fields = ['title', 'message', 'recipient__username', 'recipient__email']
    readonly_fields = [
        'id', 'onesignal_id', 'onesignal_response_display', 'created_at', 
        'updated_at', 'sent_at', 'read_at'
    ]
    date_hierarchy = 'created_at'
    ordering = ['-created_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('recipient', 'notification_type', 'category', 'priority')
        }),
        ('Content', {
            'fields': ('title', 'message', 'image_url', 'action_data_display')
        }),
        ('Status', {
            'fields': ('is_read', 'read_at', 'is_sent', 'sent_at', 'delivery_status')
        }),
        ('OneSignal Integration', {
            'fields': ('onesignal_id', 'onesignal_response_display'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def title_short(self, obj):
        """Display truncated title"""
        return obj.title[:50] + "..." if len(obj.title) > 50 else obj.title
    title_short.short_description = 'Title'
    
    def recipient_link(self, obj):
        """Link to recipient user"""
        url = reverse('admin:auth_user_change', args=[obj.recipient.id])
        return format_html('<a href="{}">{}</a>', url, obj.recipient.username)
    recipient_link.short_description = 'Recipient'
    
    def action_data_display(self, obj):
        """Display formatted action data"""
        if obj.action_data:
            return mark_safe(f'<pre>{json.dumps(obj.action_data, indent=2)}</pre>')
        return '-'
    action_data_display.short_description = 'Action Data'
    
    def onesignal_response_display(self, obj):
        """Display formatted OneSignal response"""
        if obj.onesignal_response:
            return mark_safe(f'<pre>{json.dumps(obj.onesignal_response, indent=2)}</pre>')
        return '-'
    onesignal_response_display.short_description = 'OneSignal Response'
    
    actions = ['mark_as_read', 'mark_as_unread', 'resend_notification']
    
    def mark_as_read(self, request, queryset):
        """Mark selected notifications as read"""
        updated = queryset.update(is_read=True)
        self.message_user(request, f'{updated} notifications marked as read.')
    mark_as_read.short_description = 'Mark selected notifications as read'
    
    def mark_as_unread(self, request, queryset):
        """Mark selected notifications as unread"""
        updated = queryset.update(is_read=False, read_at=None)
        self.message_user(request, f'{updated} notifications marked as unread.')
    mark_as_unread.short_description = 'Mark selected notifications as unread'
    
    def resend_notification(self, request, queryset):
        """Resend selected notifications"""
        from .manager import notification_manager
        
        success_count = 0
        for notification in queryset:
            if notification_manager._send_notification(notification):
                success_count += 1
        
        self.message_user(request, f'{success_count} notifications resent successfully.')
    resend_notification.short_description = 'Resend selected notifications'


@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    """
    Admin interface for NotificationTemplate model
    """
    list_display = [
        'notification_type', 'title_template_short', 'category', 
        'default_priority', 'is_active', 'created_at'
    ]
    list_filter = ['category', 'default_priority', 'is_active', 'created_at']
    search_fields = ['notification_type', 'title_template', 'message_template']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('notification_type', 'category', 'default_priority', 'is_active')
        }),
        ('Template Content', {
            'fields': ('title_template', 'message_template'),
            'description': 'Use Django template syntax for dynamic content. Available variables depend on notification type.'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def title_template_short(self, obj):
        """Display truncated title template"""
        return obj.title_template[:50] + "..." if len(obj.title_template) > 50 else obj.title_template
    title_template_short.short_description = 'Title Template'


@admin.register(UserNotificationSettings)
class UserNotificationSettingsAdmin(admin.ModelAdmin):
    """
    Admin interface for UserNotificationSettings model
    """
    list_display = [
        'user_link', 'push_notifications_enabled', 'email_notifications_enabled',
        'quiet_hours_enabled', 'max_daily_notifications', 'updated_at'
    ]
    list_filter = [
        'push_notifications_enabled', 'email_notifications_enabled',
        'quiet_hours_enabled', 'created_at'
    ]
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Category Preferences', {
            'fields': (
                'map_enabled', 'social_enabled', 'music_enabled', 
                'gamification_enabled', 'collection_enabled', 
                'exploration_enabled', 'customization_enabled', 'general_enabled'
            )
        }),
        ('Delivery Preferences', {
            'fields': ('push_notifications_enabled', 'email_notifications_enabled')
        }),
        ('Quiet Hours', {
            'fields': ('quiet_hours_enabled', 'quiet_start_time', 'quiet_end_time')
        }),
        ('Limits', {
            'fields': ('max_daily_notifications',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def user_link(self, obj):
        """Link to user"""
        url = reverse('admin:auth_user_change', args=[obj.user.id])
        return format_html('<a href="{}">{}</a>', url, obj.user.username)
    user_link.short_description = 'User'


@admin.register(OneSignalPlayerID)
class OneSignalPlayerIDAdmin(admin.ModelAdmin):
    """
    Admin interface for OneSignalPlayerID model
    """
    list_display = [
        'user_link', 'player_id_short', 'platform', 'is_active', 'created_at'
    ]
    list_filter = ['platform', 'is_active', 'created_at']
    search_fields = ['user__username', 'player_id']
    readonly_fields = ['created_at', 'updated_at']
    
    def user_link(self, obj):
        """Link to user"""
        url = reverse('admin:auth_user_change', args=[obj.user.id])
        return format_html('<a href="{}">{}</a>', url, obj.user.username)
    user_link.short_description = 'User'
    
    def player_id_short(self, obj):
        """Display truncated player ID"""
        return obj.player_id[:20] + "..." if len(obj.player_id) > 20 else obj.player_id
    player_id_short.short_description = 'Player ID'


@admin.register(NotificationBatch)
class NotificationBatchAdmin(admin.ModelAdmin):
    """
    Admin interface for NotificationBatch model
    """
    list_display = [
        'batch_key_short', 'recipient_link', 'notification_type', 
        'event_count', 'is_sent', 'first_event_at', 'last_event_at'
    ]
    list_filter = ['notification_type', 'is_sent', 'first_event_at']
    search_fields = ['batch_key', 'recipient__username']
    readonly_fields = [
        'first_event_at', 'last_event_at', 'aggregated_data_display', 'sent_at'
    ]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('batch_key', 'recipient', 'notification_type')
        }),
        ('Configuration', {
            'fields': ('batch_size', 'batch_window_minutes')
        }),
        ('Status', {
            'fields': ('event_count', 'is_sent', 'sent_at')
        }),
        ('Timeline', {
            'fields': ('first_event_at', 'last_event_at')
        }),
        ('Data', {
            'fields': ('aggregated_data_display',),
            'classes': ('collapse',)
        }),
    )
    
    def batch_key_short(self, obj):
        """Display truncated batch key"""
        return obj.batch_key[:30] + "..." if len(obj.batch_key) > 30 else obj.batch_key
    batch_key_short.short_description = 'Batch Key'
    
    def recipient_link(self, obj):
        """Link to recipient user"""
        url = reverse('admin:auth_user_change', args=[obj.recipient.id])
        return format_html('<a href="{}">{}</a>', url, obj.recipient.username)
    recipient_link.short_description = 'Recipient'
    
    def aggregated_data_display(self, obj):
        """Display formatted aggregated data"""
        if obj.aggregated_data:
            return mark_safe(f'<pre>{json.dumps(obj.aggregated_data, indent=2)}</pre>')
        return '-'
    aggregated_data_display.short_description = 'Aggregated Data'
    
    actions = ['send_batches']
    
    def send_batches(self, request, queryset):
        """Send selected batches"""
        from .manager import notification_manager
        
        success_count = 0
        for batch in queryset.filter(is_sent=False):
            try:
                notification_manager._send_batched_notification(batch)
                success_count += 1
            except Exception as e:
                pass
        
        self.message_user(request, f'{success_count} batches sent successfully.')
    send_batches.short_description = 'Send selected batches'


# Custom admin site configuration
admin.site.site_header = "BOP Maps Notifications Admin"
admin.site.site_title = "Notifications Admin"
admin.site.index_title = "Notification Management"
