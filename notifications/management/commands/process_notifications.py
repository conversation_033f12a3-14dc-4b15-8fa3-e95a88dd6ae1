from django.core.management.base import BaseCommand
from django.utils import timezone
from notifications.manager import notification_manager
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Management command to process batched notifications
    
    Usage:
        python manage.py process_notifications
        
    Can be run via cron job:
        */5 * * * * cd /path/to/project && python manage.py process_notifications
    """
    help = 'Process pending batched notifications'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be processed without actually sending notifications',
        )
    
    def handle(self, *args, **options):
        verbose = options['verbose']
        dry_run = options['dry_run']
        
        if verbose:
            self.stdout.write(
                self.style.SUCCESS(f'Starting batch notification processing at {timezone.now()}')
            )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No notifications will be sent')
            )
            # TODO: Add dry run functionality to show pending batches
            return
        
        try:
            # Process batched notifications
            processed_count = notification_manager.process_batched_notifications()
            
            if verbose or processed_count > 0:
                self.stdout.write(
                    self.style.SUCCESS(f'Successfully processed {processed_count} batched notifications')
                )
            
            if verbose:
                self.stdout.write(
                    self.style.SUCCESS(f'Batch processing completed at {timezone.now()}')
                )
                
        except Exception as e:
            logger.error(f'Error processing batched notifications: {str(e)}')
            self.stdout.write(
                self.style.ERROR(f'Error processing notifications: {str(e)}')
            )
            raise 