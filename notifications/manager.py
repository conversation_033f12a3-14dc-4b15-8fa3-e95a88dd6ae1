import logging
from typing import List, Dict, Any, Optional, Union
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from django.db import transaction
from django.template import Template, Context
from datetime import timed<PERSON><PERSON>

from .models import (
    Notification, NotificationTemplate, NotificationBatch, 
    UserNotificationSettings, NotificationCategory, NotificationType,
    NotificationPriority
)
from .services import onesignal_service

logger = logging.getLogger(__name__)
User = get_user_model()


class NotificationManager:
    """
    Central manager for handling all notification operations
    """
    
    def __init__(self):
        self.onesignal = onesignal_service
    
    def create_notification(
        self,
        recipient: User,
        notification_type: str,
        title: str = None,
        message: str = None,
        category: str = None,
        priority: str = NotificationPriority.MEDIUM,
        image_url: str = None,
        action_data: Dict[str, Any] = None,
        content_object = None,
        template_data: Dict[str, Any] = None,
        send_immediately: bool = True,
        batch_key: str = None
    ) -> Optional[Notification]:
        """
        Create a new notification
        
        Args:
            recipient: User to receive the notification
            notification_type: Type of notification
            title: Notification title (optional if using template)
            message: Notification message (optional if using template)
            category: Notification category
            priority: Notification priority
            image_url: Optional image URL
            action_data: Additional data for actions
            content_object: Related Django model instance
            template_data: Data for template rendering
            send_immediately: Whether to send immediately or queue
            batch_key: Key for batching similar notifications
        
        Returns:
            Created Notification instance or None if blocked
        """
        try:
            # Check if user allows this type of notification
            if not self._should_send_to_user(recipient, notification_type, category):
                logger.info(f"Notification blocked for user {recipient.username}: {notification_type}")
                return None
            
            # Handle batching if batch_key is provided
            if batch_key:
                return self._handle_batched_notification(
                    recipient=recipient,
                    notification_type=notification_type,
                    batch_key=batch_key,
                    title=title,
                    message=message,
                    category=category,
                    priority=priority,
                    image_url=image_url,
                    action_data=action_data,
                    content_object=content_object,
                    template_data=template_data
                )
            
            # Use template if title/message not provided
            if not title or not message:
                template_result = self._render_from_template(
                    notification_type, template_data or {}
                )
                if template_result:
                    title = title or template_result['title']
                    message = message or template_result['message']
                    category = category or template_result['category']
                    priority = priority or template_result['priority']
            
            # Create notification record
            notification = self._create_notification_record(
                recipient=recipient,
                notification_type=notification_type,
                title=title,
                message=message,
                category=category,
                priority=priority,
                image_url=image_url,
                action_data=action_data,
                content_object=content_object
            )
            
            # Send immediately if requested
            if send_immediately:
                self._send_notification(notification)
            
            return notification
            
        except Exception as e:
            logger.error(f"Failed to create notification: {str(e)}")
            return None
    
    def send_bulk_notification(
        self,
        recipients: List[User],
        notification_type: str,
        title: str = None,
        message: str = None,
        category: str = None,
        priority: str = NotificationPriority.MEDIUM,
        image_url: str = None,
        action_data: Dict[str, Any] = None,
        template_data: Dict[str, Any] = None,
        filter_preferences: bool = True
    ) -> Dict[str, Any]:
        """
        Send notification to multiple users
        
        Returns:
            Dictionary with success count and failed users
        """
        try:
            # Filter recipients based on preferences
            if filter_preferences:
                recipients = [
                    user for user in recipients
                    if self._should_send_to_user(user, notification_type, category)
                ]
            
            if not recipients:
                return {
                    'success': False,
                    'message': 'No eligible recipients after filtering',
                    'sent_count': 0,
                    'failed_count': 0
                }
            
            # Use template if title/message not provided
            if not title or not message:
                template_result = self._render_from_template(
                    notification_type, template_data or {}
                )
                if template_result:
                    title = title or template_result['title']
                    message = message or template_result['message']
                    category = category or template_result['category']
                    priority = priority or template_result['priority']
            
            notifications = []
            sent_count = 0
            failed_count = 0
            
            # Create notification records for all recipients
            with transaction.atomic():
                for recipient in recipients:
                    try:
                        notification = self._create_notification_record(
                            recipient=recipient,
                            notification_type=notification_type,
                            title=title,
                            message=message,
                            category=category,
                            priority=priority,
                            image_url=image_url,
                            action_data=action_data
                        )
                        notifications.append(notification)
                    except Exception as e:
                        logger.error(f"Failed to create notification for {recipient.username}: {str(e)}")
                        failed_count += 1
            
            # Send via OneSignal
            if notifications:
                onesignal_result = self.onesignal.send_to_users(
                    users=recipients,
                    title=title,
                    message=message,
                    data=action_data,
                    image_url=image_url,
                    priority='high' if priority == NotificationPriority.HIGH else 'normal'
                )
                
                if onesignal_result['success']:
                    # Mark all as sent
                    notification_ids = [n.id for n in notifications]
                    Notification.objects.filter(id__in=notification_ids).update(
                        is_sent=True,
                        sent_at=timezone.now(),
                        delivery_status='sent',
                        onesignal_id=onesignal_result.get('notification_id'),
                        onesignal_response=onesignal_result.get('response', {})
                    )
                    sent_count = len(notifications)
                else:
                    logger.error(f"OneSignal bulk send failed: {onesignal_result['error']}")
                    failed_count += len(notifications)
            
            return {
                'success': sent_count > 0,
                'sent_count': sent_count,
                'failed_count': failed_count,
                'total_recipients': len(recipients)
            }
            
        except Exception as e:
            logger.error(f"Bulk notification failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'sent_count': 0,
                'failed_count': len(recipients)
            }
    
    def process_batched_notifications(self):
        """
        Process pending batched notifications
        Should be called periodically (e.g., via cron job)
        """
        try:
            # Get batches that should be sent
            batches_to_send = NotificationBatch.objects.filter(
                is_sent=False
            ).select_related('recipient')
            
            sent_count = 0
            
            for batch in batches_to_send:
                if batch.should_send():
                    try:
                        self._send_batched_notification(batch)
                        sent_count += 1
                    except Exception as e:
                        logger.error(f"Failed to send batch {batch.batch_key}: {str(e)}")
            
            logger.info(f"Processed {sent_count} batched notifications")
            return sent_count
            
        except Exception as e:
            logger.error(f"Failed to process batched notifications: {str(e)}")
            return 0
    
    def mark_as_read(self, notification_id: int, user: User = None) -> bool:
        """
        Mark a notification as read
        """
        try:
            query = Notification.objects.filter(id=notification_id)
            if user:
                query = query.filter(recipient=user)
            
            updated = query.update(
                is_read=True,
                read_at=timezone.now()
            )
            
            return updated > 0
            
        except Exception as e:
            logger.error(f"Failed to mark notification as read: {str(e)}")
            return False
    
    def mark_all_as_read(self, user: User, category: str = None) -> int:
        """
        Mark all notifications as read for a user
        """
        try:
            query = Notification.objects.filter(
                recipient=user,
                is_read=False
            )
            
            if category and category != NotificationCategory.ALL:
                query = query.filter(category=category)
            
            updated = query.update(
                is_read=True,
                read_at=timezone.now()
            )
            
            logger.info(f"Marked {updated} notifications as read for {user.username}")
            return updated
            
        except Exception as e:
            logger.error(f"Failed to mark all notifications as read: {str(e)}")
            return 0
    
    def get_user_notifications(
        self,
        user: User,
        category: str = None,
        is_read: bool = None,
        limit: int = 50,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        Get notifications for a user with filtering and pagination
        """
        try:
            query = Notification.objects.filter(recipient=user)
            
            if category and category != NotificationCategory.ALL:
                query = query.filter(category=category)
            
            if is_read is not None:
                query = query.filter(is_read=is_read)
            
            total_count = query.count()
            notifications = query[offset:offset + limit]
            
            return {
                'notifications': list(notifications),
                'total_count': total_count,
                'unread_count': Notification.objects.filter(
                    recipient=user,
                    is_read=False
                ).count()
            }
            
        except Exception as e:
            logger.error(f"Failed to get user notifications: {str(e)}")
            return {
                'notifications': [],
                'total_count': 0,
                'unread_count': 0
            }
    
    def delete_notification(self, notification_id: int, user: User = None) -> bool:
        """
        Delete a notification
        """
        try:
            query = Notification.objects.filter(id=notification_id)
            if user:
                query = query.filter(recipient=user)
            
            deleted = query.delete()[0]
            return deleted > 0
            
        except Exception as e:
            logger.error(f"Failed to delete notification: {str(e)}")
            return False
    
    def cleanup_old_notifications(self, days: int = 30) -> int:
        """
        Clean up old notifications (run periodically)
        """
        try:
            cutoff_date = timezone.now() - timedelta(days=days)
            
            # Delete old read notifications
            deleted_count = Notification.objects.filter(
                created_at__lt=cutoff_date,
                is_read=True
            ).delete()[0]
            
            logger.info(f"Cleaned up {deleted_count} old notifications")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old notifications: {str(e)}")
            return 0
    
    # Private helper methods
    
    def _should_send_to_user(
        self,
        user: User,
        notification_type: str,
        category: str = None
    ) -> bool:
        """
        Check if notification should be sent to user based on preferences
        """
        try:
            settings, _ = UserNotificationSettings.objects.get_or_create(user=user)
            
            # Check if push notifications are enabled
            if not settings.push_notifications_enabled:
                return False
            
            # Check category preferences
            if category and not settings.is_category_enabled(category):
                return False
            
            # Check quiet hours
            if settings.is_in_quiet_hours():
                return False
            
            # Check daily limit
            today = timezone.now().date()
            daily_count = Notification.objects.filter(
                recipient=user,
                created_at__date=today
            ).count()
            
            if daily_count >= settings.max_daily_notifications:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking user notification preferences: {str(e)}")
            return True  # Default to allowing notifications
    
    def _render_from_template(
        self,
        notification_type: str,
        template_data: Dict[str, Any]
    ) -> Optional[Dict[str, str]]:
        """
        Render notification content from template
        """
        try:
            template = NotificationTemplate.objects.get(
                notification_type=notification_type,
                is_active=True
            )
            
            context = Context(template_data)
            
            title = Template(template.title_template).render(context)
            message = Template(template.message_template).render(context)
            
            return {
                'title': title,
                'message': message,
                'category': template.category,
                'priority': template.default_priority
            }
            
        except NotificationTemplate.DoesNotExist:
            logger.warning(f"No template found for notification type: {notification_type}")
            return None
        except Exception as e:
            logger.error(f"Failed to render notification template: {str(e)}")
            return None
    
    def _create_notification_record(
        self,
        recipient: User,
        notification_type: str,
        title: str,
        message: str,
        category: str = None,
        priority: str = NotificationPriority.MEDIUM,
        image_url: str = None,
        action_data: Dict[str, Any] = None,
        content_object = None
    ) -> Notification:
        """
        Create notification database record
        """
        notification = Notification.objects.create(
            recipient=recipient,
            notification_type=notification_type,
            category=category or NotificationCategory.GENERAL,
            priority=priority,
            title=title,
            message=message,
            image_url=image_url,
            action_data=action_data or {},
            content_object=content_object
        )
        
        return notification
    
    def _send_notification(self, notification: Notification) -> bool:
        """
        Send a single notification via OneSignal
        """
        try:
            result = self.onesignal.send_to_user(
                user=notification.recipient,
                title=notification.title,
                message=notification.message,
                data=notification.action_data,
                image_url=notification.image_url,
                priority='high' if notification.priority == NotificationPriority.HIGH else 'normal'
            )
            
            if result['success']:
                notification.mark_as_sent(
                    onesignal_id=result.get('notification_id'),
                    response_data=result.get('response', {})
                )
                return True
            else:
                logger.error(f"Failed to send notification {notification.id}: {result['error']}")
                notification.delivery_status = 'failed'
                notification.save()
                return False
                
        except Exception as e:
            logger.error(f"Exception sending notification {notification.id}: {str(e)}")
            notification.delivery_status = 'failed'
            notification.save()
            return False
    
    def _handle_batched_notification(
        self,
        recipient: User,
        notification_type: str,
        batch_key: str,
        **kwargs
    ) -> Optional[Notification]:
        """
        Handle batched notification logic
        """
        try:
            batch, created = NotificationBatch.objects.get_or_create(
                batch_key=batch_key,
                defaults={
                    'notification_type': notification_type,
                    'recipient': recipient,
                    'aggregated_data': {
                        'events': [kwargs]
                    }
                }
            )
            
            if not created:
                # Add to existing batch
                batch.add_event(kwargs)
            
            # Check if batch should be sent immediately
            if batch.should_send():
                return self._send_batched_notification(batch)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to handle batched notification: {str(e)}")
            return None
    
    def _send_batched_notification(self, batch: NotificationBatch) -> Optional[Notification]:
        """
        Send a batched notification
        """
        try:
            # Generate aggregated content
            title, message = self._generate_batch_content(batch)
            
            # Create and send notification
            notification = self._create_notification_record(
                recipient=batch.recipient,
                notification_type=batch.notification_type,
                title=title,
                message=message,
                action_data=batch.aggregated_data
            )
            
            if self._send_notification(notification):
                batch.mark_as_sent()
                return notification
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to send batched notification: {str(e)}")
            return None
    
    def _generate_batch_content(self, batch: NotificationBatch) -> tuple:
        """
        Generate title and message for batched notification
        """
        # This is a simple implementation - can be enhanced based on notification type
        count = batch.event_count
        
        if batch.notification_type == NotificationType.PIN_LIKE:
            title = f"Your pins are getting love! 🔥"
            message = f"{count} people liked your pins in the last hour"
        elif batch.notification_type == NotificationType.FRIEND_REQUEST:
            title = f"New friend requests 👋"
            message = f"You have {count} new friend requests"
        else:
            title = f"Updates from BOP Maps"
            message = f"You have {count} new notifications"
        
        return title, message


# Global instance
notification_manager = NotificationManager()
