# Generated by Django 4.2.7 on 2025-06-20 05:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="NotificationTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("pin_like", "Pin Liked"),
                            ("pin_comment", "Pin Comment"),
                            ("pin_trending", "Pin Trending"),
                            ("friend_nearby", "Friend Nearby"),
                            ("pin_milestone", "Pin Milestone"),
                            ("friend_request", "Friend Request"),
                            ("friend_accepted", "Friend Accepted"),
                            ("music_chat", "Music Chat"),
                            ("activity_digest", "Activity Digest"),
                            ("live_listening", "Live Listening"),
                            ("ai_recommendation", "AI Recommendation"),
                            ("new_release", "New Release"),
                            ("favorite_artist_update", "Artist Update"),
                            ("daily_mix", "Daily Mix"),
                            ("weekly_recommendation", "Weekly Mix"),
                            ("challenge_complete", "Challenge Complete"),
                            ("challenge_progress", "Challenge Progress"),
                            ("challenge_available", "New Challenge"),
                            ("level_up", "Level Up"),
                            ("achievement_unlocked", "Achievement"),
                            ("xp_earned", "XP Earned"),
                            ("collection_update", "Collection Update"),
                            ("collection_milestone", "Collection Milestone"),
                            ("collaborative_update", "Collaborative Update"),
                            ("playlist_shared", "Playlist Shared"),
                            ("new_ar_pins", "New AR Pins"),
                            ("seasonal_drop", "Seasonal Drop"),
                            ("event_available", "Event Available"),
                            ("trending_in_city", "Trending in City"),
                            ("skin_unlocked", "Skin Unlocked"),
                            ("limited_skin_available", "Limited Skin"),
                            ("customization_reminder", "Customization"),
                            ("general", "General"),
                            ("system_update", "System Update"),
                            ("retention_reminder", "Come Back"),
                            ("welcome_message", "Welcome"),
                        ],
                        max_length=50,
                        unique=True,
                    ),
                ),
                ("title_template", models.CharField(max_length=200)),
                ("message_template", models.TextField()),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("all", "All"),
                            ("map", "Map & Pins"),
                            ("social", "Social & Friends"),
                            ("music", "Music & AI"),
                            ("gamification", "Challenges & Gamification"),
                            ("collection", "Collections & Playlists"),
                            ("exploration", "Explore & Discovery"),
                            ("customization", "Skins & Customization"),
                            ("general", "General"),
                        ],
                        default="general",
                        max_length=20,
                    ),
                ),
                (
                    "default_priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="medium",
                        max_length=10,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "db_table": "notification_templates",
            },
        ),
        migrations.CreateModel(
            name="UserNotificationSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("map_enabled", models.BooleanField(default=True)),
                ("social_enabled", models.BooleanField(default=True)),
                ("music_enabled", models.BooleanField(default=True)),
                ("gamification_enabled", models.BooleanField(default=True)),
                ("collection_enabled", models.BooleanField(default=True)),
                ("exploration_enabled", models.BooleanField(default=True)),
                ("customization_enabled", models.BooleanField(default=True)),
                ("general_enabled", models.BooleanField(default=True)),
                ("push_notifications_enabled", models.BooleanField(default=True)),
                ("email_notifications_enabled", models.BooleanField(default=False)),
                ("quiet_hours_enabled", models.BooleanField(default=False)),
                ("quiet_start_time", models.TimeField(default="22:00")),
                ("quiet_end_time", models.TimeField(default="08:00")),
                ("max_daily_notifications", models.PositiveIntegerField(default=50)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notification_settings",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "user_notification_settings",
            },
        ),
        migrations.CreateModel(
            name="OneSignalPlayerID",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("player_id", models.CharField(max_length=255, unique=True)),
                (
                    "platform",
                    models.CharField(
                        choices=[
                            ("android", "Android"),
                            ("ios", "iOS"),
                            ("web", "Web"),
                        ],
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="onesignal_players",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "onesignal_player_ids",
                "unique_together": {("user", "player_id")},
            },
        ),
        migrations.CreateModel(
            name="NotificationBatch",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("batch_key", models.CharField(max_length=200, unique=True)),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("pin_like", "Pin Liked"),
                            ("pin_comment", "Pin Comment"),
                            ("pin_trending", "Pin Trending"),
                            ("friend_nearby", "Friend Nearby"),
                            ("pin_milestone", "Pin Milestone"),
                            ("friend_request", "Friend Request"),
                            ("friend_accepted", "Friend Accepted"),
                            ("music_chat", "Music Chat"),
                            ("activity_digest", "Activity Digest"),
                            ("live_listening", "Live Listening"),
                            ("ai_recommendation", "AI Recommendation"),
                            ("new_release", "New Release"),
                            ("favorite_artist_update", "Artist Update"),
                            ("daily_mix", "Daily Mix"),
                            ("weekly_recommendation", "Weekly Mix"),
                            ("challenge_complete", "Challenge Complete"),
                            ("challenge_progress", "Challenge Progress"),
                            ("challenge_available", "New Challenge"),
                            ("level_up", "Level Up"),
                            ("achievement_unlocked", "Achievement"),
                            ("xp_earned", "XP Earned"),
                            ("collection_update", "Collection Update"),
                            ("collection_milestone", "Collection Milestone"),
                            ("collaborative_update", "Collaborative Update"),
                            ("playlist_shared", "Playlist Shared"),
                            ("new_ar_pins", "New AR Pins"),
                            ("seasonal_drop", "Seasonal Drop"),
                            ("event_available", "Event Available"),
                            ("trending_in_city", "Trending in City"),
                            ("skin_unlocked", "Skin Unlocked"),
                            ("limited_skin_available", "Limited Skin"),
                            ("customization_reminder", "Customization"),
                            ("general", "General"),
                            ("system_update", "System Update"),
                            ("retention_reminder", "Come Back"),
                            ("welcome_message", "Welcome"),
                        ],
                        max_length=50,
                    ),
                ),
                ("batch_size", models.PositiveIntegerField(default=5)),
                ("batch_window_minutes", models.PositiveIntegerField(default=60)),
                ("first_event_at", models.DateTimeField(auto_now_add=True)),
                ("last_event_at", models.DateTimeField(auto_now=True)),
                ("event_count", models.PositiveIntegerField(default=1)),
                ("is_sent", models.BooleanField(default=False)),
                ("sent_at", models.DateTimeField(blank=True, null=True)),
                ("aggregated_data", models.JSONField(blank=True, default=dict)),
                (
                    "recipient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notification_batches",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "notification_batches",
                "indexes": [
                    models.Index(
                        fields=["batch_key"], name="notificatio_batch_k_d1a3f8_idx"
                    ),
                    models.Index(
                        fields=["recipient", "notification_type"],
                        name="notificatio_recipie_29fc40_idx",
                    ),
                    models.Index(
                        fields=["is_sent", "last_event_at"],
                        name="notificatio_is_sent_721191_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("pin_like", "Pin Liked"),
                            ("pin_comment", "Pin Comment"),
                            ("pin_trending", "Pin Trending"),
                            ("friend_nearby", "Friend Nearby"),
                            ("pin_milestone", "Pin Milestone"),
                            ("friend_request", "Friend Request"),
                            ("friend_accepted", "Friend Accepted"),
                            ("music_chat", "Music Chat"),
                            ("activity_digest", "Activity Digest"),
                            ("live_listening", "Live Listening"),
                            ("ai_recommendation", "AI Recommendation"),
                            ("new_release", "New Release"),
                            ("favorite_artist_update", "Artist Update"),
                            ("daily_mix", "Daily Mix"),
                            ("weekly_recommendation", "Weekly Mix"),
                            ("challenge_complete", "Challenge Complete"),
                            ("challenge_progress", "Challenge Progress"),
                            ("challenge_available", "New Challenge"),
                            ("level_up", "Level Up"),
                            ("achievement_unlocked", "Achievement"),
                            ("xp_earned", "XP Earned"),
                            ("collection_update", "Collection Update"),
                            ("collection_milestone", "Collection Milestone"),
                            ("collaborative_update", "Collaborative Update"),
                            ("playlist_shared", "Playlist Shared"),
                            ("new_ar_pins", "New AR Pins"),
                            ("seasonal_drop", "Seasonal Drop"),
                            ("event_available", "Event Available"),
                            ("trending_in_city", "Trending in City"),
                            ("skin_unlocked", "Skin Unlocked"),
                            ("limited_skin_available", "Limited Skin"),
                            ("customization_reminder", "Customization"),
                            ("general", "General"),
                            ("system_update", "System Update"),
                            ("retention_reminder", "Come Back"),
                            ("welcome_message", "Welcome"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("all", "All"),
                            ("map", "Map & Pins"),
                            ("social", "Social & Friends"),
                            ("music", "Music & AI"),
                            ("gamification", "Challenges & Gamification"),
                            ("collection", "Collections & Playlists"),
                            ("exploration", "Explore & Discovery"),
                            ("customization", "Skins & Customization"),
                            ("general", "General"),
                        ],
                        default="general",
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="medium",
                        max_length=10,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("message", models.TextField()),
                ("image_url", models.URLField(blank=True, null=True)),
                ("action_data", models.JSONField(blank=True, default=dict)),
                ("object_id", models.PositiveIntegerField(blank=True, null=True)),
                ("is_read", models.BooleanField(default=False)),
                ("read_at", models.DateTimeField(blank=True, null=True)),
                ("is_sent", models.BooleanField(default=False)),
                ("sent_at", models.DateTimeField(blank=True, null=True)),
                (
                    "delivery_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "onesignal_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("onesignal_response", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "recipient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "notifications",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["recipient", "-created_at"],
                        name="notificatio_recipie_2d3764_idx",
                    ),
                    models.Index(
                        fields=["recipient", "is_read"],
                        name="notificatio_recipie_583549_idx",
                    ),
                    models.Index(
                        fields=["notification_type"],
                        name="notificatio_notific_19df93_idx",
                    ),
                    models.Index(
                        fields=["category"], name="notificatio_categor_c83784_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="notificatio_created_e4c995_idx"
                    ),
                ],
            },
        ),
    ]
