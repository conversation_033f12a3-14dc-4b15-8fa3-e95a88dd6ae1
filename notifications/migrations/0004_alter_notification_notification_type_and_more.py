# Generated by Django 4.2.7 on 2025-07-09 04:07

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("notifications", "0003_remove_ai_features"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="notification",
            name="notification_type",
            field=models.CharField(
                choices=[
                    ("pin_like", "Pin Liked"),
                    ("pin_comment", "Pin Comment"),
                    ("pin_trending", "Pin Trending"),
                    ("friend_nearby", "Friend Nearby"),
                    ("pin_milestone", "Pin Milestone"),
                    ("friend_request", "Friend Request"),
                    ("friend_accepted", "Friend Accepted"),
                    ("music_chat", "Music Chat"),
                    ("activity_digest", "Activity Digest"),
                    ("live_listening", "Live Listening"),
                    ("weekly_digest", "Weekly Digest"),
                    ("new_release", "New Release"),
                    ("favorite_artist_update", "Artist Update"),
                    ("daily_mix", "Daily Mix"),
                    ("weekly_recommendation", "Weekly Mix"),
                    ("music_sync", "Music Sync"),
                    ("challenge_complete", "Challenge Complete"),
                    ("challenge_progress", "Challenge Progress"),
                    ("challenge_available", "New Challenge"),
                    ("level_up", "Level Up"),
                    ("achievement_unlocked", "Achievement"),
                    ("xp_earned", "XP Earned"),
                    ("collection_update", "Collection Update"),
                    ("collection_milestone", "Collection Milestone"),
                    ("collaborative_update", "Collaborative Update"),
                    ("playlist_shared", "Playlist Shared"),
                    ("new_ar_pins", "New AR Pins"),
                    ("seasonal_drop", "Seasonal Drop"),
                    ("event_available", "Event Available"),
                    ("trending_in_city", "Trending in City"),
                    ("new_pins_nearby", "New Pins Nearby"),
                    ("fresh_pins_found", "Fresh Pins Found"),
                    ("seasonal_event", "Seasonal Event"),
                    ("skin_unlocked", "Skin Unlocked"),
                    ("limited_skin_available", "Limited Skin"),
                    ("customization_reminder", "Customization"),
                    ("general", "General"),
                    ("system_update", "System Update"),
                    ("retention_reminder", "Come Back"),
                    ("welcome_message", "Welcome"),
                    ("unread_reminder", "Unread Reminder"),
                ],
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="notificationbatch",
            name="notification_type",
            field=models.CharField(
                choices=[
                    ("pin_like", "Pin Liked"),
                    ("pin_comment", "Pin Comment"),
                    ("pin_trending", "Pin Trending"),
                    ("friend_nearby", "Friend Nearby"),
                    ("pin_milestone", "Pin Milestone"),
                    ("friend_request", "Friend Request"),
                    ("friend_accepted", "Friend Accepted"),
                    ("music_chat", "Music Chat"),
                    ("activity_digest", "Activity Digest"),
                    ("live_listening", "Live Listening"),
                    ("weekly_digest", "Weekly Digest"),
                    ("new_release", "New Release"),
                    ("favorite_artist_update", "Artist Update"),
                    ("daily_mix", "Daily Mix"),
                    ("weekly_recommendation", "Weekly Mix"),
                    ("music_sync", "Music Sync"),
                    ("challenge_complete", "Challenge Complete"),
                    ("challenge_progress", "Challenge Progress"),
                    ("challenge_available", "New Challenge"),
                    ("level_up", "Level Up"),
                    ("achievement_unlocked", "Achievement"),
                    ("xp_earned", "XP Earned"),
                    ("collection_update", "Collection Update"),
                    ("collection_milestone", "Collection Milestone"),
                    ("collaborative_update", "Collaborative Update"),
                    ("playlist_shared", "Playlist Shared"),
                    ("new_ar_pins", "New AR Pins"),
                    ("seasonal_drop", "Seasonal Drop"),
                    ("event_available", "Event Available"),
                    ("trending_in_city", "Trending in City"),
                    ("new_pins_nearby", "New Pins Nearby"),
                    ("fresh_pins_found", "Fresh Pins Found"),
                    ("seasonal_event", "Seasonal Event"),
                    ("skin_unlocked", "Skin Unlocked"),
                    ("limited_skin_available", "Limited Skin"),
                    ("customization_reminder", "Customization"),
                    ("general", "General"),
                    ("system_update", "System Update"),
                    ("retention_reminder", "Come Back"),
                    ("welcome_message", "Welcome"),
                    ("unread_reminder", "Unread Reminder"),
                ],
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="notificationtemplate",
            name="notification_type",
            field=models.CharField(
                choices=[
                    ("pin_like", "Pin Liked"),
                    ("pin_comment", "Pin Comment"),
                    ("pin_trending", "Pin Trending"),
                    ("friend_nearby", "Friend Nearby"),
                    ("pin_milestone", "Pin Milestone"),
                    ("friend_request", "Friend Request"),
                    ("friend_accepted", "Friend Accepted"),
                    ("music_chat", "Music Chat"),
                    ("activity_digest", "Activity Digest"),
                    ("live_listening", "Live Listening"),
                    ("weekly_digest", "Weekly Digest"),
                    ("new_release", "New Release"),
                    ("favorite_artist_update", "Artist Update"),
                    ("daily_mix", "Daily Mix"),
                    ("weekly_recommendation", "Weekly Mix"),
                    ("music_sync", "Music Sync"),
                    ("challenge_complete", "Challenge Complete"),
                    ("challenge_progress", "Challenge Progress"),
                    ("challenge_available", "New Challenge"),
                    ("level_up", "Level Up"),
                    ("achievement_unlocked", "Achievement"),
                    ("xp_earned", "XP Earned"),
                    ("collection_update", "Collection Update"),
                    ("collection_milestone", "Collection Milestone"),
                    ("collaborative_update", "Collaborative Update"),
                    ("playlist_shared", "Playlist Shared"),
                    ("new_ar_pins", "New AR Pins"),
                    ("seasonal_drop", "Seasonal Drop"),
                    ("event_available", "Event Available"),
                    ("trending_in_city", "Trending in City"),
                    ("new_pins_nearby", "New Pins Nearby"),
                    ("fresh_pins_found", "Fresh Pins Found"),
                    ("seasonal_event", "Seasonal Event"),
                    ("skin_unlocked", "Skin Unlocked"),
                    ("limited_skin_available", "Limited Skin"),
                    ("customization_reminder", "Customization"),
                    ("general", "General"),
                    ("system_update", "System Update"),
                    ("retention_reminder", "Come Back"),
                    ("welcome_message", "Welcome"),
                    ("unread_reminder", "Unread Reminder"),
                ],
                max_length=50,
                unique=True,
            ),
        ),
    ]
