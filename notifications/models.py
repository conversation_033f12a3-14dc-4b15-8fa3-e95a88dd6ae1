from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
import json

User = get_user_model()


class NotificationCategory(models.TextChoices):
    ALL = 'all', 'All'
    MAP = 'map', 'Map & Pins'
    SOCIAL = 'social', 'Social & Friends'
    MUSIC = 'music', 'Music'
    GAMIFICATION = 'gamification', 'Challenges & Gamification'
    COLLECTION = 'collection', 'Collections & Playlists'
    EXPLORATION = 'exploration', 'Explore & Discovery'
    CUSTOMIZATION = 'customization', 'Skins & Customization'
    GENERAL = 'general', 'General'


class NotificationType(models.TextChoices):
    # Map & Pin related
    PIN_LIKE = 'pin_like', 'Pin Liked'
    PIN_COMMENT = 'pin_comment', 'Pin Comment' 
    PIN_TRENDING = 'pin_trending', 'Pin Trending'
    FRIEND_NEARBY = 'friend_nearby', 'Friend Nearby'
    PIN_MILESTONE = 'pin_milestone', 'Pin Milestone'
    
    # Social & Friends
    FRIEND_REQUEST = 'friend_request', 'Friend Request'
    FRIEND_ACCEPTED = 'friend_accepted', 'Friend Accepted'
    MUSIC_CHAT = 'music_chat', 'Music Chat'
    ACTIVITY_DIGEST = 'activity_digest', 'Activity Digest'
    LIVE_LISTENING = 'live_listening', 'Live Listening'
    WEEKLY_DIGEST = 'weekly_digest', 'Weekly Digest'
    
    # Music
    NEW_RELEASE = 'new_release', 'New Release'
    FAVORITE_ARTIST_UPDATE = 'favorite_artist_update', 'Artist Update'
    DAILY_MIX = 'daily_mix', 'Daily Mix'
    WEEKLY_RECOMMENDATION = 'weekly_recommendation', 'Weekly Mix'
    MUSIC_SYNC = 'music_sync', 'Music Sync'
    
    # Challenges & Gamification
    CHALLENGE_COMPLETE = 'challenge_complete', 'Challenge Complete'
    CHALLENGE_PROGRESS = 'challenge_progress', 'Challenge Progress'
    CHALLENGE_AVAILABLE = 'challenge_available', 'New Challenge'
    LEVEL_UP = 'level_up', 'Level Up'
    ACHIEVEMENT_UNLOCKED = 'achievement_unlocked', 'Achievement'
    XP_EARNED = 'xp_earned', 'XP Earned'
    
    # Collections & Playlists
    COLLECTION_UPDATE = 'collection_update', 'Collection Update'
    COLLECTION_MILESTONE = 'collection_milestone', 'Collection Milestone'
    COLLABORATIVE_UPDATE = 'collaborative_update', 'Collaborative Update'
    PLAYLIST_SHARED = 'playlist_shared', 'Playlist Shared'
    
    # Explore & Discovery
    NEW_AR_PINS = 'new_ar_pins', 'New AR Pins'
    SEASONAL_DROP = 'seasonal_drop', 'Seasonal Drop'
    EVENT_AVAILABLE = 'event_available', 'Event Available'
    TRENDING_IN_CITY = 'trending_in_city', 'Trending in City'
    NEW_PINS_NEARBY = 'new_pins_nearby', 'New Pins Nearby'
    SEASONAL_EVENT = 'seasonal_event', 'Seasonal Event'
    
    # Skins & Customization
    SKIN_UNLOCKED = 'skin_unlocked', 'Skin Unlocked'
    LIMITED_SKIN_AVAILABLE = 'limited_skin_available', 'Limited Skin'
    CUSTOMIZATION_REMINDER = 'customization_reminder', 'Customization'
    
    # System & General
    GENERAL = 'general', 'General'
    SYSTEM_UPDATE = 'system_update', 'System Update'
    RETENTION_REMINDER = 'retention_reminder', 'Come Back'
    WELCOME_MESSAGE = 'welcome_message', 'Welcome'
    UNREAD_REMINDER = 'unread_reminder', 'Unread Reminder'


class NotificationPriority(models.TextChoices):
    LOW = 'low', 'Low'
    MEDIUM = 'medium', 'Medium'
    HIGH = 'high', 'High'
    URGENT = 'urgent', 'Urgent'


class NotificationTemplate(models.Model):
    """
    Template for generating notifications with dynamic content
    """
    notification_type = models.CharField(
        max_length=50,
        choices=NotificationType.choices,
        unique=True
    )
    title_template = models.CharField(max_length=200)
    message_template = models.TextField()
    category = models.CharField(
        max_length=20,
        choices=NotificationCategory.choices,
        default=NotificationCategory.GENERAL
    )
    default_priority = models.CharField(
        max_length=10,
        choices=NotificationPriority.choices,
        default=NotificationPriority.MEDIUM
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'notification_templates'

    def __str__(self):
        return f"{self.get_notification_type_display()} Template"


class Notification(models.Model):
    """
    Individual notification record
    """
    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notifications'
    )
    notification_type = models.CharField(
        max_length=50,
        choices=NotificationType.choices
    )
    category = models.CharField(
        max_length=20,
        choices=NotificationCategory.choices,
        default=NotificationCategory.GENERAL
    )
    priority = models.CharField(
        max_length=10,
        choices=NotificationPriority.choices,
        default=NotificationPriority.MEDIUM
    )
    
    title = models.CharField(max_length=200)
    message = models.TextField()
    
    # Optional image/media
    image_url = models.URLField(blank=True, null=True)
    
    # Action data (JSON field for storing button actions, deep links, etc.)
    action_data = models.JSONField(default=dict, blank=True)
    
    # Generic foreign key to link to any model
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        blank=True,
        null=True
    )
    object_id = models.PositiveIntegerField(blank=True, null=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # Delivery tracking
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(blank=True, null=True)
    is_sent = models.BooleanField(default=False)
    sent_at = models.DateTimeField(blank=True, null=True)
    delivery_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('sent', 'Sent'),
            ('delivered', 'Delivered'),
            ('failed', 'Failed'),
        ],
        default='pending'
    )
    
    # OneSignal integration
    onesignal_id = models.CharField(max_length=100, blank=True, null=True)
    onesignal_response = models.JSONField(default=dict, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'notifications'
        indexes = [
            models.Index(fields=['recipient', '-created_at']),
            models.Index(fields=['recipient', 'is_read']),
            models.Index(fields=['notification_type']),
            models.Index(fields=['category']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.recipient.username}"

    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])

    def mark_as_sent(self, onesignal_id=None, response_data=None):
        """Mark notification as sent"""
        self.is_sent = True
        self.sent_at = timezone.now()
        self.delivery_status = 'sent'
        if onesignal_id:
            self.onesignal_id = onesignal_id
        if response_data:
            self.onesignal_response = response_data
        self.save(update_fields=['is_sent', 'sent_at', 'delivery_status', 'onesignal_id', 'onesignal_response'])


class UserNotificationSettings(models.Model):
    """
    User preferences for notification categories and types
    """
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='notification_settings'
    )
    
    # Category preferences
    map_enabled = models.BooleanField(default=True)
    social_enabled = models.BooleanField(default=True)
    music_enabled = models.BooleanField(default=True)
    gamification_enabled = models.BooleanField(default=True)
    collection_enabled = models.BooleanField(default=True)
    exploration_enabled = models.BooleanField(default=True)
    customization_enabled = models.BooleanField(default=True)
    general_enabled = models.BooleanField(default=True)
    
    # Delivery preferences
    push_notifications_enabled = models.BooleanField(default=True)
    email_notifications_enabled = models.BooleanField(default=False)
    
    # Quiet hours
    quiet_hours_enabled = models.BooleanField(default=False)
    quiet_start_time = models.TimeField(default='22:00')
    quiet_end_time = models.TimeField(default='08:00')
    
    # Frequency limits
    max_daily_notifications = models.PositiveIntegerField(default=50)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user_notification_settings'

    def __str__(self):
        return f"Notification Settings - {self.user.username}"

    def is_category_enabled(self, category):
        """Check if a category is enabled for this user"""
        category_mapping = {
            NotificationCategory.MAP: self.map_enabled,
            NotificationCategory.SOCIAL: self.social_enabled,
            NotificationCategory.MUSIC: self.music_enabled,
            NotificationCategory.GAMIFICATION: self.gamification_enabled,
            NotificationCategory.COLLECTION: self.collection_enabled,
            NotificationCategory.EXPLORATION: self.exploration_enabled,
            NotificationCategory.CUSTOMIZATION: self.customization_enabled,
            NotificationCategory.GENERAL: self.general_enabled,
        }
        return category_mapping.get(category, True)

    def is_in_quiet_hours(self):
        """Check if current time is within quiet hours"""
        if not self.quiet_hours_enabled:
            return False
        
        current_time = timezone.now().time()
        start_time = self.quiet_start_time
        end_time = self.quiet_end_time
        
        if start_time <= end_time:
            return start_time <= current_time <= end_time
        else:  # Quiet hours span midnight
            return current_time >= start_time or current_time <= end_time


class NotificationBatch(models.Model):
    """
    For batching notifications to avoid spam
    """
    batch_key = models.CharField(max_length=200, unique=True)
    notification_type = models.CharField(
        max_length=50,
        choices=NotificationType.choices
    )
    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notification_batches'
    )
    
    # Batching configuration
    batch_size = models.PositiveIntegerField(default=5)
    batch_window_minutes = models.PositiveIntegerField(default=60)
    
    # Tracking
    first_event_at = models.DateTimeField(auto_now_add=True)
    last_event_at = models.DateTimeField(auto_now=True)
    event_count = models.PositiveIntegerField(default=1)
    is_sent = models.BooleanField(default=False)
    sent_at = models.DateTimeField(blank=True, null=True)
    
    # Aggregated data
    aggregated_data = models.JSONField(default=dict, blank=True)

    class Meta:
        db_table = 'notification_batches'
        indexes = [
            models.Index(fields=['batch_key']),
            models.Index(fields=['recipient', 'notification_type']),
            models.Index(fields=['is_sent', 'last_event_at']),
        ]

    def __str__(self):
        return f"Batch {self.batch_key} - {self.event_count} events"

    def should_send(self):
        """Check if batch should be sent based on size or time window"""
        if self.is_sent:
            return False
        
        # Check if batch size reached
        if self.event_count >= self.batch_size:
            return True
        
        # Check if time window exceeded
        time_window = timezone.now() - self.first_event_at
        if time_window.total_seconds() >= (self.batch_window_minutes * 60):
            return True
        
        return False

    def add_event(self, event_data):
        """Add an event to the batch"""
        self.event_count += 1
        self.last_event_at = timezone.now()
        
        # Update aggregated data
        if not self.aggregated_data:
            self.aggregated_data = {'events': []}
        
        self.aggregated_data['events'].append({
            'timestamp': timezone.now().isoformat(),
            'data': event_data
        })
        
        self.save()

    def mark_as_sent(self):
        """Mark batch as sent"""
        self.is_sent = True
        self.sent_at = timezone.now()
        self.save()


class OneSignalPlayerID(models.Model):
    """
    Store OneSignal player IDs for users
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='onesignal_players'
    )
    player_id = models.CharField(max_length=255, unique=True)
    platform = models.CharField(
        max_length=20,
        choices=[
            ('android', 'Android'),
            ('ios', 'iOS'),
            ('web', 'Web'),
        ]
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'onesignal_player_ids'
        unique_together = ['user', 'player_id']

    def __str__(self):
        return f"{self.user.username} - {self.platform} - {self.player_id[:10]}..."
