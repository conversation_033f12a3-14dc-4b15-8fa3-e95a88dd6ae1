from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    Notification, NotificationTemplate, UserNotificationSettings, 
    OneSignalPlayerID, NotificationCategory, NotificationType, NotificationPriority
)

User = get_user_model()


class NotificationSerializer(serializers.ModelSerializer):
    """
    Serializer for Notification model
    """
    recipient_username = serializers.CharField(source='recipient.username', read_only=True)
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    type_display = serializers.CharField(source='get_notification_type_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    time_since = serializers.SerializerMethodField()
    
    # Override enum fields to return string values instead of integers
    notification_type = serializers.SerializerMethodField()
    category = serializers.SerializerMethodField()
    priority = serializers.SerializerMethodField()
    
    class Meta:
        model = Notification
        fields = [
            'id', 'notification_type', 'type_display', 'category', 'category_display',
            'priority', 'priority_display', 'title', 'message', 'image_url',
            'action_data', 'is_read', 'read_at', 'is_sent', 'sent_at',
            'delivery_status', 'created_at', 'updated_at', 'recipient_username',
            'time_since'
        ]
        read_only_fields = [
            'id', 'is_sent', 'sent_at', 'delivery_status', 'created_at', 
            'updated_at', 'recipient_username', 'type_display', 'category_display',
            'priority_display', 'time_since', 'notification_type', 'category', 'priority'
        ]
    
    def get_time_since(self, obj):
        """
        Get human-readable time since notification was created
        """
        from django.utils import timezone
        from datetime import datetime, timedelta
        
        now = timezone.now()
        diff = now - obj.created_at
        
        if diff.days > 7:
            return obj.created_at.strftime("%b %d, %Y")
        elif diff.days > 0:
            return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        else:
            return "Just now"

    def get_notification_type(self, obj):
        """
        Return notification_type as string value instead of integer
        """
        return obj.notification_type
    
    def get_category(self, obj):
        """
        Return category as string value instead of integer
        """
        return obj.category
    
    def get_priority(self, obj):
        """
        Return priority as string value instead of integer
        """
        return obj.priority


class NotificationCreateSerializer(serializers.Serializer):
    """
    Serializer for creating notifications
    """
    notification_type = serializers.ChoiceField(choices=NotificationType.choices)
    title = serializers.CharField(max_length=200, required=False, allow_blank=True)
    message = serializers.CharField(required=False, allow_blank=True)
    category = serializers.ChoiceField(
        choices=NotificationCategory.choices, 
        required=False,
        default=NotificationCategory.GENERAL
    )
    priority = serializers.ChoiceField(
        choices=NotificationPriority.choices,
        required=False,
        default=NotificationPriority.MEDIUM
    )
    image_url = serializers.URLField(required=False, allow_blank=True)
    action_data = serializers.JSONField(required=False, default=dict)
    send_immediately = serializers.BooleanField(default=True)
    batch_key = serializers.CharField(max_length=200, required=False, allow_blank=True)
    template_data = serializers.JSONField(required=False, default=dict)


class BulkNotificationSerializer(serializers.Serializer):
    """
    Serializer for sending bulk notifications
    """
    recipient_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1,
        max_length=1000  # Limit for safety
    )
    notification_type = serializers.ChoiceField(choices=NotificationType.choices)
    title = serializers.CharField(max_length=200, required=False, allow_blank=True)
    message = serializers.CharField(required=False, allow_blank=True)
    category = serializers.ChoiceField(
        choices=NotificationCategory.choices, 
        required=False,
        default=NotificationCategory.GENERAL
    )
    priority = serializers.ChoiceField(
        choices=NotificationPriority.choices,
        required=False,
        default=NotificationPriority.MEDIUM
    )
    image_url = serializers.URLField(required=False, allow_blank=True)
    action_data = serializers.JSONField(required=False, default=dict)
    template_data = serializers.JSONField(required=False, default=dict)
    filter_preferences = serializers.BooleanField(default=True)


class NotificationMarkAsReadSerializer(serializers.Serializer):
    """
    Serializer for marking notifications as read
    """
    notification_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False
    )
    category = serializers.ChoiceField(
        choices=NotificationCategory.choices,
        required=False
    )
    mark_all = serializers.BooleanField(default=False)


class UserNotificationSettingsSerializer(serializers.ModelSerializer):
    """
    Serializer for UserNotificationSettings
    """
    class Meta:
        model = UserNotificationSettings
        fields = [
            'map_enabled', 'social_enabled', 'music_enabled', 'gamification_enabled',
            'collection_enabled', 'exploration_enabled', 'customization_enabled',
            'general_enabled', 'push_notifications_enabled', 'email_notifications_enabled',
            'quiet_hours_enabled', 'quiet_start_time', 'quiet_end_time',
            'max_daily_notifications'
        ]
    
    def validate_max_daily_notifications(self, value):
        """
        Validate daily notification limit
        """
        if value < 1:
            raise serializers.ValidationError("Daily notification limit must be at least 1")
        if value > 500:
            raise serializers.ValidationError("Daily notification limit cannot exceed 500")
        return value


class OneSignalPlayerRegistrationSerializer(serializers.Serializer):
    """
    Serializer for registering OneSignal player IDs
    """
    player_id = serializers.CharField(max_length=255)
    platform = serializers.ChoiceField(
        choices=[
            ('android', 'Android'),
            ('ios', 'iOS'),
            ('web', 'Web'),
        ],
        default='web'
    )


class OneSignalPlayerIDSerializer(serializers.ModelSerializer):
    """
    Serializer for OneSignalPlayerID model
    """
    username = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = OneSignalPlayerID
        fields = [
            'id', 'player_id', 'platform', 'is_active', 
            'created_at', 'updated_at', 'username'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'username']


class NotificationTemplateSerializer(serializers.ModelSerializer):
    """
    Serializer for NotificationTemplate (admin use)
    """
    type_display = serializers.CharField(source='get_notification_type_display', read_only=True)
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    priority_display = serializers.CharField(source='get_default_priority_display', read_only=True)
    
    class Meta:
        model = NotificationTemplate
        fields = [
            'id', 'notification_type', 'type_display', 'title_template',
            'message_template', 'category', 'category_display', 'default_priority',
            'priority_display', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'type_display', 'category_display', 'priority_display']


class NotificationStatsSerializer(serializers.Serializer):
    """
    Serializer for notification statistics
    """
    total_notifications = serializers.IntegerField()
    unread_count = serializers.IntegerField()
    read_count = serializers.IntegerField()
    sent_count = serializers.IntegerField()
    failed_count = serializers.IntegerField()
    category_breakdown = serializers.DictField()
    type_breakdown = serializers.DictField()
    daily_counts = serializers.ListField()


class TestNotificationSerializer(serializers.Serializer):
    """
    Serializer for sending test notifications
    """
    title = serializers.CharField(max_length=200)
    message = serializers.CharField()
    notification_type = serializers.ChoiceField(
        choices=NotificationType.choices,
        default=NotificationType.GENERAL
    )
    category = serializers.ChoiceField(
        choices=NotificationCategory.choices,
        default=NotificationCategory.GENERAL
    )
    priority = serializers.ChoiceField(
        choices=NotificationPriority.choices,
        default=NotificationPriority.MEDIUM
    )
    image_url = serializers.URLField(required=False, allow_blank=True)
    action_data = serializers.JSONField(required=False, default=dict)
    
    def validate_notification_type(self, value):
        """Ensure notification_type is returned as string"""
        return str(value)
    
    def validate_category(self, value):
        """Ensure category is returned as string"""
        return str(value)
    
    def validate_priority(self, value):
        """Ensure priority is returned as string"""
        return str(value) 