"""
Django signals to automatically trigger notifications based on app events
"""
import logging
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone

from .utils import (
    notify_pin_liked, notify_friend_request, notify_friend_accepted,
    notify_challenge_complete, notify_level_up, notify_skin_unlocked,
    notify_welcome_message, notify_collection_milestone
)

logger = logging.getLogger(__name__)
User = get_user_model()


@receiver(post_save, sender=User)
def user_created_notification(sender, instance, created, **kwargs):
    """
    Send welcome notification when a new user is created
    """
    if created:
        try:
            # Small delay to ensure user is fully created
            from django.utils import timezone
            from datetime import timedelta
            
            # Send welcome notification after a short delay
            notify_welcome_message(instance)
            
        except Exception as e:
            logger.error(f"Failed to send welcome notification: {str(e)}")


# Pin-related notifications
@receiver(post_save)
def pin_interaction_notification(sender, instance, created, **kwargs):
    """
    Handle pin interaction notifications
    """
    if not created:
        return
    
    # Import here to avoid circular imports
    try:
        from pins.models import PinInteraction
        
        if sender == PinInteraction and instance.interaction_type == 'like':
            # Get current like count for the pin
            like_count = PinInteraction.objects.filter(
                pin=instance.pin,
                interaction_type='like'
            ).count()
            
            notify_pin_liked(
                pin=instance.pin,
                liked_by_user=instance.user,
                like_count=like_count
            )
            
    except ImportError:
        pass
    except Exception as e:
        logger.error(f"Failed to handle pin interaction notification: {str(e)}")


# Friend-related notifications
@receiver(post_save)
def friend_request_notification(sender, instance, created, **kwargs):
    """
    Handle friend request notifications
    """
    if not created:
        return
    
    try:
        from friends.models import Friend
        
        if sender == Friend:
            if instance.status == 'pending':
                # New friend request
                notify_friend_request(
                    recipient=instance.friend,
                    sender=instance.user
                )
            elif instance.status == 'accepted':
                # Friend request accepted
                notify_friend_accepted(
                    recipient=instance.user,
                    accepter=instance.friend
                )
                
    except ImportError:
        pass
    except Exception as e:
        logger.error(f"Failed to handle friend notification: {str(e)}")


# Challenge-related notifications
@receiver(post_save)
def challenge_completion_notification(sender, instance, created, **kwargs):
    """
    Handle challenge completion notifications
    """
    try:
        from challenges.models import ChallengeParticipation
        
        if sender == ChallengeParticipation and not created:
            # Check if challenge was just completed
            if hasattr(instance, 'is_completed') and instance.is_completed:
                challenge_name = instance.challenge.name if hasattr(instance, 'challenge') else "Challenge"
                xp_earned = getattr(instance.challenge, 'xp_reward', None) if hasattr(instance, 'challenge') else None
                
                notify_challenge_complete(
                    user=instance.user,
                    challenge_name=challenge_name,
                    xp_earned=xp_earned
                )
                
    except ImportError:
        pass
    except Exception as e:
        logger.error(f"Failed to handle challenge completion notification: {str(e)}")


# Gamification notifications
@receiver(post_save)
def user_level_up_notification(sender, instance, created, **kwargs):
    """
    Handle user level up notifications
    """
    if sender == User and not created:
        try:
            # Check if user leveled up (you'd need to implement level tracking)
            # This is a placeholder implementation
            if hasattr(instance, '_level_changed') and instance._level_changed:
                current_level = getattr(instance, 'level', 1)
                
                notify_level_up(
                    user=instance,
                    new_level=current_level
                )
                
        except Exception as e:
            logger.error(f"Failed to handle level up notification: {str(e)}")


# Skin unlock notifications
@receiver(post_save)
def skin_unlock_notification(sender, instance, created, **kwargs):
    """
    Handle skin unlock notifications
    """
    if not created:
        return
    
    try:
        from gamification.models import UserSkin
        
        if sender == UserSkin:
            skin_name = instance.skin.name if hasattr(instance.skin, 'name') else "New Skin"
            
            notify_skin_unlocked(
                user=instance.user,
                skin_name=skin_name,
                unlock_reason="Achievement unlocked"
            )
            
    except ImportError:
        pass
    except Exception as e:
        logger.error(f"Failed to handle skin unlock notification: {str(e)}")


# Collection milestone notifications
@receiver(post_save)
def collection_milestone_notification(sender, instance, created, **kwargs):
    """
    Handle collection milestone notifications
    """
    try:
        from pins.models import Collection
        
        if sender == Collection and not created:
            # Check for milestone achievements (1000, 5000, 10000 plays)
            milestones = [100, 500, 1000, 5000, 10000]
            current_plays = getattr(instance, 'total_plays', 0)
            
            # Simple check - in production you'd want more sophisticated tracking
            for milestone in milestones:
                if current_plays >= milestone:
                    # Check if we've already notified for this milestone
                    # You'd implement milestone tracking in your Collection model
                    
                    notify_collection_milestone(
                        user=instance.owner,
                        collection_name=instance.name,
                        milestone_count=milestone,
                        milestone_type="plays"
                    )
                    break
                    
    except ImportError:
        pass
    except Exception as e:
        logger.error(f"Failed to handle collection milestone notification: {str(e)}")


# Location-based notifications for nearby friends
@receiver(post_save)
def nearby_friend_notification(sender, instance, created, **kwargs):
    """
    Handle nearby friend notifications when pins are created
    """
    if not created:
        return
    
    try:
        from pins.models import Pin
        from notifications.utils import notify_nearby_friends
        
        if sender == Pin and hasattr(instance, 'location'):
            # Notify nearby friends about the new pin
            notify_nearby_friends(
                user_location=instance.location,
                user=instance.owner,
                radius_km=5.0
            )
            
    except ImportError:
        pass
    except Exception as e:
        logger.error(f"Failed to handle nearby friend notification: {str(e)}")


# Connect signals when app is ready
def connect_notification_signals():
    """
    Connect all notification signals
    This function should be called in the AppConfig.ready() method
    """
    logger.info("Notification signals connected")


# Auto-connect signals when this module is imported
from django.apps import apps
if apps.ready:
    connect_notification_signals() 