from django.test import TestCase, override_settings, TransactionTestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.db import connection, transaction
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch, MagicMock
import json

from .models import (
    Notification, NotificationTemplate, UserNotificationSettings,
    OneSignalPlayerID, NotificationBatch, NotificationCategory,
    NotificationType, NotificationPriority
)
from .manager import notification_manager
from .services import onesignal_service
from .utils import notify_pin_liked, notify_friend_request, notify_level_up

User = get_user_model()


class NotificationModelTests(TransactionTestCase):
    """
    Test notification models with proper transaction handling
    """
    
    def setUp(self):
        # Ensure clean database state
        connection.ensure_connection()
        super().setUp()
        
        # Create test user with proper error handling
        try:
            self.user = User.objects.create_user(
                username='testuser',
                email='<EMAIL>',
                password='testpass123'
            )
        except Exception as e:
            # If user already exists, get it
            self.user = User.objects.get(username='testuser')
    
    def tearDown(self):
        # Clean up and close connections properly
        try:
            super().tearDown()
        except:
            pass
        finally:
            connection.close()
    
    def test_notification_creation(self):
        """Test creating a notification"""
        notification = Notification.objects.create(
            recipient=self.user,
            notification_type=NotificationType.PIN_LIKE,
            category=NotificationCategory.MAP,
            priority=NotificationPriority.MEDIUM,
            title="Test Notification",
            message="This is a test notification"
        )
        
        self.assertEqual(notification.recipient, self.user)
        self.assertEqual(notification.notification_type, NotificationType.PIN_LIKE)
        self.assertFalse(notification.is_read)
        self.assertFalse(notification.is_sent)
    
    def test_notification_mark_as_read(self):
        """Test marking notification as read"""
        notification = Notification.objects.create(
            recipient=self.user,
            notification_type=NotificationType.GENERAL,
            title="Test",
            message="Test message"
        )
        
        self.assertFalse(notification.is_read)
        self.assertIsNone(notification.read_at)
        
        notification.mark_as_read()
        
        self.assertTrue(notification.is_read)
        self.assertIsNotNone(notification.read_at)
    
    def test_user_notification_settings_creation(self):
        """Test user notification settings creation"""
        # Use get_or_create since signals might have already created settings
        settings, created = UserNotificationSettings.objects.get_or_create(user=self.user)
        
        # Check defaults (settings might already exist from signals)
        self.assertTrue(settings.push_notifications_enabled)
        self.assertTrue(settings.map_enabled)
        self.assertTrue(settings.social_enabled)
    
    def test_user_notification_settings_category_check(self):
        """Test category enablement check"""
        # Use get_or_create and update instead of create
        settings, created = UserNotificationSettings.objects.get_or_create(user=self.user)
        settings.map_enabled = False
        settings.social_enabled = True
        settings.save()
        
        self.assertFalse(settings.is_category_enabled(NotificationCategory.MAP))
        self.assertTrue(settings.is_category_enabled(NotificationCategory.SOCIAL))
    
    def test_onesignal_player_id_creation(self):
        """Test OneSignal player ID creation"""
        player = OneSignalPlayerID.objects.create(
            user=self.user,
            player_id="test-player-id-123",
            platform="web"
        )
        
        self.assertEqual(player.user, self.user)
        self.assertEqual(player.platform, "web")
        self.assertTrue(player.is_active)


class NotificationManagerTests(TransactionTestCase):
    """
    Test notification manager functionality with proper transaction handling
    """
    
    def setUp(self):
        # Ensure clean database state
        connection.ensure_connection()
        super().setUp()
        
        # Create test users with proper error handling
        try:
            self.user = User.objects.create_user(
                username='testuser_manager',
                email='<EMAIL>',
                password='testpass123'
            )
        except Exception as e:
            # If user already exists, get it
            self.user = User.objects.get(username='testuser_manager')
            
        try:
            self.recipient = User.objects.create_user(
                username='recipient_manager',
                email='<EMAIL>',
                password='testpass123'
            )
        except Exception as e:
            # If user already exists, get it
            self.recipient = User.objects.get(username='recipient_manager')
    
    def tearDown(self):
        # Clean up and close connections properly
        try:
            super().tearDown()
        except:
            pass
        finally:
            connection.close()
    
    @patch('notifications.services.onesignal_service.send_to_user')
    def test_create_notification_success(self, mock_send):
        """Test successful notification creation"""
        mock_send.return_value = {
            'success': True,
            'notification_id': 'test-notification-id'
        }
        
        notification = notification_manager.create_notification(
            recipient=self.recipient,
            notification_type=NotificationType.PIN_LIKE,
            title="Test Notification",
            message="Test message",
            category=NotificationCategory.MAP
        )
        
        self.assertIsNotNone(notification)
        self.assertEqual(notification.recipient, self.recipient)
        self.assertEqual(notification.title, "Test Notification")
        mock_send.assert_called_once()
    
    def test_create_notification_blocked_by_settings(self):
        """Test notification blocked by user settings"""
        # Get or create settings and disable map notifications
        settings, created = UserNotificationSettings.objects.get_or_create(user=self.recipient)
        settings.map_enabled = False
        settings.save()
        
        notification = notification_manager.create_notification(
            recipient=self.recipient,
            notification_type=NotificationType.PIN_LIKE,
            title="Test Notification",
            message="Test message",
            category=NotificationCategory.MAP
        )
        
        # Should be None because map notifications are disabled
        self.assertIsNone(notification)
    
    def test_mark_as_read(self):
        """Test marking notification as read"""
        notification = Notification.objects.create(
            recipient=self.recipient,
            notification_type=NotificationType.GENERAL,
            title="Test",
            message="Test message"
        )
        
        success = notification_manager.mark_as_read(notification.id, self.recipient)
        
        self.assertTrue(success)
        notification.refresh_from_db()
        self.assertTrue(notification.is_read)
    
    def test_get_user_notifications(self):
        """Test getting user notifications"""
        # Clear any existing notifications from signals
        Notification.objects.filter(recipient=self.recipient).delete()
        
        # Create test notifications
        Notification.objects.create(
            recipient=self.recipient,
            notification_type=NotificationType.PIN_LIKE,
            category=NotificationCategory.MAP,
            title="Pin Liked",
            message="Someone liked your pin"
        )
        Notification.objects.create(
            recipient=self.recipient,
            notification_type=NotificationType.FRIEND_REQUEST,
            category=NotificationCategory.SOCIAL,
            title="Friend Request",
            message="New friend request"
        )
        
        # Get all notifications
        result = notification_manager.get_user_notifications(self.recipient)
        
        self.assertEqual(len(result['notifications']), 2)
        self.assertEqual(result['total_count'], 2)
        self.assertEqual(result['unread_count'], 2)
        
        # Get only map notifications
        result = notification_manager.get_user_notifications(
            self.recipient,
            category=NotificationCategory.MAP
        )
        
        self.assertEqual(len(result['notifications']), 1)
        self.assertEqual(result['notifications'][0].category, NotificationCategory.MAP)


class NotificationAPITests(APITestCase):
    """
    Test notification API endpoints
    """
    
    def setUp(self):
        # Ensure clean database state
        connection.ensure_connection()
        
        # Create test user with proper error handling
        try:
            self.user = User.objects.create_user(
                username='testuser_api',
                email='<EMAIL>',
                password='testpass123'
            )
        except Exception as e:
            # If user already exists, get it
            self.user = User.objects.get(username='testuser_api')
            
        self.client.force_authenticate(user=self.user)
    
    def tearDown(self):
        # Clean up and close connections properly
        try:
            connection.close()
        except:
            pass
    
    def test_list_notifications(self):
        """Test listing user notifications"""
        # Clear any existing notifications from signals
        Notification.objects.filter(recipient=self.user).delete()
        
        # Create test notification
        Notification.objects.create(
            recipient=self.user,
            notification_type=NotificationType.PIN_LIKE,
            title="Test Notification",
            message="Test message"
        )
        
        url = reverse('notification-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['unread_count'], 1)
    
    def test_mark_notification_as_read(self):
        """Test marking notification as read via API"""
        notification = Notification.objects.create(
            recipient=self.user,
            notification_type=NotificationType.PIN_LIKE,
            title="Test Notification",
            message="Test message"
        )
        
        url = reverse('notification-mark-read', kwargs={'pk': notification.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        notification.refresh_from_db()
        self.assertTrue(notification.is_read)
    
    def test_mark_all_as_read(self):
        """Test marking all notifications as read"""
        # Clear existing notifications first
        Notification.objects.filter(recipient=self.user).delete()
        
        # Create multiple notifications
        for i in range(3):
            Notification.objects.create(
                recipient=self.user,
                notification_type=NotificationType.GENERAL,
                title=f"Test {i}",
                message=f"Message {i}"
            )
        
        url = reverse('notification-mark-all-read')
        response = self.client.post(url, {'mark_all': True})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Check all notifications are read
        unread_count = Notification.objects.filter(
            recipient=self.user,
            is_read=False
        ).count()
        self.assertEqual(unread_count, 0)
    
    def test_notification_stats(self):
        """Test notification statistics endpoint"""
        # Clear existing notifications first
        Notification.objects.filter(recipient=self.user).delete()
        
        # Create test notifications
        Notification.objects.create(
            recipient=self.user,
            notification_type=NotificationType.PIN_LIKE,
            category=NotificationCategory.MAP,
            title="Test 1",
            message="Message 1",
            is_read=True
        )
        Notification.objects.create(
            recipient=self.user,
            notification_type=NotificationType.FRIEND_REQUEST,
            category=NotificationCategory.SOCIAL,
            title="Test 2",
            message="Message 2",
            is_read=False
        )
        
        url = reverse('notification-stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_notifications'], 2)
        self.assertEqual(response.data['unread_count'], 1)
        self.assertEqual(response.data['read_count'], 1)
    
    def test_send_test_notification(self):
        """Test sending test notification"""
        url = reverse('send-test-notification')
        data = {
            'title': 'Test Notification',
            'message': 'This is a test message',
            'notification_type': NotificationType.GENERAL
        }
        
        with patch('notifications.services.onesignal_service.send_to_user') as mock_send:
            mock_send.return_value = {
                'success': True,
                'notification_id': 'test-id'
            }
            
            response = self.client.post(url, data)
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertTrue(response.data['success'])
    
    def test_onesignal_player_registration(self):
        """Test OneSignal player ID registration"""
        url = reverse('onesignal-register-player')
        data = {
            'player_id': 'test-player-id-123',
            'platform': 'web'
        }
        
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Check player was created
        player = OneSignalPlayerID.objects.get(user=self.user)
        self.assertEqual(player.player_id, 'test-player-id-123')
        self.assertEqual(player.platform, 'web')


class NotificationUtilsTests(TransactionTestCase):
    """
    Test notification utility functions with proper transaction handling
    """
    
    def setUp(self):
        # Ensure clean database state
        connection.ensure_connection()
        super().setUp()
        
        # Create test users with proper error handling
        try:
            self.user = User.objects.create_user(
                username='testuser_utils',
                email='<EMAIL>',
                password='testpass123'
            )
        except Exception as e:
            # If user already exists, get it
            self.user = User.objects.get(username='testuser_utils')
            
        try:
            self.friend = User.objects.create_user(
                username='friend_utils',
                email='<EMAIL>',
                password='testpass123'
            )
        except Exception as e:
            # If user already exists, get it
            self.friend = User.objects.get(username='friend_utils')
    
    def tearDown(self):
        # Clean up and close connections properly
        try:
            super().tearDown()
        except:
            pass
        finally:
            connection.close()
    
    @patch('notifications.manager.notification_manager.create_notification')
    def test_notify_friend_request(self, mock_create):
        """Test friend request notification utility"""
        notify_friend_request(self.user, self.friend)
        
        mock_create.assert_called_once()
        call_args = mock_create.call_args
        
        self.assertEqual(call_args[1]['recipient'], self.user)
        self.assertEqual(call_args[1]['notification_type'], NotificationType.FRIEND_REQUEST)
        self.assertEqual(call_args[1]['category'], NotificationCategory.SOCIAL)
    
    @patch('notifications.manager.notification_manager.create_notification')
    def test_notify_pin_liked_self_like_ignored(self, mock_create):
        """Test that users don't get notified for their own likes"""
        # Create a mock pin
        mock_pin = MagicMock()
        mock_pin.owner = self.user
        mock_pin.title = "Test Pin"
        
        # User likes their own pin - should not create notification
        notify_pin_liked(mock_pin, self.user, 1)
        
        mock_create.assert_not_called()
    
    @patch('notifications.manager.notification_manager.create_notification')
    def test_notify_pin_liked_other_user(self, mock_create):
        """Test notification when other user likes pin"""
        # Create a mock pin
        mock_pin = MagicMock()
        mock_pin.owner = self.user
        mock_pin.title = "Test Pin"
        mock_pin.id = 1
        
        # Friend likes user's pin - should create notification
        notify_pin_liked(mock_pin, self.friend, 1)
        
        mock_create.assert_called_once()
        call_args = mock_create.call_args
        
        self.assertEqual(call_args[1]['recipient'], self.user)
        self.assertEqual(call_args[1]['notification_type'], NotificationType.PIN_LIKE)
    
    @patch('notifications.manager.notification_manager.create_notification')
    def test_notify_level_up(self, mock_create):
        """Test level-up notification utility"""
        notify_level_up(self.user, new_level=2, badge_name="Selector")
        
        mock_create.assert_called_once()
        call_args = mock_create.call_args
        
        self.assertEqual(call_args[1]['recipient'], self.user)
        self.assertEqual(call_args[1]['notification_type'], NotificationType.LEVEL_UP)
        self.assertEqual(call_args[1]['category'], NotificationCategory.GAMIFICATION)
        self.assertEqual(call_args[1]['priority'], NotificationPriority.HIGH)
        self.assertIn("Level 2", call_args[1]['message'])
        self.assertIn("Selector", call_args[1]['message'])


class OneSignalServiceTests(TransactionTestCase):
    """
    Test OneSignal service functionality with proper transaction handling
    """
    
    def setUp(self):
        # Ensure clean database state
        connection.ensure_connection()
        super().setUp()
        
        # Create test user with proper error handling
        try:
            self.user = User.objects.create_user(
                username='testuser_onesignal',
                email='<EMAIL>',
                password='testpass123'
            )
        except Exception as e:
            # If user already exists, get it
            self.user = User.objects.get(username='testuser_onesignal')
            
        # Create OneSignal player ID
        OneSignalPlayerID.objects.create(
            user=self.user,
            player_id='test-player-id',
            platform='web'
        )
    
    def tearDown(self):
        # Clean up and close connections properly
        try:
            super().tearDown()
        except:
            pass
        finally:
            connection.close()
    
    @patch('requests.post')
    def test_send_notification_success(self, mock_post):
        """Test successful OneSignal notification send"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'id': 'notification-id-123',
            'recipients': 1
        }
        mock_post.return_value = mock_response
        
        result = onesignal_service.send_to_user(
            user=self.user,
            title="Test Title",
            message="Test Message"
        )
        
        self.assertTrue(result['success'])
        self.assertEqual(result['notification_id'], 'notification-id-123')
        mock_post.assert_called_once()
    
    @patch('requests.post')
    def test_send_notification_failure(self, mock_post):
        """Test failed OneSignal notification send"""
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.text = 'Bad Request'
        mock_post.return_value = mock_response
        
        result = onesignal_service.send_to_user(
            user=self.user,
            title="Test Title",
            message="Test Message"
        )
        
        self.assertFalse(result['success'])
        self.assertIn('error', result)
    
    def test_register_player(self):
        """Test player registration"""
        new_user = User.objects.create_user(
            username='newuser_onesignal',
            email='<EMAIL>',
            password='testpass123'
        )
        
        success = onesignal_service.register_player(
            user=new_user,
            player_id='new-player-id',
            platform='ios'
        )
        
        self.assertTrue(success)
        
        # Check player was created
        player = OneSignalPlayerID.objects.get(user=new_user)
        self.assertEqual(player.player_id, 'new-player-id')
        self.assertEqual(player.platform, 'ios')


class GamificationIntegrationTests(TransactionTestCase):
    """
    Test gamification and level-up notification integration with proper transaction handling
    """
    
    def setUp(self):
        # Ensure clean database state
        connection.ensure_connection()
        super().setUp()
        
        # Create test user with proper error handling
        try:
            self.user = User.objects.create_user(
                username='testuser_gamification',
                email='<EMAIL>',
                password='testpass123'
            )
        except Exception as e:
            # If user already exists, get it
            self.user = User.objects.get(username='testuser_gamification')
            
        # Ensure user has the XP fields set
        self.user.total_xp = 0
        self.user.current_level = 1
        self.user.save()
    
    def tearDown(self):
        # Clean up and close connections properly
        try:
            super().tearDown()
        except:
            pass
        finally:
            connection.close()
    
    @patch('notifications.services.onesignal_service.send_to_user')
    def test_achievement_completion_triggers_xp_update(self, mock_send):
        """Test that completing an achievement updates XP and triggers level-up if needed"""
        from gamification.models import Achievement, UserAchievement
        from gamification.services.xp_calculator import XPCalculator
        from django.utils import timezone
        
        mock_send.return_value = {'success': True, 'notification_id': 'test-id'}
        
        # Clear any existing notifications
        Notification.objects.filter(recipient=self.user).delete()
        
        # Create a high-XP achievement that will trigger level-up
        achievement = Achievement.objects.create(
            name='Test Level Up Achievement',
            description='Achievement for testing level-up',
            type='achievement',
            xp_reward=600,  # Enough to reach level 2 (needs 500)
            criteria={'test': True}
        )
        
        # Create and complete the achievement
        user_achievement = UserAchievement.objects.create(
            user=self.user,
            achievement=achievement,
            progress={}
        )
        
        # Complete the achievement (this should trigger the signal)
        user_achievement.completed_at = timezone.now()
        user_achievement.save()
        
        # Manually trigger the signal to ensure it works
        from gamification.signals import handle_achievement_completion
        handle_achievement_completion(
            sender=UserAchievement,
            instance=user_achievement,
            created=False
        )
        
        # Check that user XP and level were updated
        self.user.refresh_from_db()
        self.assertEqual(self.user.total_xp, 600)
        self.assertEqual(self.user.current_level, 2)
        
        # Check that level-up notification was created
        level_up_notifications = Notification.objects.filter(
            recipient=self.user,
            notification_type=NotificationType.LEVEL_UP
        )
        self.assertEqual(level_up_notifications.count(), 1)

        notification = level_up_notifications.first()
        self.assertEqual(notification.category, NotificationCategory.GAMIFICATION)
        self.assertEqual(notification.priority, NotificationPriority.HIGH)
        self.assertIn("Level 2", notification.message)
        self.assertIn("Selector", notification.message)


class FreshPinsNotificationTests(TransactionTestCase):
    """
    Test fresh pins notification functionality with proper transaction handling
    """
    
    def setUp(self):
        # Ensure clean database state
        connection.ensure_connection()
        super().setUp()
        
        # Create test users with proper error handling
        try:
            self.user = User.objects.create_user(
                username='testuser_freshpins',
                email='<EMAIL>',
                password='testpass123'
            )
        except Exception as e:
            # If user already exists, get it
            self.user = User.objects.get(username='testuser_freshpins')
            
        try:
            self.friend = User.objects.create_user(
                username='friend_freshpins',
                email='<EMAIL>',
                password='testpass123'
            )
        except Exception as e:
            # If user already exists, get it
            self.friend = User.objects.get(username='friend_freshpins')
    
    def tearDown(self):
        # Clean up and close connections properly
        try:
            super().tearDown()
        except:
            pass
        finally:
            connection.close()
    
    @patch('notifications.manager.notification_manager.create_notification')
    def test_notify_fresh_pins_discovered_with_valid_count(self, mock_create):
        """Test that fresh pins notification is sent for valid pin count"""
        from notifications.utils import notify_fresh_pins_discovered
        
        notify_fresh_pins_discovered(self.user, 5, "Central Park")
        
        mock_create.assert_called_once()
        call_args = mock_create.call_args[1]
        
        self.assertEqual(call_args['recipient'], self.user)
        self.assertEqual(call_args['notification_type'], NotificationType.NEW_PINS_NEARBY)
        self.assertEqual(call_args['category'], NotificationCategory.EXPLORATION)
        self.assertEqual(call_args['priority'], NotificationPriority.MEDIUM)
        self.assertEqual(call_args['title'], "Fresh pins discovered! 🎵")
        self.assertIn("5 fresh pins", call_args['message'])
        self.assertIn("Central Park", call_args['message'])
        self.assertEqual(call_args['action_data']['pin_count'], 5)
        self.assertEqual(call_args['action_data']['location_name'], "Central Park")
        self.assertEqual(call_args['action_data']['type'], "explore_nearby")
    
    @patch('notifications.manager.notification_manager.create_notification')
    def test_notify_fresh_pins_discovered_with_no_location(self, mock_create):
        """Test that fresh pins notification works without location name"""
        from notifications.utils import notify_fresh_pins_discovered
        
        notify_fresh_pins_discovered(self.user, 3)
        
        mock_create.assert_called_once()
        call_args = mock_create.call_args[1]
        
        self.assertEqual(call_args['recipient'], self.user)
        self.assertIn("3 fresh pins nearby", call_args['message'])
        self.assertIsNone(call_args['action_data']['location_name'])
    
    @patch('notifications.manager.notification_manager.create_notification')
    def test_notify_fresh_pins_discovered_too_few_pins(self, mock_create):
        """Test that no notification is sent for too few pins"""
        from notifications.utils import notify_fresh_pins_discovered
        
        # Test with 1 pin (should not send notification)
        notify_fresh_pins_discovered(self.user, 1)
        mock_create.assert_not_called()
        
        # Test with 0 pins (should not send notification)
        notify_fresh_pins_discovered(self.user, 0)
        mock_create.assert_not_called()
        
        # Test with negative pins (should not send notification)
        notify_fresh_pins_discovered(self.user, -1)
        mock_create.assert_not_called()
    
    @patch('notifications.manager.notification_manager.create_notification')
    def test_notify_fresh_pins_discovered_minimum_threshold(self, mock_create):
        """Test that notification is sent exactly at minimum threshold"""
        from notifications.utils import notify_fresh_pins_discovered
        
        notify_fresh_pins_discovered(self.user, 2)
        
        mock_create.assert_called_once()
        call_args = mock_create.call_args[1]
        
        self.assertEqual(call_args['recipient'], self.user)
        self.assertIn("2 fresh pins", call_args['message'])
        self.assertEqual(call_args['action_data']['pin_count'], 2)


class PinViewInteractionTests(TransactionTestCase):
    """
    Test pin view interactions with notification system with proper transaction handling
    """
    
    def setUp(self):
        # Ensure clean database state
        connection.ensure_connection()
        super().setUp()
        
        # Create test users with proper error handling
        try:
            self.user = User.objects.create_user(
                username='testuser_pinview',
                email='<EMAIL>',
                password='testpass123'
            )
        except Exception as e:
            # If user already exists, get it
            self.user = User.objects.get(username='testuser_pinview')
            
        try:
            self.friend = User.objects.create_user(
                username='friend_pinview',
                email='<EMAIL>',
                password='testpass123'
            )
        except Exception as e:
            # If user already exists, get it
            self.friend = User.objects.get(username='friend_pinview')
        
        # Create mock pins for testing
        from pins.models import Pin, PinSkin
        from django.contrib.gis.geos import Point
        
        # Create default skin
        self.default_skin, _ = PinSkin.objects.get_or_create(
            id=1,
            defaults={
                'name': 'Default',
                'image': 'https://example.com/default.png'
            }
        )
        
        # Create test pins
        self.pin1 = Pin.objects.create(
            owner=self.friend,
            location=Point(-74.0060, 40.7128, srid=4326),  # NYC coordinates
            title='Test Pin 1',
            track_title='Test Track 1',
            track_artist='Test Artist 1',
            track_url='https://example.com/track1',
            service='spotify',
            skin=self.default_skin
        )
        
        self.pin2 = Pin.objects.create(
            owner=self.friend,
            location=Point(-74.0059, 40.7129, srid=4326),  # Nearby NYC coordinates
            title='Test Pin 2',
            track_title='Test Track 2',
            track_artist='Test Artist 2',
            track_url='https://example.com/track2',
            service='spotify',
            skin=self.default_skin
        )
        
        self.pin3 = Pin.objects.create(
            owner=self.friend,
            location=Point(-74.0058, 40.7130, srid=4326),  # Nearby NYC coordinates
            title='Test Pin 3',
            track_title='Test Track 3',
            track_artist='Test Artist 3',
            track_url='https://example.com/track3',
            service='spotify',
            skin=self.default_skin
        )
    
    def tearDown(self):
        # Clean up and close connections properly
        try:
            super().tearDown()
        except:
            pass
        finally:
            connection.close()
    
    @patch('pins.views.notify_fresh_pins_discovered')
    @patch('pins.views.SeedingService')
    def test_nearby_view_marks_pins_as_viewed(self, mock_seeding_service, mock_notify):
        """Test that nearby view marks pins as viewed"""
        from pins.models import PinInteraction
        from rest_framework.test import APIClient
        from django.urls import reverse
        
        # Mock the seeding service to return no seeding needed
        mock_seeding_instance = mock_seeding_service.return_value
        mock_seeding_instance.check_personalized_seeding_needed.return_value = {
            'should_seed': False,
            'reason': 'Test environment - seeding disabled'
        }
        
        client = APIClient()
        client.force_authenticate(user=self.user)
        
        # Clear any existing interactions
        PinInteraction.objects.filter(user=self.user).delete()
        
        # Call nearby endpoint
        url = reverse('pin-nearby')
        response = client.get(url, {
            'latitude': 40.7128,
            'longitude': -74.0060,
            'radius': 1000
        })
        
        self.assertEqual(response.status_code, 200)
        
        # Check that pins were marked as viewed (expect at least our 3 test pins)
        view_interactions = PinInteraction.objects.filter(
            user=self.user,
            interaction_type='view'
        )
        
        # Should have at least our 3 test pins marked as viewed
        self.assertGreaterEqual(view_interactions.count(), 3)
        
        # Check that notification was called with the actual pin count
        mock_notify.assert_called_once()
        call_args = mock_notify.call_args
        actual_pin_count = call_args[1]['pin_count']
        self.assertGreaterEqual(actual_pin_count, 3)  # Should have at least 3 pins
    
    @patch('pins.views.notify_fresh_pins_discovered')
    @patch('pins.views.SeedingService')
    def test_nearby_fresh_view_marks_pins_as_viewed(self, mock_seeding_service, mock_notify):
        """Test that nearby_fresh view marks pins as viewed"""
        from pins.models import PinInteraction
        from rest_framework.test import APIClient
        from django.urls import reverse
        
        # Mock the seeding service to return no seeding needed
        mock_seeding_instance = mock_seeding_service.return_value
        mock_seeding_instance.check_personalized_seeding_needed.return_value = {
            'should_seed': False,
            'reason': 'Test environment - seeding disabled'
        }
        
        client = APIClient()
        client.force_authenticate(user=self.user)
        
        # Clear any existing interactions
        PinInteraction.objects.filter(user=self.user).delete()
        
        # Call nearby_fresh endpoint
        url = reverse('pin-nearby-fresh')
        response = client.get(url, {
            'latitude': 40.7128,
            'longitude': -74.0060,
            'radius': 1000
        })
        
        self.assertEqual(response.status_code, 200)
        
        # Check that pins were marked as viewed (expect at least our 3 test pins)
        view_interactions = PinInteraction.objects.filter(
            user=self.user,
            interaction_type='view'
        )
        
        # Should have at least our 3 test pins marked as viewed
        self.assertGreaterEqual(view_interactions.count(), 3)
        
        # Check that notification was called with the actual pin count
        mock_notify.assert_called_once()
        call_args = mock_notify.call_args
        actual_pin_count = call_args[1]['pin_count']
        self.assertGreaterEqual(actual_pin_count, 3)  # Should have at least 3 pins
    
    @patch('pins.views.notify_fresh_pins_discovered')
    @patch('pins.views.SeedingService')
    def test_nearby_friends_view_marks_pins_as_viewed(self, mock_seeding_service, mock_notify):
        """Test that nearby_friends view marks pins as viewed"""
        from pins.models import PinInteraction
        from friends.models import Friend
        from rest_framework.test import APIClient
        from django.urls import reverse
        
        # Mock the seeding service to return no seeding needed
        mock_seeding_instance = mock_seeding_service.return_value
        mock_seeding_instance.check_personalized_seeding_needed.return_value = {
            'should_seed': False,
            'reason': 'Test environment - seeding disabled'
        }
        
        # Create friendship
        Friend.objects.create(
            requester=self.user,
            recipient=self.friend,
            status='accepted'
        )
        
        client = APIClient()
        client.force_authenticate(user=self.user)
        
        # Clear any existing interactions
        PinInteraction.objects.filter(user=self.user).delete()
        
        # Call nearby_friends endpoint
        url = reverse('pin-nearby-friends')
        response = client.get(url, {
            'latitude': 40.7128,
            'longitude': -74.0060,
            'radius': 1000
        })
        
        self.assertEqual(response.status_code, 200)
        
        # Check that pins were marked as viewed (expect at least our 3 test pins)
        view_interactions = PinInteraction.objects.filter(
            user=self.user,
            interaction_type='view'
        )
        
        # Should have at least our 3 test pins marked as viewed
        self.assertGreaterEqual(view_interactions.count(), 3)
        
        # Check that notification was called with the actual pin count
        mock_notify.assert_called_once()
        call_args = mock_notify.call_args
        actual_pin_count = call_args[1]['pin_count']
        self.assertGreaterEqual(actual_pin_count, 3)  # Should have at least 3 pins
    
    @patch('pins.views.notify_fresh_pins_discovered')
    @patch('pins.views.SeedingService')
    def test_no_notification_for_insufficient_pins(self, mock_seeding_service, mock_notify):
        """Test that no notification is sent when there are insufficient fresh pins"""
        from pins.models import PinInteraction
        from rest_framework.test import APIClient
        from django.urls import reverse
        
        # Mock the seeding service to return no seeding needed
        mock_seeding_instance = mock_seeding_service.return_value
        mock_seeding_instance.check_personalized_seeding_needed.return_value = {
            'should_seed': False,
            'reason': 'Test environment - seeding disabled'
        }
        
        client = APIClient()
        client.force_authenticate(user=self.user)
        
        # Mark most pins as already viewed to leave only 1 fresh pin
        # Get all pins in the area and mark them as viewed except one
        from pins.models import Pin
        nearby_pins = Pin.objects.filter(
            location__distance_lte=(self.pin1.location, 1000)  # 1km radius
        )
        
        # Mark all but one pin as viewed
        for i, pin in enumerate(nearby_pins):
            if i < len(nearby_pins) - 1:  # Mark all but the last one
                PinInteraction.objects.create(
                    user=self.user,
                    pin=pin,
                    interaction_type='view'
                )
        
        # Call nearby endpoint (should only find 1 fresh pin)
        url = reverse('pin-nearby')
        response = client.get(url, {
            'latitude': 40.7128,
            'longitude': -74.0060,
            'radius': 1000
        })
        
        self.assertEqual(response.status_code, 200)
        
        # Check that notification was not called (only 1 fresh pin, below threshold of 2)
        mock_notify.assert_not_called()
    
    @patch('pins.views.notify_fresh_pins_discovered')
    @patch('pins.views.SeedingService')
    def test_notification_sent_for_sufficient_pins(self, mock_seeding_service, mock_notify):
        """Test that notification is sent when there are sufficient fresh pins"""
        from pins.models import PinInteraction
        from rest_framework.test import APIClient
        from django.urls import reverse
        
        # Mock the seeding service to return no seeding needed
        mock_seeding_instance = mock_seeding_service.return_value
        mock_seeding_instance.check_personalized_seeding_needed.return_value = {
            'should_seed': False,
            'reason': 'Test environment - seeding disabled'
        }
        
        client = APIClient()
        client.force_authenticate(user=self.user)
        
        # Mark some pins as already viewed to leave exactly 2 fresh pins
        from pins.models import Pin
        nearby_pins = Pin.objects.filter(
            location__distance_lte=(self.pin1.location, 1000)  # 1km radius
        )
        
        # Mark all but 2 pins as viewed
        for i, pin in enumerate(nearby_pins):
            if i < len(nearby_pins) - 2:  # Mark all but the last 2
                PinInteraction.objects.create(
                    user=self.user,
                    pin=pin,
                    interaction_type='view'
                )
        
        # Call nearby endpoint (should find 2 fresh pins)
        url = reverse('pin-nearby')
        response = client.get(url, {
            'latitude': 40.7128,
            'longitude': -74.0060,
            'radius': 1000
        })
        
        self.assertEqual(response.status_code, 200)
        
        # Check that notification was called with correct count (2 fresh pins)
        mock_notify.assert_called_once()
        call_args = mock_notify.call_args
        actual_pin_count = call_args[1]['pin_count']
        self.assertEqual(actual_pin_count, 2)  # Should have exactly 2 fresh pins
    
    def test_xp_calculator_level_info(self):
        """Test XP calculator level information"""
        from gamification.services.xp_calculator import XPCalculator
        from gamification.models import Achievement, UserAchievement
        from django.utils import timezone
        
        # Test level 1 user (no achievements)
        level_info = XPCalculator.get_user_level_info(self.user)
        self.assertEqual(level_info['current_level']['level'], 1)
        self.assertEqual(level_info['current_level']['name'], 'Basement Bopper')
        self.assertEqual(level_info['next_level']['level'], 2)
        self.assertEqual(level_info['next_level']['name'], 'Selector')
        
        # Create and complete an achievement with 600 XP to reach level 2
        achievement = Achievement.objects.create(
            name='Level 2 Achievement',
            description='Achievement to reach level 2',
            type='achievement',
            xp_reward=600,
            criteria={'test': True}
        )
        
        UserAchievement.objects.create(
            user=self.user,
            achievement=achievement,
            progress={},
            completed_at=timezone.now()
        )
        
        # Test level 2 user (with completed achievement)
        level_info = XPCalculator.get_user_level_info(self.user)
        self.assertEqual(level_info['current_level']['level'], 2)
        self.assertEqual(level_info['current_level']['name'], 'Selector')
    
    def test_no_level_up_notification_for_same_level(self):
        """Test that no notification is sent if level doesn't change"""
        from gamification.models import Achievement, UserAchievement
        from django.utils import timezone
        
        # Clear any existing notifications
        Notification.objects.filter(recipient=self.user).delete()
        
        # Create a low-XP achievement that won't trigger level-up
        achievement = Achievement.objects.create(
            name='Low XP Achievement',
            description='Achievement with low XP',
            type='achievement',
            xp_reward=100,  # Not enough to level up
            criteria={'test': True}
        )
        
        # Complete the achievement
        user_achievement = UserAchievement.objects.create(
            user=self.user,
            achievement=achievement,
            progress={},
            completed_at=timezone.now()
        )
        
        # Manually trigger the signal
        from gamification.signals import handle_achievement_completion
        handle_achievement_completion(
            sender=UserAchievement,
            instance=user_achievement,
            created=False
        )
        
        # Check that user XP was updated but level didn't change
        self.user.refresh_from_db()
        self.assertEqual(self.user.total_xp, 100)
        self.assertEqual(self.user.current_level, 1)  # Still level 1
        
        # Check that no level-up notification was created
        level_up_notifications = Notification.objects.filter(
            recipient=self.user,
            notification_type=NotificationType.LEVEL_UP
        )
        self.assertEqual(level_up_notifications.count(), 0)
    
    @patch('notifications.services.onesignal_service.send_to_user')
    def test_multiple_achievements_cumulative_xp(self, mock_send):
        """Test that multiple achievements give cumulative XP and can trigger level-up"""
        from gamification.models import Achievement, UserAchievement
        from django.utils import timezone
        
        mock_send.return_value = {'success': True, 'notification_id': 'test-id'}
        
        # Clear any existing notifications
        Notification.objects.filter(recipient=self.user).delete()
        
        # Create multiple achievements
        achievement1 = Achievement.objects.create(
            name='Achievement 1',
            description='First achievement',
            type='achievement',
            xp_reward=200,
            criteria={'test': True}
        )
        
        achievement2 = Achievement.objects.create(
            name='Achievement 2',
            description='Second achievement',
            type='achievement',
            xp_reward=350,  # Total will be 550, enough for level 2
            criteria={'test': True}
        )
        
        # Complete first achievement
        user_achievement1 = UserAchievement.objects.create(
            user=self.user,
            achievement=achievement1,
            progress={},
            completed_at=timezone.now()
        )
        
        from gamification.signals import handle_achievement_completion
        handle_achievement_completion(UserAchievement, user_achievement1, False)
        
        self.user.refresh_from_db()
        self.assertEqual(self.user.total_xp, 200)
        self.assertEqual(self.user.current_level, 1)  # Still level 1
        
        # Complete second achievement (should trigger level-up)
        user_achievement2 = UserAchievement.objects.create(
            user=self.user,
            achievement=achievement2,
            progress={},
            completed_at=timezone.now()
        )
        
        handle_achievement_completion(UserAchievement, user_achievement2, False)
        
        self.user.refresh_from_db()
        self.assertEqual(self.user.total_xp, 550)
        self.assertEqual(self.user.current_level, 2)  # Now level 2
        
        # Check that level-up notification was created
        level_up_notifications = Notification.objects.filter(
            recipient=self.user,
            notification_type=NotificationType.LEVEL_UP
        )
        self.assertEqual(level_up_notifications.count(), 1)
