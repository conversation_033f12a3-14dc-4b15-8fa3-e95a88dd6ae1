from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

# Create router and register viewsets
router = DefaultRouter()
router.register(r'notifications', views.NotificationViewSet, basename='notification')
router.register(r'settings', views.UserNotificationSettingsViewSet, basename='notification-settings')
router.register(r'onesignal', views.OneSignalViewSet, basename='onesignal')
router.register(r'templates', views.NotificationTemplateViewSet, basename='notification-template')

urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
    
    # Additional endpoints
    path('send-bulk/', views.send_bulk_notification, name='send-bulk-notification'),
    path('send-test/', views.send_test_notification, name='send-test-notification'),
    path('process-batches/', views.process_batched_notifications, name='process-batched-notifications'),
    path('cleanup/', views.cleanup_old_notifications, name='cleanup-old-notifications'),
] 