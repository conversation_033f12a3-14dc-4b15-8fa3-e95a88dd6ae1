"""
Utility functions for creating notifications for various app events
"""
import logging
from typing import List, Optional, Dict, Any
from django.contrib.auth import get_user_model
from django.contrib.gis.geos import Point
from django.contrib.gis.measure import Distance
from django.db.models import Q
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from .manager import notification_manager
from .models import NotificationType, NotificationCategory, NotificationPriority

logger = logging.getLogger(__name__)
User = get_user_model()


# Map & Pin related notifications

def notify_pin_liked(pin, liked_by_user, like_count=None):
    """
    Notify when a pin receives likes
    
    Args:
        pin: Pin instance that was liked
        liked_by_user: User who liked the pin
        like_count: Current total like count (optional)
    """
    if pin.owner == liked_by_user:
        return  # Don't notify users for their own likes
    
    try:
        # Use batching for like notifications to prevent spam
        batch_key = f"pin_like_{pin.owner.id}_{pin.id}_{timezone.now().date()}"
        
        notification_manager.create_notification(
            recipient=pin.owner,
            notification_type=NotificationType.PIN_LIKE,
            category=NotificationCategory.MAP,
            priority=NotificationPriority.MEDIUM,
            batch_key=batch_key,
            template_data={
                'pin_title': pin.title or pin.track_name,
                'liked_by': liked_by_user.username,
                'like_count': like_count or 1
            },
            action_data={
                'pin_id': pin.id,
                'liked_by_user_id': liked_by_user.id,
                'type': 'pin_detail'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create pin like notification: {str(e)}")


def notify_pin_trending(pin, view_count=None):
    """
    Notify when a pin is trending
    
    Args:
        pin: Pin instance that's trending
        view_count: Current view count
    """
    try:
        notification_manager.create_notification(
            recipient=pin.owner,
            notification_type=NotificationType.PIN_TRENDING,
            category=NotificationCategory.MAP,
            priority=NotificationPriority.HIGH,
            title=f"Your pin is trending! 🔥",
            message=f'Your pin "{pin.title or pin.track_name}" has {view_count or "many"} views today!',
            action_data={
                'pin_id': pin.id,
                'view_count': view_count,
                'type': 'pin_detail'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create pin trending notification: {str(e)}")


def notify_friend_nearby(user, friend, pin, distance_km=None):
    """
    Notify when a friend drops a pin nearby
    
    Args:
        user: User to notify
        friend: Friend who dropped the pin
        pin: Pin that was dropped
        distance_km: Distance in kilometers
    """
    try:
        distance_text = f"{distance_km:.1f} km" if distance_km else "nearby"
        
        notification_manager.create_notification(
            recipient=user,
            notification_type=NotificationType.FRIEND_NEARBY,
            category=NotificationCategory.SOCIAL,
            priority=NotificationPriority.HIGH,
            title=f"Friend nearby 📍",
            message=f'{friend.username} just dropped a pin {distance_text} from you - "{pin.title or pin.track_name}"',
            action_data={
                'friend_id': friend.id,
                'pin_id': pin.id,
                'distance_km': distance_km,
                'type': 'map_location'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create friend nearby notification: {str(e)}")


# Social & Friends notifications

def notify_friend_request(recipient, sender):
    """
    Notify about new friend request
    
    Args:
        recipient: User receiving the friend request
        sender: User sending the friend request
    """
    try:
        notification_manager.create_notification(
            recipient=recipient,
            notification_type=NotificationType.FRIEND_REQUEST,
            category=NotificationCategory.SOCIAL,
            priority=NotificationPriority.MEDIUM,
            title=f"New friend request 👋",
            message=f"{sender.username} wants to connect with you",
            action_data={
                'friend_id': sender.id,
                'type': 'friend_requests'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create friend request notification: {str(e)}")


def notify_friend_accepted(recipient, accepter):
    """
    Notify when friend request is accepted
    
    Args:
        recipient: User whose request was accepted
        accepter: User who accepted the request
    """
    try:
        notification_manager.create_notification(
            recipient=recipient,
            notification_type=NotificationType.FRIEND_ACCEPTED,
            category=NotificationCategory.SOCIAL,
            priority=NotificationPriority.MEDIUM,
            title=f"Friend request accepted! ✨",
            message=f"{accepter.username} is now your friend",
            action_data={
                'friend_id': accepter.id,
                'type': 'friend_profile'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create friend accepted notification: {str(e)}")


def notify_music_chat_message(recipient, sender, message_preview):
    """
    Notify about new music chat message
    
    Args:
        recipient: User receiving the message
        sender: User sending the message
        message_preview: Preview of the message content
    """
    try:
        notification_manager.create_notification(
            recipient=recipient,
            notification_type=NotificationType.MUSIC_CHAT,
            category=NotificationCategory.SOCIAL,
            priority=NotificationPriority.HIGH,
            title=f"New message 💬",
            message=f"{sender.username}: \"{message_preview}\"",
            action_data={
                'sender_id': sender.id,
                'type': 'music_chat'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create music chat notification: {str(e)}")


# Music notifications

def notify_new_release(user, artist_name, track_name, release_type="single"):
    """
    Notify about new release from followed artist
    
    Args:
        user: User to notify
        artist_name: Name of the artist
        track_name: Name of the new track/album
        release_type: Type of release (single, album, etc.)
    """
    try:
        notification_manager.create_notification(
            recipient=user,
            notification_type=NotificationType.NEW_RELEASE,
            category=NotificationCategory.MUSIC,
            priority=NotificationPriority.HIGH,
            title=f"New from {artist_name} 🎵",
            message=f"Your favorite artist just released \"{track_name}\"",
            action_data={
                'artist_name': artist_name,
                'track_name': track_name,
                'release_type': release_type,
                'type': 'play_track'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create new release notification: {str(e)}")


# Challenges & Gamification notifications

def notify_challenge_available(user, challenge_name, xp_reward=None):
    """
    Notify about new challenge available
    
    Args:
        user: User to notify
        challenge_name: Name of the challenge
        xp_reward: XP reward for completing
    """
    try:
        xp_text = f" and earn {xp_reward} XP" if xp_reward else ""
        
        notification_manager.create_notification(
            recipient=user,
            notification_type=NotificationType.CHALLENGE_AVAILABLE,
            category=NotificationCategory.GAMIFICATION,
            priority=NotificationPriority.MEDIUM,
            title=f"New challenge available! 🎯",
            message=f"Complete the \"{challenge_name}\" challenge{xp_text}",
            action_data={
                'challenge_name': challenge_name,
                'xp_reward': xp_reward,
                'type': 'challenges'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create challenge available notification: {str(e)}")


def notify_challenge_complete(user, challenge_name, xp_earned=None):
    """
    Notify when user completes a challenge
    
    Args:
        user: User who completed the challenge
        challenge_name: Name of completed challenge
        xp_earned: XP earned from completion
    """
    try:
        xp_text = f" and earned {xp_earned} XP" if xp_earned else ""
        
        notification_manager.create_notification(
            recipient=user,
            notification_type=NotificationType.CHALLENGE_COMPLETE,
            category=NotificationCategory.GAMIFICATION,
            priority=NotificationPriority.HIGH,
            title=f"Challenge completed! 🏆",
            message=f"You finished \"{challenge_name}\"{xp_text}",
            action_data={
                'challenge_name': challenge_name,
                'xp_earned': xp_earned,
                'type': 'challenges'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create challenge complete notification: {str(e)}")


def notify_level_up(user, new_level, badge_name=None):
    """
    Notify when user levels up
    
    Args:
        user: User who leveled up
        new_level: New level reached
        badge_name: Optional badge earned
    """
    try:
        badge_text = f" and earned the {badge_name} badge" if badge_name else ""
        
        notification_manager.create_notification(
            recipient=user,
            notification_type=NotificationType.LEVEL_UP,
            category=NotificationCategory.GAMIFICATION,
            priority=NotificationPriority.HIGH,
            title=f"Level Up! 🚀",
            message=f"Congratulations! You're now Level {new_level}{badge_text}",
            action_data={
                'new_level': new_level,
                'badge_name': badge_name,
                'type': 'profile'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create level up notification: {str(e)}")


# Collections & Playlists notifications

def notify_collection_milestone(user, collection_name, milestone_count, milestone_type="plays"):
    """
    Notify when collection reaches milestone
    
    Args:
        user: Collection owner
        collection_name: Name of the collection
        milestone_count: Milestone number reached
        milestone_type: Type of milestone (plays, likes, etc.)
    """
    try:
        notification_manager.create_notification(
            recipient=user,
            notification_type=NotificationType.COLLECTION_MILESTONE,
            category=NotificationCategory.COLLECTION,
            priority=NotificationPriority.LOW,
            title=f"Collection milestone! 🎯",
            message=f"Your \"{collection_name}\" collection reached {milestone_count:,} {milestone_type}",
            action_data={
                'collection_name': collection_name,
                'milestone_count': milestone_count,
                'milestone_type': milestone_type,
                'type': 'collection'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create collection milestone notification: {str(e)}")


# Skins & Customization notifications

def notify_skin_unlocked(user, skin_name, unlock_reason=None):
    """
    Notify when user unlocks a new skin
    
    Args:
        user: User who unlocked the skin
        skin_name: Name of the skin
        unlock_reason: Reason for unlock (challenge, purchase, etc.)
    """
    try:
        reason_text = f" - {unlock_reason}" if unlock_reason else ""
        
        notification_manager.create_notification(
            recipient=user,
            notification_type=NotificationType.SKIN_UNLOCKED,
            category=NotificationCategory.CUSTOMIZATION,
            priority=NotificationPriority.MEDIUM,
            title=f"New skin unlocked! ✨",
            message=f"{skin_name} skin is now available{reason_text}",
            action_data={
                'skin_name': skin_name,
                'unlock_reason': unlock_reason,
                'type': 'skins'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create skin unlock notification: {str(e)}")


def notify_limited_skin_available(users, skin_name, duration_hours=24):
    """
    Notify multiple users about limited-time skin availability
    
    Args:
        users: List of users to notify
        skin_name: Name of the limited skin
        duration_hours: How long the skin is available
    """
    try:
        notification_manager.send_bulk_notification(
            recipients=users,
            notification_type=NotificationType.LIMITED_SKIN_AVAILABLE,
            category=NotificationCategory.CUSTOMIZATION,
            priority=NotificationPriority.MEDIUM,
            title=f"Limited skin available! ⏰",
            message=f"{skin_name} skin is free for the next {duration_hours} hours",
            action_data={
                'skin_name': skin_name,
                'duration_hours': duration_hours,
                'type': 'skins'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create limited skin notification: {str(e)}")


# System & General notifications

def notify_welcome_message(user):
    """
    Send welcome message to new user
    
    Args:
        user: Newly registered user
    """
    try:
        notification_manager.create_notification(
            recipient=user,
            notification_type=NotificationType.WELCOME_MESSAGE,
            category=NotificationCategory.GENERAL,
            priority=NotificationPriority.MEDIUM,
            title=f"Welcome to BOP Maps! 🎵",
            message=f"Hi {user.username}! Create your first pin to start earning XP and discovering music.",
            action_data={
                'type': 'create_pin'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create welcome notification: {str(e)}")


def notify_retention_reminder(users, days_inactive=7):
    """
    Send retention reminder to inactive users
    
    Args:
        users: List of inactive users
        days_inactive: Number of days user has been inactive
    """
    try:
        notification_manager.send_bulk_notification(
            recipients=users,
            notification_type=NotificationType.RETENTION_REMINDER,
            category=NotificationCategory.GENERAL,
            priority=NotificationPriority.LOW,
            title=f"Come back to the music! 🎵",
            message=f"It's been a while - 3 new challenges await you!",
            action_data={
                'days_inactive': days_inactive,
                'type': 'challenges'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create retention notification: {str(e)}")


# Additional notifications from blueprint

def notify_real_time_sync(user, friend, activity="listening live"):
    """
    Notify about real-time music sync opportunity
    
    Args:
        user: User to notify
        friend: Friend who is listening
        activity: Type of activity
    """
    try:
        notification_manager.create_notification(
            recipient=user,
            notification_type=NotificationType.MUSIC_SYNC,
            category=NotificationCategory.MUSIC,
            priority=NotificationPriority.URGENT,
            title=f"Live music session 🎧",
            message=f"Party with {friend.username} – tap to listen live!",
            action_data={
                'friend_id': friend.id,
                'activity': activity,
                'type': 'live_session'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create real-time sync notification: {str(e)}")


def notify_challenge_progress(user, challenge_name, remaining_count, progress_percentage):
    """
    Nudge user about challenge progress
    
    Args:
        user: User to nudge
        challenge_name: Name of the challenge
        remaining_count: How many more needed
        progress_percentage: Current progress as percentage
    """
    try:
        if progress_percentage >= 70:  # Only send when 70% or more complete
            notification_manager.create_notification(
                recipient=user,
                notification_type=NotificationType.CHALLENGE_PROGRESS,
                category=NotificationCategory.GAMIFICATION,
                priority=NotificationPriority.MEDIUM,
                title=f"Almost there! 🎯",
                message=f"Just {remaining_count} more songs to finish '{challenge_name}'",
                action_data={
                    'challenge_name': challenge_name,
                    'remaining_count': remaining_count,
                    'progress_percentage': progress_percentage,
                    'type': 'challenge_detail'
                }
            )
            
    except Exception as e:
        logger.error(f"Failed to create challenge progress notification: {str(e)}")


def notify_collection_collaboration(user, collaborator, collection_name, tracks_added):
    """
    Notify about collaborative collection updates
    
    Args:
        user: Collection owner to notify
        collaborator: User who added tracks
        collection_name: Name of the collection
        tracks_added: Number of tracks added
    """
    try:
        tracks_text = f"{tracks_added} tracks" if tracks_added > 1 else "a track"
        
        notification_manager.create_notification(
            recipient=user,
            notification_type=NotificationType.COLLECTION_UPDATE,
            category=NotificationCategory.COLLECTION,
            priority=NotificationPriority.MEDIUM,
            title=f"Collection updated! 🎵",
            message=f"{collaborator.username} added {tracks_text} to '{collection_name}'",
            action_data={
                'collaborator_id': collaborator.id,
                'collection_name': collection_name,
                'tracks_added': tracks_added,
                'type': 'collection_detail'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create collection collaboration notification: {str(e)}")


def notify_new_pins_in_city(users, city_name, pin_count):
    """
    Notify users about new pins in their city
    
    Args:
        users: List of users in the city
        city_name: Name of the city
        pin_count: Number of new pins
    """
    try:
        notification_manager.send_bulk_notification(
            recipients=users,
            notification_type=NotificationType.NEW_PINS_NEARBY,
            category=NotificationCategory.EXPLORATION,
            priority=NotificationPriority.LOW,
            title=f"Hot in {city_name}! 🔥",
            message=f"{pin_count} new AR pins appeared today in your area",
            action_data={
                'city_name': city_name,
                'pin_count': pin_count,
                'type': 'map_explore'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create new pins notification: {str(e)}")


def notify_fresh_pins_discovered(user, pin_count, location_name=None):
    """
    Notify user about fresh pins they haven't viewed nearby
    
    Args:
        user: User to notify
        pin_count: Number of fresh pins found
        location_name: Optional location name for context
    """
    try:
        if pin_count <= 0:
            return
            
        # Don't send notification if pin count is too low
        if pin_count < 2:
            return
            
        location_text = f" near {location_name}" if location_name else " nearby"
        
        notification_manager.create_notification(
            recipient=user,
            notification_type=NotificationType.NEW_PINS_NEARBY,
            category=NotificationCategory.EXPLORATION,
            priority=NotificationPriority.MEDIUM,
            title=f"Fresh pins discovered! 🎵",
            message=f"We found {pin_count} fresh pins{location_text} for you to explore",
            action_data={
                'pin_count': pin_count,
                'location_name': location_name,
                'type': 'explore_nearby'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create fresh pins notification: {str(e)}")


def notify_seasonal_event(users, event_name, event_type="map layer"):
    """
    Notify about seasonal or special events
    
    Args:
        users: List of users to notify
        event_name: Name of the event
        event_type: Type of event
    """
    try:
        notification_manager.send_bulk_notification(
            recipients=users,
            notification_type=NotificationType.SEASONAL_EVENT,
            category=NotificationCategory.EXPLORATION,
            priority=NotificationPriority.HIGH,
            title=f"Special event live! 🎉",
            message=f"{event_name} {event_type} is now available!",
            action_data={
                'event_name': event_name,
                'event_type': event_type,
                'type': 'map_explore'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create seasonal event notification: {str(e)}")


def notify_customization_reminder(user, days_since_signup=7):
    """
    Remind user to customize their pin
    
    Args:
        user: User to remind
        days_since_signup: Days since they signed up
    """
    try:
        if days_since_signup == 7:  # Send one-time reminder after 7 days
            notification_manager.create_notification(
                recipient=user,
                notification_type=NotificationType.CUSTOMIZATION_REMINDER,
                category=NotificationCategory.CUSTOMIZATION,
                priority=NotificationPriority.LOW,
                title=f"Stand out on the map! ✨",
                message=f"You haven't customized your pin yet – make it uniquely yours!",
                action_data={
                    'days_since_signup': days_since_signup,
                    'type': 'skins'
                }
            )
            
    except Exception as e:
        logger.error(f"Failed to create customization reminder: {str(e)}")


def notify_weekly_digest(user, tracks_discovered, friends_active):
    """
    Send weekly activity digest
    
    Args:
        user: User to send digest to
        tracks_discovered: Number of tracks discovered by friends
        friends_active: Number of active friends this week
    """
    try:
        notification_manager.create_notification(
            recipient=user,
            notification_type=NotificationType.WEEKLY_DIGEST,
            category=NotificationCategory.SOCIAL,
            priority=NotificationPriority.LOW,
            title=f"Your week in music 📊",
            message=f"Your friends discovered {tracks_discovered} new tracks this week!",
            action_data={
                'tracks_discovered': tracks_discovered,
                'friends_active': friends_active,
                'type': 'activity_feed'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create weekly digest notification: {str(e)}")


def notify_unread_reminders(users, unread_count):
    """
    Remind users about unread notifications (for users inactive ≥ 3 days)
    
    Args:
        users: List of inactive users
        unread_count: Number of unread notifications
    """
    try:
        if unread_count > 0:
            notification_manager.send_bulk_notification(
                recipients=users,
                notification_type=NotificationType.UNREAD_REMINDER,
                category=NotificationCategory.GENERAL,
                priority=NotificationPriority.LOW,
                title=f"Missed notifications 📬",
                message=f"You have {unread_count} unread notifications",
                action_data={
                    'unread_count': unread_count,
                    'type': 'notifications'
                }
            )
            
    except Exception as e:
        logger.error(f"Failed to create unread reminder notification: {str(e)}")


def notify_achievement_unlocked(user, achievement_name, achievement_description):
    """
    Notify when user unlocks an achievement
    
    Args:
        user: User who unlocked achievement
        achievement_name: Name of achievement
        achievement_description: Description of what was achieved
    """
    try:
        notification_manager.create_notification(
            recipient=user,
            notification_type=NotificationType.ACHIEVEMENT_UNLOCKED,
            category=NotificationCategory.GAMIFICATION,
            priority=NotificationPriority.HIGH,
            title=f"Achievement unlocked! 🏆",
            message=f"{achievement_name}: {achievement_description}",
            action_data={
                'achievement_name': achievement_name,
                'achievement_description': achievement_description,
                'type': 'achievements'
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to create achievement notification: {str(e)}")


# Geo-fenced and batch utilities

def check_and_notify_nearby_friends(user, location_point, radius_km=5.0):
    """
    Check for friends near a location and notify them (geo-fenced, max 1/day)
    
    Args:
        user: User who just dropped a pin
        location_point: Point representing pin location
        radius_km: Radius in kilometers to check for friends
    """
    try:
        from friends.models import Friend
        from pins.models import Pin
        
        # Get user's friends using correct field names
        friends = Friend.objects.filter(
            Q(requester=user, status='accepted') | Q(recipient=user, status='accepted')
        )
        
        friend_users = []
        for friendship in friends:
            friend_user = friendship.recipient if friendship.requester == user else friendship.requester
            friend_users.append(friend_user)
        
        if not friend_users:
            return
        
        # Get the most recent pin for context
        recent_pin = Pin.objects.filter(owner=user).order_by('-created_at').first()
        if not recent_pin:
            return
        
        # For each friend, check daily limit and notify if appropriate
        for friend_user in friend_users:
            # Check if we've already sent a nearby notification today
            today = timezone.now().date()
            existing_notification = notification_manager.get_user_notifications(
                user=friend_user,
                notification_type=NotificationType.FRIEND_NEARBY,
                date_from=today
            )
            
            if len(existing_notification['notifications']) == 0:  # No notification today
                # In a real implementation, you'd calculate actual distance
                # For now, we'll assume they're within range
                notify_friend_nearby(
                    user=friend_user,
                    friend=user,
                    pin=recent_pin,
                    distance_km=0.5  # Placeholder distance
                )
                
    except Exception as e:
        logger.error(f"Failed to check nearby friends: {str(e)}")


# Batched notification utilities

def process_batched_pin_likes():
    """
    Process batched pin like notifications (run periodically)
    """
    try:
        from .models import NotificationBatch
        
        # Process batches that are ready to send
        ready_batches = NotificationBatch.objects.filter(
            is_sent=False,
            scheduled_time__lte=timezone.now()
        )
        
        for batch in ready_batches:
            # Get aggregated data for the batch
            notifications = batch.notifications.all()
            if notifications.count() > 0:
                first_notification = notifications.first()
                total_likes = notifications.count()
                
                # Create summary notification
                notification_manager.create_notification(
                    recipient=first_notification.recipient,
                    notification_type=NotificationType.PIN_LIKE,
                    category=NotificationCategory.MAP,
                    priority=NotificationPriority.MEDIUM,
                    title=f"{total_likes} people liked your pin! 👍",
                    message=f'Your pin "{first_notification.template_data.get("pin_title", "")}" received {total_likes} likes',
                    batch_key=None  # Don't batch the summary
                )
                
                # Mark batch as sent
                batch.mark_as_sent()
                
    except Exception as e:
        logger.error(f"Failed to process batched notifications: {str(e)}")



