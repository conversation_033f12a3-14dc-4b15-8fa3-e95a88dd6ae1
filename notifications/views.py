from django.shortcuts import render
from rest_framework import status, viewsets, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from django.contrib.auth import get_user_model
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
import logging

from .models import (
    Notification, NotificationTemplate, UserNotificationSettings,
    OneSignalPlayerID, NotificationCategory, NotificationType
)
from .serializers import (
    NotificationSerializer, NotificationCreateSerializer, BulkNotificationSerializer,
    NotificationMarkAsReadSerializer, UserNotificationSettingsSerializer,
    OneSignalPlayerRegistrationSerializer, OneSignalPlayerIDSerializer,
    NotificationTemplateSerializer, NotificationStatsSerializer,
    TestNotificationSerializer
)
from .manager import notification_manager
from .services import onesignal_service
from bopmaps.utils import create_error_response

logger = logging.getLogger(__name__)
User = get_user_model()


class NotificationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing user notifications
    """
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """
        Return notifications for the current user only
        """
        return Notification.objects.filter(recipient=self.request.user)
    
    def create(self, request, *args, **kwargs):
        """
        Create a new notification (admin/system use)
        """
        # Only admins can create notifications directly
        if not request.user.is_staff:
            return Response(
                {"error": "Permission denied"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = NotificationCreateSerializer(data=request.data)
        if serializer.is_valid():
            try:
                # Get recipient from request data or default to current user
                recipient_id = request.data.get('recipient_id')
                if recipient_id:
                    recipient = User.objects.get(id=recipient_id)
                else:
                    recipient = request.user
                
                notification = notification_manager.create_notification(
                    recipient=recipient,
                    **serializer.validated_data
                )
                
                if notification:
                    response_serializer = NotificationSerializer(notification)
                    return Response(response_serializer.data, status=status.HTTP_201_CREATED)
                else:
                    return Response(
                        {"error": "Notification was blocked or failed to create"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                    
            except User.DoesNotExist:
                return Response(
                    {"error": "Recipient not found"},
                    status=status.HTTP_404_NOT_FOUND
                )
            except Exception as e:
                logger.error(f"Failed to create notification: {str(e)}")
                return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def list(self, request, *args, **kwargs):
        """
        List user's notifications with filtering and pagination
        """
        try:
            # Get query parameters
            category = request.query_params.get('category', None)
            is_read = request.query_params.get('is_read', None)
            limit = min(int(request.query_params.get('limit', 50)), 100)
            offset = int(request.query_params.get('offset', 0))
            
            # Convert is_read to boolean
            if is_read is not None:
                is_read = is_read.lower() == 'true'
            
            # Get notifications using manager
            result = notification_manager.get_user_notifications(
                user=request.user,
                category=category,
                is_read=is_read,
                limit=limit,
                offset=offset
            )
            
            # Serialize notifications
            serializer = self.get_serializer(result['notifications'], many=True)
            
            return Response({
                'results': serializer.data,
                'count': result['total_count'],
                'unread_count': result['unread_count'],
                'next': offset + limit if offset + limit < result['total_count'] else None,
                'previous': offset - limit if offset > 0 else None
            })
            
        except Exception as e:
            logger.error(f"Failed to list notifications: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def mark_read(self, request, pk=None):
        """
        Mark a specific notification as read
        """
        try:
            success = notification_manager.mark_as_read(
                notification_id=int(pk),
                user=request.user
            )
            
            if success:
                return Response({"success": True, "message": "Notification marked as read"})
            else:
                return Response(
                    {"error": "Notification not found or already read"},
                    status=status.HTTP_404_NOT_FOUND
                )
                
        except Exception as e:
            logger.error(f"Failed to mark notification as read: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def mark_all_read(self, request):
        """
        Mark multiple notifications as read
        """
        serializer = NotificationMarkAsReadSerializer(data=request.data)
        if serializer.is_valid():
            try:
                data = serializer.validated_data
                
                if data.get('mark_all', False):
                    # Mark all notifications as read
                    count = notification_manager.mark_all_as_read(
                        user=request.user,
                        category=data.get('category')
                    )
                    return Response({
                        "success": True,
                        "message": f"Marked {count} notifications as read"
                    })
                else:
                    # Mark specific notifications as read
                    notification_ids = data.get('notification_ids', [])
                    if not notification_ids:
                        return Response(
                            {"error": "No notification IDs provided"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                    
                    count = 0
                    for notification_id in notification_ids:
                        if notification_manager.mark_as_read(notification_id, request.user):
                            count += 1
                    
                    return Response({
                        "success": True,
                        "message": f"Marked {count} notifications as read"
                    })
                    
            except Exception as e:
                logger.error(f"Failed to mark notifications as read: {str(e)}")
                return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['delete'])
    def bulk_delete(self, request):
        """
        Delete multiple notifications
        """
        try:
            notification_ids = request.data.get('notification_ids', [])
            if not notification_ids:
                return Response(
                    {"error": "No notification IDs provided"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Only allow deletion of user's own notifications
            deleted_count = Notification.objects.filter(
                id__in=notification_ids,
                recipient=request.user
            ).delete()[0]
            
            return Response({
                "success": True,
                "message": f"Deleted {deleted_count} notifications"
            })
            
        except Exception as e:
            logger.error(f"Failed to delete notifications: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """
        Get notification statistics for the user
        """
        try:
            user = request.user
            
            # Basic counts
            all_notifications = Notification.objects.filter(recipient=user)
            total_count = all_notifications.count()
            unread_count = all_notifications.filter(is_read=False).count()
            read_count = total_count - unread_count
            sent_count = all_notifications.filter(is_sent=True).count()
            failed_count = all_notifications.filter(delivery_status='failed').count()
            
            # Category breakdown
            category_breakdown = {}
            for category, _ in NotificationCategory.choices:
                count = all_notifications.filter(category=category).count()
                category_breakdown[category] = count
            
            # Type breakdown (top 10)
            type_breakdown = dict(
                all_notifications.values('notification_type')
                .annotate(count=Count('id'))
                .order_by('-count')[:10]
                .values_list('notification_type', 'count')
            )
            
            # Daily counts for last 7 days
            daily_counts = []
            for i in range(7):
                date = timezone.now().date() - timedelta(days=i)
                count = all_notifications.filter(created_at__date=date).count()
                daily_counts.append({
                    'date': date.isoformat(),
                    'count': count
                })
            
            stats_data = {
                'total_notifications': total_count,
                'unread_count': unread_count,
                'read_count': read_count,
                'sent_count': sent_count,
                'failed_count': failed_count,
                'category_breakdown': category_breakdown,
                'type_breakdown': type_breakdown,
                'daily_counts': daily_counts
            }
            
            serializer = NotificationStatsSerializer(data=stats_data)
            if serializer.is_valid():
                return Response(serializer.data)
            else:
                return Response(serializer.errors, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except Exception as e:
            logger.error(f"Failed to get notification stats: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserNotificationSettingsViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing user notification settings
    """
    serializer_class = UserNotificationSettingsSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """
        Return settings for the current user only
        """
        return UserNotificationSettings.objects.filter(user=self.request.user)
    
    def get_object(self):
        """
        Get or create notification settings for the current user
        """
        settings, created = UserNotificationSettings.objects.get_or_create(
            user=self.request.user
        )
        return settings
    
    def list(self, request, *args, **kwargs):
        """
        Get user's notification settings
        """
        settings = self.get_object()
        serializer = self.get_serializer(settings)
        return Response(serializer.data)
    
    def update(self, request, *args, **kwargs):
        """
        Update user's notification settings
        """
        settings = self.get_object()
        serializer = self.get_serializer(settings, data=request.data, partial=True)
        
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class OneSignalViewSet(viewsets.ViewSet):
    """
    ViewSet for OneSignal player ID management
    """
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['post'])
    def register_player(self, request):
        """
        Register a OneSignal player ID for the current user
        """
        serializer = OneSignalPlayerRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            try:
                success = onesignal_service.register_player(
                    user=request.user,
                    player_id=serializer.validated_data['player_id'],
                    platform=serializer.validated_data['platform']
                )
                
                if success:
                    return Response({
                        "success": True,
                        "message": "OneSignal player ID registered successfully"
                    })
                else:
                    return Response(
                        {"error": "Failed to register player ID"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                    
            except Exception as e:
                logger.error(f"Failed to register OneSignal player: {str(e)}")
                return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def unregister_player(self, request):
        """
        Unregister a OneSignal player ID for the current user
        """
        player_id = request.data.get('player_id')
        if not player_id:
            return Response(
                {"error": "Player ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            success = onesignal_service.unregister_player(
                user=request.user,
                player_id=player_id
            )
            
            if success:
                return Response({
                    "success": True,
                    "message": "OneSignal player ID unregistered successfully"
                })
            else:
                return Response(
                    {"error": "Failed to unregister player ID"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            logger.error(f"Failed to unregister OneSignal player: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def my_players(self, request):
        """
        Get current user's registered OneSignal player IDs
        """
        try:
            players = OneSignalPlayerID.objects.filter(user=request.user)
            serializer = OneSignalPlayerIDSerializer(players, many=True)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Failed to get OneSignal players: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)


# Admin-only views
class NotificationTemplateViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing notification templates (admin only)
    """
    queryset = NotificationTemplate.objects.all()
    serializer_class = NotificationTemplateSerializer
    permission_classes = [IsAdminUser]


@api_view(['POST'])
@permission_classes([IsAdminUser])
def send_bulk_notification(request):
    """
    Send bulk notifications (admin only)
    """
    serializer = BulkNotificationSerializer(data=request.data)
    if serializer.is_valid():
        try:
            data = serializer.validated_data
            
            # Get recipients
            recipients = User.objects.filter(id__in=data['recipient_ids'])
            if not recipients:
                return Response(
                    {"error": "No valid recipients found"},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Send bulk notification
            result = notification_manager.send_bulk_notification(
                recipients=list(recipients),
                notification_type=data['notification_type'],
                title=data.get('title'),
                message=data.get('message'),
                category=data.get('category'),
                priority=data.get('priority'),
                image_url=data.get('image_url'),
                action_data=data.get('action_data'),
                template_data=data.get('template_data'),
                filter_preferences=data.get('filter_preferences', True)
            )
            
            return Response(result)
            
        except Exception as e:
            logger.error(f"Failed to send bulk notification: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_test_notification(request):
    """
    Send a test notification to the current user
    """
    serializer = TestNotificationSerializer(data=request.data)
    if serializer.is_valid():
        try:
            data = serializer.validated_data
            
            notification = notification_manager.create_notification(
                recipient=request.user,
                notification_type=data['notification_type'],
                title=data['title'],
                message=data['message'],
                category=data['category'],
                priority=data['priority'],
                image_url=data.get('image_url'),
                action_data=data.get('action_data'),
                send_immediately=True
            )
            
            if notification:
                response_serializer = NotificationSerializer(notification)
                return Response({
                    "success": True,
                    "message": "Test notification sent successfully",
                    "notification": response_serializer.data
                })
            else:
                return Response(
                    {"error": "Failed to send test notification"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            logger.error(f"Failed to send test notification: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def process_batched_notifications(request):
    """
    Manually trigger processing of batched notifications (admin only)
    """
    try:
        count = notification_manager.process_batched_notifications()
        return Response({
            "success": True,
            "message": f"Processed {count} batched notifications"
        })
        
    except Exception as e:
        logger.error(f"Failed to process batched notifications: {str(e)}")
        return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def cleanup_old_notifications(request):
    """
    Clean up old notifications (admin only)
    """
    try:
        days = int(request.data.get('days', 30))
        count = notification_manager.cleanup_old_notifications(days=days)
        
        return Response({
            "success": True,
            "message": f"Cleaned up {count} old notifications"
        })
        
    except Exception as e:
        logger.error(f"Failed to cleanup notifications: {str(e)}")
        return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
