#!/usr/bin/env python
"""
Django management command to award OG Listener skin to early users
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime
import pytz
from users.models import User
from pins.models import PinSkin, UserSkin
from notifications.utils import notify_skin_unlocked


class Command(BaseCommand):
    help = 'Award OG Listener skin to early BOPMaps users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cutoff-date',
            type=str,
            default='2024-12-31',
            help='Users who joined before this date get the OG skin (YYYY-MM-DD)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would happen without actually awarding',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        cutoff_str = options['cutoff_date']
        
        if dry_run:
            self.stdout.write('🔍 DRY RUN MODE - No changes will be made')
        
        # Parse cutoff date
        try:
            cutoff_date = datetime.strptime(cutoff_str, '%Y-%m-%d')
            cutoff_date = pytz.UTC.localize(cutoff_date)
        except ValueError:
            self.stdout.write(self.style.ERROR('❌ Invalid date format. Use YYYY-MM-DD'))
            return
        
        self.stdout.write(f'🎵 Awarding OG Listener skin to users who joined before {cutoff_str}...')
        
        # Get the OG Listener skin
        og_skin = PinSkin.objects.filter(
            slug='og-listener'
        ).first()
        
        if not og_skin:
            self.stdout.write(self.style.ERROR('❌ OG Listener skin not found!'))
            self.stdout.write('   Run generate_production_pins first')
            return
        
        # Find eligible users
        eligible_users = User.objects.filter(
            date_joined__lt=cutoff_date
        )
        
        # Exclude users who already have it
        users_with_skin = UserSkin.objects.filter(
            skin=og_skin
        ).values_list('user_id', flat=True)
        
        eligible_users = eligible_users.exclude(id__in=users_with_skin)
        
        self.stdout.write(f'📊 Found {eligible_users.count()} eligible OG users')
        
        awarded_count = 0
        
        for user in eligible_users:
            if dry_run:
                self.stdout.write(
                    f'   🎯 Would award to: {user.username} '
                    f'(joined: {user.date_joined.strftime("%Y-%m-%d")})'
                )
            else:
                try:
                    # Create UserSkin entry
                    UserSkin.objects.create(
                        user=user,
                        skin=og_skin
                    )
                    
                    # Send notification
                    notify_skin_unlocked(
                        user=user,
                        skin_name=og_skin.name,
                        unlock_reason="Thank you for being an early supporter of BOPMaps! 🎵"
                    )
                    
                    awarded_count += 1
                    self.stdout.write(
                        f'   ✅ Awarded to: {user.username} '
                        f'(joined: {user.date_joined.strftime("%Y-%m-%d")})'
                    )
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(
                            f'   ❌ Failed to award to {user.username}: {str(e)}'
                        )
                    )
        
        if not dry_run:
            self.stdout.write(f'\n🎉 Successfully awarded OG Listener to {awarded_count} users!')
        else:
            self.stdout.write(f'\n🔍 Would have awarded OG Listener to {eligible_users.count()} users') 