#!/usr/bin/env python
"""
Django management command to create and distribute the Default pin skin

This command does the following:
1. Uploads the Default.png image to Backblaze B2 (if not already uploaded)
2. Creates or updates the Default pin skin in the database
3. Ensures every user has the Default pin skin

Usage:
    python manage.py create_default_pin_skin [options]

Options:
    --dry-run       Show what would be created without making changes
    --skip-upload   Skip B2 upload (useful for local testing)

Examples:
    # Normal run (upload to B2 and distribute)
    python manage.py create_default_pin_skin

    # Dry run to see what would happen
    python manage.py create_default_pin_skin --dry-run

    # Local testing without B2 upload
    python manage.py create_default_pin_skin --skip-upload
"""

from django.core.management.base import BaseCommand
from django.conf import settings
from django.utils.text import slugify
from django.utils import timezone
from django.db import transaction
from pins.models import PinSkin, UserSkin
from users.models import User
import boto3
from botocore.exceptions import ClientError
from pathlib import Path


class Command(BaseCommand):
    help = 'Create and distribute the Default pin skin to all users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating',
        )
        parser.add_argument(
            '--skip-upload',
            action='store_true',
            help='Skip B2 upload (for testing locally)',
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        self.skip_upload = options['skip_upload']
        
        if self.dry_run:
            self.stdout.write('🔍 DRY RUN MODE - No changes will be made')
        
        self.stdout.write('🎨 Creating Default pin skin...')
        
        # Initialize B2 client if not skipping upload
        if not self.skip_upload:
            self.b2_client = self.setup_b2_client()
            if not self.b2_client:
                return
        
        # Process the Default pin
        default_pin_data = {
            'name': 'Default',
            'slug': 'default-pin',
            'description': 'The standard pin for all BOPMaps users',
            'image_file': 'Default.png',
            'metadata': {
                'unlock_type': 'DEFAULT',
                'category': 'base',
                'theme_color': '#4A90E2',
                'color_theme': 'blue',
                'rarity': 'common'
            }
        }
        
        # Upload image and create/get pin skin
        pin_skin = self.process_default_pin(default_pin_data)
        
        if pin_skin and not self.dry_run:
            # Distribute to all users
            self.distribute_default_pin(pin_skin)
        
        self.stdout.write('\n✅ Default pin skin process completed!')

    def setup_b2_client(self):
        """Setup B2 client for image uploads"""
        try:
            # Get B2 credentials from settings
            b2_key_id = getattr(settings, 'B2_APPLICATION_KEY_ID', '')
            b2_key = getattr(settings, 'B2_APPLICATION_KEY', '')
            b2_bucket = getattr(settings, 'B2_BUCKET_NAME', '')
            b2_region = getattr(settings, 'B2_REGION', 'us-east-005')
            b2_endpoint = getattr(settings, 'B2_ENDPOINT_URL', '')
            if not b2_endpoint:
                b2_endpoint = f'https://s3.{b2_region}.backblazeb2.com'
            
            # Check for B2 credentials
            if not all([b2_key_id, b2_key, b2_bucket]):
                self.stdout.write(self.style.ERROR('❌ B2 credentials not configured in settings'))
                self.stdout.write('   Please set: B2_APPLICATION_KEY_ID, B2_APPLICATION_KEY, B2_BUCKET_NAME')
                return None
            
            # Create B2 client
            client = boto3.client(
                's3',
                endpoint_url=b2_endpoint,
                aws_access_key_id=b2_key_id,
                aws_secret_access_key=b2_key,
                region_name=b2_region,
            )
            
            self.stdout.write('✅ B2 client initialized successfully')
            return client
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Failed to setup B2 client: {str(e)}'))
            return None

    def check_b2_object_exists(self, bucket, key):
        """Check if object already exists in B2 bucket"""
        try:
            self.b2_client.head_object(Bucket=bucket, Key=key)
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            else:
                # Some other error occurred
                raise e

    def upload_image_to_b2(self, image_filename):
        """Upload image to B2 bucket (skip if already exists)"""
        try:
            # Construct paths
            local_path = Path('pin_images') / image_filename
            if not local_path.exists():
                self.stdout.write(self.style.WARNING(f'      ⚠️  Image file not found: {local_path}'))
                return None
            
            # Generate B2 key
            b2_key = f'pin_skins/{image_filename.replace(" ", "_").lower()}'
            
            # Get bucket name
            b2_bucket = getattr(settings, 'B2_BUCKET_NAME', '')
            b2_region = getattr(settings, 'B2_REGION', 'us-east-005')
            
            # Check if image already exists in B2
            if self.check_b2_object_exists(b2_bucket, b2_key):
                self.stdout.write(f'      ✓ Already exists in B2: {b2_key}')
                # Construct public URL for existing image
                image_url = f'https://{b2_bucket}.s3.{b2_region}.backblazeb2.com/{b2_key}'
                return image_url
            
            # Upload to B2
            with open(local_path, 'rb') as f:
                self.b2_client.upload_fileobj(
                    f,
                    b2_bucket,
                    b2_key,
                    ExtraArgs={
                        'ContentType': 'image/png',
                        'CacheControl': 'max-age=31536000'  # 1 year cache
                    }
                )
            
            # Construct public URL
            image_url = f'https://{b2_bucket}.s3.{b2_region}.backblazeb2.com/{b2_key}'
            self.stdout.write(f'      ✓ Uploaded to B2: {b2_key}')
            
            return image_url
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'      ❌ B2 upload failed: {str(e)}'))
            return None

    def process_default_pin(self, pin_data):
        """Process the Default pin - create/update and upload image"""
        try:
            # Check if pin exists by slug
            existing_pin = PinSkin.objects.filter(slug=pin_data['slug']).first()
            
            if self.dry_run:
                if existing_pin:
                    self.stdout.write(f'   🔄 Would update: {pin_data["name"]} (Default Pin)')
                else:
                    self.stdout.write(f'   ✨ Would create: {pin_data["name"]} (Default Pin)')
                
                # Check if image exists
                image_path = Path('pin_images') / pin_data['image_file']
                if image_path.exists():
                    self.stdout.write(f'      ✓ Image found: {pin_data["image_file"]}')
                else:
                    self.stdout.write(self.style.WARNING(f'      ⚠️  Image not found: {image_path}'))
                
                return existing_pin
            
            # Upload image to B2
            image_url = None
            if not self.skip_upload:
                image_url = self.upload_image_to_b2(pin_data['image_file'])
                if not image_url:
                    self.stdout.write(self.style.WARNING(f'   ⚠️  Failed to upload image for {pin_data["name"]}'))
                    return None
            else:
                # Use placeholder URL for local testing
                image_url = f'https://placeholder.com/pin_skins/{pin_data["slug"]}.png'
            
            if existing_pin:
                # Update existing pin
                existing_pin.name = pin_data['name']
                existing_pin.description = pin_data['description']
                existing_pin.image = image_url
                existing_pin.metadata = pin_data['metadata']
                existing_pin.save()
                self.stdout.write(f'   🔄 Updated: {pin_data["name"]} (Default Pin)')
                return existing_pin
            else:
                # Create new default pin
                default_pin = PinSkin.objects.create(
                    name=pin_data['name'],
                    slug=pin_data['slug'],
                    description=pin_data['description'],
                    image=image_url,
                    skin_type=PinSkin.HOUSE,
                    is_premium=False,
                    metadata=pin_data['metadata']
                )
                self.stdout.write(f'   ✨ Created: {pin_data["name"]} (Default Pin)')
                return default_pin
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ❌ Error processing {pin_data["name"]}: {str(e)}'))
            return None

    def distribute_default_pin(self, default_pin):
        """Ensure every user has the default pin skin"""
        self.stdout.write('\n🔓 Distributing Default pin to all users...')
        
        # Get all users who don't have the default pin
        users_without_default = User.objects.exclude(
            unlocked_skins__skin=default_pin
        )
        
        # Bulk create UserSkin entries
        user_skins_to_create = [
            UserSkin(user=user, skin=default_pin) 
            for user in users_without_default
        ]
        
        # Use transaction to improve performance
        with transaction.atomic():
            UserSkin.objects.bulk_create(user_skins_to_create, ignore_conflicts=True)
        
        self.stdout.write(f'   🎉 Distributed Default pin to {len(user_skins_to_create)} users') 