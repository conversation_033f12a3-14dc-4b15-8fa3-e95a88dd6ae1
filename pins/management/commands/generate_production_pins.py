#!/usr/bin/env python
"""
Django management command to generate production house pins for BOPMaps
"""

from django.core.management.base import BaseCommand
from django.conf import settings
from django.utils import timezone
from pins.models import PinSkin, UserSkin
from users.models import User
from rankings.models import UserRanking, RANK_LEVELS
import boto3
from botocore.exceptions import ClientError
import os
import json
from pathlib import Path
from datetime import timed<PERSON>ta
from django.db import models


class Command(BaseCommand):
    help = 'Generate production house pins and upload images to B2'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating',
        )
        parser.add_argument(
            '--skip-upload',
            action='store_true',
            help='Skip B2 upload (for testing locally)',
        )
        parser.add_argument(
            '--cleanup-duplicates',
            action='store_true',
            help='Clean up duplicate skins before creating new ones',
        )
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear all existing pin skins before creating new ones',
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        self.skip_upload = options['skip_upload']
        self.cleanup_duplicates = options['cleanup_duplicates']
        
        if self.dry_run:
            self.stdout.write('🔍 DRY RUN MODE - No changes will be made')
        
        self.stdout.write('🎨 Generating production house pins for BOPMaps...')
        
        # Clear existing pin skins if requested
        if options['clear_existing'] and not self.dry_run:
            self.stdout.write('🧹 Clearing existing pin skins...')
            self.clear_existing_skins()
        
        # Clean up duplicates if requested
        if self.cleanup_duplicates and not self.dry_run:
            self.stdout.write('🧹 Cleaning up duplicate pin skins...')
            self.cleanup_duplicate_skins()
        
        # Initialize B2 client if not skipping upload
        if not self.skip_upload:
            self.b2_client = self.setup_b2_client()
            if not self.b2_client:
                return
        
        # Define all 25 production pins
        pins_data = self.get_pins_data()
        
        # Process each pin
        created_count = 0
        updated_count = 0
        
        for pin_data in pins_data:
            result = self.process_pin(pin_data)
            if result == 'created':
                created_count += 1
            elif result == 'updated':
                updated_count += 1
        
        # Unlock rank-based skins for users who have already reached those ranks
        if not self.dry_run:
            self.unlock_rank_skins_for_existing_users()
        
        self.stdout.write('\n✅ Production pin generation completed!')
        self.stdout.write(f'   📦 Pins created: {created_count}')
        self.stdout.write(f'   🔄 Pins updated: {updated_count}')
        
        if not self.dry_run:
            self.stdout.write('\n💡 Pin skins are now ready for production use!')

    def setup_b2_client(self):
        """Setup B2 client for image uploads"""
        try:
            # Get B2 credentials from settings
            b2_key_id = getattr(settings, 'B2_APPLICATION_KEY_ID', '')
            b2_key = getattr(settings, 'B2_APPLICATION_KEY', '')
            b2_bucket = getattr(settings, 'B2_BUCKET_NAME', '')
            b2_region = getattr(settings, 'B2_REGION', 'us-east-005')
            b2_endpoint = getattr(settings, 'B2_ENDPOINT_URL', '')
            if not b2_endpoint:
                b2_endpoint = f'https://s3.{b2_region}.backblazeb2.com'
            
            # Check for B2 credentials
            if not all([b2_key_id, b2_key, b2_bucket]):
                self.stdout.write(self.style.ERROR('❌ B2 credentials not configured in settings'))
                self.stdout.write('   Please set: B2_APPLICATION_KEY_ID, B2_APPLICATION_KEY, B2_BUCKET_NAME')
                return None
            
            # Create B2 client
            client = boto3.client(
                's3',
                endpoint_url=b2_endpoint,
                aws_access_key_id=b2_key_id,
                aws_secret_access_key=b2_key,
                region_name=b2_region,
            )
            
            self.stdout.write('✅ B2 client initialized successfully')
            return client
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Failed to setup B2 client: {str(e)}'))
            return None

    def get_pins_data(self):
        """Define all 25 production pins with their metadata"""
        return [
            # Mood/Activity Pins (1-15)
            {
                'name': 'Nostalgic Connection',
                'slug': 'nostalgic-connection',
                'description': 'For those special songs that transport you back in time',
                'image_file': 'Nostalgic Connection.png',
                'achievement_id': 'loyal_listener',
                'metadata': {
                    'unlock_type': 'ACHIEVEMENT',
                    'achievement': 'loyal_listener',
                    'category': 'mood',
                    'theme_color': '#FF6B9D',
                    'color_theme': 'pink',
                    'rarity': 'common'
                }
            },
            {
                'name': 'Euphoric Moment',
                'slug': 'euphoric-moment',
                'description': 'Capture the peak moments of musical bliss',
                'image_file': 'Euphoric Moment.png',
                'achievement_id': 'popular_pin',
                'metadata': {
                    'unlock_type': 'ACHIEVEMENT',
                    'achievement': 'popular_pin',
                    'category': 'mood',
                    'theme_color': '#C44569',
                    'color_theme': 'purple',
                    'rarity': 'uncommon'
                }
            },
            {
                'name': 'Heartbreak Anthem',
                'slug': 'heartbreak-anthem',
                'description': 'For the songs that helped you through tough times',
                'image_file': 'Heartbreak Anthem.png',
                'achievement_id': 'comments_25',
                'metadata': {
                    'unlock_type': 'ACHIEVEMENT',
                    'achievement': 'comments_25',
                    'category': 'mood',
                    'theme_color': '#7B68EE',
                    'color_theme': 'blue',
                    'rarity': 'uncommon'
                }
            },
            {
                'name': 'Party Vibes',
                'slug': 'party-vibes',
                'description': 'Mark the spots where the party never stops',
                'image_file': 'Party Vibes.png',
                'achievement_id': 'react_streak_7',
                'metadata': {
                    'unlock_type': 'ACHIEVEMENT',
                    'achievement': 'react_streak_7',
                    'category': 'mood',
                    'theme_color': '#FF1744',
                    'color_theme': 'red',
                    'rarity': 'rare'
                }
            },
            {
                'name': 'Rainy Day Listening',
                'slug': 'rainy-day-listening',
                'description': 'Perfect for those cozy, introspective moments',
                'image_file': 'Rainy Day Listening.png',
                'achievement_id': 'after_hours',
                'metadata': {
                    'unlock_type': 'ACHIEVEMENT',
                    'achievement': 'after_hours',
                    'category': 'mood',
                    'theme_color': '#546E7A',
                    'color_theme': 'grey',
                    'rarity': 'common'
                }
            },
            {
                'name': 'Chill Lo-Fi Beat',
                'slug': 'chill-lofi-beat',
                'description': 'For laid-back study sessions and relaxation',
                'image_file': 'Chill Lo-Fi Beat.png',
                'achievement_id': 'genre_devotee_I',
                'metadata': {
                    'unlock_type': 'ACHIEVEMENT',
                    'achievement': 'genre_devotee_I',
                    'category': 'genre',
                    'theme_color': '#A1887F',
                    'color_theme': 'brown',
                    'rarity': 'uncommon'
                }
            },
            {
                'name': 'Hyped EDM Energy',
                'slug': 'hyped-edm-energy',
                'description': 'Drop the bass and mark your festival moments',
                'image_file': 'Hyped EDM Energy.png',
                'achievement_id': 'electronic_explorer',
                'metadata': {
                    'unlock_type': 'ACHIEVEMENT',
                    'achievement': 'electronic_explorer',
                    'category': 'genre',
                    'theme_color': '#00E676',
                    'color_theme': 'green',
                    'rarity': 'rare'
                }
            },
            {
                'name': 'Jazzy Groove',
                'slug': 'jazzy-groove',
                'description': 'Smooth jazz vibes for sophisticated spots',
                'image_file': 'Jazzy Groove.png',
                'achievement_id': 'album_run',
                'metadata': {
                    'unlock_type': 'ACHIEVEMENT',
                    'achievement': 'album_run',
                    'category': 'genre',
                    'theme_color': '#FFB300',
                    'color_theme': 'gold',
                    'rarity': 'rare'
                }
            },
            {
                'name': 'New City Anthem',
                'slug': 'new-city-anthem',
                'description': 'Mark your first musical discovery in a new place',
                'image_file': 'New City Anthem.png',
                'achievement_id': 'city_sampler',
                'metadata': {
                    'unlock_type': 'ACHIEVEMENT',
                    'achievement': 'city_sampler',
                    'category': 'location',
                    'theme_color': '#00ACC1',
                    'color_theme': 'cyan',
                    'rarity': 'rare'
                }
            },
            {
                'name': 'Hidden Gem',
                'slug': 'hidden-gem',
                'description': 'For those underground tracks nobody else knows',
                'image_file': 'Hidden Gem.png',
                'achievement_id': 'artist_pioneer',
                'metadata': {
                    'unlock_type': 'ACHIEVEMENT',
                    'achievement': 'artist_pioneer',
                    'category': 'discovery',
                    'theme_color': '#8E24AA',
                    'color_theme': 'purple',
                    'rarity': 'epic'
                }
            },
            {
                'name': 'Road Trip Anthem',
                'slug': 'road-trip-anthem',
                'description': 'Songs that made the journey unforgettable',
                'image_file': 'Road Trip Anthem.png',
                'achievement_id': 'cross_town_sound',
                'metadata': {
                    'unlock_type': 'ACHIEVEMENT',
                    'achievement': 'cross_town_sound',
                    'category': 'location',
                    'theme_color': '#F4511E',
                    'color_theme': 'orange',
                    'rarity': 'epic'
                }
            },
            {
                'name': 'Secret Pin',
                'slug': 'secret-pin',
                'description': 'Shhh... you found something special',
                'image_file': 'Secret Pin.png',
                'achievement_id': 'pin_pioneer',
                'metadata': {
                    'unlock_type': 'SECRET',
                    'achievement': 'pin_pioneer',
                    'category': 'special',
                    'theme_color': '#424242',
                    'color_theme': 'black',
                    'rarity': 'legendary'
                }
            },
            {
                'name': 'Shared Memory',
                'slug': 'shared-memory',
                'description': 'Music moments better shared with friends',
                'image_file': 'Shared Memory.png',
                'achievement_id': 'local_buzz',
                'metadata': {
                    'unlock_type': 'ACHIEVEMENT',
                    'achievement': 'local_buzz',
                    'category': 'social',
                    'theme_color': '#EC407A',
                    'color_theme': 'pink',
                    'rarity': 'epic'
                }
            },
            {
                'name': 'Surprise Drop',
                'slug': 'surprise-drop',
                'description': 'For those unexpected musical discoveries',
                'image_file': 'Surprise Drop.png',
                'metadata': {
                    'unlock_type': 'BOP_DROP',
                    'category': 'special',
                    'theme_color': '#FDD835',
                    'color_theme': 'yellow',
                    'rarity': 'uncommon'
                }
            },
            {
                'name': 'First Drop',
                'slug': 'first-drop',
                'description': 'Commemorating your very first pin on BOPMaps',
                'image_file': 'First Drop.png',
                'achievement_id': 'first_drop',
                'metadata': {
                    'unlock_type': 'ACHIEVEMENT',
                    'achievement': 'first_drop',
                    'category': 'milestone',
                    'theme_color': '#26C6DA',
                    'color_theme': 'cyan',
                    'rarity': 'common'
                }
            },
           
            # Special Event Pins (16-18) - Limited Time
            {
                'name': 'OG Listener',
                'slug': 'og-listener',
                'description': 'For the early adopters who believed in BOPMaps',
                'image_file': 'OG Listener.png',
                'expiry_days': 90,  # Available for 90 days from creation
                'metadata': {
                    'unlock_type': 'LIMITED_TIME',
                    'category': 'special',
                    'theme_color': '#FFD700',
                    'color_theme': 'gold',
                    'rarity': 'rare'
                }
            },
            {
                'name': 'Ultra Rare',
                'slug': 'ultra-rare',
                'description': 'The rarest of pins for the most dedicated users',
                'image_file': 'Ultra Rare.png',
                'expiry_days': 30,  # Available for 30 days from creation
                'metadata': {
                    'unlock_type': 'SPECIAL_EVENT',
                    'category': 'special',
                    'theme_color': '#E91E63',
                    'color_theme': 'rainbow',
                    'rarity': 'legendary'
                }
            },
           
            # Rank-Based Pins (19-25)
            {
                'name': 'Basement Bopper',
                'slug': 'basement-bopper-rank-1',
                'description': 'Starting your musical journey from the underground',
                'image_file': 'Basement Bopper (Rank 1).png',
                'metadata': {
                    'unlock_type': 'RANK',
                    'required_rank': 1,
                    'rank_name': 'Basement Bopper',
                    'category': 'rank',
                    'theme_color': '#1A1A1A',
                    'color_theme': 'black',
                    'rarity': 'common'
                }
            },
            {
                'name': 'Selector',
                'slug': 'selector-rank-5',
                'description': 'You\'ve proven your taste in music selection',
                'image_file': 'Selector (Rank 5).png',
                'metadata': {
                    'unlock_type': 'RANK',
                    'required_rank': 5,
                    'rank_name': 'Selector',
                    'category': 'rank',
                    'theme_color': '#4A90E2',
                    'color_theme': 'blue',
                    'rarity': 'uncommon'
                }
            },
            {
                'name': 'Tastemaker',
                'slug': 'tastemaker-rank-10',
                'description': 'Your musical taste influences others',
                'image_file': 'Tastemaker (Rank 10).png',
                'metadata': {
                    'unlock_type': 'RANK',
                    'required_rank': 10,
                    'rank_name': 'Tastemaker',
                    'category': 'rank',
                    'theme_color': '#9B51E0',
                    'color_theme': 'purple',
                    'rarity': 'rare'
                }
            },
            {
                'name': 'Trendsetter',
                'slug': 'trendsetter-rank-15',
                'description': 'You set the trends others follow',
                'image_file': 'Trendsetter (Rank 15).png',
                'metadata': {
                    'unlock_type': 'RANK',
                    'required_rank': 15,
                    'rank_name': 'Trendsetter',
                    'category': 'rank',
                    'theme_color': '#F2994A',
                    'color_theme': 'orange',
                    'rarity': 'rare'
                }
            },
            {
                'name': 'Icon',
                'slug': 'icon-rank-20',
                'description': 'A true icon in the BOPMaps community',
                'image_file': 'Icon (Rank 20).png',
                'metadata': {
                    'unlock_type': 'RANK',
                    'required_rank': 20,
                    'rank_name': 'Icon',
                    'category': 'rank',
                    'theme_color': '#EB5757',
                    'color_theme': 'red',
                    'rarity': 'epic'
                }
            },
            {
                'name': 'Architect',
                'slug': 'architect-rank-25',
                'description': 'Building the musical landscape of your city',
                'image_file': 'Architect (Rank 25).png',
                'metadata': {
                    'unlock_type': 'RANK',
                    'required_rank': 25,
                    'rank_name': 'Architect',
                    'category': 'rank',
                    'theme_color': '#27AE60',
                    'color_theme': 'green',
                    'rarity': 'epic'
                }
            },
            {
                'name': 'Legend',
                'slug': 'legend-rank-30',
                'description': 'A living legend in the world of music discovery',
                'image_file': 'Legend (Rank 30).png',
                'metadata': {
                    'unlock_type': 'RANK',
                    'required_rank': 30,
                    'rank_name': 'Legend',
                    'category': 'rank',
                    'theme_color': '#F2C94C',
                    'color_theme': 'gold',
                    'rarity': 'legendary'
                }
            }
        ]

    def process_pin(self, pin_data):
        """Process a single pin - create/update and upload image"""
        try:
            # Check if pin exists by slug (primary check)
            existing_pin = PinSkin.objects.filter(slug=pin_data['slug']).first()
            
            # If not found by slug, check by name (secondary check for safety)
            if not existing_pin:
                existing_pin_by_name = PinSkin.objects.filter(name__iexact=pin_data['name']).first()
                if existing_pin_by_name:
                    self.stdout.write(
                        self.style.WARNING(
                            f'   ⚠️  Found existing skin with same name but different slug: {existing_pin_by_name.name} '
                            f'(slug: {existing_pin_by_name.slug}). Using existing skin.'
                        )
                    )
                    existing_pin = existing_pin_by_name
            
            if self.dry_run:
                rarity = pin_data['metadata'].get('rarity', 'common')
                color_theme = pin_data['metadata'].get('color_theme', 'default')
                
                # Check for achievement or expiry
                achievement_info = ""
                if 'achievement_id' in pin_data:
                    achievement_info = f" (Achievement: {pin_data['achievement_id']})"
                if 'expiry_days' in pin_data:
                    achievement_info += f" (Limited: {pin_data['expiry_days']} days)"
                
                if existing_pin:
                    self.stdout.write(f'   🔄 Would update: {pin_data["name"]} ({rarity}, {color_theme}){achievement_info}')
                else:
                    self.stdout.write(f'   ✨ Would create: {pin_data["name"]} ({rarity}, {color_theme}){achievement_info}')
                
                # Check if image exists
                image_path = Path('pin_images') / pin_data['image_file']
                if image_path.exists():
                    self.stdout.write(f'      ✓ Image found: {pin_data["image_file"]}')
                else:
                    self.stdout.write(self.style.WARNING(f'      ⚠️  Image not found: {image_path}'))
                
                return 'dry_run'
            
            # Upload image to B2
            image_url = None
            if not self.skip_upload:
                image_url = self.upload_image_to_b2(pin_data['image_file'])
                if not image_url:
                    self.stdout.write(self.style.WARNING(f'   ⚠️  Failed to upload image for {pin_data["name"]}'))
                    return None
            else:
                # Use placeholder URL for local testing
                image_url = f'https://placeholder.com/pin_skins/{pin_data["slug"]}.png'
            
            # Find achievement if specified
            achievement = None
            if 'achievement_id' in pin_data:
                from gamification.models import Achievement
                achievement = Achievement.objects.filter(
                    challenge_id=pin_data['achievement_id']
                ).first()
                if not achievement:
                    self.stdout.write(
                        self.style.WARNING(f'   ⚠️  Achievement not found: {pin_data["achievement_id"]}')
                    )
            
            # Calculate expiry date if specified
            expiry_date = None
            if 'expiry_days' in pin_data:
                expiry_date = timezone.now() + timedelta(days=pin_data['expiry_days'])
            
            # Create or update pin skin
            rarity = pin_data['metadata'].get('rarity', 'common')
            color_theme = pin_data['metadata'].get('color_theme', 'default')
            
            # Build info string for logging
            info_parts = [f"({rarity}, {color_theme})"]
            if achievement:
                info_parts.append(f"Achievement: {achievement.name}")
            if expiry_date:
                info_parts.append(f"Expires: {expiry_date.strftime('%Y-%m-%d')}")
            info_string = " " + " | ".join(info_parts)
            
            if existing_pin:
                # Update existing - but only if slug matches or we're updating name match
                if existing_pin.slug != pin_data['slug']:
                    # Update slug to match our intended slug
                    existing_pin.slug = pin_data['slug']
                
                existing_pin.name = pin_data['name']
                existing_pin.description = pin_data['description']
                existing_pin.image = image_url
                existing_pin.metadata = pin_data['metadata']
                existing_pin.achievement = achievement
                existing_pin.expiry_date = expiry_date
                existing_pin.save()
                self.stdout.write(f'   🔄 Updated: {pin_data["name"]}{info_string}')
                return 'updated'
            else:
                # Create new - but double-check for duplicates one more time
                final_check = PinSkin.objects.filter(
                    models.Q(slug=pin_data['slug']) | models.Q(name__iexact=pin_data['name'])
                ).first()
                
                if final_check:
                    self.stdout.write(
                        self.style.WARNING(
                            f'   ⚠️  Duplicate detected during creation, updating existing: {final_check.name}'
                        )
                    )
                    # Update the found duplicate instead
                    final_check.slug = pin_data['slug']
                    final_check.name = pin_data['name']
                    final_check.description = pin_data['description']
                    final_check.image = image_url
                    final_check.metadata = pin_data['metadata']
                    final_check.achievement = achievement
                    final_check.expiry_date = expiry_date
                    final_check.save()
                    self.stdout.write(f'   🔄 Updated (duplicate): {pin_data["name"]}{info_string}')
                    return 'updated'
                
                PinSkin.objects.create(
                    name=pin_data['name'],
                    slug=pin_data['slug'],
                    description=pin_data['description'],
                    image=image_url,
                    skin_type=PinSkin.HOUSE,
                    is_premium=False,
                    metadata=pin_data['metadata'],
                    achievement=achievement,
                    expiry_date=expiry_date
                )
                self.stdout.write(f'   ✨ Created: {pin_data["name"]}{info_string}')
                return 'created'
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ❌ Error processing {pin_data["name"]}: {str(e)}'))
            return None

    def check_b2_object_exists(self, bucket, key):
        """Check if object already exists in B2 bucket"""
        try:
            self.b2_client.head_object(Bucket=bucket, Key=key)
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            else:
                # Some other error occurred
                raise e

    def upload_image_to_b2(self, image_filename):
        """Upload image to B2 bucket (skip if already exists)"""
        try:
            # Construct paths
            local_path = Path('pin_images') / image_filename
            if not local_path.exists():
                self.stdout.write(self.style.WARNING(f'      ⚠️  Image file not found: {local_path}'))
                return None
            
            # Generate B2 key
            b2_key = f'pin_skins/{image_filename.replace(" ", "_").lower()}'
            
            # Get bucket name
            b2_bucket = getattr(settings, 'B2_BUCKET_NAME', '')
            b2_region = getattr(settings, 'B2_REGION', 'us-east-005')
            
            # Check if image already exists in B2
            if self.check_b2_object_exists(b2_bucket, b2_key):
                self.stdout.write(f'      ✓ Already exists in B2: {b2_key}')
                # Construct public URL for existing image
                image_url = f'https://{b2_bucket}.s3.{b2_region}.backblazeb2.com/{b2_key}'
                return image_url
            
            # Upload to B2
            with open(local_path, 'rb') as f:
                self.b2_client.upload_fileobj(
                    f,
                    b2_bucket,
                    b2_key,
                    ExtraArgs={
                        'ContentType': 'image/png',
                        'CacheControl': 'max-age=31536000'  # 1 year cache
                    }
                )
            
            # Construct public URL
            image_url = f'https://{b2_bucket}.s3.{b2_region}.backblazeb2.com/{b2_key}'
            self.stdout.write(f'      ✓ Uploaded to B2: {b2_key}')
            
            return image_url
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'      ❌ B2 upload failed: {str(e)}'))
            return None

    def unlock_rank_skins_for_existing_users(self):
        """Unlock rank-based skins for users who have already reached those ranks"""
        self.stdout.write('\n🔓 Unlocking rank skins for existing users...')
        
        # Get all rank-based skins
        rank_skins = PinSkin.objects.filter(
            metadata__unlock_type='RANK'
        )
        
        unlock_count = 0
        
        for skin in rank_skins:
            required_rank = skin.metadata.get('required_rank', 1)
            
            # Find all users at or above this rank
            eligible_rankings = UserRanking.objects.filter(
                level__gte=required_rank
            ).select_related('user')
            
            for ranking in eligible_rankings:
                # Check if user already has this skin
                if not UserSkin.objects.filter(user=ranking.user, skin=skin).exists():
                    UserSkin.objects.create(
                        user=ranking.user,
                        skin=skin
                    )
                    unlock_count += 1
            
            self.stdout.write(f'   ✓ Unlocked {skin.name} for {eligible_rankings.count()} users')
        
        self.stdout.write(f'   🎉 Total unlocks: {unlock_count}') 

    def cleanup_duplicate_skins(self):
        """Clean up duplicate pin skins based on name or slug"""
        try:
            from django.db.models import Count
            
            # Find duplicate slugs
            duplicate_slugs = PinSkin.objects.values('slug').annotate(
                count=Count('id')
            ).filter(count__gt=1)
            
            duplicates_removed = 0
            
            for duplicate in duplicate_slugs:
                slug = duplicate['slug']
                skins = PinSkin.objects.filter(slug=slug).order_by('created_at')
                
                # Keep the first (oldest) one, delete the rest
                skins_to_delete = skins[1:]
                for skin in skins_to_delete:
                    self.stdout.write(f'   ❌ Removing duplicate skin: {skin.name} (ID: {skin.id})')
                    skin.delete()
                    duplicates_removed += 1
            
            # Find duplicate names (case-insensitive)
            duplicate_names = PinSkin.objects.values('name').annotate(
                count=Count('id')
            ).filter(count__gt=1)
            
            for duplicate in duplicate_names:
                name = duplicate['name']
                skins = PinSkin.objects.filter(name__iexact=name).order_by('created_at')
                
                if skins.count() > 1:
                    # Keep the first (oldest) one, delete the rest
                    skins_to_delete = skins[1:]
                    for skin in skins_to_delete:
                        self.stdout.write(f'   ❌ Removing duplicate skin by name: {skin.name} (ID: {skin.id})')
                        skin.delete()
                        duplicates_removed += 1
            
            if duplicates_removed > 0:
                self.stdout.write(f'   ✓ Removed {duplicates_removed} duplicate skins')
            else:
                self.stdout.write('   ✓ No duplicate skins found')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ❌ Error cleaning up duplicates: {str(e)}')) 

    def clear_existing_skins(self):
        """Clear all existing pin skins"""
        # First, delete pins that reference these skins to avoid foreign key constraints
        # Since skin_id has NOT NULL constraint, we need to delete pins rather than update
        from pins.models import Pin
        pin_count = Pin.objects.exclude(skin__isnull=True).count()
        if pin_count > 0:
            Pin.objects.exclude(skin__isnull=True).delete()
            self.stdout.write(f'   ✓ Deleted {pin_count} pins that referenced skins')
        
        # Clear UserSkin relationships first
        from pins.models import UserSkin
        user_skin_count, _ = UserSkin.objects.all().delete()
        self.stdout.write(f'   ✓ Cleared {user_skin_count} user skin relationships')
        
        # Now clear the pin skins
        deleted_count, _ = PinSkin.objects.all().delete()
        self.stdout.write(f'   ✓ Cleared {deleted_count} existing pin skins') 