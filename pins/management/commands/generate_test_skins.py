#!/usr/bin/env python
"""
Django management command to generate test skins for development and testing
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from pins.models import <PERSON>n<PERSON><PERSON>, WeeklyChallenge
from music.models import Artist


class Command(BaseCommand):
    help = 'Generate test skins and challenges for development and testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing test data before creating new data',
        )
        parser.add_argument(
            '--house-skins-only',
            action='store_true',
            help='Only create house skins (no challenges or artist skins)',
        )

    def handle(self, *args, **options):
        if options['clear_existing']:
            self.stdout.write('🧹 Clearing existing test data...')
            self.clear_test_data()

        self.stdout.write('🎨 Generating test skins...')
        
        # Create house skins
        house_skins_created = self.create_house_skins()
        
        if not options['house_skins_only']:
            # Create artists for challenges
            artists_created = self.create_test_artists()
            
            # Create weekly challenges
            challenges_created = self.create_weekly_challenges()
            
            # Create artist skins tied to challenges
            artist_skins_created = self.create_artist_skins()
        else:
            artists_created = 0
            challenges_created = 0
            artist_skins_created = 0

        self.stdout.write('\n✅ Test data generation completed!')
        self.stdout.write(f'   📦 House skins created: {house_skins_created}')
        if not options['house_skins_only']:
            self.stdout.write(f'   🎤 Artists created: {artists_created}')
            self.stdout.write(f'   🏆 Challenges created: {challenges_created}')
            self.stdout.write(f'   🎯 Artist skins created: {artist_skins_created}')
        
        self.stdout.write('\n💡 You can now use these test skins in your development!')

    def clear_test_data(self):
        """Clear existing test data"""
        # Delete test skins (but keep ID=1 default skin)
        PinSkin.objects.filter(
            name__icontains='Test'
        ).exclude(id=1).delete()
        
        # Delete test challenges
        WeeklyChallenge.objects.filter(
            title__icontains='Test'
        ).delete()
        
        # Delete test artists
        Artist.objects.filter(
            name__icontains='Test'
        ).delete()
        
        self.stdout.write('   ✓ Test data cleared')

    def create_house_skins(self):
        """Create sample house skins"""
        house_skins_data = [
            {
                'name': 'Test Classic Blue',
                'slug': 'test-classic-blue',
                'description': 'A classic blue house pin for testing',
                'image': 'https://picsum.photos/seed/classic-blue/64/64',
                'is_premium': False,
                'metadata': {'unlock_type': 'free', 'color_theme': 'blue'}
            },
            {
                'name': 'Test Modern Green',
                'slug': 'test-modern-green',
                'description': 'Modern green house design',
                'image': 'https://picsum.photos/seed/modern-green/64/64',
                'is_premium': False,
                'metadata': {'unlock_type': 'free', 'color_theme': 'green'}
            },
            {
                'name': 'Test Premium Gold',
                'slug': 'test-premium-gold',
                'description': 'Exclusive gold house skin for premium users',
                'image': 'https://picsum.photos/seed/premium-gold/64/64',
                'is_premium': True,
                'metadata': {'unlock_type': 'premium', 'color_theme': 'gold'}
            },
            {
                'name': 'Test Premium Diamond',
                'slug': 'test-premium-diamond',
                'description': 'Ultra-exclusive diamond house skin',
                'image': 'https://picsum.photos/seed/premium-diamond/64/64',
                'is_premium': True,
                'metadata': {'unlock_type': 'premium', 'color_theme': 'purple'}
            },
            {
                'name': 'Test Neon Pink',
                'slug': 'test-neon-pink',
                'description': 'Vibrant neon pink house skin',
                'image': 'https://picsum.photos/seed/neon-pink/64/64',
                'is_premium': False,
                'metadata': {'unlock_type': 'free', 'color_theme': 'pink'}
            },
            {
                'name': 'Test Cyber Orange',
                'slug': 'test-cyber-orange',
                'description': 'Futuristic orange cyber house',
                'image': 'https://picsum.photos/seed/cyber-orange/64/64',
                'is_premium': False,
                'metadata': {'unlock_type': 'free', 'color_theme': 'orange'}
            }
        ]

        created_count = 0
        for skin_data in house_skins_data:
            skin, created = PinSkin.objects.get_or_create(
                slug=skin_data['slug'],
                defaults={
                    'name': skin_data['name'],
                    'description': skin_data['description'],
                    'image': skin_data['image'],
                    'skin_type': PinSkin.HOUSE,
                    'is_premium': skin_data['is_premium'],
                    'metadata': skin_data['metadata']
                }
            )
            if created:
                created_count += 1
                self.stdout.write(f'   ✓ Created house skin: {skin.name}')
        
        return created_count

    def create_test_artists(self):
        """Create test artists for challenges"""
        artists_data = [
            {
                'name': 'Test Artist - DJ Nebula',
                'spotify_id': 'test_dj_nebula_spotify'
            },
            {
                'name': 'Test Artist - Bass Prophet',
                'spotify_id': 'test_bass_prophet_spotify'
            },
            {
                'name': 'Test Artist - Synth Master',
                'spotify_id': 'test_synth_master_spotify'
            },
            {
                'name': 'Test Artist - Beat Wizard',
                'spotify_id': 'test_beat_wizard_spotify'
            },
            {
                'name': 'Test Artist - Echo Chamber',
                'spotify_id': 'test_echo_chamber_spotify'
            }
        ]

        created_count = 0
        for artist_data in artists_data:
            artist, created = Artist.objects.get_or_create(
                spotify_id=artist_data['spotify_id'],
                defaults={'name': artist_data['name']}
            )
            if created:
                created_count += 1
                self.stdout.write(f'   ✓ Created artist: {artist.name}')
        
        return created_count

    def create_weekly_challenges(self):
        """Create test weekly challenges"""
        now = timezone.now()
        
        challenges_data = [
            {
                'title': 'Test EDM Blast Challenge',
                'description': 'Drop your most explosive EDM tracks this week!',
                'start_date': now - timedelta(days=3),
                'end_date': now + timedelta(days=4),
                'is_active': True,
                'max_participants': None
            },
            {
                'title': 'Test Hip-Hop Cypher',
                'description': 'Showcase your best hip-hop beats and verses',
                'start_date': now - timedelta(days=10),
                'end_date': now - timedelta(days=3),
                'is_active': True,  # Ended but still active for processing
                'max_participants': 50
            },
            {
                'title': 'Test Lo-Fi Vibes',
                'description': 'Chill lo-fi tracks for studying and relaxation',
                'start_date': now + timedelta(days=1),
                'end_date': now + timedelta(days=8),
                'is_active': True,
                'max_participants': None
            },
            {
                'title': 'Test Electronic Fusion',
                'description': 'Blend electronic with any other genre',
                'start_date': now - timedelta(days=17),
                'end_date': now - timedelta(days=10),
                'is_active': False,  # Fully processed
                'max_participants': None
            },
            {
                'title': 'Test Ambient Soundscapes',
                'description': 'Create immersive ambient soundscapes',
                'start_date': now + timedelta(days=7),
                'end_date': now + timedelta(days=14),
                'is_active': True,
                'max_participants': 25
            }
        ]

        created_count = 0
        for challenge_data in challenges_data:
            challenge, created = WeeklyChallenge.objects.get_or_create(
                title=challenge_data['title'],
                defaults=challenge_data
            )
            if created:
                created_count += 1
                status = "🟢 Active" if challenge.is_ongoing else "🔴 Ended" if challenge.has_ended else "🟡 Upcoming"
                self.stdout.write(f'   ✓ Created challenge: {challenge.title} {status}')
        
        return created_count

    def create_artist_skins(self):
        """Create artist skins tied to challenges"""
        # Get test artists and challenges
        artists = Artist.objects.filter(name__icontains='Test Artist')
        challenges = WeeklyChallenge.objects.filter(title__icontains='Test')
        
        if not artists.exists() or not challenges.exists():
            self.stdout.write('   ⚠️  No test artists or challenges found, skipping artist skins')
            return 0

        artist_skins_data = [
            # EDM Challenge skins
            {
                'name': 'Test DJ Nebula Badge',
                'slug': 'test-dj-nebula-badge',
                'description': 'Participation badge for EDM Blast Challenge',
                'image': 'https://picsum.photos/seed/dj-nebula-badge/64/64',
                'artist_name': 'Test Artist - DJ Nebula',
                'challenge_title': 'Test EDM Blast Challenge',
                'metadata': {'unlock_type': 'PARTICIPATE', 'rarity': 'common'}
            },
            {
                'name': 'Test DJ Nebula Gold',
                'slug': 'test-dj-nebula-gold',
                'description': 'Top 3 finisher in EDM Blast Challenge',
                'image': 'https://picsum.photos/seed/dj-nebula-gold/64/64?blur=1',
                'artist_name': 'Test Artist - DJ Nebula',
                'challenge_title': 'Test EDM Blast Challenge',
                'metadata': {'unlock_type': 'TOP_N', 'n': 3, 'rarity': 'legendary'}
            },
            
            # Hip-Hop Challenge skins
            {
                'name': 'Test Bass Prophet Mic',
                'slug': 'test-bass-prophet-mic',
                'description': 'Hip-Hop Cypher participation reward',
                'image': 'https://picsum.photos/seed/bass-prophet-mic/64/64',
                'artist_name': 'Test Artist - Bass Prophet',
                'challenge_title': 'Test Hip-Hop Cypher',
                'metadata': {'unlock_type': 'PARTICIPATE', 'rarity': 'common'}
            },
            {
                'name': 'Test Bass Prophet Crown',
                'slug': 'test-bass-prophet-crown',
                'description': 'Hip-Hop Cypher champion crown',
                'image': 'https://picsum.photos/seed/bass-prophet-crown/64/64?blur=2',
                'artist_name': 'Test Artist - Bass Prophet',
                'challenge_title': 'Test Hip-Hop Cypher',
                'metadata': {'unlock_type': 'TOP_N', 'n': 1, 'rarity': 'mythic'}
            },
            
            # Lo-Fi Challenge skins
            {
                'name': 'Test Synth Master Vinyl',
                'slug': 'test-synth-master-vinyl',
                'description': 'Lo-Fi Vibes challenge participation',
                'image': 'https://picsum.photos/seed/synth-master-vinyl/64/64?grayscale',
                'artist_name': 'Test Artist - Synth Master',
                'challenge_title': 'Test Lo-Fi Vibes',
                'metadata': {'unlock_type': 'PARTICIPATE', 'rarity': 'common'}
            },
            
            # Electronic Fusion skins
            {
                'name': 'Test Beat Wizard Orb',
                'slug': 'test-beat-wizard-orb',
                'description': 'Electronic Fusion mastery orb',
                'image': 'https://picsum.photos/seed/beat-wizard-orb/64/64?blur=3',
                'artist_name': 'Test Artist - Beat Wizard',
                'challenge_title': 'Test Electronic Fusion',
                'metadata': {'unlock_type': 'TOP_N', 'n': 5, 'rarity': 'epic'}
            },
            
            # Ambient Challenge skins
            {
                'name': 'Test Echo Chamber Wave',
                'slug': 'test-echo-chamber-wave',
                'description': 'Ambient Soundscapes wave badge',
                'image': 'https://picsum.photos/seed/echo-chamber-wave/64/64?grayscale&blur=1',
                'artist_name': 'Test Artist - Echo Chamber',
                'challenge_title': 'Test Ambient Soundscapes',
                'metadata': {'unlock_type': 'PARTICIPATE', 'rarity': 'common'}
            }
        ]

        created_count = 0
        for skin_data in artist_skins_data:
            # Find the artist and challenge
            try:
                artist = artists.get(name=skin_data['artist_name'])
                challenge = challenges.get(title=skin_data['challenge_title'])
                
                skin, created = PinSkin.objects.get_or_create(
                    slug=skin_data['slug'],
                    defaults={
                        'name': skin_data['name'],
                        'description': skin_data['description'],
                        'image': skin_data['image'],
                        'skin_type': PinSkin.ARTIST,
                        'artist': artist,
                        'challenge': challenge,
                        'is_premium': False,
                        'metadata': skin_data['metadata']
                    }
                )
                
                if created:
                    created_count += 1
                    unlock_type = skin_data['metadata'].get('unlock_type', 'PARTICIPATE')
                    rarity = skin_data['metadata'].get('rarity', 'common')
                    self.stdout.write(f'   ✓ Created artist skin: {skin.name} ({unlock_type}, {rarity})')
                    
            except (Artist.DoesNotExist, WeeklyChallenge.DoesNotExist) as e:
                self.stdout.write(f'   ⚠️  Skipped {skin_data["name"]}: {str(e)}')
                continue
        
        return created_count

    def stdout_write_summary(self):
        """Write a summary of all available test skins"""
        self.stdout.write('\n📋 Test Skins Summary:')
        
        house_skins = PinSkin.objects.filter(
            skin_type=PinSkin.HOUSE,
            name__icontains='Test'
        )
        self.stdout.write(f'   🏠 House Skins: {house_skins.count()}')
        for skin in house_skins:
            premium_text = "👑 Premium" if skin.is_premium else "🆓 Free"
            self.stdout.write(f'      • {skin.name} ({premium_text})')
        
        artist_skins = PinSkin.objects.filter(
            skin_type=PinSkin.ARTIST,
            name__icontains='Test'
        )
        self.stdout.write(f'\n   🎨 Artist Skins: {artist_skins.count()}')
        for skin in artist_skins:
            unlock_type = skin.metadata.get('unlock_type', 'PARTICIPATE')
            rarity = skin.metadata.get('rarity', 'common')
            challenge_status = "🟢" if skin.challenge and skin.challenge.is_ongoing else "🔴" if skin.challenge and skin.challenge.has_ended else "🟡"
            self.stdout.write(f'      • {skin.name} ({unlock_type}, {rarity}) {challenge_status}') 