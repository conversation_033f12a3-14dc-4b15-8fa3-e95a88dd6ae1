# Generated by Django 4.2.7 on 2025-04-07 16:22

import django.contrib.gis.db.models.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Pin",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "location",
                    django.contrib.gis.db.models.fields.PointField(
                        geography=True, srid=4326
                    ),
                ),
                ("title", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
                ("track_title", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("track_artist", models.Char<PERSON><PERSON>(max_length=255)),
                ("album", models.CharField(blank=True, max_length=255, null=True)),
                ("track_url", models.URL<PERSON>ield()),
                (
                    "service",
                    models.Char<PERSON>ield(
                        choices=[
                            ("spotify", "Spotify"),
                            ("apple", "Apple Music"),
                            ("soundcloud", "SoundCloud"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "rarity",
                    models.CharField(
                        choices=[
                            ("common", "Common"),
                            ("uncommon", "Uncommon"),
                            ("rare", "Rare"),
                            ("epic", "Epic"),
                            ("legendary", "Legendary"),
                        ],
                        default="common",
                        max_length=20,
                    ),
                ),
                ("aura_radius", models.IntegerField(default=50)),
                ("is_private", models.BooleanField(default=False)),
                ("expiration_date", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="PinInteraction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "interaction_type",
                    models.CharField(
                        choices=[
                            ("view", "Viewed"),
                            ("collect", "Collected"),
                            ("like", "Liked"),
                            ("share", "Shared"),
                        ],
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "pin",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="interactions",
                        to="pins.pin",
                    ),
                ),
            ],
        ),
    ]
