# Generated by Django 4.2.7 on 2025-04-07 16:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("pins", "0001_initial"),
        ("gamification", "0002_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="pininteraction",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="pin_interactions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="pin",
            name="owner",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="pins",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="pin",
            name="skin",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.SET_DEFAULT,
                to="gamification.pinskin",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="pininteraction",
            unique_together={("user", "pin", "interaction_type")},
        ),
    ]
