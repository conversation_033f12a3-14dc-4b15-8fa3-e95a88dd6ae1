# Generated by Django 4.2.7 on 2025-05-22 20:49

from django.conf import settings
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("pins", "0003_pinanalytics_pin_genre_pin_mood_pin_tags_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Collection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
                ("is_public", models.BooleanField(default=True)),
                (
                    "primary_color",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "cover_image_urls",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.URLField(),
                        blank=True,
                        default=list,
                        size=None,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="collections",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CollectionPin",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("added_at", models.DateTimeField(auto_now_add=True)),
                (
                    "collection",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="collection_pins",
                        to="pins.collection",
                    ),
                ),
                (
                    "pin",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="pin_collections",
                        to="pins.pin",
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["added_at"], name="pins_collec_added_a_1a7251_idx"
                    )
                ],
                "unique_together": {("collection", "pin")},
            },
        ),
        migrations.AddIndex(
            model_name="collection",
            index=models.Index(
                fields=["created_at"], name="pins_collec_created_306303_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="collection",
            index=models.Index(
                fields=["is_public"], name="pins_collec_is_publ_c5a13f_idx"
            ),
        ),
    ]
