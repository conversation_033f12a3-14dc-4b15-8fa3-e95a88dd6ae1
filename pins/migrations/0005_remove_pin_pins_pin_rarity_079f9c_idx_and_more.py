# Generated by Django 4.2.7 on 2025-05-27 01:08

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("pins", "0004_collection_collectionpin_and_more"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="pin",
            name="pins_pin_rarity_079f9c_idx",
        ),
        migrations.RemoveField(
            model_name="pin",
            name="rarity",
        ),
        migrations.AddField(
            model_name="pin",
            name="downvote_count",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="pin",
            name="upvote_count",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="pin",
            name="vote_score",
            field=models.IntegerField(default=0),
        ),
        migrations.AddIndex(
            model_name="pin",
            index=models.Index(
                fields=["vote_score"], name="pins_pin_vote_sc_5ff1ec_idx"
            ),
        ),
    ]
