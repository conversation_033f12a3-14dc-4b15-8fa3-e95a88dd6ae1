# Generated by Django 4.2.7 on 2025-06-14 05:59

from django.conf import settings
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("gamification", "0002_initial"),
        ("pins", "0006_pin_caption"),
    ]

    operations = [
        migrations.CreateModel(
            name="VirtualPin",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "caption",
                    models.CharField(
                        blank=True,
                        help_text="Short note about this track",
                        max_length=150,
                        null=True,
                    ),
                ),
                ("track_title", models.Char<PERSON><PERSON>(max_length=255)),
                ("track_artist", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("album", models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ("track_url", models.URL<PERSON>ield()),
                (
                    "service",
                    models.CharField(
                        choices=[
                            ("spotify", "Spotify"),
                            ("apple", "Apple Music"),
                            ("soundcloud", "SoundCloud"),
                        ],
                        max_length=20,
                    ),
                ),
                ("genre", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "mood",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("happy", "Happy"),
                            ("chill", "Chill"),
                            ("energetic", "Energetic"),
                            ("sad", "Sad"),
                            ("romantic", "Romantic"),
                            ("focus", "Focus"),
                            ("party", "Party"),
                            ("workout", "Workout"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "tags",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=50),
                        blank=True,
                        null=True,
                        size=None,
                    ),
                ),
                ("upvote_count", models.PositiveIntegerField(default=0)),
                ("downvote_count", models.PositiveIntegerField(default=0)),
                ("vote_score", models.IntegerField(default=0)),
                ("is_private", models.BooleanField(default=True)),
                ("expiration_date", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="VirtualPinInteraction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "interaction_type",
                    models.CharField(
                        choices=[
                            ("view", "Viewed"),
                            ("collect", "Collected"),
                            ("like", "Liked"),
                            ("share", "Shared"),
                        ],
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.AlterUniqueTogether(
            name="collectionpin",
            unique_together=set(),
        ),
        migrations.AlterField(
            model_name="collectionpin",
            name="pin",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="pin_collections",
                to="pins.pin",
            ),
        ),
        migrations.AddField(
            model_name="virtualpininteraction",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="virtual_pin_interactions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="virtualpininteraction",
            name="virtual_pin",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="virtual_interactions",
                to="pins.virtualpin",
            ),
        ),
        migrations.AddField(
            model_name="virtualpin",
            name="owner",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="virtual_pins",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="virtualpin",
            name="skin",
            field=models.ForeignKey(
                blank=True,
                default=1,
                null=True,
                on_delete=django.db.models.deletion.SET_DEFAULT,
                to="gamification.pinskin",
            ),
        ),
        migrations.AddField(
            model_name="collectionpin",
            name="virtual_pin",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="virtual_pin_collections",
                to="pins.virtualpin",
            ),
        ),
        migrations.AddConstraint(
            model_name="collectionpin",
            constraint=models.CheckConstraint(
                check=models.Q(
                    models.Q(("pin__isnull", False), ("virtual_pin__isnull", True)),
                    models.Q(("pin__isnull", True), ("virtual_pin__isnull", False)),
                    _connector="OR",
                ),
                name="collection_pin_exactly_one_pin_type",
            ),
        ),
        migrations.AddIndex(
            model_name="virtualpininteraction",
            index=models.Index(
                fields=["interaction_type"], name="pins_virtua_interac_0fb091_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="virtualpininteraction",
            index=models.Index(
                fields=["created_at"], name="pins_virtua_created_b0dff1_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="virtualpininteraction",
            unique_together={("user", "virtual_pin", "interaction_type")},
        ),
        migrations.AddIndex(
            model_name="virtualpin",
            index=models.Index(
                fields=["created_at"], name="pins_virtua_created_7f9642_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="virtualpin",
            index=models.Index(
                fields=["service"], name="pins_virtua_service_41b951_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="virtualpin",
            index=models.Index(fields=["genre"], name="pins_virtua_genre_7c898c_idx"),
        ),
        migrations.AddIndex(
            model_name="virtualpin",
            index=models.Index(fields=["mood"], name="pins_virtua_mood_818474_idx"),
        ),
        migrations.AddIndex(
            model_name="virtualpin",
            index=models.Index(
                fields=["is_private"], name="pins_virtua_is_priv_7ddbf6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="virtualpin",
            index=models.Index(
                fields=["vote_score"], name="pins_virtua_vote_sc_9dd317_idx"
            ),
        ),
    ]
