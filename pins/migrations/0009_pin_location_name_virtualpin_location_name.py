# Generated by Django 4.2.7 on 2025-06-14 22:46

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("pins", "0008_pin_artwork_url_pin_duration_ms_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="pin",
            name="location_name",
            field=models.Char<PERSON>ield(
                default="Unknown Location",
                help_text="Human-readable location name",
                max_length=200,
            ),
        ),
        migrations.AddField(
            model_name="virtualpin",
            name="location_name",
            field=models.Char<PERSON>ield(
                blank=True,
                default="Unknown Location",
                help_text="Optional location name for virtual pins",
                max_length=200,
                null=True,
            ),
        ),
    ]
