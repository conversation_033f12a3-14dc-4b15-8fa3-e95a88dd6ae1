# Generated by Django 4.2.7 on 2025-06-14 23:36

from django.db import migrations


def update_apple_service_to_apple_music(apps, schema_editor):
    """Update existing 'apple' service values to 'apple_music'"""
    Pin = apps.get_model('pins', 'Pin')
    VirtualPin = apps.get_model('pins', 'VirtualPin')
    
    # Update Pin model
    Pin.objects.filter(service='apple').update(service='apple_music')
    
    # Update VirtualPin model
    VirtualPin.objects.filter(service='apple').update(service='apple_music')


def reverse_apple_music_to_apple(apps, schema_editor):
    """Reverse migration: update 'apple_music' back to 'apple'"""
    Pin = apps.get_model('pins', 'Pin')
    VirtualPin = apps.get_model('pins', 'VirtualPin')
    
    # Reverse Pin model
    Pin.objects.filter(service='apple_music').update(service='apple')
    
    # Reverse VirtualPin model
    VirtualPin.objects.filter(service='apple_music').update(service='apple')


class Migration(migrations.Migration):
    dependencies = [
        ("pins", "0009_pin_location_name_virtualpin_location_name"),
    ]

    operations = [
        migrations.RunPython(update_apple_service_to_apple_music, reverse_apple_music_to_apple),
    ]
