# Generated by Django 4.2.7 on 2025-06-16 21:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("music", "0003_artist"),
        ("pins", "0010_auto_20250614_2336"),
    ]

    operations = [
        migrations.CreateModel(
            name="WeeklyChallenge",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField()),
                ("is_active", models.BooleanField(default=True)),
                (
                    "max_participants",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Maximum number of participants (null for unlimited)",
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.AlterField(
            model_name="pin",
            name="service",
            field=models.CharField(
                choices=[
                    ("spotify", "Spotify"),
                    ("apple_music", "Apple Music"),
                    ("soundcloud", "SoundCloud"),
                ],
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="virtualpin",
            name="service",
            field=models.CharField(
                choices=[
                    ("spotify", "Spotify"),
                    ("apple_music", "Apple Music"),
                    ("soundcloud", "SoundCloud"),
                ],
                max_length=20,
            ),
        ),
        migrations.CreateModel(
            name="PinSkin",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=50)),
                ("slug", models.SlugField(unique=True)),
                ("image", models.ImageField(upload_to="pin_skins/")),
                ("description", models.TextField(blank=True)),
                (
                    "skin_type",
                    models.CharField(
                        choices=[("HOUSE", "House"), ("ARTIST", "Artist limited")],
                        default="HOUSE",
                        max_length=10,
                    ),
                ),
                ("is_premium", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="JSON for unlock rules, e.g., {'unlock_type': 'TOP_N', 'n': 3}",
                    ),
                ),
                (
                    "artist",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="skins",
                        to="music.artist",
                    ),
                ),
                (
                    "challenge",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="reward_skins",
                        to="pins.weeklychallenge",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ChallengeParticipation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("vote_score", models.IntegerField(default=0)),
                ("participated_at", models.DateTimeField(auto_now_add=True)),
                (
                    "challenge",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="participations",
                        to="pins.weeklychallenge",
                    ),
                ),
                (
                    "pin",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="challenge_entries",
                        to="pins.pin",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="challenge_participations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "virtual_pin",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="challenge_entries",
                        to="pins.virtualpin",
                    ),
                ),
            ],
        ),
        migrations.AlterField(
            model_name="pin",
            name="skin",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.SET_DEFAULT,
                to="pins.pinskin",
            ),
        ),
        migrations.AlterField(
            model_name="virtualpin",
            name="skin",
            field=models.ForeignKey(
                blank=True,
                default=1,
                null=True,
                on_delete=django.db.models.deletion.SET_DEFAULT,
                to="pins.pinskin",
            ),
        ),
        migrations.CreateModel(
            name="UserSkin",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("unlocked_at", models.DateTimeField(auto_now_add=True)),
                (
                    "skin",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="pins.pinskin"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="unlocked_skins",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("user", "skin")},
            },
        ),
        migrations.AddConstraint(
            model_name="challengeparticipation",
            constraint=models.CheckConstraint(
                check=models.Q(
                    ("pin__isnull", False),
                    ("virtual_pin__isnull", False),
                    _connector="OR",
                ),
                name="challenge_participation_requires_pin_or_virtual_pin",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="challengeparticipation",
            unique_together={("user", "challenge")},
        ),
    ]
