from django.db import migrations

def create_default_skin(apps, schema_editor):
    PinSkin = apps.get_model('pins', 'PinSkin')
    if not PinSkin.objects.filter(id=1).exists():
        PinSkin.objects.create(
            id=1,
            name='Default Skin',
            slug='default-skin',
            image='pin_skins/default.png',
            skin_type='HOUSE',
            is_premium=False,
            metadata={'unlock_type': 'ALWAYS_AVAILABLE'}
        )

def remove_default_skin(apps, schema_editor):
    PinSkin = apps.get_model('pins', 'PinSkin')
    PinSkin.objects.filter(id=1).delete()

class Migration(migrations.Migration):
    dependencies = [
        ('pins', '0011_weeklychallenge_alter_pin_service_and_more'),
    ]

    operations = [
        migrations.RunPython(create_default_skin, remove_default_skin),
    ] 