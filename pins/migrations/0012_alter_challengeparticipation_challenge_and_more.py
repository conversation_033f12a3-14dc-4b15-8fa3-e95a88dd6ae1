# Generated by Django 4.2.7 on 2025-06-17 00:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("challenges", "0003_weeklychallenge_max_participants"),
        ("pins", "0011_weeklychallenge_alter_pin_service_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="challengeparticipation",
            name="challenge",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="pin_participations",
                to="challenges.weeklychallenge",
            ),
        ),
        migrations.AlterField(
            model_name="challengeparticipation",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="pin_challenge_participations",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="pinskin",
            name="challenge",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="reward_skins",
                to="challenges.weeklychallenge",
            ),
        ),
        migrations.DeleteModel(
            name="WeeklyChallenge",
        ),
    ]
