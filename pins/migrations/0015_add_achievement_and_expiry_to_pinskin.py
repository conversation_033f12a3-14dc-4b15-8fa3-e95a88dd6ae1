# Generated by Django 4.2.7 on 2025-07-05 13:12

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        (
            "gamification",
            "0007_remove_userachievement_gamificatio_user_id_4b2f15_idx_and_more",
        ),
        ("pins", "0014_alter_pinskin_image"),
    ]

    operations = [
        migrations.AddField(
            model_name="pinskin",
            name="achievement",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="reward_skins",
                to="gamification.achievement",
            ),
        ),
        migrations.AddField(
            model_name="pinskin",
            name="expiry_date",
            field=models.DateTimeField(
                blank=True,
                help_text="When this skin becomes unavailable (for limited-time releases)",
                null=True,
            ),
        ),
    ]
