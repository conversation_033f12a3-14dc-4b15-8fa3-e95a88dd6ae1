# Generated by Django 4.2.7 on 2025-07-09 05:38

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("pins", "0015_add_achievement_and_expiry_to_pinskin"),
    ]

    operations = [
        migrations.AddField(
            model_name="pin",
            name="altitude",
            field=models.FloatField(
                blank=True, help_text="Altitude in meters from phone GPS", null=True
            ),
        ),
        migrations.AddField(
            model_name="pin",
            name="ar_position_x",
            field=models.FloatField(
                blank=True, help_text="AR X coordinate position", null=True
            ),
        ),
        migrations.AddField(
            model_name="pin",
            name="ar_position_y",
            field=models.FloatField(
                blank=True, help_text="AR Y coordinate position", null=True
            ),
        ),
        migrations.AddField(
            model_name="pin",
            name="ar_position_z",
            field=models.FloatField(
                blank=True, help_text="AR Z coordinate position", null=True
            ),
        ),
    ]
