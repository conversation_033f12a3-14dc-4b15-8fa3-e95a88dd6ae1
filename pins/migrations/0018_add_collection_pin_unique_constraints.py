# Generated by Django 4.2.7 on 2025-07-26 20:43

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("pins", "0017_pin_pins_pin_expirat_eb854f_idx"),
    ]

    operations = [
        migrations.AddConstraint(
            model_name="collectionpin",
            constraint=models.UniqueConstraint(
                condition=models.Q(("pin__isnull", False)),
                fields=("collection", "pin"),
                name="unique_collection_pin",
            ),
        ),
        migrations.AddConstraint(
            model_name="collectionpin",
            constraint=models.UniqueConstraint(
                condition=models.Q(("virtual_pin__isnull", False)),
                fields=("collection", "virtual_pin"),
                name="unique_collection_virtual_pin",
            ),
        ),
    ]
