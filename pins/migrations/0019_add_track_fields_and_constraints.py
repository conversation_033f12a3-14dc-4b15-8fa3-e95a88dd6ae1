# Generated by Django 4.2.7 on 2025-07-26 20:58

from django.db import migrations, models


def populate_track_fields_and_remove_duplicates(apps, schema_editor):
    """
    Populate track_title and track_artist fields from related Pin/VirtualPin objects
    and remove duplicate entries before adding the unique constraint.
    """
    CollectionPin = apps.get_model('pins', 'CollectionPin')

    # Update track fields from related Pin objects
    for collection_pin in CollectionPin.objects.filter(pin__isnull=False).select_related('pin'):
        collection_pin.track_title = collection_pin.pin.track_title or "Unknown Title"
        collection_pin.track_artist = collection_pin.pin.track_artist or "Unknown Artist"
        collection_pin.save()

    # Update track fields from related VirtualPin objects
    for collection_pin in CollectionPin.objects.filter(virtual_pin__isnull=False).select_related('virtual_pin'):
        collection_pin.track_title = collection_pin.virtual_pin.track_title or "Unknown Title"
        collection_pin.track_artist = collection_pin.virtual_pin.track_artist or "Unknown Artist"
        collection_pin.save()

    # Use raw SQL to remove duplicates more efficiently
    # Keep the record with the smallest ID (oldest) for each unique combination
    with schema_editor.connection.cursor() as cursor:
        cursor.execute("""
            DELETE FROM pins_collectionpin
            WHERE id NOT IN (
                SELECT MIN(id)
                FROM pins_collectionpin
                GROUP BY collection_id, track_title, track_artist
            )
        """)
        deleted_count = cursor.rowcount
        if deleted_count > 0:
            print(f"Removed {deleted_count} duplicate CollectionPin entries")


def reverse_populate_track_fields(apps, schema_editor):
    """
    Reverse migration - just clear the track fields
    """
    CollectionPin = apps.get_model('pins', 'CollectionPin')
    CollectionPin.objects.all().update(track_title="", track_artist="")


class Migration(migrations.Migration):
    dependencies = [
        ("pins", "0018_add_collection_pin_unique_constraints"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="collectionpin",
            name="unique_collection_pin",
        ),
        migrations.RemoveConstraint(
            model_name="collectionpin",
            name="unique_collection_virtual_pin",
        ),
        migrations.AddField(
            model_name="collectionpin",
            name="track_artist",
            field=models.CharField(default="Unknown Artist", max_length=255),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="collectionpin",
            name="track_title",
            field=models.CharField(default="Unknown Title", max_length=255),
            preserve_default=False,
        ),
        # Populate the track fields and remove duplicates
        migrations.RunPython(
            populate_track_fields_and_remove_duplicates,
            reverse_populate_track_fields,
        ),
        migrations.AddIndex(
            model_name="collectionpin",
            index=models.Index(
                fields=["track_title", "track_artist"],
                name="pins_collec_track_t_df4046_idx",
            ),
        ),
        migrations.AddConstraint(
            model_name="collectionpin",
            constraint=models.UniqueConstraint(
                fields=("collection", "track_title", "track_artist"),
                name="unique_collection_song",
            ),
        ),
    ]
