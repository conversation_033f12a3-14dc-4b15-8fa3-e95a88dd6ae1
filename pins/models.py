from django.db import models
from django.contrib.gis.db import models as gis_models
from django.conf import settings
from django.utils.functional import cached_property
from django.contrib.postgres.fields import ArrayField
from django.db.models import Count
from django.utils.text import slugify
from django.utils import timezone
from challenges.models import WeeklyChallenge  # Use central WeeklyChallenge model


class PinSkin(models.Model):
    """
    Visual customizations for pins.
    """
    HOUSE = "HOUSE"
    ARTIST = "ARTIST"
    SKIN_TYPES = [(HOUSE, "House"), (ARTIST, "Artist limited")]

    name = models.CharField(max_length=50)
    slug = models.SlugField(unique=True)               # API key
    image = models.URLField(help_text="URL to the pin skin image")   # PNG/SVG image URL
    description = models.TextField(blank=True)
    skin_type = models.CharField(max_length=10, choices=SKIN_TYPES, default=HOUSE)
    artist = models.ForeignKey(
        "music.Artist", null=True, blank=True, on_delete=models.SET_NULL, related_name="skins"
    )                                                          # optional
    is_premium = models.BooleanField(default=False)          # e.g. Plus subscribers
    created_at = models.DateTimeField(auto_now_add=True)

    # NEW— tie one-to-one with a weekly challenge (null for house skins):
    challenge = models.ForeignKey(
        WeeklyChallenge, null=True, blank=True,
        on_delete=models.SET_NULL, related_name="reward_skins"
    )
    
    # NEW— tie to achievement for achievement-based skins
    achievement = models.ForeignKey(
        "gamification.Achievement", null=True, blank=True,
        on_delete=models.SET_NULL, related_name="reward_skins"
    )
    
    # NEW— expiry date for limited-time skins
    expiry_date = models.DateTimeField(
        null=True, blank=True,
        help_text="When this skin becomes unavailable (for limited-time releases)"
    )
    
    # Metadata for unlock rules
    metadata = models.JSONField(default=dict, blank=True, help_text="JSON for unlock rules, e.g., {'unlock_type': 'TOP_N', 'n': 3}")

    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)
    
    @property
    def is_expired(self):
        """Check if this skin has expired"""
        if self.expiry_date:
            return self.expiry_date < timezone.now()
        return False
    
    @property
    def is_limited_time(self):
        """Check if this is a limited-time skin"""
        return self.expiry_date is not None
    
    @property
    def time_remaining(self):
        """Get time remaining until expiry (if limited)"""
        if self.expiry_date and not self.is_expired:
            return self.expiry_date - timezone.now()
        return None


class UserSkin(models.Model):
    """
    Which skins a user has unlocked or claimed.
    """
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE,
                             related_name="unlocked_skins")
    skin = models.ForeignKey(PinSkin, on_delete=models.CASCADE)
    unlocked_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("user", "skin")
    
    def __str__(self):
        return f"{self.user.username} - {self.skin.name}"


class ChallengeParticipation(models.Model):
    """
    Model representing user participation in weekly challenges
    """
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='pin_challenge_participations'
    )
    challenge = models.ForeignKey(
        WeeklyChallenge,
        on_delete=models.CASCADE,
        related_name='pin_participations'
    )
    pin = models.ForeignKey(
        'Pin',
        on_delete=models.CASCADE,
        related_name='challenge_entries',
        null=True,
        blank=True
    )
    virtual_pin = models.ForeignKey(
        'VirtualPin',
        on_delete=models.CASCADE,
        related_name='challenge_entries',
        null=True,
        blank=True
    )
    vote_score = models.IntegerField(default=0)
    participated_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('user', 'challenge')
        constraints = [
            models.CheckConstraint(
                check=models.Q(pin__isnull=False) | models.Q(virtual_pin__isnull=False),
                name='challenge_participation_requires_pin_or_virtual_pin'
            )
        ]
    
    def __str__(self):
        item = self.pin or self.virtual_pin
        return f"{self.user.username} - {self.challenge.title} - {item.title if item else 'No entry'}"


class Pin(models.Model):
    """
    Model representing a music pin dropped at a physical location.
    """
    SERVICES = (
        ('spotify', 'Spotify'),
        ('apple_music', 'Apple Music'),
        ('soundcloud', 'SoundCloud')
    )
    
    MOOD_CHOICES = (
        ('happy', 'Happy'),
        ('chill', 'Chill'),
        ('energetic', 'Energetic'),
        ('sad', 'Sad'),
        ('romantic', 'Romantic'),
        ('focus', 'Focus'),
        ('party', 'Party'),
        ('workout', 'Workout'),
    )
    
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='pins'
    )
    location = gis_models.PointField(geography=True)
    location_name = models.CharField(max_length=200, default='Unknown Location', help_text="Human-readable location name")
    title = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    caption = models.CharField(max_length=150, blank=True, null=True, help_text="Short note displayed when dropping a pin")
    
    # Music data
    track_title = models.CharField(max_length=255)
    track_artist = models.CharField(max_length=255)
    album = models.CharField(max_length=255, blank=True, null=True)
    track_url = models.URLField()
    service = models.CharField(max_length=20, choices=SERVICES)
    artwork_url = models.URLField(blank=True, null=True)  # Album/track artwork URL
    duration_ms = models.PositiveIntegerField(null=True, blank=True)  # Track duration in milliseconds
    
    # Enhanced discovery fields
    genre = models.CharField(max_length=50, blank=True, null=True)
    mood = models.CharField(max_length=20, choices=MOOD_CHOICES, blank=True, null=True)
    tags = ArrayField(models.CharField(max_length=50), blank=True, null=True)
    
    # Customization
    skin = models.ForeignKey(
        PinSkin,
        on_delete=models.SET_DEFAULT,
        default=1
    )
    
    # Vote tracking
    upvote_count = models.PositiveIntegerField(default=0)
    downvote_count = models.PositiveIntegerField(default=0)
    vote_score = models.IntegerField(default=0)  # upvotes - downvotes
    
    # Discovery
    aura_radius = models.IntegerField(default=50)  # meters
    is_private = models.BooleanField(default=False)
    expiration_date = models.DateTimeField(blank=True, null=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # AR positioning data
    ar_position_x = models.FloatField(null=True, blank=True, help_text="AR X coordinate position")
    ar_position_y = models.FloatField(null=True, blank=True, help_text="AR Y coordinate position")
    ar_position_z = models.FloatField(null=True, blank=True, help_text="AR Z coordinate position")
    altitude = models.FloatField(null=True, blank=True, help_text="Altitude in meters from phone GPS")
    
    class Meta:
        indexes = [
            models.Index(fields=['created_at']),
            models.Index(fields=['service']),
            models.Index(fields=['genre']),
            models.Index(fields=['mood']),
            models.Index(fields=['is_private']),
            models.Index(fields=['vote_score']),  # For sorting by votes
            models.Index(fields=['expiration_date']),  # For seed cleanup
        ]
    
    def __str__(self):
        return f"{self.title} by {self.owner.username} - {self.track_title}"
    
    @cached_property
    def total_interactions(self):
        """Get total number of interactions with this pin"""
        return self.interactions.count()
    
    @cached_property
    def like_count(self):
        """Get number of likes for this pin"""
        return self.interactions.filter(interaction_type='like').count()
    
    @cached_property
    def collect_count(self):
        """Get number of collects for this pin"""
        return self.interactions.filter(interaction_type='collect').count()
    
    @cached_property
    def view_count(self):
        """Get number of views for this pin"""
        return self.interactions.filter(interaction_type='view').count()
    
    def find_similar_pins(self, limit=5):
        """Find similar pins based on music attributes"""
        from django.db.models import Count, Q
        
        # Start with base queryset
        qs = Pin.objects.exclude(id=self.id)
        
        # Filter by similar properties
        if self.genre:
            qs = qs.filter(genre=self.genre)
        
        if self.service:
            qs = qs.filter(service=self.service)
        
        if self.mood:
            qs = qs.filter(mood=self.mood)
        
        # Add a relevance score based on artist match and votes
        qs = qs.extra(
            select={
                'artist_match': f"track_artist = '{self.track_artist}'",
                'vote_relevance': 'vote_score'
            }
        )
        
        # Order by relevance factors
        return qs.order_by('-artist_match', '-vote_score', '-created_at')[:limit]
        
    def update_vote_counts(self):
        """Update vote counts from actual votes"""
        from votes.models import Vote
        vote_counts = Vote.objects.filter(pin=self).aggregate(
            upvotes=Count('id', filter=models.Q(value=1)),
            downvotes=Count('id', filter=models.Q(value=-1))
        )
        
        self.upvote_count = vote_counts['upvotes']
        self.downvote_count = vote_counts['downvotes']
        self.vote_score = self.upvote_count - self.downvote_count
        self.save(update_fields=['upvote_count', 'downvote_count', 'vote_score'])

    @property
    def is_public(self):
        """Return True if pin is public (inverse of is_private)"""
        return not self.is_private

    @property
    def is_virtual(self):
        """Return False for regular pins"""
        return False

    @property
    def is_seed(self):
        """Return True if this is a seed pin"""
        return self.tags and 'seed' in self.tags

    @property
    def is_expired(self):
        """Return True if this pin has expired"""
        if not self.expiration_date:
            return False
        return self.expiration_date < timezone.now()

    def mark_as_seed(self, zone_type: str = None, location_type: str = None):
        """Mark this pin as a seed pin with optional metadata"""
        if not self.tags:
            self.tags = []

        if 'seed' not in self.tags:
            self.tags.append('seed')

        if zone_type and zone_type not in self.tags:
            self.tags.append(zone_type)

        if location_type and location_type not in self.tags:
            self.tags.append(location_type)

        self.save(update_fields=['tags'])

    @classmethod
    def get_seed_pins(cls):
        """Get all seed pins"""
        return cls.objects.filter(tags__contains=['seed'])

    @classmethod
    def get_organic_pins(cls):
        """Get all non-seed pins"""
        return cls.objects.exclude(tags__contains=['seed'])

    @classmethod
    def get_expired_seeds(cls):
        """Get all expired seed pins"""
        return cls.objects.filter(
            tags__contains=['seed'],
            expiration_date__lt=timezone.now()
        )


class PinInteraction(models.Model):
    """
    Model for tracking user interactions with pins
    (viewed, collected, liked, shared)
    """
    INTERACTION_TYPES = (
        ('view', 'Viewed'),
        ('collect', 'Collected'),
        ('like', 'Liked'),
        ('share', 'Shared')
    )
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='pin_interactions'
    )
    pin = models.ForeignKey(
        Pin,
        on_delete=models.CASCADE,
        related_name='interactions'
    )
    interaction_type = models.CharField(max_length=20, choices=INTERACTION_TYPES)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('user', 'pin', 'interaction_type')
        indexes = [
            models.Index(fields=['interaction_type']),
            models.Index(fields=['created_at']),
        ]
        
    def __str__(self):
        return f"{self.user.username} {self.interaction_type} {self.pin.title}"


class PinAnalytics(models.Model):
    """
    Model for tracking analytics data about pins
    """
    pin = models.OneToOneField(Pin, on_delete=models.CASCADE, related_name='analytics')
    total_views = models.IntegerField(default=0)
    unique_viewers = models.IntegerField(default=0)
    collection_rate = models.FloatField(default=0)
    peak_hour = models.IntegerField(default=0, help_text="Hour of day with most views (0-23)")
    last_updated = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name_plural = "Pin analytics"
    
    def __str__(self):
        return f"Analytics for {self.pin.title}"
    
    @classmethod
    def update_for_pin(cls, pin):
        """Update analytics for a pin"""
        from django.db.models import Count
        from django.utils import timezone
        from datetime import timedelta
        
        analytics, created = cls.objects.get_or_create(pin=pin)
        
        # Update metrics
        analytics.total_views = pin.interactions.filter(interaction_type='view').count()
        analytics.unique_viewers = pin.interactions.filter(interaction_type='view').values('user').distinct().count()
        
        # Calculate collection rate
        if analytics.total_views > 0:
            collect_count = pin.interactions.filter(interaction_type='collect').count()
            analytics.collection_rate = (collect_count / analytics.total_views)
        
        # Find peak hour
        recent_views = pin.interactions.filter(
            interaction_type='view',
            created_at__gte=timezone.now() - timedelta(days=7)
        )
        if recent_views.exists():
            hour_counts = recent_views.extra(
                {'hour': "EXTRACT(HOUR FROM created_at)"}
            ).values('hour').annotate(count=Count('id')).order_by('-count')
            if hour_counts:
                analytics.peak_hour = hour_counts[0]['hour']
        
        analytics.save()
        return analytics


class Collection(models.Model):
    """
    Model representing a user collection of pins
    """
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='collections'
    )
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    is_public = models.BooleanField(default=True)
    primary_color = models.CharField(max_length=20, blank=True, null=True)
    
    # Store multiple cover images
    cover_image_urls = ArrayField(models.URLField(), default=list, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['created_at']),
            models.Index(fields=['is_public']),
        ]
    
    def __str__(self):
        return f"{self.name} by {self.owner.username}"
    
    @property
    def item_count(self):
        """Get count of pins in this collection.
        If the value has been annotated (e.g., via `.annotate(item_count=...)`),
        return the annotated value instead of executing an additional query.
        """
        return getattr(self, '_item_count', self.collection_pins.count())

    @item_count.setter
    def item_count(self, value):
        # Django will try to set this attribute when it is provided as an
        # annotation (`.annotate(item_count=...)`). Save it to a private
        # attribute so the getter can return it without hitting the database.
        self._item_count = value
    
    @property
    def last_updated(self):
        """Get the last update time (either collection update or when pins were added/removed)"""
        latest_pin = self.collection_pins.order_by('-added_at').first()
        if latest_pin and latest_pin.added_at > self.updated_at:
            return latest_pin.added_at
        return self.updated_at


class VirtualPin(models.Model):
    """
    Model representing a music track without a physical location.
    Used for private playlists and collections.
    """
    SERVICES = (
        ('spotify', 'Spotify'),
        ('apple_music', 'Apple Music'),
        ('soundcloud', 'SoundCloud')
    )
    
    MOOD_CHOICES = (
        ('happy', 'Happy'),
        ('chill', 'Chill'),
        ('energetic', 'Energetic'),
        ('sad', 'Sad'),
        ('romantic', 'Romantic'),
        ('focus', 'Focus'),
        ('party', 'Party'),
        ('workout', 'Workout'),
    )
    
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='virtual_pins'
    )
    location_name = models.CharField(max_length=200, default='Unknown Location', blank=True, null=True, help_text="Optional location name for virtual pins")
    title = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    caption = models.CharField(max_length=150, blank=True, null=True, help_text="Short note about this track")
    
    # Music data
    track_title = models.CharField(max_length=255)
    track_artist = models.CharField(max_length=255)
    album = models.CharField(max_length=255, blank=True, null=True)
    track_url = models.URLField()
    service = models.CharField(max_length=20, choices=SERVICES)
    artwork_url = models.URLField(blank=True, null=True)  # Album/track artwork URL
    duration_ms = models.PositiveIntegerField(null=True, blank=True)  # Track duration in milliseconds
    
    # Enhanced discovery fields
    genre = models.CharField(max_length=50, blank=True, null=True)
    mood = models.CharField(max_length=20, choices=MOOD_CHOICES, blank=True, null=True)
    tags = ArrayField(models.CharField(max_length=50), blank=True, null=True)
    
    # Customization (optional for virtual pins)
    skin = models.ForeignKey(
        PinSkin,
        on_delete=models.SET_DEFAULT,
        default=1,
        blank=True,
        null=True
    )
    
    # Vote tracking
    upvote_count = models.PositiveIntegerField(default=0)
    downvote_count = models.PositiveIntegerField(default=0)
    vote_score = models.IntegerField(default=0)  # upvotes - downvotes
    
    # Virtual pins are always private by default (no location = no public discovery)
    is_private = models.BooleanField(default=True)
    expiration_date = models.DateTimeField(blank=True, null=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['created_at']),
            models.Index(fields=['service']),
            models.Index(fields=['genre']),
            models.Index(fields=['mood']),
            models.Index(fields=['is_private']),
            models.Index(fields=['vote_score']),
        ]
    
    def __str__(self):
        return f"{self.title} by {self.owner.username} - {self.track_title} (Virtual)"
    
    @property
    def is_public(self):
        """Return True if virtual pin is public (inverse of is_private)"""
        return not self.is_private
    
    @property
    def is_virtual(self):
        """Return True for virtual pins"""
        return True
    
    @property
    def location(self):
        """Virtual pins have no location"""
        return None
    
    @property
    def aura_radius(self):
        """Virtual pins have no aura radius"""
        return None
    
    # Interaction methods similar to Pin
    @cached_property
    def total_interactions(self):
        """Get total number of interactions with this virtual pin"""
        return self.virtual_interactions.count()
    
    @cached_property
    def like_count(self):
        """Get number of likes for this virtual pin"""
        return self.virtual_interactions.filter(interaction_type='like').count()
    
    @cached_property
    def collect_count(self):
        """Get number of collects for this virtual pin"""
        return self.virtual_interactions.filter(interaction_type='collect').count()
    
    @cached_property
    def view_count(self):
        """Get number of views for this virtual pin"""
        return self.virtual_interactions.filter(interaction_type='view').count()
    
    def update_vote_counts(self):
        """Update vote counts from actual votes"""
        from votes.models import Vote
        vote_counts = Vote.objects.filter(virtual_pin=self).aggregate(
            upvotes=Count('id', filter=models.Q(value=1)),
            downvotes=Count('id', filter=models.Q(value=-1))
        )
        
        self.upvote_count = vote_counts['upvotes']
        self.downvote_count = vote_counts['downvotes']
        self.vote_score = self.upvote_count - self.downvote_count
        self.save(update_fields=['upvote_count', 'downvote_count', 'vote_score'])


class VirtualPinInteraction(models.Model):
    """
    Model for tracking user interactions with virtual pins
    (viewed, collected, liked, shared)
    """
    INTERACTION_TYPES = (
        ('view', 'Viewed'),
        ('collect', 'Collected'),
        ('like', 'Liked'),
        ('share', 'Shared')
    )
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='virtual_pin_interactions'
    )
    virtual_pin = models.ForeignKey(
        VirtualPin,
        on_delete=models.CASCADE,
        related_name='virtual_interactions'
    )
    interaction_type = models.CharField(max_length=20, choices=INTERACTION_TYPES)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('user', 'virtual_pin', 'interaction_type')
        indexes = [
            models.Index(fields=['interaction_type']),
            models.Index(fields=['created_at']),
        ]
        
    def __str__(self):
        return f"{self.user.username} {self.interaction_type} {self.virtual_pin.title} (Virtual)"


class CollectionPin(models.Model):
    """
    Model for linking pins and virtual pins to collections
    """
    collection = models.ForeignKey(
        Collection,
        on_delete=models.CASCADE,
        related_name='collection_pins'
    )
    # Either pin or virtual_pin should be set, but not both
    pin = models.ForeignKey(
        Pin,
        on_delete=models.CASCADE,
        related_name='pin_collections',
        blank=True,
        null=True
    )
    virtual_pin = models.ForeignKey(
        VirtualPin,
        on_delete=models.CASCADE,
        related_name='virtual_pin_collections',
        blank=True,
        null=True
    )
    added_at = models.DateTimeField(auto_now_add=True)

    # Track information for duplicate prevention based on song content
    track_title = models.CharField(max_length=255)
    track_artist = models.CharField(max_length=255)

    class Meta:
        indexes = [
            models.Index(fields=['added_at']),
            models.Index(fields=['track_title', 'track_artist']),
        ]
        constraints = [
            models.CheckConstraint(
                check=(
                    models.Q(pin__isnull=False, virtual_pin__isnull=True) |
                    models.Q(pin__isnull=True, virtual_pin__isnull=False)
                ),
                name='collection_pin_exactly_one_pin_type'
            ),
            # Prevent duplicate songs in the same collection based on track content
            models.UniqueConstraint(
                fields=['collection', 'track_title', 'track_artist'],
                name='unique_collection_song'
            )
        ]
        
    def __str__(self):
        item_title = self.pin.title if self.pin else self.virtual_pin.title
        return f"{item_title} in {self.collection.name}"
    
    @property
    def item(self):
        """Get the actual pin or virtual pin object"""
        return self.pin if self.pin else self.virtual_pin
    
    @property
    def is_virtual(self):
        """Check if this collection item is a virtual pin"""
        return self.virtual_pin is not None
    
    def clean(self):
        """Ensure exactly one of pin or virtual_pin is set"""
        from django.core.exceptions import ValidationError
        if not self.pin and not self.virtual_pin:
            raise ValidationError("Either pin or virtual_pin must be set")
        if self.pin and self.virtual_pin:
            raise ValidationError("Cannot set both pin and virtual_pin")
