from rest_framework import serializers
from rest_framework_gis.serializers import GeoFeatureModelSerializer
from .models import (
    Pin, PinInteraction, Collection, CollectionPin, VirtualPin, VirtualPinInteraction,
    PinSkin, UserSkin, ChallengeParticipation
)
from users.serializers import UserSerializer
from bopmaps.serializers import BaseSerializer, TimeStampedModelSerializer
from bopmaps.validators import MusicURLValidator
from django.utils import timezone
from django.contrib.gis.geos import Point
from django.contrib.gis.geos import Point
import logging
from challenges.models import WeeklyChallenge

logger = logging.getLogger('bopmaps')


class PinSkinSerializer(TimeStampedModelSerializer):
    """
    Serializer for the Pin<PERSON>kin model
    """
    is_equipped = serializers.SerializerMethodField()
    is_unlocked = serializers.SerializerMethodField()
    locked = serializers.SerializerMethodField()
    unlock_rule = serializers.SerializerMethodField()
    challenge_id = serializers.SerializerMethodField()
    achievement_id = serializers.SerializerMethodField()
    challenge_details = serializers.SerializerMethodField()
    achievement_details = serializers.SerializerMethodField()
    expiry_details = serializers.SerializerMethodField()
    is_expired = serializers.SerializerMethodField()
    is_limited_time = serializers.SerializerMethodField()
    time_remaining = serializers.SerializerMethodField()
    metadata = serializers.JSONField(read_only=True)
    
    class Meta(TimeStampedModelSerializer.Meta):
        model = PinSkin
        fields = [
            'id', 'name', 'slug', 'image', 'description', 'skin_type', 
            'artist', 'is_premium', 'created_at', 'is_equipped', 'is_unlocked',
            'locked', 'unlock_rule', 'challenge_id', 'achievement_id',
            'challenge_details', 'achievement_details', 'expiry_date',
            'expiry_details', 'is_expired', 'is_limited_time', 'time_remaining',
            'metadata'
        ]
    
    def to_representation(self, instance):
        """Override to handle potential None values safely"""
        if instance is None:
            return None
            
        try:
            return super().to_representation(instance)
        except AttributeError as e:
            logger.error(f"Error in PinSkinSerializer.to_representation: {str(e)}")
            # Create a representation with minimal data to avoid breaking the API
            return {
                'id': instance.id if hasattr(instance, 'id') else None,
                'name': instance.name if hasattr(instance, 'name') else 'Unknown skin',
                'is_equipped': False,
                'is_unlocked': False,
                'locked': True,
                'error': 'Failed to fully serialize skin due to missing data'
            }
        
    def get_is_equipped(self, obj):
        """
        Check if the current user has this skin equipped.
        """
        request = self.context.get('request')
        if not request:
            return False
            
        user = getattr(request, 'user', None)
        if not user or not user.is_authenticated:
            return False
            
        return user.current_pin_skin_id == obj.id
        
    def get_is_unlocked(self, obj):
        """
        Check if the current user has unlocked this skin.
        """
        request = self.context.get('request')
        if not request:
            return False
            
        user = getattr(request, 'user', None)
        if not user or not user.is_authenticated:
            return False
            
        # Check UserSkin table
        return UserSkin.objects.filter(user=user, skin=obj).exists()
    
    def get_locked(self, obj):
        """Return True if skin is locked for the current user"""
        return not self.get_is_unlocked(obj)
    
    def get_unlock_rule(self, obj):
        """Get the unlock rule for this skin"""
        if obj.skin_type == PinSkin.HOUSE:
            if obj.is_premium:
                return "PREMIUM_SUBSCRIPTION"
            return "ALWAYS_AVAILABLE"
        elif obj.challenge:
            metadata = obj.metadata or {}
            unlock_type = metadata.get('unlock_type', 'PARTICIPATE')
            if unlock_type == 'TOP_N':
                return f"TOP_{metadata.get('n', 3)}_CHALLENGE_WINNERS"
            return "COMPLETE_WEEKLY_CHALLENGE"
        elif obj.achievement:
            metadata = obj.metadata or {}
            unlock_type = metadata.get('unlock_type', 'ACHIEVEMENT')
            return f"COMPLETE_ACHIEVEMENT_{obj.achievement.challenge_id.upper()}"
        return "UNKNOWN"
    
    def get_challenge_id(self, obj):
        """Get the challenge ID if this skin is tied to a challenge"""
        return obj.challenge.id if obj.challenge else None
    
    def get_achievement_id(self, obj):
        """Get the achievement ID if this skin is tied to an achievement"""
        return obj.achievement.id if obj.achievement else None
    
    def get_challenge_details(self, obj):
        """Get detailed challenge information if skin is tied to a challenge"""
        if not obj.challenge:
            return None
        
        return {
            'id': obj.challenge.id,
            'title': obj.challenge.title,
            'description': obj.challenge.description,
            'start_date': obj.challenge.start_date,
            'end_date': obj.challenge.end_date,
            'is_active': obj.challenge.is_active,
            'is_ongoing': obj.challenge.is_ongoing,
            'has_ended': obj.challenge.has_ended,
            'max_participants': obj.challenge.max_participants
        }
    
    def get_achievement_details(self, obj):
        """Get detailed achievement information if skin is tied to an achievement"""
        if not obj.achievement:
            return None
        
        return {
            'id': obj.achievement.id,
            'challenge_id': obj.achievement.challenge_id,
            'name': obj.achievement.name,
            'description': obj.achievement.description,
            'type': obj.achievement.type,
            'tier': obj.achievement.tier,
            'xp_reward': obj.achievement.xp_reward,
            'icon_name': obj.achievement.icon_name,
            'primary_color': obj.achievement.primary_color,
            'background_color': obj.achievement.background_color,
            'is_secret': obj.achievement.is_secret
        }
    
    def get_expiry_details(self, obj):
        """Get expiry information for limited-time skins"""
        if not obj.expiry_date:
            return None
        
        return {
            'expiry_date': obj.expiry_date,
            'is_expired': obj.is_expired,
            'time_remaining_seconds': obj.time_remaining.total_seconds() if obj.time_remaining else 0
        }
    
    def get_is_expired(self, obj):
        """Check if this skin has expired"""
        return obj.is_expired
    
    def get_is_limited_time(self, obj):
        """Check if this is a limited-time skin"""
        return obj.is_limited_time
    
    def get_time_remaining(self, obj):
        """Get time remaining in seconds until expiry"""
        if obj.time_remaining:
            return obj.time_remaining.total_seconds()
        return None


class UserSkinSerializer(TimeStampedModelSerializer):
    """
    Serializer for UserSkin model
    """
    skin_details = PinSkinSerializer(source='skin', read_only=True)
    
    class Meta(TimeStampedModelSerializer.Meta):
        model = UserSkin
        fields = ['id', 'user', 'skin', 'skin_details', 'unlocked_at', 'created_at']
        read_only_fields = ['id', 'user', 'unlocked_at', 'created_at']


class WeeklyChallengeSerializer(TimeStampedModelSerializer):
    """
    Serializer for WeeklyChallenge model
    """
    is_ongoing = serializers.ReadOnlyField()
    has_ended = serializers.ReadOnlyField()
    participant_count = serializers.SerializerMethodField()
    user_participated = serializers.SerializerMethodField()
    
    class Meta(TimeStampedModelSerializer.Meta):
        model = WeeklyChallenge
        fields = [
            'id', 'title', 'description', 'start_date', 'end_date', 
            'is_active', 'max_participants', 'created_at', 'is_ongoing',
            'has_ended', 'participant_count', 'user_participated'
        ]
    
    def get_participant_count(self, obj):
        """Get the number of participants in this challenge"""
        return obj.participations.count()
    
    def get_user_participated(self, obj):
        """Check if current user has participated in this challenge"""
        request = self.context.get('request')
        if not request:
            return False
            
        user = getattr(request, 'user', None)
        if not user or not user.is_authenticated:
            return False
            
        return obj.participations.filter(user=user).exists()


class ChallengeParticipationSerializer(TimeStampedModelSerializer):
    """
    Serializer for ChallengeParticipation model
    """
    challenge_details = WeeklyChallengeSerializer(source='challenge', read_only=True)
    pin_details = serializers.SerializerMethodField()
    
    class Meta(TimeStampedModelSerializer.Meta):
        model = ChallengeParticipation
        fields = [
            'id', 'user', 'challenge', 'challenge_details', 'pin', 'virtual_pin',
            'pin_details', 'vote_score', 'participated_at', 'created_at'
        ]
        read_only_fields = ['id', 'user', 'vote_score', 'participated_at', 'created_at']
    
    def get_pin_details(self, obj):
        """Get details of the pin or virtual pin used in this participation"""
        if obj.pin:
            from .serializers import PinSerializer
            return PinSerializer(obj.pin, context=self.context).data
        elif obj.virtual_pin:
            from .serializers import VirtualPinSerializer
            return VirtualPinSerializer(obj.virtual_pin, context=self.context).data
        return None


class PinSerializer(TimeStampedModelSerializer):
    """
    Serializer for Pin model
    """
    owner = UserSerializer(read_only=True)
    skin_details = PinSkinSerializer(source='skin', read_only=True)
    interaction_count = serializers.SerializerMethodField()
    distance = serializers.SerializerMethodField()
    has_expired = serializers.SerializerMethodField()
    upvote_count = serializers.SerializerMethodField()
    downvote_count = serializers.SerializerMethodField()
    
    class Meta(TimeStampedModelSerializer.Meta):
        model = Pin
        fields = [
            'id', 'owner', 'location', 'location_name', 'title', 'description', 'caption',
            'track_title', 'track_artist', 'album', 'track_url',
            'service', 'skin', 'skin_details', 'aura_radius',
            'is_private', 'expiration_date', 'created_at', 'updated_at',
            'interaction_count', 'distance', 'has_expired', 'artwork_url', 'duration_ms',
            'upvote_count', 'downvote_count', 'vote_score', 'tags', 'genre', 'ar_position_x',
            'ar_position_y', 'ar_position_z', 'altitude'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'owner', 'skin_details', 'distance', 'has_expired']
        extra_kwargs = {
            'track_url': {'validators': [MusicURLValidator()]},
            'expiration_date': {'required': False, 'allow_null': True},
        }
    
    def to_representation(self, instance):
        """Override to handle potential None values safely"""
        try:
            return super().to_representation(instance)
        except AttributeError as e:
            logger.error(f"Error in PinSerializer.to_representation: {str(e)}")
            # Create a representation with minimal data to avoid breaking the API
            data = {
                'id': instance.id if hasattr(instance, 'id') else None,
                'title': instance.title if hasattr(instance, 'title') else 'Unknown pin',
                'error': 'Failed to fully serialize pin due to missing data'
            }
            return data
    
    def to_representation(self, instance):
        """Override to handle potential None values safely"""
        try:
            return super().to_representation(instance)
        except AttributeError as e:
            logger.error(f"Error in PinSerializer.to_representation: {str(e)}")
            # Create a representation with minimal data to avoid breaking the API
            data = {
                'id': instance.id if hasattr(instance, 'id') else None,
                'title': instance.title if hasattr(instance, 'title') else 'Unknown pin',
                'error': 'Failed to fully serialize pin due to missing data'
            }
            return data
    
    def get_interaction_count(self, obj):
        """Get count of different interactions for this pin"""
        counts = {}
        for interaction_type, _ in PinInteraction.INTERACTION_TYPES:
            counts[interaction_type] = obj.interactions.filter(
                interaction_type=interaction_type
            ).count()
        return counts
    
    def get_distance(self, obj):
        """Get distance if annotated by the query"""
        if hasattr(obj, 'distance'):
            # Convert to meters
            return float(obj.distance.m)
        return None
    
    def get_has_expired(self, obj):
        """Check if pin has expired"""
        if obj.expiration_date:
            return obj.expiration_date < timezone.now()
        return False
    
    def get_upvote_count(self, obj):
        """Get number of upvotes for this pin"""
        from votes.models import Vote
        return Vote.objects.filter(pin=obj, value=1).count()
    
    def get_downvote_count(self, obj):
        """Get number of downvotes for this pin"""
        from votes.models import Vote
        return Vote.objects.filter(pin=obj, value=-1).count()
    
    def validate(self, data):
        """
        Validate the pin data.
        - Ensure the track_url matches the service type
        - Ensure aura_radius is within limits
        """
        service = data.get('service')
        track_url = data.get('track_url')
        
        if service and track_url:
            # Use our service-specific validator
            validator = MusicURLValidator(service=service)
            try:
                validator(track_url)
            except serializers.ValidationError as e:
                raise serializers.ValidationError({'track_url': e.detail})
                
        # Validate aura_radius limits
        aura_radius = data.get('aura_radius')
        if aura_radius is not None:
            if aura_radius < 10:
                raise serializers.ValidationError({'aura_radius': 'Aura radius must be at least 10 meters.'})
            elif aura_radius > 1000:
                raise serializers.ValidationError({'aura_radius': 'Aura radius cannot exceed 1000 meters.'})
                
        return data
        
    def create(self, validated_data):
        """Ensure owner is set to request user when creating a pin"""
        validated_data['owner'] = self.context.get('request').user
        location_data = validated_data.pop('location', None)
        if location_data and isinstance(location_data, dict):
            coordinates = location_data.get('coordinates')
            if coordinates and len(coordinates) == 2:
                validated_data['location'] = Point(coordinates[0], coordinates[1], srid=4326)
            else:
                raise serializers.ValidationError({"location": "Invalid coordinates."}) 
        elif location_data: # If it's already a Point object or other, pass through or handle as error
             validated_data['location'] = location_data # Assuming it might be a Point already from some contexts
        else: # Location is required
            raise serializers.ValidationError({"location": "Location is required."})

        location_data = validated_data.pop('location', None)
        if location_data and isinstance(location_data, dict):
            coordinates = location_data.get('coordinates')
            if coordinates and len(coordinates) == 2:
                validated_data['location'] = Point(coordinates[0], coordinates[1], srid=4326)
            else:
                raise serializers.ValidationError({"location": "Invalid coordinates."}) 
        elif location_data: # If it's already a Point object or other, pass through or handle as error
             validated_data['location'] = location_data # Assuming it might be a Point already from some contexts
        else: # Location is required
            raise serializers.ValidationError({"location": "Location is required."})

        try:
            instance = super().create(validated_data)
            logger.info(f"Pin created by {instance.owner.username}: {instance.title}")
            return instance
        except Exception as e:
            logger.error(f"Error creating pin: {str(e)}")
            raise

    def update(self, instance, validated_data):
        location_data = validated_data.pop('location', None)
        if location_data and isinstance(location_data, dict):
            coordinates = location_data.get('coordinates')
            if coordinates and len(coordinates) == 2:
                instance.location = Point(coordinates[0], coordinates[1], srid=4326)
            else:
                raise serializers.ValidationError({"location": "Invalid coordinates."}) 
        elif location_data: # If it's already a Point object or other, pass through or handle as error
            instance.location = location_data # Assuming it might be a Point already from some contexts

        return super().update(instance, validated_data)

    def update(self, instance, validated_data):
        location_data = validated_data.pop('location', None)
        if location_data and isinstance(location_data, dict):
            coordinates = location_data.get('coordinates')
            if coordinates and len(coordinates) == 2:
                instance.location = Point(coordinates[0], coordinates[1], srid=4326)
            else:
                raise serializers.ValidationError({"location": "Invalid coordinates."}) 
        elif location_data: # If it's already a Point object or other, pass through or handle as error
            instance.location = location_data # Assuming it might be a Point already from some contexts

        return super().update(instance, validated_data)


class PinGeoSerializer(GeoFeatureModelSerializer):
    """
    GeoJSON serializer for Pin model to display on map
    """
    owner_name = serializers.CharField(source='owner.username', read_only=True)
    like_count = serializers.SerializerMethodField()
    collect_count = serializers.SerializerMethodField()
    distance = serializers.SerializerMethodField()
    has_expired = serializers.SerializerMethodField()
    upvote_count = serializers.SerializerMethodField()
    downvote_count = serializers.SerializerMethodField()
    vote_score = serializers.SerializerMethodField()
    
    class Meta:
        model = Pin
        geo_field = 'location'
        fields = [
            'id', 'owner_name', 'location_name', 'title', 'track_title', 'caption',
            'track_artist', 'service', 'like_count',
            'collect_count', 'created_at', 'distance', 'has_expired',
            'aura_radius', 'artwork_url', 'duration_ms', 'upvote_count', 'downvote_count', 'vote_score'
        ]
    
    def to_representation(self, instance):
        """Override to handle potential None values safely"""
        try:
            return super().to_representation(instance)
        except AttributeError as e:
            logger.error(f"Error in PinGeoSerializer.to_representation: {str(e)}")
            # Create a minimal GeoJSON feature to avoid breaking the API
            return {
                "type": "Feature",
                "geometry": {"type": "Point", "coordinates": [0, 0]},
                "properties": {
                    "id": instance.id if hasattr(instance, 'id') else None,
                    "title": instance.title if hasattr(instance, 'title') else "Unknown pin",
                    "error": "Failed to fully serialize pin"
                }
            }
    
    def get_like_count(self, obj):
        return obj.interactions.filter(interaction_type='like').count()
    
    def get_collect_count(self, obj):
        return obj.interactions.filter(interaction_type='collect').count()
        
    def get_distance(self, obj):
        """Get distance if annotated by the query"""
        if hasattr(obj, 'distance'):
            # Convert to meters
            return float(obj.distance.m)
        return None
    
    def get_has_expired(self, obj):
        """Check if pin has expired"""
        if obj.expiration_date:
            return obj.expiration_date < timezone.now()
        return False
    
    def get_upvote_count(self, obj):
        """Get number of upvotes for this pin"""
        return getattr(obj, 'upvote_count', 0)
    
    def get_downvote_count(self, obj):
        """Get number of downvotes for this pin"""
        return getattr(obj, 'downvote_count', 0)
    
    def get_vote_score(self, obj):
        """Get vote score (upvotes - downvotes) for this pin"""
        return getattr(obj, 'vote_score', 0)


class PinInteractionSerializer(BaseSerializer):
    """
    Serializer for PinInteraction model
    """
    user = serializers.PrimaryKeyRelatedField(read_only=True)
    
    class Meta:
        model = PinInteraction
        fields = ['id', 'user', 'pin', 'interaction_type', 'created_at']
        read_only_fields = ['id', 'created_at', 'user']
    
    def to_representation(self, instance):
        """Override to handle potential None values safely"""
        try:
            return super().to_representation(instance)
        except AttributeError as e:
            logger.error(f"Error in PinInteractionSerializer.to_representation: {str(e)}")
            # Create a minimal representation to avoid breaking the API
            return {
                'id': instance.id if hasattr(instance, 'id') else None,
                'interaction_type': instance.interaction_type if hasattr(instance, 'interaction_type') else 'unknown',
                'error': 'Failed to fully serialize interaction'
            }
    
    def to_representation(self, instance):
        """Override to handle potential None values safely"""
        try:
            return super().to_representation(instance)
        except AttributeError as e:
            logger.error(f"Error in PinInteractionSerializer.to_representation: {str(e)}")
            # Create a minimal representation to avoid breaking the API
            return {
                'id': instance.id if hasattr(instance, 'id') else None,
                'interaction_type': instance.interaction_type if hasattr(instance, 'interaction_type') else 'unknown',
                'error': 'Failed to fully serialize interaction'
            }
    
    def create(self, validated_data):
        """Ensure user is set to request user when creating an interaction"""
        validated_data['user'] = self.context.get('request').user
        
        try:
            instance = super().create(validated_data)
            logger.info(f"Pin interaction: {instance.user.username} {instance.interaction_type} pin {instance.pin.id}")
            return instance
        except Exception as e:
            logger.error(f"Error creating pin interaction: {str(e)}")
            raise 

class CollectionPinSerializer(serializers.ModelSerializer):
    """Serializer for CollectionPin model"""
    pin_details = serializers.SerializerMethodField()
    is_virtual = serializers.SerializerMethodField()
    
    class Meta:
        model = CollectionPin
        fields = ['id', 'pin', 'virtual_pin', 'pin_details', 'is_virtual', 'added_at']
        read_only_fields = ['added_at', 'is_virtual']
    
    def get_pin_details(self, obj):
        """Get details of either pin or virtual pin"""
        if obj.pin:
            return PinSerializer(obj.pin).data
        elif obj.virtual_pin:
            return VirtualPinSerializer(obj.virtual_pin).data
        return None
    
    def get_is_virtual(self, obj):
        """Check if this collection item is a virtual pin"""
        return obj.is_virtual
    
    def validate(self, data):
        """Ensure exactly one of pin or virtual_pin is provided"""
        pin = data.get('pin')
        virtual_pin = data.get('virtual_pin')
        
        if not pin and not virtual_pin:
            raise serializers.ValidationError("Either pin or virtual_pin must be provided")
        if pin and virtual_pin:
            raise serializers.ValidationError("Cannot provide both pin and virtual_pin")
        
        return data

class CollectionSerializer(serializers.ModelSerializer):
    """Serializer for Collection model"""
    owner_name = serializers.CharField(source='owner.username', read_only=True)
    item_count = serializers.IntegerField(read_only=True)
    last_updated = serializers.DateTimeField(read_only=True)
    
    class Meta:
        model = Collection
        fields = [
            'id', 'name', 'description', 'is_public', 'primary_color',
            'cover_image_urls', 'created_at', 'updated_at', 
            'owner', 'owner_name', 'item_count', 'last_updated'
        ]
        read_only_fields = ['created_at', 'updated_at', 'owner']
    
    def create(self, validated_data):
        # Set the owner to the current user
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)

class CollectionDetailSerializer(CollectionSerializer):
    """Detailed Collection serializer with pins"""
    pins = serializers.SerializerMethodField()
    
    class Meta(CollectionSerializer.Meta):
        fields = CollectionSerializer.Meta.fields + ['pins']
    
    def get_pins(self, obj):
        collection_pins = obj.collection_pins.all().order_by('-added_at')
        return CollectionPinSerializer(collection_pins, many=True).data 

class VirtualPinSerializer(TimeStampedModelSerializer):
    """
    Serializer for VirtualPin model
    """
    owner = UserSerializer(read_only=True)
    skin_details = PinSkinSerializer(source='skin', read_only=True)
    interaction_count = serializers.SerializerMethodField()
    has_expired = serializers.SerializerMethodField()
    is_virtual = serializers.SerializerMethodField()
    
    class Meta(TimeStampedModelSerializer.Meta):
        model = VirtualPin
        fields = [
            'id', 'owner', 'location_name', 'title', 'description', 'caption',
            'track_title', 'track_artist', 'album', 'track_url',
            'service', 'skin', 'skin_details', 'is_private', 
            'expiration_date', 'created_at', 'updated_at',
            'interaction_count', 'has_expired', 'is_virtual', 'artwork_url', 'duration_ms'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'owner', 'skin_details', 'is_virtual']
        extra_kwargs = {
            'track_url': {'validators': [MusicURLValidator()]},
            'expiration_date': {'required': False, 'allow_null': True},
        }
    
    def to_representation(self, instance):
        """Override to handle potential None values safely"""
        try:
            return super().to_representation(instance)
        except AttributeError as e:
            logger.error(f"Error in VirtualPinSerializer.to_representation: {str(e)}")
            # Create a representation with minimal data to avoid breaking the API
            data = {
                'id': instance.id if hasattr(instance, 'id') else None,
                'title': instance.title if hasattr(instance, 'title') else 'Unknown virtual pin',
                'is_virtual': True,
                'error': 'Failed to fully serialize virtual pin due to missing data'
            }
            return data
    
    def get_interaction_count(self, obj):
        """Get count of different interactions for this virtual pin"""
        counts = {}
        for interaction_type, _ in VirtualPinInteraction.INTERACTION_TYPES:
            counts[interaction_type] = obj.virtual_interactions.filter(
                interaction_type=interaction_type
            ).count()
        return counts
    
    def get_has_expired(self, obj):
        """Check if virtual pin has expired"""
        if obj.expiration_date:
            return obj.expiration_date < timezone.now()
        return False
    
    def get_is_virtual(self, obj):
        """Always return True for virtual pins"""
        return True
    
    def validate(self, data):
        """
        Validate the virtual pin data.
        - Ensure the track_url matches the service type
        """
        service = data.get('service')
        track_url = data.get('track_url')
        
        if service and track_url:
            # Use our service-specific validator
            validator = MusicURLValidator(service=service)
            try:
                validator(track_url)
            except serializers.ValidationError as e:
                raise serializers.ValidationError({'track_url': e.detail})
                
        return data
        
    def create(self, validated_data):
        """Ensure owner is set to request user when creating a virtual pin"""
        validated_data['owner'] = self.context.get('request').user
        
        try:
            instance = super().create(validated_data)
            logger.info(f"Virtual pin created by {instance.owner.username}: {instance.title}")
            return instance
        except Exception as e:
            logger.error(f"Error creating virtual pin: {str(e)}")
            raise


class VirtualPinInteractionSerializer(BaseSerializer):
    """
    Serializer for VirtualPinInteraction model
    """
    user = serializers.PrimaryKeyRelatedField(read_only=True)
    
    class Meta:
        model = VirtualPinInteraction
        fields = ['id', 'user', 'virtual_pin', 'interaction_type', 'created_at']
        read_only_fields = ['id', 'created_at', 'user']
    
    def to_representation(self, instance):
        """Override to handle potential None values safely"""
        try:
            return super().to_representation(instance)
        except AttributeError as e:
            logger.error(f"Error in VirtualPinInteractionSerializer.to_representation: {str(e)}")
            # Create a minimal representation to avoid breaking the API
            return {
                'id': instance.id if hasattr(instance, 'id') else None,
                'interaction_type': instance.interaction_type if hasattr(instance, 'interaction_type') else 'unknown',
                'is_virtual': True,
                'error': 'Failed to fully serialize virtual interaction'
            }
    
    def create(self, validated_data):
        """Ensure user is set to request user when creating an interaction"""
        validated_data['user'] = self.context.get('request').user
        
        try:
            instance = super().create(validated_data)
            logger.info(f"Virtual pin interaction: {instance.user.username} {instance.interaction_type} virtual pin {instance.virtual_pin.id}")
            return instance
        except Exception as e:
            logger.error(f"Error creating virtual pin interaction: {str(e)}")
            raise 