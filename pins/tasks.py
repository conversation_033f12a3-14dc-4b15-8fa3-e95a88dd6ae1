from celery import shared_task
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger('bopmaps')


@shared_task
def close_completed_challenges():
    """
    Daily task to mark expired challenges as inactive, compute winners, 
    grant skins, and send notifications.
    """
    from .models import WeeklyChallenge, ChallengeParticipation, <PERSON><PERSON><PERSON><PERSON>, <PERSON>n<PERSON><PERSON>
    
    try:
        # Find challenges that have ended but are still active
        ended_challenges = WeeklyChallenge.objects.filter(
            is_active=True,
            end_date__lt=timezone.now()
        )
        
        for challenge in ended_challenges:
            logger.info(f"Processing ended challenge: {challenge.title}")
            
            # Mark as inactive
            challenge.is_active = False
            challenge.save()
            
            # Process rewards for this challenge
            reward_skins = challenge.reward_skins.all()
            
            for skin in reward_skins:
                metadata = skin.metadata or {}
                unlock_type = metadata.get('unlock_type', 'PARTICIPATE')
                
                if unlock_type == 'TOP_N':
                    # Grant skin to top N participants
                    n = metadata.get('n', 3)
                    top_participants = ChallengeParticipation.objects.filter(
                        challenge=challenge
                    ).order_by('-vote_score')[:n]
                    
                    for participation in top_participants:
                        user_skin, created = UserSkin.objects.get_or_create(
                            user=participation.user,
                            skin=skin
                        )
                        if created:
                            logger.info(f"Granted TOP_N skin {skin.name} to {participation.user.username}")
                            # Here you could send notifications
                            
                # For PARTICIPATE type, they should already be auto-unlocked
                # when users joined the challenge
                
            logger.info(f"Completed processing challenge: {challenge.title}")
            
    except Exception as e:
        logger.error(f"Error in close_completed_challenges: {str(e)}")
        raise


@shared_task
def auto_unlock_participation_skin(challenge_id, user_id):
    """
    Realtime task triggered when user participates in a challenge.
    Grants participation skins immediately.
    """
    from .models import WeeklyChallenge, UserSkin, PinSkin
    from users.models import User
    
    try:
        challenge = WeeklyChallenge.objects.get(id=challenge_id)
        user = User.objects.get(id=user_id)
        
        # Check all skins for this challenge
        for skin in challenge.reward_skins.all():
            metadata = skin.metadata or {}
            unlock_type = metadata.get('unlock_type', 'PARTICIPATE')
            
            if unlock_type == 'PARTICIPATE':
                user_skin, created = UserSkin.objects.get_or_create(
                    user=user,
                    skin=skin
                )
                if created:
                    logger.info(f"Auto-unlocked participation skin {skin.name} for {user.username}")
                    # Here you could send push notifications
                    
    except Exception as e:
        logger.error(f"Error in auto_unlock_participation_skin: {str(e)}")
        raise


@shared_task
def cleanup_inactive_challenges():
    """
    Weekly task to clean up old inactive challenges and related data.
    """
    from .models import WeeklyChallenge
    
    try:
        # Mark challenges older than 30 days as inactive if not already
        old_date = timezone.now() - timedelta(days=30)
        old_challenges = WeeklyChallenge.objects.filter(
            end_date__lt=old_date,
            is_active=True
        )
        
        count = old_challenges.update(is_active=False)
        logger.info(f"Marked {count} old challenges as inactive")
        
    except Exception as e:
        logger.error(f"Error in cleanup_inactive_challenges: {str(e)}")
        raise


@shared_task
def generate_weekly_house_skin():
    """
    Optional weekly task to auto-generate new house skins using AI.
    This is a stretch feature mentioned in the requirements.
    """
    from .models import PinSkin, WeeklyChallenge
    
    try:
        # This is a placeholder for AI-generated skin creation
        # In a real implementation, this would:
        # 1. Call OpenAI/FLUX API to generate a new skin image
        # 2. Create a new PinSkin with the generated image
        # 3. Optionally tie it to an upcoming weekly challenge
        
        logger.info("Auto-generation of house skins is not implemented yet")
        # Example implementation:
        # 
        # new_skin = PinSkin.objects.create(
        #     name=f"Generated Skin {timezone.now().strftime('%Y%m%d')}",
        #     slug=f"generated-{timezone.now().strftime('%Y%m%d')}",
        #     image="path/to/generated/image.png",
        #     skin_type=PinSkin.HOUSE,
        #     is_premium=False,
        #     description="Auto-generated weekly house skin"
        # )
        
    except Exception as e:
        logger.error(f"Error in generate_weekly_house_skin: {str(e)}")
        raise 