from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    PinViewSet, PinInteractionViewSet, CollectionViewSet, VirtualPinViewSet,
    PinSkinViewSet, UserSkinViewSet, WeeklyChallengeViewSet, ChallengeParticipationViewSet
)

# Create a router for viewsets
router = DefaultRouter()
# Register PinInteractionViewSet first with a specific basename
router.register(r'interactions', PinInteractionViewSet, basename='pininteraction')
# Register VirtualPinViewSet
router.register(r'virtual-pins', VirtualPinViewSet, basename='virtualpin')
# Register CollectionViewSet
router.register(r'collections', CollectionViewSet, basename='collection')
# Register new skin system viewsets
router.register(r'skins', PinSkinViewSet, basename='skins')
router.register(r'user-skins', UserSkinViewSet, basename='user-skins')
router.register(r'challenges', WeeklyChallengeViewSet, basename='challenges')
router.register(r'challenge-participations', ChallengeParticipationViewSet, basename='challenge-participations')
# Register PinViewSet after with an explicit basename
router.register(r'', PinViewSet, basename='pin')

urlpatterns = [
    # ViewSet routes
    path('', include(router.urls)),
    
    # Additional endpoints are handled by the viewset's actions
    # PinSkinViewSet: /skins/available/, /skins/featured/, /skins/limited/, /skins/{id}/claim/, /skins/{id}/equip/, /skins/unlocked/
    # WeeklyChallengeViewSet: /challenges/active/, /challenges/upcoming/
    # PinViewSet: /nearby/, /nearby_fresh/, /nearby_friends/, /trending/, /list_map/, /my_pins/, /collected/, /{id}/view/, /{id}/like/, /{id}/collect/, /{id}/share/, /{id}/map_details/, /{id}/vote_info/, /{id}/vote_post/, /{id}/comment_info/, /{id}/comment_post/, /{id}/interactions/, /{id}/likes/, /{id}/views/, /{id}/engagement/
] 