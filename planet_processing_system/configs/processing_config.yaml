# Planet Processing Configuration
# Complete settings for processing planet-wide vector tiles

# Processing Parameters
MAX_ZOOM=14
MIN_ZOOM=0
BUILDING_ENHANCEMENT=true
INCLUDE_3D_BUILDINGS=true
PARALLEL_PROCESSING=true

# Performance Settings
PARALLEL_UPLOADS=20
CHUNK_SIZE=10000
COMPRESSION=true
OPTIMIZE_STORAGE=true

# B2 Configuration
B2_BUCKET="bopmaps-prod-tiles-5911027e"
B2_REMOTE="b2tiles"
B2_ENDPOINT="https://s3.us-east-005.backblazeb2.com"
B2_REGION="us-east-005"

# Database Settings
DB_CONNECTIONS=8
DB_WORK_MEM="1GB"
DB_SHARED_BUFFERS="4GB"
DB_EFFECTIVE_CACHE_SIZE="8GB"

# Docker Settings
DOCKER_MEMORY="16GB"
DOCKER_CPUS="8"

# Feature Flags
ENABLE_3D_BUILDINGS=true
ENABLE_ENHANCED_ROADS=true
ENABLE_LANDCOVER=true
ENABLE_WATER_FEATURES=true
ENABLE_BOUNDARIES=true
ENABLE_LABELS=true
ENABLE_POIS=true

# Style Configuration
DARK_THEME=true
LIGHT_THEME=true
SATELLITE_THEME=true

# Monitoring
ENABLE_LOGGING=true
LOG_LEVEL="INFO"
PROGRESS_REPORTING=true
HEALTH_CHECKS=true

# Cleanup Settings
AUTO_CLEANUP=true
KEEP_TEMP_FILES=false
KEEP_LOGS_DAYS=30

# Export environment variables
export MAX_ZOOM MIN_ZOOM BUILDING_ENHANCEMENT INCLUDE_3D_BUILDINGS PARALLEL_PROCESSING
export B2_BUCKET B2_REMOTE B2_ENDPOINT B2_REGION
export ENABLE_3D_BUILDINGS ENABLE_ENHANCED_ROADS ENABLE_LANDCOVER ENABLE_WATER_FEATURES
export ENABLE_BOUNDARIES ENABLE_LABELS ENABLE_POIS 