#!/bin/bash

################################################################################
# 🌍 Full Planet Processing Script
# 
# Processes the entire planet with all features including:
# - Enhanced 3D buildings with realistic heights
# - All map layers (water, landcover, transportation, etc.)
# - Optimized for Backblaze B2 storage
# - Production-ready vector tiles
################################################################################

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SYSTEM_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$SYSTEM_DIR")"
TILE_DIR="$PROJECT_ROOT/tile_processing/openmaptiles"
PLANET_FILE="$TILE_DIR/data/planet.osm.pbf"
CONFIG_FILE="$SYSTEM_DIR/configs/processing_config.yaml"
LOG_FILE="$SYSTEM_DIR/logs/planet_processing.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Create logs directory
mkdir -p "$SYSTEM_DIR/logs"

# Logging functions
log() {
    echo -e "${GREEN}$(date '+%Y-%m-%d %H:%M:%S')${NC} - $1" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}$(date '+%Y-%m-%d %H:%M:%S') WARNING${NC} - $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}$(date '+%Y-%m-%d %H:%M:%S') ERROR${NC} - $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}$(date '+%Y-%m-%d %H:%M:%S') INFO${NC} - $1" | tee -a "$LOG_FILE"
}

# Load configuration
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        source "$CONFIG_FILE"
    else
        warn "Configuration file not found, using defaults"
        MAX_ZOOM=14
        BUILDING_ENHANCEMENT=true
        INCLUDE_3D_BUILDINGS=true
        PARALLEL_PROCESSING=true
    fi
}

# Check system requirements
check_system() {
    log "🔍 System Requirements Check"
    
    # Check planet file exists
    if [[ ! -f "$PLANET_FILE" ]]; then
        error "❌ Planet file not found: $PLANET_FILE"
        info "Please download planet.osm.pbf first"
        exit 1
    fi
    
    local planet_size=$(du -h "$PLANET_FILE" | cut -f1)
    log "📊 Planet file size: $planet_size"
    
    # Check available disk space
    local available=$(df -h "$PROJECT_ROOT" | awk 'NR==2 {print $4}' | sed 's/Gi//')
    log "💾 Available disk space: ${available}GB"
    
    if [[ $available -lt 100 ]]; then
        error "❌ Insufficient disk space (need at least 100GB for planet processing)"
        info "Consider processing by regions instead"
        exit 1
    fi
    
    # Check Docker
    if ! docker ps &>/dev/null; then
        error "❌ Docker not running"
        exit 1
    fi
    
    # Check required tools
    local required_tools=("python3" "sqlite3" "rclone")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &>/dev/null; then
            error "❌ Required tool missing: $tool"
            exit 1
        fi
    done
    
    log "✅ System requirements satisfied"
}

# Enhanced building processing setup
setup_building_enhancement() {
    log "🏗️ Setting up enhanced building processing"
    
    # Create enhanced building SQL
    cat > "$TILE_DIR/sql/enhanced_buildings.sql" << 'EOF'
-- Enhanced 3D Building Processing
-- This creates realistic building heights and materials

-- Update building table with enhanced height calculation
UPDATE building_polygon SET
    render_height = CASE
        -- Use explicit height if available
        WHEN height IS NOT NULL AND height > 0 THEN GREATEST(height, 3)
        -- Calculate from levels
        WHEN levels IS NOT NULL AND levels > 0 THEN GREATEST(levels * 3.66, 5)
        -- Special building types
        WHEN building IN ('skyscraper', 'tower') THEN 50
        WHEN building IN ('office', 'commercial', 'retail') THEN 15
        WHEN building IN ('industrial', 'warehouse') THEN 12
        WHEN building IN ('hospital', 'school', 'university') THEN 20
        WHEN building IN ('church', 'cathedral', 'mosque') THEN 25
        -- Material-based heights
        WHEN material IN ('glass', 'concrete', 'steel') THEN 12
        WHEN material IN ('wood', 'brick', 'stone') THEN 8
        -- Amenity-based heights
        WHEN amenity IN ('office', 'commercial') THEN 15
        WHEN amenity IN ('hospital', 'school') THEN 18
        -- Default minimum height
        ELSE 6
    END,
    render_min_height = CASE
        -- Use explicit min height if available
        WHEN min_height IS NOT NULL AND min_height > 0 THEN min_height
        -- Calculate from min_level
        WHEN min_level IS NOT NULL AND min_level > 0 THEN min_level * 3.66
        -- Default ground level
        ELSE 0
    END,
    building_material = CASE
        WHEN material IS NOT NULL THEN material
        WHEN building IN ('skyscraper', 'office') THEN 'glass'
        WHEN building IN ('warehouse', 'industrial') THEN 'concrete'
        WHEN building IN ('house', 'residential') THEN 'brick'
        WHEN building IN ('barn', 'shed') THEN 'wood'
        ELSE 'concrete'
    END;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS building_render_height_idx ON building_polygon (render_height);
CREATE INDEX IF NOT EXISTS building_material_idx ON building_polygon (building_material);
EOF
    
    log "✅ Enhanced building processing configured"
}

# Process tiles with all features
process_planet_tiles() {
    log "🌍 Processing planet tiles with all features"
    
    cd "$TILE_DIR"
    
    # Clean previous processing
    log "🧹 Cleaning previous processing attempts"
    make destroy-db 2>/dev/null || true
    make clean 2>/dev/null || true
    
    # Set environment for enhanced processing
    export DIFF_MODE=false
    export DOCKER_CLI_HINTS=false
    export COMPOSE_INTERACTIVE_NO_CLI=1
    export BUILDING_ENHANCEMENT=true
    export INCLUDE_3D_BUILDINGS=true
    export MAX_ZOOM=$MAX_ZOOM
    
    # Build OpenMapTiles layers
    log "🔧 Building OpenMapTiles layers"
    if timeout 45m make all 2>&1 | while read line; do
        if [[ $line =~ (ERROR|FAILED|error|failed) ]]; then
            error "BUILD ERROR: $line"
        else
            log "BUILD: $line"
        fi
    done; then
        log "✅ OpenMapTiles layers built successfully"
    else
        error "❌ Failed to build OpenMapTiles layers"
        exit 1
    fi
    
    # Start database
    log "🗄️ Starting enhanced database"
    if timeout 15m make start-db-preloaded 2>&1 | while read line; do
        log "DB: $line"
    done; then
        log "✅ Database started successfully"
    else
        error "❌ Failed to start database"
        exit 1
    fi
    
    # Import OSM data
    log "📥 Importing OSM data (this will take many hours)"
    info "Processing entire planet - estimated time: 12-24 hours"
    
    local start_time=$(date +%s)
    if timeout 48h make import-osm area=planet 2>&1 | while read line; do
        if [[ $((RANDOM % 100)) -eq 0 ]] || [[ "$line" =~ (Progress|imported|Error|COPY|completed) ]]; then
            local current_time=$(date +%s)
            local elapsed=$((current_time - start_time))
            log "OSM_IMPORT(${elapsed}s): $line"
        fi
    done; then
        log "✅ OSM data imported successfully"
    else
        error "❌ OSM import failed or timed out"
        exit 1
    fi
    
    # Apply building enhancements
    log "🏗️ Applying building enhancements"
    if [[ -f "sql/enhanced_buildings.sql" ]]; then
        docker-compose exec -T postgres psql -U openmaptiles -d openmaptiles -f /sql/enhanced_buildings.sql
        log "✅ Building enhancements applied"
    fi
    
    # Import additional data
    log "📥 Importing additional data"
    make import-wikidata 2>/dev/null || warn "⚠️ Wikidata import failed"
    make import-sql || error "❌ SQL import failed"
    make analyze-db || error "❌ Database analysis failed"
    
    # Generate tiles
    log "🎯 Generating enhanced tiles (z0-${MAX_ZOOM})"
    export MIN_ZOOM=0
    export MAX_ZOOM=$MAX_ZOOM
    
    if timeout 24h make generate-tiles-pg area=planet 2>&1 | while read line; do
        if [[ $((RANDOM % 50)) -eq 0 ]] || [[ "$line" =~ (Progress|generated|Error|completed) ]]; then
            log "TILES: $line"
        fi
    done; then
        log "✅ Tile generation completed"
    else
        error "❌ Tile generation failed"
        exit 1
    fi
    
    # Verify tiles
    local tiles_file="data/tiles.mbtiles"
    if [[ -f "$tiles_file" ]]; then
        local tiles_size=$(du -h "$tiles_file" | cut -f1)
        log "📊 Generated tiles: $tiles_size"
        
        # Get tile statistics
        python3 -c "
import sqlite3
conn = sqlite3.connect('$tiles_file')
cursor = conn.cursor()
cursor.execute('SELECT COUNT(*) FROM tiles')
total = cursor.fetchone()[0]
cursor.execute('SELECT MIN(zoom_level), MAX(zoom_level) FROM tiles')
min_zoom, max_zoom = cursor.fetchone()
print(f'📊 Total tiles: {total}')
print(f'🔍 Zoom range: {min_zoom}-{max_zoom}')
conn.close()
"
        log "✅ Planet tile processing completed successfully"
    else
        error "❌ No tiles file generated"
        exit 1
    fi
}

# Generate comprehensive map style
generate_planet_style() {
    log "🎨 Generating comprehensive planet style"
    
    # Create the comprehensive style based on Albania working style
    cat > "$SYSTEM_DIR/configs/planet_full_style.json" << 'EOF'
{
    "version": 8,
    "name": "BOP Maps - Planet Full Features",
    "metadata": {
        "bopmaps:version": "2.0",
        "bopmaps:coverage": "planet",
        "openmaptiles:version": "3.x"
    },
    "sources": {
        "planet": {
            "type": "vector",
            "tiles": [
                "https://f005.backblazeb2.com/file/bopmaps-prod-tiles-5911027e/planet/{z}/{x}/{y}.pbf"
            ],
            "minzoom": 0,
            "maxzoom": 14
        }
    },
    "sprite": "https://openmaptiles.github.io/osm-bright-gl-style/sprite",
    "glyphs": "https://fonts.openmaptiles.org/{fontstack}/{range}.pbf",
    "layers": [
        {
            "id": "background",
            "type": "background",
            "paint": {
                "background-color": "#0a0a0a"
            }
        },
        {
            "id": "water",
            "type": "fill",
            "source": "planet",
            "source-layer": "water",
            "paint": {
                "fill-color": "#1a237e",
                "fill-opacity": 0.9
            }
        },
        {
            "id": "waterway",
            "type": "line",
            "source": "planet",
            "source-layer": "waterway",
            "paint": {
                "line-color": "#1a237e",
                "line-width": ["interpolate", ["linear"], ["zoom"], 8, 0.5, 14, 3]
            }
        },
        {
            "id": "landcover-wood",
            "type": "fill",
            "source": "planet",
            "source-layer": "landcover",
            "filter": ["==", "class", "wood"],
            "paint": {
                "fill-color": "#1b5e20",
                "fill-opacity": 0.8
            }
        },
        {
            "id": "landcover-grass",
            "type": "fill",
            "source": "planet",
            "source-layer": "landcover",
            "filter": ["==", "class", "grass"],
            "paint": {
                "fill-color": "#2e7d32",
                "fill-opacity": 0.6
            }
        },
        {
            "id": "landcover-sand",
            "type": "fill",
            "source": "planet",
            "source-layer": "landcover",
            "filter": ["==", "class", "sand"],
            "paint": {
                "fill-color": "#ffc107",
                "fill-opacity": 0.7
            }
        },
        {
            "id": "landuse-residential",
            "type": "fill",
            "source": "planet",
            "source-layer": "landuse",
            "filter": ["==", "class", "residential"],
            "paint": {
                "fill-color": "#37474f",
                "fill-opacity": 0.3
            }
        },
        {
            "id": "landuse-commercial",
            "type": "fill",
            "source": "planet",
            "source-layer": "landuse",
            "filter": ["==", "class", "commercial"],
            "paint": {
                "fill-color": "#455a64",
                "fill-opacity": 0.4
            }
        },
        {
            "id": "landuse-industrial",
            "type": "fill",
            "source": "planet",
            "source-layer": "landuse",
            "filter": ["==", "class", "industrial"],
            "paint": {
                "fill-color": "#546e7a",
                "fill-opacity": 0.5
            }
        },
        {
            "id": "park",
            "type": "fill",
            "source": "planet",
            "source-layer": "park",
            "paint": {
                "fill-color": "#388e3c",
                "fill-opacity": 0.6
            }
        },
        {
            "id": "building-base",
            "type": "fill",
            "source": "planet",
            "source-layer": "building",
            "minzoom": 13,
            "paint": {
                "fill-color": "#424242",
                "fill-opacity": 0.7
            }
        },
        {
            "id": "building-3d",
            "type": "fill-extrusion",
            "source": "planet",
            "source-layer": "building",
            "minzoom": 13,
            "paint": {
                "fill-extrusion-color": [
                    "case",
                    ["==", ["get", "building_material"], "glass"], "#4a90e2",
                    ["==", ["get", "building_material"], "concrete"], "#8e8e93",
                    ["==", ["get", "building_material"], "brick"], "#d2691e",
                    ["==", ["get", "building_material"], "wood"], "#daa520",
                    ["==", ["get", "building_material"], "steel"], "#708090",
                    "#606060"
                ],
                "fill-extrusion-height": [
                    "interpolate",
                    ["linear"],
                    ["get", "render_height"],
                    0, 3,
                    5, 5,
                    10, 10,
                    20, 20,
                    50, 50,
                    100, 100
                ],
                "fill-extrusion-base": ["get", "render_min_height"],
                "fill-extrusion-opacity": [
                    "interpolate",
                    ["linear"],
                    ["get", "render_height"],
                    0, 0.6,
                    10, 0.7,
                    30, 0.8,
                    50, 0.9
                ]
            }
        },
        {
            "id": "highway-path",
            "type": "line",
            "source": "planet",
            "source-layer": "transportation",
            "filter": ["==", "class", "path"],
            "paint": {
                "line-color": "#666666",
                "line-width": ["interpolate", ["linear"], ["zoom"], 13, 0.5, 18, 2],
                "line-dasharray": [1, 1]
            }
        },
        {
            "id": "highway-minor",
            "type": "line",
            "source": "planet",
            "source-layer": "transportation",
            "filter": ["==", "class", "minor"],
            "paint": {
                "line-color": "#888888",
                "line-width": ["interpolate", ["linear"], ["zoom"], 10, 1, 18, 4]
            }
        },
        {
            "id": "highway-secondary",
            "type": "line",
            "source": "planet",
            "source-layer": "transportation",
            "filter": ["==", "class", "secondary"],
            "paint": {
                "line-color": "#ffc107",
                "line-width": ["interpolate", ["linear"], ["zoom"], 8, 1, 18, 6]
            }
        },
        {
            "id": "highway-primary",
            "type": "line",
            "source": "planet",
            "source-layer": "transportation",
            "filter": ["==", "class", "primary"],
            "paint": {
                "line-color": "#ff9800",
                "line-width": ["interpolate", ["linear"], ["zoom"], 6, 1, 18, 8]
            }
        },
        {
            "id": "highway-trunk",
            "type": "line",
            "source": "planet",
            "source-layer": "transportation",
            "filter": ["==", "class", "trunk"],
            "paint": {
                "line-color": "#ff5722",
                "line-width": ["interpolate", ["linear"], ["zoom"], 5, 1, 18, 10]
            }
        },
        {
            "id": "highway-motorway",
            "type": "line",
            "source": "planet",
            "source-layer": "transportation",
            "filter": ["==", "class", "motorway"],
            "paint": {
                "line-color": "#e53935",
                "line-width": ["interpolate", ["linear"], ["zoom"], 4, 1, 18, 12]
            }
        },
        {
            "id": "railway",
            "type": "line",
            "source": "planet",
            "source-layer": "transportation",
            "filter": ["==", "class", "rail"],
            "paint": {
                "line-color": "#78909c",
                "line-width": ["interpolate", ["linear"], ["zoom"], 8, 0.5, 18, 2]
            }
        },
        {
            "id": "boundary-country",
            "type": "line",
            "source": "planet",
            "source-layer": "boundary",
            "filter": ["==", "admin_level", 2],
            "paint": {
                "line-color": "#9c27b0",
                "line-width": ["interpolate", ["linear"], ["zoom"], 4, 1, 10, 3],
                "line-dasharray": [3, 3]
            }
        },
        {
            "id": "boundary-state",
            "type": "line",
            "source": "planet",
            "source-layer": "boundary",
            "filter": ["==", "admin_level", 4],
            "paint": {
                "line-color": "#7b1fa2",
                "line-width": ["interpolate", ["linear"], ["zoom"], 6, 0.5, 10, 2],
                "line-dasharray": [2, 2]
            }
        },
        {
            "id": "place-country",
            "type": "symbol",
            "source": "planet",
            "source-layer": "place",
            "filter": ["==", "class", "country"],
            "layout": {
                "text-field": ["get", "name"],
                "text-font": ["Open Sans Bold"],
                "text-size": ["interpolate", ["linear"], ["zoom"], 3, 10, 7, 16],
                "text-transform": "uppercase"
            },
            "paint": {
                "text-color": "#ffffff",
                "text-halo-color": "#000000",
                "text-halo-width": 2
            }
        },
        {
            "id": "place-city",
            "type": "symbol",
            "source": "planet",
            "source-layer": "place",
            "filter": ["==", "class", "city"],
            "layout": {
                "text-field": ["get", "name"],
                "text-font": ["Open Sans Semibold"],
                "text-size": ["interpolate", ["linear"], ["zoom"], 6, 12, 12, 18]
            },
            "paint": {
                "text-color": "#ffffff",
                "text-halo-color": "#000000",
                "text-halo-width": 1.5
            }
        },
        {
            "id": "place-town",
            "type": "symbol",
            "source": "planet",
            "source-layer": "place",
            "filter": ["==", "class", "town"],
            "minzoom": 8,
            "layout": {
                "text-field": ["get", "name"],
                "text-font": ["Open Sans Regular"],
                "text-size": ["interpolate", ["linear"], ["zoom"], 8, 10, 14, 14]
            },
            "paint": {
                "text-color": "#ffffff",
                "text-halo-color": "#000000",
                "text-halo-width": 1
            }
        },
        {
            "id": "place-village",
            "type": "symbol",
            "source": "planet",
            "source-layer": "place",
            "filter": ["==", "class", "village"],
            "minzoom": 10,
            "layout": {
                "text-field": ["get", "name"],
                "text-font": ["Open Sans Regular"],
                "text-size": ["interpolate", ["linear"], ["zoom"], 10, 8, 16, 12]
            },
            "paint": {
                "text-color": "#cccccc",
                "text-halo-color": "#000000",
                "text-halo-width": 1
            }
        },
        {
            "id": "road-labels",
            "type": "symbol",
            "source": "planet",
            "source-layer": "transportation_name",
            "minzoom": 12,
            "layout": {
                "text-field": ["get", "name"],
                "text-font": ["Open Sans Regular"],
                "text-size": 12,
                "symbol-placement": "line"
            },
            "paint": {
                "text-color": "#ffffff",
                "text-halo-color": "#000000",
                "text-halo-width": 1
            }
        },
        {
            "id": "poi-labels",
            "type": "symbol",
            "source": "planet",
            "source-layer": "poi",
            "minzoom": 14,
            "layout": {
                "text-field": ["get", "name"],
                "text-font": ["Open Sans Regular"],
                "text-size": 10,
                "text-offset": [0, 1]
            },
            "paint": {
                "text-color": "#ffffff",
                "text-halo-color": "#000000",
                "text-halo-width": 1
            }
        }
    ]
}
EOF
    
    log "✅ Comprehensive planet style generated"
}

# Main execution
main() {
    echo -e "${PURPLE}"
    echo "🌍 PLANET PROCESSING SYSTEM"
    echo "============================"
    echo "Processing entire planet with all features"
    echo "Including enhanced 3D buildings and comprehensive layers"
    echo -e "${NC}"
    
    log "🚀 Starting full planet processing..."
    
    load_config
    check_system
    setup_building_enhancement
    process_planet_tiles
    generate_planet_style
    
    echo -e "${GREEN}"
    echo "🎉 SUCCESS! Planet processing completed!"
    echo "Your comprehensive planet tiles are ready for deployment to B2."
    echo "Next: Run ./deploy_planet_to_b2.sh to upload to Backblaze B2"
    echo -e "${NC}"
    
    log "✅ Planet processing completed successfully"
}

# Run if called directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 