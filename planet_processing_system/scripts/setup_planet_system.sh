#!/bin/bash

################################################################################
# 🛠️ Planet Processing System Setup
# 
# Sets up the complete environment for planet-wide vector tile processing
# including all dependencies, configurations, and validations
################################################################################

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SYSTEM_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$SYSTEM_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${GREEN}$(date '+%Y-%m-%d %H:%M:%S')${NC} - $1"
}

warn() {
    echo -e "${YELLOW}$(date '+%Y-%m-%d %H:%M:%S') WARNING${NC} - $1"
}

error() {
    echo -e "${RED}$(date '+%Y-%m-%d %H:%M:%S') ERROR${NC} - $1"
}

info() {
    echo -e "${BLUE}$(date '+%Y-%m-%d %H:%M:%S') INFO${NC} - $1"
}

# Create directory structure
create_directories() {
    log "📁 Creating directory structure"
    
    mkdir -p "$SYSTEM_DIR/logs"
    mkdir -p "$SYSTEM_DIR/configs"
    mkdir -p "$SYSTEM_DIR/docs"
    mkdir -p "$SYSTEM_DIR/scripts"
    
    log "✅ Directory structure created"
}

# Check system requirements
check_system_requirements() {
    log "🔍 Checking system requirements"
    
    # Check OS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        log "🍎 macOS detected"
        PACKAGE_MANAGER="brew"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        log "🐧 Linux detected"
        if command -v apt-get &>/dev/null; then
            PACKAGE_MANAGER="apt"
        elif command -v yum &>/dev/null; then
            PACKAGE_MANAGER="yum"
        fi
    else
        error "❌ Unsupported operating system"
        exit 1
    fi
    
    # Check disk space
    local available=$(df -h "$PROJECT_ROOT" | awk 'NR==2 {print $4}' | sed 's/[^0-9]//g')
    if [[ $available -lt 100 ]]; then
        error "❌ Insufficient disk space (need 100GB+)"
        exit 1
    fi
    
    log "💾 Available disk space: ${available}GB"
    
    # Check memory
    if [[ "$OSTYPE" == "darwin"* ]]; then
        local memory=$(sysctl -n hw.memsize | awk '{print int($1/1024/1024/1024)}')
    else
        local memory=$(free -g | awk 'NR==2{print $2}')
    fi
    
    if [[ $memory -lt 16 ]]; then
        warn "⚠️ Low memory detected (${memory}GB). Recommend 16GB+ for planet processing"
    fi
    
    log "🧠 System memory: ${memory}GB"
    
    log "✅ System requirements check completed"
}

# Install dependencies
install_dependencies() {
    log "📦 Installing dependencies"
    
    # Check and install Docker
    if ! command -v docker &>/dev/null; then
        log "🐳 Installing Docker..."
        if [[ "$PACKAGE_MANAGER" == "brew" ]]; then
            brew install --cask docker
        elif [[ "$PACKAGE_MANAGER" == "apt" ]]; then
            curl -fsSL https://get.docker.com -o get-docker.sh
            sh get-docker.sh
        fi
    else
        log "✅ Docker already installed"
    fi
    
    # Check and install Docker Compose
    if ! command -v docker-compose &>/dev/null; then
        log "🐙 Installing Docker Compose..."
        if [[ "$PACKAGE_MANAGER" == "brew" ]]; then
            brew install docker-compose
        elif [[ "$PACKAGE_MANAGER" == "apt" ]]; then
            sudo apt-get update
            sudo apt-get install -y docker-compose
        fi
    else
        log "✅ Docker Compose already installed"
    fi
    
    # Check and install rclone
    if ! command -v rclone &>/dev/null; then
        log "☁️ Installing rclone..."
        if [[ "$PACKAGE_MANAGER" == "brew" ]]; then
            brew install rclone
        elif [[ "$PACKAGE_MANAGER" == "apt" ]]; then
            sudo apt-get install -y rclone
        fi
    else
        log "✅ rclone already installed"
    fi
    
    # Check and install Python dependencies
    if ! command -v python3 &>/dev/null; then
        log "🐍 Installing Python3..."
        if [[ "$PACKAGE_MANAGER" == "brew" ]]; then
            brew install python3
        elif [[ "$PACKAGE_MANAGER" == "apt" ]]; then
            sudo apt-get install -y python3 python3-pip
        fi
    else
        log "✅ Python3 already installed"
    fi
    
    # Install Python packages
    pip3 install --user sqlite3 pathlib
    
    log "✅ Dependencies installed"
}

# Configure OpenMapTiles
configure_openmaptiles() {
    log "🗺️ Configuring OpenMapTiles"
    
    local omt_dir="$PROJECT_ROOT/tile_processing/openmaptiles"
    
    if [[ ! -d "$omt_dir" ]]; then
        log "📥 Cloning OpenMapTiles..."
        git clone https://github.com/openmaptiles/openmaptiles.git "$omt_dir"
    else
        log "✅ OpenMapTiles directory exists"
    fi
    
    # Create enhanced configuration
    cat > "$omt_dir/.env" << EOF
# Enhanced OpenMapTiles Configuration
COMPOSE_PROJECT_NAME=openmaptiles
POSTGRES_DB=openmaptiles
POSTGRES_USER=openmaptiles
POSTGRES_PASSWORD=openmaptiles
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Performance tuning
POSTGRES_WORK_MEM=1GB
POSTGRES_SHARED_BUFFERS=4GB
POSTGRES_EFFECTIVE_CACHE_SIZE=8GB

# Enable enhanced features
BUILDING_ENHANCEMENT=true
INCLUDE_3D_BUILDINGS=true
ENABLE_GENERALIZATION=true
EOF
    
    log "✅ OpenMapTiles configured"
}

# Setup B2 configuration
setup_b2_config() {
    log "☁️ Setting up B2 configuration"
    
    if [[ ! -f "$PROJECT_ROOT/rclone_b2_config.sh" ]]; then
        error "❌ B2 configuration not found"
        info "Please create rclone_b2_config.sh with your B2 credentials"
        info "Example:"
        echo "rclone config create b2tiles b2 account=<your_account> key=<your_key> endpoint=https://s3.us-east-005.backblazeb2.com"
        exit 1
    fi
    
    # Test B2 connection
    source "$PROJECT_ROOT/rclone_b2_config.sh"
    
    if rclone lsd b2tiles: &>/dev/null; then
        log "✅ B2 connection verified"
    else
        error "❌ B2 connection failed"
        exit 1
    fi
}

# Make scripts executable
make_scripts_executable() {
    log "🔧 Making scripts executable"
    
    chmod +x "$SYSTEM_DIR/scripts/"*.sh
    
    log "✅ Scripts made executable"
}

# Create documentation
create_documentation() {
    log "📚 Creating documentation"
    
    # Implementation guide
    cat > "$SYSTEM_DIR/docs/IMPLEMENTATION.md" << 'EOF'
# Planet Processing Implementation Guide

## Overview
This document provides detailed implementation guidance for the planet processing system.

## Processing Pipeline

### 1. Data Download
- Download planet.osm.pbf (~70GB)
- Verify checksum
- Place in tile_processing/openmaptiles/data/

### 2. Database Setup
- PostgreSQL with PostGIS
- Performance tuning for large datasets
- Custom building enhancement functions

### 3. Tile Generation
- Enhanced 3D buildings processing
- Multi-layer generation
- Zoom level optimization

### 4. B2 Upload
- Parallel upload optimization
- Proper headers for vector tiles
- Style generation and deployment

## Performance Optimization

### System Requirements
- 16GB+ RAM (32GB recommended)
- 200GB+ free disk space
- 8+ CPU cores
- SSD storage recommended

### Database Tuning
```sql
-- PostgreSQL configuration
work_mem = '1GB'
shared_buffers = '4GB'
effective_cache_size = '8GB'
maintenance_work_mem = '2GB'
```

### Processing Optimization
- Parallel processing enabled
- Chunk-based uploads
- Automatic cleanup
- Progress monitoring

## Troubleshooting

### Common Issues
1. **Out of memory**: Increase swap space or reduce concurrency
2. **Disk space**: Monitor and clean temp files
3. **Docker issues**: Restart Docker daemon
4. **B2 timeout**: Increase timeout settings

### Monitoring
- Check logs in planet_processing_system/logs/
- Monitor disk usage during processing
- Track upload progress

## Cost Optimization

### B2 Storage
- Use lifecycle policies
- Enable compression
- Optimize tile sizes
- Monitor bandwidth usage

### Processing Costs
- Use spot instances for cloud processing
- Schedule processing during off-peak hours
- Consider regional processing for large datasets
EOF
    
    # Frontend integration guide
    cat > "$SYSTEM_DIR/docs/FRONTEND_INTEGRATION.md" << 'EOF'
# Frontend Integration Guide

## MapLibre Integration

### Basic Setup
```dart
import 'package:maplibre_gl/maplibre_gl.dart';

class PlanetMapWidget extends StatefulWidget {
  @override
  _PlanetMapWidgetState createState() => _PlanetMapWidgetState();
}

class _PlanetMapWidgetState extends State<PlanetMapWidget> {
  MaplibreMapController? mapController;
  
  @override
  Widget build(BuildContext context) {
    return MaplibreMap(
      styleString: 'https://f005.backblazeb2.com/file/bopmaps-prod-tiles-5911027e/planet_full_style.json',
      initialCameraPosition: CameraPosition(
        target: LatLng(40.7128, -74.0060),
        zoom: 10.0,
      ),
      onMapCreated: (MaplibreMapController controller) {
        mapController = controller;
        
        // Enable 3D buildings
        controller.setBearing(30);
        controller.setPitch(45);
      },
    );
  }
}
```

### Advanced Features
```dart
// Switch between themes
void switchTheme(String theme) {
  String styleUrl;
  switch (theme) {
    case 'dark':
      styleUrl = 'https://f005.backblazeb2.com/file/bopmaps-prod-tiles-5911027e/planet_full_style.json';
      break;
    case 'light':
      styleUrl = 'https://f005.backblazeb2.com/file/bopmaps-prod-tiles-5911027e/planet_light_style.json';
      break;
    case 'satellite':
      styleUrl = 'https://f005.backblazeb2.com/file/bopmaps-prod-tiles-5911027e/planet_satellite_style.json';
      break;
  }
  
  mapController?.setStyle(styleUrl);
}

// Add 3D building interactions
void enable3DBuildings() {
  mapController?.setBearing(45);
  mapController?.setPitch(60);
}
```

## Performance Optimization

### Tile Caching
```dart
// Enable tile caching
MaplibreMap(
  tileOptions: TileOptions(
    cachePolicy: CachePolicy.cacheFirst,
    maxCacheSize: 100 * 1024 * 1024, // 100MB
  ),
)
```

### Memory Management
```dart
// Optimize memory usage
@override
void dispose() {
  mapController?.dispose();
  super.dispose();
}
```

## Error Handling

### Network Errors
```dart
try {
  await mapController?.setStyle(styleUrl);
} catch (e) {
  // Handle network errors
  print('Failed to load style: $e');
  // Fallback to default style
}
```

### Tile Loading Errors
```dart
MaplibreMap(
  onMapError: (error) {
    print('Map error: $error');
    // Show user-friendly error message
  },
)
```
EOF
    
    # Troubleshooting guide
    cat > "$SYSTEM_DIR/docs/TROUBLESHOOTING.md" << 'EOF'
# Troubleshooting Guide

## Common Issues and Solutions

### 1. Processing Issues

#### Out of Memory
**Problem**: Process killed due to memory exhaustion
**Solution**: 
- Increase system swap space
- Reduce parallel processing settings
- Use cloud instance with more RAM

#### Disk Space Issues
**Problem**: No space left on device
**Solution**:
- Clean temp directories
- Increase disk space
- Enable auto-cleanup

#### Docker Issues
**Problem**: Docker containers not starting
**Solution**:
- Restart Docker daemon
- Check Docker memory limits
- Verify Docker Compose version

### 2. Upload Issues

#### B2 Connection Timeout
**Problem**: rclone uploads timing out
**Solution**:
- Increase timeout settings
- Reduce parallel uploads
- Check network connectivity

#### Invalid Credentials
**Problem**: B2 authentication failed
**Solution**:
- Verify B2 credentials
- Check bucket permissions
- Regenerate application keys

### 3. Frontend Issues

#### Tiles Not Loading
**Problem**: Map tiles not displaying
**Solution**:
- Check CORS settings
- Verify tile URLs
- Test individual tile access

#### Style Errors
**Problem**: Map style not loading
**Solution**:
- Validate JSON syntax
- Check sprite/glyph URLs
- Verify source definitions

## Diagnostic Commands

### Check System Status
```bash
# Check disk space
df -h

# Check memory usage
free -h

# Check Docker status
docker ps -a

# Check B2 connectivity
rclone lsd b2tiles:
```

### Test Individual Components
```bash
# Test tile access
curl -I https://f005.backblazeb2.com/file/bopmaps-prod-tiles-5911027e/planet/0/0/0.pbf

# Test style validation
curl https://f005.backblazeb2.com/file/bopmaps-prod-tiles-5911027e/planet_full_style.json | jq .
```

## Performance Monitoring

### Database Performance
```sql
-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

### Upload Performance
```bash
# Monitor upload speed
rclone copy --progress --stats 10s source destination
```

## Getting Help

### Log Files
- Processing logs: `planet_processing_system/logs/`
- B2 deployment logs: `planet_processing_system/logs/b2_deployment.log`
- OpenMapTiles logs: `tile_processing/openmaptiles/logs/`

### Community Resources
- OpenMapTiles GitHub: https://github.com/openmaptiles/openmaptiles
- MapLibre Community: https://github.com/maplibre
- Backblaze B2 Documentation: https://www.backblaze.com/b2/docs/
EOF
    
    log "✅ Documentation created"
}

# Run system validation
validate_setup() {
    log "🔍 Validating setup"
    
    # Check Docker
    if ! docker ps &>/dev/null; then
        error "❌ Docker not accessible"
        exit 1
    fi
    
    # Check rclone
    if ! command -v rclone &>/dev/null; then
        error "❌ rclone not found"
        exit 1
    fi
    
    # Check B2 connection
    if ! rclone lsd b2tiles: &>/dev/null; then
        error "❌ B2 connection failed"
        exit 1
    fi
    
    # Check OpenMapTiles
    if [[ ! -d "$PROJECT_ROOT/tile_processing/openmaptiles" ]]; then
        error "❌ OpenMapTiles not found"
        exit 1
    fi
    
    log "✅ Setup validation passed"
}

# Main execution
main() {
    echo -e "${PURPLE}"
    echo "🛠️ PLANET PROCESSING SYSTEM SETUP"
    echo "=================================="
    echo "Setting up comprehensive planet tile processing"
    echo -e "${NC}"
    
    log "🚀 Starting system setup..."
    
    create_directories
    check_system_requirements
    install_dependencies
    configure_openmaptiles
    setup_b2_config
    make_scripts_executable
    create_documentation
    validate_setup
    
    echo -e "${GREEN}"
    echo "🎉 SUCCESS! Planet processing system is ready!"
    echo ""
    echo "Next steps:"
    echo "1. Download planet.osm.pbf to tile_processing/openmaptiles/data/"
    echo "2. Run: ./process_planet_full.sh"
    echo "3. Run: ./deploy_planet_to_b2.sh"
    echo -e "${NC}"
    
    log "✅ System setup completed successfully"
}

# Run if called directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 