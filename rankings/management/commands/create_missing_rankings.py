"""
Django management command to create UserRanking records for users who don't have them
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from rankings.models import UserRanking

User = get_user_model()


class Command(BaseCommand):
    help = 'Create UserRanking records for users who don\'t have them'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating records',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # Find users without rankings
        users_with_rankings = set(UserRanking.objects.values_list('user_id', flat=True))
        users_without_rankings = User.objects.exclude(id__in=users_with_rankings)
        
        self.stdout.write(f'🔍 Found {users_without_rankings.count()} users without rankings')
        
        if dry_run:
            self.stdout.write('🧪 DRY RUN - showing what would be created:')
            for user in users_without_rankings:
                self.stdout.write(f'  - Would create ranking for: {user.username} (joined: {user.date_joined.strftime("%Y-%m-%d")})')
            return
        
        if users_without_rankings.count() == 0:
            self.stdout.write('✅ All users already have rankings!')
            return
            
        # Create rankings for users who don't have them
        created_count = 0
        for user in users_without_rankings:
            ranking, created = UserRanking.objects.get_or_create(
                user=user,
                defaults={
                    'total_score': 0,
                    'current_rank': 0,
                    'previous_rank': 0,
                    'challenge_score': 0,
                    'pin_score': 0,
                    'social_score': 0,
                    'achievement_score': 0,
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(f'  ✅ Created ranking for: {user.username}')
            else:
                self.stdout.write(f'  ⚠️  Ranking already exists for: {user.username}')
        
        self.stdout.write(f'\n🎉 Created {created_count} new ranking records!')
        
        # Show summary
        total_users = User.objects.count()
        total_rankings = UserRanking.objects.count()
        self.stdout.write(f'\n📊 Summary:')
        self.stdout.write(f'   Total users: {total_users}')
        self.stdout.write(f'   Total rankings: {total_rankings}')
        self.stdout.write(f'   Coverage: {(total_rankings/total_users)*100:.1f}%')
        
        if total_rankings == total_users:
            self.stdout.write('✅ All users now have rankings!')
        else:
            missing = total_users - total_rankings
            self.stdout.write(f'⚠️  Still missing {missing} rankings') 