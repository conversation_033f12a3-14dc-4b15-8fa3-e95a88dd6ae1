"""
Django management command to generate test users with rankings for Powder Springs, Atlanta area
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.gis.geos import Point
from django.utils import timezone
from django.db.models import Q
from random import randint, choice, uniform
from datetime import timedelta
from django.contrib.gis.measure import D

from rankings.models import UserRanking
from friends.models import Friend

User = get_user_model()


class Command(BaseCommand):
    help = 'Generate test users with rankings for Powder Springs, Atlanta area'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=50,
            help='Number of test users to create (default: 50)',
        )
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing test users before creating new ones',
        )
        parser.add_argument(
            '--make-friends',
            action='store_true',
            help='Create random friendships between generated users',
        )
        parser.add_argument(
            '--set-my-location',
            action='store_true',
            help='Set your user location to Powder Springs (requires username)',
        )
        parser.add_argument(
            '--my-username',
            type=str,
            help='Your username to set location for (use with --set-my-location)',
        )

    def handle(self, *args, **options):
        count = options['count']
        
        if options['clear_existing']:
            self.stdout.write('🧹 Clearing existing test users...')
            # Delete test users (those with test_ prefix or in test domain)
            test_users = User.objects.filter(
                username__startswith='test_'
            )
            for user in test_users:
                if hasattr(user, 'ranking'):
                    user.ranking.delete()
                user.delete()
            self.stdout.write('   ✓ Cleared existing test users')

        self.stdout.write(f'🏠 Creating {count} test users for Powder Springs, Atlanta area...')
        
        # Powder Springs coordinates: 33.8626° N, 84.6893° W
        base_lat = 33.8626
        base_lng = -84.6893
        
        # Handle setting current user location if requested
        if options['set_my_location']:
            username = options['my_username']
            if not username:
                self.stdout.write('❌ Please provide --my-username when using --set-my-location')
                return
                
            try:
                current_user = User.objects.get(username=username)
                location = Point(base_lng, base_lat)
                
                # Get or create ranking for current user
                ranking, created = UserRanking.objects.get_or_create(
                    user=current_user,
                    defaults={
                        'total_score': randint(500, 1500),
                        'challenge_score': randint(100, 400),
                        'pin_score': randint(50, 300),
                        'social_score': randint(30, 200),
                        'achievement_score': randint(20, 100),
                        'current_rank': 0,
                        'previous_rank': 0,
                        'rank_change': randint(-5, 10),
                        'last_location': location,
                        'location_updated_at': timezone.now()
                    }
                )
                
                # Update location even if ranking existed
                ranking.last_location = location
                ranking.location_updated_at = timezone.now()
                ranking.save()
                ranking.calculate_level()
                
                self.stdout.write(f'✅ Set {username}\'s location to Powder Springs')
                self.stdout.write(f'   Score: {ranking.total_score} pts, Badge: {ranking.badge_name}')
                
            except User.DoesNotExist:
                self.stdout.write(f'❌ User "{username}" not found')
                return
        
        # Generate realistic names
        first_names = [
            'Alex', 'Jordan', 'Taylor', 'Morgan', 'Casey', 'Riley', 'Cameron', 'Avery',
            'Blake', 'Dakota', 'Emery', 'Finley', 'Harper', 'Hayden', 'Jamie', 'Jesse',
            'Kendall', 'Logan', 'Mason', 'Parker', 'Quinn', 'Reese', 'Sage', 'Skyler',
            'Drew', 'Peyton', 'Rowan', 'Shawn', 'Spencer', 'Tatum', 'Ryan', 'Devon',
            'Chloe', 'Emma', 'Olivia', 'Sophia', 'Isabella', 'Mia', 'Charlotte', 'Amelia',
            'Evelyn', 'Abigail', 'Harper', 'Emily', 'Elizabeth', 'Avery', 'Sofia', 'Ella',
            'Madison', 'Scarlett', 'Victoria', 'Aria', 'Grace', 'Chloe', 'Camila', 'Penelope',
            'Ethan', 'Liam', 'Noah', 'William', 'James', 'Benjamin', 'Lucas', 'Henry',
            'Alexander', 'Mason', 'Michael', 'Sebastian', 'Elijah', 'Logan', 'Matthew', 'Daniel',
            'Jackson', 'David', 'Owen', 'Joseph', 'Samuel', 'Wyatt', 'John', 'Jack'
        ]
        
        last_names = [
            'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis',
            'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas',
            'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson', 'White',
            'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young',
            'Allen', 'King', 'Wright', 'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores',
            'Green', 'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell',
            'Carter', 'Roberts', 'Gomez', 'Phillips', 'Evans', 'Turner', 'Diaz', 'Parker'
        ]
        
        music_genres = ['rap', 'pop', 'rock', 'country', 'r&b', 'electronic', 'indie', 'jazz']
        
        created_users = []
        
        for i in range(count):
            # Generate random coordinates within ~5 mile radius of Powder Springs
            lat_offset = uniform(-0.08, 0.08)  # About 5 miles in latitude
            lng_offset = uniform(-0.08, 0.08)  # About 5 miles in longitude
            
            user_lat = base_lat + lat_offset
            user_lng = base_lng + lng_offset
            
            # Create unique username and email
            first_name = choice(first_names)
            last_name = choice(last_names)
            username = f"test_{first_name.lower()}_{last_name.lower()}_{i+1}"
            email = f"{username}@testuser.com"
            
            # Create user
            user = User.objects.create_user(
                username=username,
                email=email,
                password='testpass123',
                first_name=first_name,
                last_name=last_name
            )
            
            # Generate realistic scores with some variety
            base_score = randint(100, 2000)
            
            # Create UserRanking with realistic component scores
            challenge_score = randint(50, base_score // 2)
            pin_score = randint(30, base_score // 3)
            social_score = randint(20, base_score // 4)
            achievement_score = randint(10, base_score // 5)
            
            total_score = challenge_score + pin_score + social_score + achievement_score
            
            # Create location point
            location = Point(user_lng, user_lat)
            
            # Generate realistic rank change
            rank_change = randint(-10, 15)  # Slight positive bias
            
            ranking, created = UserRanking.objects.get_or_create(
                user=user,
                defaults={
                    'total_score': total_score,
                    'challenge_score': challenge_score,
                    'pin_score': pin_score,
                    'social_score': social_score,
                    'achievement_score': achievement_score,
                    'current_rank': 0,  # Will be calculated later
                    'previous_rank': 0,
                    'rank_change': rank_change,
                    'last_location': location,
                    'location_updated_at': timezone.now() - timedelta(hours=randint(1, 24))
                }
            )
            
            # Calculate level and badge
            ranking.calculate_level()
            
            created_users.append(user)
            
            if (i + 1) % 10 == 0:
                self.stdout.write(f'   ✓ Created {i + 1}/{count} users')
        
        # Update current ranks based on scores
        self.stdout.write('📊 Calculating ranks...')
        all_rankings = UserRanking.objects.all().order_by('-total_score')
        for rank, ranking in enumerate(all_rankings, 1):
            ranking.current_rank = rank
            ranking.previous_rank = max(1, rank + ranking.rank_change)
            ranking.save()
        
        # Create friendships if requested
        if options['make_friends']:
            self.stdout.write('👥 Creating random friendships...')
            friendship_count = 0
            
            for user in created_users:
                # Each user gets 3-8 random friends
                num_friends = randint(3, 8)
                potential_friends = [u for u in created_users if u != user]
                
                # Select random friends
                for _ in range(min(num_friends, len(potential_friends))):
                    friend = choice(potential_friends)
                    potential_friends.remove(friend)
                    
                    # Check if friendship already exists
                    existing = Friend.objects.filter(
                        (Q(requester=user, recipient=friend) | 
                         Q(requester=friend, recipient=user)),
                        status='accepted'
                    ).exists()
                    
                    if not existing:
                        Friend.objects.create(
                            requester=user,
                            recipient=friend,
                            status='accepted',
                            friendship_score=uniform(50, 500),
                            interaction_count=randint(5, 50),
                            last_interaction=timezone.now() - timedelta(days=randint(1, 30))
                        )
                        friendship_count += 1
            
            self.stdout.write(f'   ✓ Created {friendship_count} friendships')
        
        # Generate summary stats
        total_users = User.objects.count()
        total_rankings = UserRanking.objects.count()
        
        # Fix distance query - use D(km=5) for proper distance calculation
        atlanta_users = UserRanking.objects.filter(
            last_location__distance_lte=(Point(base_lng, base_lat), D(km=5))
        ).count()
        
        self.stdout.write(f'\n✅ Test data generation completed!')
        self.stdout.write(f'   👥 Total users in system: {total_users}')
        self.stdout.write(f'   🏆 Total rankings: {total_rankings}')
        self.stdout.write(f'   🏠 Users in Powder Springs area (5mi): {atlanta_users}')
        
        # Show top 5 local users
        self.stdout.write(f'\n🏆 Top 5 users in Powder Springs area:')
        top_local = UserRanking.objects.filter(
            last_location__distance_lte=(Point(base_lng, base_lat), D(km=5))
        ).order_by('-total_score')[:5]
        
        for i, ranking in enumerate(top_local, 1):
            self.stdout.write(f'   {i}. {ranking.user.get_full_name()} ({ranking.user.username}) - {ranking.total_score} pts - {ranking.badge_name}')
        
        self.stdout.write(f'\n📱 API Testing:')
        self.stdout.write(f'   Local: GET /api/rankings/leaderboard/?scope=local&lat={base_lat}&lng={base_lng}&radius_km=5')
        self.stdout.write(f'   Friends: GET /api/rankings/leaderboard/?scope=friends')
        self.stdout.write(f'   All: GET /api/rankings/leaderboard/?scope=all')
        
        self.stdout.write(f'\n🧪 Test user credentials:')
        self.stdout.write(f'   Username: {created_users[0].username}')
        self.stdout.write(f'   Password: testpass123')
        self.stdout.write(f'   (All test users have the same password)') 