# Generated by Django 4.2.7 on 2025-06-14 02:33

from django.conf import settings
import django.contrib.gis.db.models.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("users", "0003_school_user_school_email_verified_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserRanking",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("total_score", models.IntegerField(default=0)),
                ("previous_score", models.IntegerField(default=0)),
                ("current_rank", models.PositiveIntegerField(default=0)),
                ("previous_rank", models.PositiveIntegerField(default=0)),
                ("rank_change", models.IntegerField(default=0)),
                ("challenge_score", models.IntegerField(default=0)),
                ("pin_score", models.IntegerField(default=0)),
                ("social_score", models.IntegerField(default=0)),
                ("achievement_score", models.IntegerField(default=0)),
                (
                    "last_location",
                    django.contrib.gis.db.models.fields.PointField(
                        blank=True, geography=True, null=True, srid=4326
                    ),
                ),
                ("location_updated_at", models.DateTimeField(blank=True, null=True)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "school",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="student_rankings",
                        to="users.school",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ranking",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["-total_score"], name="rankings_us_total_s_999f02_idx"
                    ),
                    models.Index(
                        fields=["current_rank"], name="rankings_us_current_f97d6b_idx"
                    ),
                    models.Index(
                        fields=["user"], name="rankings_us_user_id_2edc08_idx"
                    ),
                    models.Index(
                        fields=["school"], name="rankings_us_school__3a1d29_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="RankingHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("total_score", models.IntegerField()),
                ("rank", models.PositiveIntegerField()),
                ("recorded_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ranking_history",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-recorded_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "-recorded_at"],
                        name="rankings_ra_user_id_4a6a79_idx",
                    ),
                    models.Index(
                        fields=["-recorded_at"], name="rankings_ra_recorde_9e1b11_idx"
                    ),
                ],
            },
        ),
    ]
