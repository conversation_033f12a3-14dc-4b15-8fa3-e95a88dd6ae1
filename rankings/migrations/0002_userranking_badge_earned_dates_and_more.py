# Generated by Django 4.2.7 on 2025-06-15 01:24

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rankings", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="userranking",
            name="badge_earned_dates",
            field=models.JSONField(default=dict),
        ),
        migrations.AddField(
            model_name="userranking",
            name="badge_name",
            field=models.CharField(default="Basement Bopper", max_length=50),
        ),
        migrations.AddField(
            model_name="userranking",
            name="level",
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AddField(
            model_name="userranking",
            name="xp",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddIndex(
            model_name="userranking",
            index=models.Index(fields=["level"], name="rankings_us_level_f8d689_idx"),
        ),
    ]
