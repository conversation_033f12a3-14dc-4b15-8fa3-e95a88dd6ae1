from django.db import models
from django.conf import settings
from django.contrib.gis.db import models as gis_models
from django.contrib.gis.geos import Point
from django.utils import timezone

# Rank definitions
RANK_LEVELS = {
    1: "Basement Bopper",  # 0 XP
    2: "Selector",         # 500 XP
    3: "Tastemaker",      # 1,500 XP
    4: "Trendsetter",     # 3,500 XP
    5: "Icon",            # 7,000 XP
    6: "Architect",       # 12,000 XP
    7: "Legend"           # 20,000 XP
}

RANK_COLORS = {
    "Basement Bopper": {"primary": "#1A1A1A", "background": "#2D2D2D"},
    "Selector": {"primary": "#4A90E2", "background": "#2171C7"},
    "Tastemaker": {"primary": "#9B51E0", "background": "#6B2E9E"},
    "Trendsetter": {"primary": "#F2994A", "background": "#D97B29"},
    "Icon": {"primary": "#EB5757", "background": "#C62828"},
    "Architect": {"primary": "#27AE60", "background": "#1E8449"},
    "Legend": {"primary": "#F2C94C", "background": "#DBA520"}
}

RANK_EFFECTS = {
    "Basement Bopper": {"effect": "matte_dark", "description": "Flickering pin light"},
    "Selector": {"effect": "trail", "description": "Animated waveform badge"},
    "Tastemaker": {"effect": "swirl", "description": "Swirling motion badge"},
    "Trendsetter": {"effect": "pulse", "description": "Radiating pulse icon"},
    "Icon": {"effect": "flame", "description": "Glowing flame with 3D flicker"},
    "Architect": {"effect": "constellation", "description": "Constellation with floating pins"},
    "Legend": {"effect": "crown", "description": "Crown with golden echo loop"}
}

class UserRanking(models.Model):
    """
    Centralized model for tracking user rankings across all features
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='ranking'
    )
    
    # Core ranking fields
    total_score = models.IntegerField(default=0)
    previous_score = models.IntegerField(default=0)
    current_rank = models.PositiveIntegerField(default=0)
    previous_rank = models.PositiveIntegerField(default=0)
    rank_change = models.IntegerField(default=0)  # Positive for improvement, negative for decline
    
    # Score components
    challenge_score = models.IntegerField(default=0)  # From weekly challenges
    pin_score = models.IntegerField(default=0)  # From pin activity
    social_score = models.IntegerField(default=0)  # From friend interactions
    achievement_score = models.IntegerField(default=0)  # From completed achievements
    
    # New fields for level/badge system
    level = models.PositiveIntegerField(default=1)
    xp = models.PositiveIntegerField(default=0)
    badge_name = models.CharField(max_length=50, default="Basement Bopper")
    badge_earned_dates = models.JSONField(default=dict)  # Store when each badge was earned
    
    # Location tracking for local rankings
    last_location = gis_models.PointField(geography=True, null=True, blank=True)
    location_updated_at = models.DateTimeField(null=True, blank=True)
    
    # School info for school rankings
    school = models.ForeignKey(
        'users.School',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='student_rankings'
    )
    
    # Metadata
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['-total_score']),
            models.Index(fields=['current_rank']),
            models.Index(fields=['user']),
            models.Index(fields=['school']),
            models.Index(fields=['level']),  # New index for level queries
        ]
        
    def __str__(self):
        return f"{self.user.username}'s Ranking (#{self.current_rank})"
    
    def update_location(self, lat, lng):
        """Update user's location for local rankings"""
        if lat and lng:
            self.last_location = Point(float(lng), float(lat))
            self.location_updated_at = timezone.now()
            self.save(update_fields=['last_location', 'location_updated_at'])
    
    def calculate_total_score(self):
        """Calculate total score from all components (EXCLUDING weekly challenge scores)"""
        self.previous_score = self.total_score
        self.total_score = sum([
            # self.challenge_score,  # REMOVED: Weekly challenges should not contribute to XP
            self.pin_score,
            self.social_score,
            self.achievement_score  # This is the ONLY source of XP (from gamification challenges)
        ])
        self.save(update_fields=['total_score', 'previous_score'])
        
    def calculate_level(self):
        """Calculate level based on XP"""
        # XP thresholds for each level
        level_thresholds = [
            {'level': 1, 'name': 'Basement Bopper', 'required_xp': 0},
            {'level': 2, 'name': 'Selector', 'required_xp': 500},
            {'level': 3, 'name': 'Tastemaker', 'required_xp': 1500},
            {'level': 4, 'name': 'Trendsetter', 'required_xp': 3500},
            {'level': 5, 'name': 'Icon', 'required_xp': 7000},
            {'level': 6, 'name': 'Architect', 'required_xp': 12000},
            {'level': 7, 'name': 'Legend', 'required_xp': 20000},
        ]
        
        current_xp = self.total_score * 10  # Convert score to XP
        
        # Find the highest level that can be achieved with current XP
        for threshold in reversed(level_thresholds):
            if current_xp >= threshold['required_xp']:
                new_level = threshold['level']
                new_badge = threshold['name']
                break
        else:
            new_level = 1
            new_badge = 'Basement Bopper'
        
        # Update level and badge if changed
        if new_level != self.level or new_badge != self.badge_name:
            self.level = new_level
            self.badge_name = new_badge
            if new_badge not in self.badge_earned_dates:
                self.badge_earned_dates[new_badge] = timezone.now().isoformat()
            self.save(update_fields=['level', 'badge_name', 'badge_earned_dates'])
    
    def get_badge_details(self):
        """Get visual details for current badge"""
        return {
            'name': self.badge_name,
            'colors': RANK_COLORS.get(self.badge_name, RANK_COLORS["Basement Bopper"]),
            'effects': RANK_EFFECTS.get(self.badge_name, RANK_EFFECTS["Basement Bopper"]),
            'earned_at': self.badge_earned_dates.get(self.badge_name),
            'level': self.level,
            'xp': self.total_score * 10,  # Convert score to XP
            'next_level_xp': (self.level + 1) * 500 if self.level < 30 else None
        }

class RankingHistory(models.Model):
    """
    Track historical ranking data for trend analysis
    """
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='ranking_history'
    )
    total_score = models.IntegerField()
    rank = models.PositiveIntegerField()
    recorded_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-recorded_at']
        indexes = [
            models.Index(fields=['user', '-recorded_at']),
            models.Index(fields=['-recorded_at']),
        ]
        
    def __str__(self):
        return f"{self.user.username}'s Rank #{self.rank} at {self.recorded_at}"
