from rest_framework import serializers
from .models import UserRanking, RankingHistory

class UserRankingSerializer(serializers.ModelSerializer):
    """
    Serializer for user rankings with user details
    """
    name = serializers.SerializerMethodField()
    username = serializers.CharField(source='user.username')
    avatar = serializers.SerializerMethodField()
    
    class Meta:
        model = UserRanking
        fields = [
            'id',
            'name',
            'username',
            'avatar',
            'total_score',
            'current_rank',
            'rank_change',
            'challenge_score',
            'pin_score',
            'social_score',
            'achievement_score',
            'school'
        ]
    
    def get_name(self, obj):
        return obj.user.get_full_name() or obj.user.username
    
    def get_avatar(self, obj):
        request = self.context.get('request')
        if obj.user.profile_pic and request:
            return request.build_absolute_uri(obj.user.profile_pic)
        return None

class RankingHistorySerializer(serializers.ModelSerializer):
    """
    Serializer for historical ranking data
    """
    class Meta:
        model = RankingHistory
        fields = [
            'total_score',
            'rank',
            'recorded_at'
        ] 