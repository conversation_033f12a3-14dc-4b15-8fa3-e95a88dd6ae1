from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.db.models import F, Count, Q, Sum
from django.utils import timezone
from django.db import transaction
from django.contrib.auth import get_user_model

from challenges.models import ChallengeParticipation
from pins.models import Pin, PinInteraction
from friends.models import Friend
from gamification.models import UserAchievement
from .models import UserRanking, RankingHistory

User = get_user_model()

@receiver(post_save, sender=User)
def create_user_ranking(sender, instance, created, **kwargs):
    """Create UserRanking instance when a new user is created"""
    if created:
        UserRanking.objects.create(user=instance)

@receiver([post_save, post_delete], sender=ChallengeParticipation)
def update_challenge_score(sender, instance, **kwargs):
    """Update challenge score when participations change - DISABLED for XP-only system"""
    # DISABLED: Weekly challenges should not contribute to XP
    # Only gamification challenges should affect XP through UserAchievement
    pass
    
    # OLD CODE (now disabled):
    # with transaction.atomic():
    #     # Get or create user ranking
    #     ranking, _ = UserRanking.objects.get_or_create(user=instance.user)
    #     
    #     # Calculate new challenge score
    #     challenge_score = ChallengeParticipation.objects.filter(
    #         user=instance.user
    #     ).aggregate(
    #         participation_points=Count('id') * 10,
    #         upvotes=Count('votes', filter=Q(votes__value=1)),
    #         downvotes=Count('votes', filter=Q(votes__value=-1))
    #     )
    #     
    #     # Update score
    #     ranking.challenge_score = (
    #         challenge_score['participation_points'] +
    #         challenge_score['upvotes'] -
    #         challenge_score['downvotes']
    #     )
    #     ranking.save()
    #     _update_user_ranking(ranking)

@receiver([post_save, post_delete], sender=Pin)
@receiver([post_save, post_delete], sender=PinInteraction)
def update_pin_score(sender, instance, **kwargs):
    """Update pin score when pins or interactions change"""
    user = instance.owner if isinstance(instance, Pin) else instance.user
    
    with transaction.atomic():
        # Get or create user ranking
        ranking, _ = UserRanking.objects.get_or_create(user=user)
        
        # Calculate new pin score
        pin_stats = Pin.objects.filter(owner=user).aggregate(
            pin_count=Count('id') * 5,  # 5 points per pin
            upvotes=Count('votes', filter=Q(votes__value=1)),
            downvotes=Count('votes', filter=Q(votes__value=-1)),
            interactions=Count('interactions') * 2  # 2 points per interaction
        )
        
        # Update score
        ranking.pin_score = (
            pin_stats['pin_count'] +
            pin_stats['upvotes'] -
            pin_stats['downvotes'] +
            pin_stats['interactions']
        )
        ranking.save()
        _update_user_ranking(ranking)

@receiver([post_save], sender=Friend)
def update_social_score(sender, instance, **kwargs):
    """Update social score when friend relationships change"""
    if instance.status == 'accepted':
        with transaction.atomic():
            # Update both users' rankings
            for user in [instance.requester, instance.recipient]:
                # Get or create user ranking
                ranking, _ = UserRanking.objects.get_or_create(user=user)
                
                # Calculate new social score
                friend_stats = Friend.objects.filter(
                    (Q(requester=user) | Q(recipient=user)),
                    status='accepted'
                ).aggregate(
                    friend_count=Count('id') * 20,  # 20 points per friend
                    interaction_score=Sum('friendship_score')
                )
                
                # Update score
                ranking.social_score = (
                    friend_stats['friend_count'] +
                    (friend_stats['interaction_score'] or 0)
                )
                ranking.save()
                _update_user_ranking(ranking)

@receiver([post_save], sender=UserAchievement)
def update_achievement_score(sender, instance, **kwargs):
    """Update achievement score when achievements are completed - use centralized XP calculator"""
    with transaction.atomic():
        # Get or create user ranking
        ranking, _ = UserRanking.objects.get_or_create(user=instance.user)
        
        # Use centralized XP calculator to get proper XP from gamification challenges
        from gamification.services.xp_calculator import XPCalculator
        total_xp = XPCalculator.get_user_total_xp(instance.user)
        
        # Update XP and achievement score (store XP directly)
        ranking.xp = total_xp
        ranking.achievement_score = total_xp  # Store XP directly, no conversion needed
        ranking.save()
        _update_user_ranking(ranking)

def _update_user_ranking(ranking):
    """
    Update user's total score, rank, and history
    This is called after any component score is updated
    """
    with transaction.atomic():
        # Calculate new total score
        ranking.calculate_total_score()
        
        # Get user's new rank
        new_rank = UserRanking.objects.filter(
            total_score__gt=ranking.total_score
        ).count() + 1
        
        # Update rank change
        ranking.previous_rank = ranking.current_rank or new_rank
        ranking.current_rank = new_rank
        ranking.rank_change = ranking.previous_rank - ranking.current_rank
        
        # Calculate new level based on total score
        ranking.calculate_level()
        
        ranking.save()
        
        # Record history
        RankingHistory.objects.create(
            user=ranking.user,
            total_score=ranking.total_score,
            rank=ranking.current_rank
        ) 