from django.test import TestCase
from django.contrib.auth import get_user_model
from django.contrib.gis.geos import Point
from django.utils import timezone
from rest_framework.test import APITestCase
from rest_framework import status
from datetime import timedelta

from .models import UserRanking, RankingHistory
from friends.models import Friend
from users.models import School

User = get_user_model()

class UserRankingModelTests(TestCase):
    @classmethod
    def setUpTestData(cls):
        """Set up data for all test methods"""
        # Clean up any existing data
        UserRanking.objects.all().delete()
        User.objects.all().delete()
        
        # Create test user
        cls.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
    
    def setUp(self):
        """Set up fresh ranking for each test"""
        # Ensure no rankings exist
        UserRanking.objects.all().delete()
        
        # Create fresh ranking
        self.ranking = UserRanking.objects.create(
            user=self.user,
            total_score=100,
            challenge_score=40,
            pin_score=30,
            social_score=20,
            achievement_score=10,
            current_rank=1,
            previous_rank=2
        )
    
    def test_ranking_creation(self):
        """Test that a ranking can be created with all fields"""
        self.assertEqual(self.ranking.total_score, 100)
        self.assertEqual(self.ranking.current_rank, 1)
        self.assertEqual(self.ranking.previous_rank, 2)
        self.assertEqual(self.ranking.rank_change, 0)
    
    def test_calculate_total_score(self):
        """Test total score calculation from components"""
        self.ranking.challenge_score = 50
        self.ranking.pin_score = 40
        self.ranking.social_score = 30
        self.ranking.achievement_score = 20
        
        self.ranking.calculate_total_score()
        self.assertEqual(self.ranking.total_score, 140)
        self.assertEqual(self.ranking.previous_score, 100)
    
    def test_update_location(self):
        """Test updating user's location"""
        self.ranking.update_location(40.7128, -74.0060)
        self.assertIsNotNone(self.ranking.last_location)
        self.assertIsNotNone(self.ranking.location_updated_at)
        self.assertEqual(self.ranking.last_location.x, -74.0060)
        self.assertEqual(self.ranking.last_location.y, 40.7128)

class RankingAPITests(APITestCase):
    @classmethod
    def setUpTestData(cls):
        """Set up data for all test methods"""
        # Clean up any existing data
        UserRanking.objects.all().delete()
        User.objects.all().delete()
        School.objects.all().delete()
        Friend.objects.all().delete()
        RankingHistory.objects.all().delete()
        
        # Create test users
        cls.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        cls.friend = User.objects.create_user(
            username="friend",
            email="<EMAIL>",
            password="friend123"
        )
        cls.stranger = User.objects.create_user(
            username="stranger",
            email="<EMAIL>",
            password="stranger123"
        )
        
        # Create school
        cls.school = School.objects.create(
            name="Test School",
            location=Point(-74.0060, 40.7128)
        )
        cls.user.school = cls.school
        cls.user.save()
        
        # Create friendship
        Friend.objects.create(
            requester=cls.user,
            recipient=cls.friend,
            status='accepted'
        )
    
    def setUp(self):
        """Set up fresh rankings for each test"""
        # Clean up any existing rankings
        UserRanking.objects.all().delete()
        RankingHistory.objects.all().delete()
        
        # Create rankings
        self.user_ranking = UserRanking.objects.create(
            user=self.user,
            total_score=100,
            challenge_score=40,
            pin_score=30,
            social_score=20,
            achievement_score=10,
            current_rank=1,
            previous_rank=2,
            school=self.school,
            last_location=Point(-74.0060, 40.7128)
        )
        
        self.friend_ranking = UserRanking.objects.create(
            user=self.friend,
            total_score=90,
            current_rank=2,
            previous_rank=1,
            last_location=Point(-74.0060, 40.7128)
        )
        
        self.stranger_ranking = UserRanking.objects.create(
            user=self.stranger,
            total_score=80,
            current_rank=3,
            previous_rank=3,
            last_location=Point(-73.9352, 40.7306)  # Different location
        )
        
        # Authenticate test user
        self.client.force_authenticate(user=self.user)
    
    def test_leaderboard_all(self):
        """Test retrieving global leaderboard"""
        response = self.client.get('/api/rankings/leaderboard/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['leaderboard']), 3)
        self.assertEqual(response.data['leaderboard'][0]['score'], '100')
    
    def test_leaderboard_friends(self):
        """Test retrieving friends-only leaderboard"""
        response = self.client.get('/api/rankings/leaderboard/', {'scope': 'friends'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['leaderboard']), 2)  # User and friend only
    
    def test_leaderboard_local(self):
        """Test retrieving local leaderboard"""
        response = self.client.get(
            '/api/rankings/leaderboard/',
            {
                'scope': 'local',
                'lat': '40.7128',
                'lng': '-74.0060',
                'radius_km': '5'
            }
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['leaderboard']), 2)  # User and friend only (same location)
    
    def test_leaderboard_school(self):
        """Test retrieving school leaderboard"""
        response = self.client.get('/api/rankings/leaderboard/', {'scope': 'school'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['leaderboard']), 1)  # Only user is in school
    
    def test_ranking_history(self):
        """Test retrieving user's ranking history"""
        # Create some history entries
        RankingHistory.objects.create(
            user=self.user,
            total_score=90,
            rank=2
        )
        RankingHistory.objects.create(
            user=self.user,
            total_score=100,
            rank=1
        )
        
        response = self.client.get('/api/rankings/history/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
    
    def test_ranking_stats(self):
        """Test retrieving detailed ranking stats"""
        response = self.client.get('/api/rankings/stats/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_score'], 100)
        self.assertEqual(response.data['current_rank'], 1)
        self.assertEqual(response.data['components']['challenge_score'], 40)
        self.assertEqual(response.data['components']['pin_score'], 30)
        self.assertEqual(response.data['components']['social_score'], 20)
        self.assertEqual(response.data['components']['achievement_score'], 10)

    def test_leaderboard_friends_detailed(self):
        """Test retrieving friends-only leaderboard with detailed verification"""
        # Create additional test users and friendships
        extra_friend1 = User.objects.create_user(
            username="extra_friend1",
            email="<EMAIL>",
            password="friend123"
        )
        extra_friend2 = User.objects.create_user(
            username="extra_friend2",
            email="<EMAIL>",
            password="friend123"
        )
        
        # Create rankings for the extra friends
        extra_ranking1, _ = UserRanking.objects.get_or_create(
            user=extra_friend1,
            defaults={
                'total_score': 150,  # Higher than user's 100
                'current_rank': 1,
                'previous_rank': 2
            }
        )
        extra_ranking2, _ = UserRanking.objects.get_or_create(
            user=extra_friend2,
            defaults={
                'total_score': 75,   # Lower than user's 100
                'current_rank': 4,
                'previous_rank': 3
            }
        )
        
        # Update scores in case they already existed
        extra_ranking1.total_score = 150
        extra_ranking1.current_rank = 1
        extra_ranking1.previous_rank = 2
        extra_ranking1.save()
        
        extra_ranking2.total_score = 75
        extra_ranking2.current_rank = 4
        extra_ranking2.previous_rank = 3
        extra_ranking2.save()
        
        # Also update the main user's ranking to ensure consistent test
        self.user_ranking.total_score = 100  # Keep original test score
        self.user_ranking.save()
        
        # Create friendships (bidirectional relationships)
        Friend.objects.create(
            requester=self.user,
            recipient=extra_friend1,
            status='accepted'
        )
        Friend.objects.create(
            requester=extra_friend2,
            recipient=self.user,
            status='accepted'
        )
        
        # Test the friends leaderboard
        response = self.client.get('/api/rankings/leaderboard/', {'scope': 'friends'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        leaderboard = response.data['leaderboard']
        
        # Should include: user (100), friend (90), extra_friend1 (150), extra_friend2 (75)
        # Plus the current user should be included
        expected_users = 5  # user + original friend + 2 extra friends + current user should be in results
        
        print(f"Friends leaderboard count: {len(leaderboard)}")
        print("Friends in leaderboard:")
        for i, entry in enumerate(leaderboard):
            print(f"  {i+1}. {entry['name']} ({entry['username']}) - {entry['score']} pts - Current: {entry['isCurrentUser']}")
        
        # Verify all expected friends are included
        usernames_in_leaderboard = [entry['username'] for entry in leaderboard]
        
        # Check that all friends are included
        self.assertIn(self.user.username, usernames_in_leaderboard, "Current user should be in friends leaderboard")
        self.assertIn(self.friend.username, usernames_in_leaderboard, "Original friend should be in leaderboard")
        self.assertIn(extra_friend1.username, usernames_in_leaderboard, "Extra friend 1 should be in leaderboard")
        self.assertIn(extra_friend2.username, usernames_in_leaderboard, "Extra friend 2 should be in leaderboard")
        
        # Verify they are ordered by score (highest first)
        scores = [int(entry['score']) for entry in leaderboard]
        self.assertEqual(scores, sorted(scores, reverse=True), "Friends should be ordered by score descending")
        
        # Verify the highest score is extra_friend1 with 150
        self.assertEqual(leaderboard[0]['username'], extra_friend1.username)
        self.assertEqual(leaderboard[0]['score'], '150')
        
        # Verify current user is marked correctly
        current_user_entries = [entry for entry in leaderboard if entry['isCurrentUser']]
        self.assertEqual(len(current_user_entries), 1, "Exactly one entry should be marked as current user")
        self.assertEqual(current_user_entries[0]['username'], self.user.username)
