from django.shortcuts import render
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import F, Q
from django.contrib.gis.geos import Point
from django.contrib.gis.measure import D
from django.utils import timezone
from django.db import transaction

from .models import UserRanking, RankingHistory
from .serializers import UserRankingSerializer, RankingHistorySerializer
from friends.models import Friend

class RankingViewSet(viewsets.ModelViewSet):
    """
    ViewSet for user rankings with filtering options
    """
    serializer_class = UserRankingSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Base queryset for rankings"""
        return UserRanking.objects.all().select_related('user')
    
    # Override to make most actions read-only except for specific ones
    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ['update_location']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def list(self, request, *args, **kwargs):
        """Allow listing rankings"""
        return super().list(request, *args, **kwargs)
    
    def retrieve(self, request, *args, **kwargs):
        """Allow retrieving individual rankings"""
        return super().retrieve(request, *args, **kwargs)
    
    # Disable create, update, destroy for regular CRUD
    def create(self, request, *args, **kwargs):
        return Response(
            {"error": "Creating rankings directly is not allowed"},
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )
    
    def update(self, request, *args, **kwargs):
        return Response(
            {"error": "Updating rankings directly is not allowed"},
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )
    
    def destroy(self, request, *args, **kwargs):
        return Response(
            {"error": "Deleting rankings is not allowed"},
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )
    
    @action(detail=False, methods=['post'])
    def update_location(self, request):
        """
        Update user's location for ranking purposes
        This also updates the user's main location and creates location history
        """
        try:
            lat = request.data.get('latitude')
            lng = request.data.get('longitude')
            
            if not lat or not lng:
                return Response(
                    {"error": "Latitude and longitude are required"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            try:
                # Validate coordinates
                lat_float = float(lat)
                lng_float = float(lng)
                location = Point(lng_float, lat_float)
            except (ValueError, TypeError):
                return Response(
                    {"error": "Invalid coordinates"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            with transaction.atomic():
                # Update user's main location
                request.user.location = location
                request.user.last_location_update = timezone.now()
                request.user.save(update_fields=['location', 'last_location_update'])
                
                # Get or create user ranking and update ranking location
                ranking, created = UserRanking.objects.get_or_create(
                    user=request.user,
                    defaults={
                        'total_score': 0,
                        'current_rank': 0,
                        'previous_rank': 0
                    }
                )
                
                # Update ranking location
                ranking.update_location(lat_float, lng_float)
                
                # Create location history entry (optional - for analytics)
                from geo.models import UserLocation
                UserLocation.objects.create(
                    user=request.user,
                    location=location
                )
                
            return Response({
                "success": True,
                "message": "Location updated successfully",
                "location": {
                    "latitude": lat_float,
                    "longitude": lng_float,
                    "updated_at": ranking.location_updated_at
                }
            })
            
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False)
    def leaderboard(self, request):
        """
        Get leaderboard data with filtering options:
        - scope: 'local', 'friends', 'school', or 'all' (default)
        - lat/lng: coordinates for local rankings
        - radius_km: radius for local rankings (default: 5)
        - limit: number of results (default: 20)
        """
        # Get filter parameters
        scope = request.query_params.get('scope', 'all')
        lat = request.query_params.get('lat')
        lng = request.query_params.get('lng')
        radius_km = float(request.query_params.get('radius_km', 5))
        limit = int(request.query_params.get('limit', 20))
        
        # Generate cache key with reasonable precision for local rankings
        cache_key = None
        if scope == 'local' and lat and lng:
            # Round to 1 decimal place (≈11km grid) for reasonable caching zones
            lat_grid = round(float(lat), 1)
            lng_grid = round(float(lng), 1)
            cache_key = f"leaderboard:local:{lat_grid}:{lng_grid}:{radius_km}:{limit}"
            
            # Try to get from cache first
            from django.core.cache import cache
            cached_data = cache.get(cache_key)
            if cached_data:
                return Response(cached_data)
        elif scope == 'friends':
            cache_key = f"leaderboard:friends:{request.user.id}:{limit}"
            from django.core.cache import cache
            cached_data = cache.get(cache_key)
            if cached_data:
                return Response(cached_data)
        elif scope == 'school' and request.user.school:
            cache_key = f"leaderboard:school:{request.user.school.id}:{limit}"
            from django.core.cache import cache
            cached_data = cache.get(cache_key)
            if cached_data:
                return Response(cached_data)
        elif scope == 'all':
            cache_key = f"leaderboard:all:{limit}"
            from django.core.cache import cache
            cached_data = cache.get(cache_key)
            if cached_data:
                return Response(cached_data)
        
        # Start with base queryset
        queryset = self.get_queryset()
        
        # Apply scope filters
        if scope == 'friends':
            # Get friend IDs
            friend_ids = Friend.objects.filter(
                (Q(requester=request.user) | Q(recipient=request.user)),
                status='accepted'
            ).values_list(
                'requester_id', 'recipient_id'
            )
            
            # Flatten and get all friend IDs (including current user)
            friend_ids_set = {request.user.id}  # Include current user in friends ranking
            for req_id, rec_id in friend_ids:
                if req_id != request.user.id:
                    friend_ids_set.add(req_id)
                if rec_id != request.user.id:
                    friend_ids_set.add(rec_id)
            
            queryset = queryset.filter(user_id__in=friend_ids_set)
            
        elif scope == 'local' and lat and lng:
            try:
                user_location = Point(float(lng), float(lat))
                # Use ranking location first, fallback to user location
                # This allows for more flexible location-based ranking
                queryset = queryset.filter(
                    Q(last_location__isnull=False, last_location__distance_lte=(user_location, D(km=radius_km))) |
                    Q(last_location__isnull=True, user__location__isnull=False, user__location__distance_lte=(user_location, D(km=radius_km)))
                ).select_related('user')
            except (ValueError, TypeError):
                return Response(
                    {"error": "Invalid coordinates"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        elif scope == 'school':
            if not request.user.school:
                return Response(
                    {"error": "You must be associated with a school to view school rankings"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            queryset = queryset.filter(school=request.user.school)
        
        # Order by score and get paginated results
        rankings = queryset.order_by('-total_score')[:limit]
        
        # Format response data
        leaderboard_data = []
        school_info = None
        
        # Get school info if this is a school-scoped ranking
        if scope == 'school' and request.user.school:
            school_info = {
                'id': request.user.school.id,
                'name': request.user.school.name,
                'total_students': queryset.count(),
                'verified': request.user.school.verified
            }
        
        for i, ranking in enumerate(rankings):
            entry = {
                'id': str(ranking.user.id),
                'name': ranking.user.get_full_name() or ranking.user.username,
                'username': ranking.user.username,
                'avatar': request.build_absolute_uri(ranking.user.profile_pic) if ranking.user.profile_pic else None,
                'score': str(ranking.total_score),  # Use score directly, no conversion needed
                'change': 'up' if ranking.rank_change > 0 else ('down' if ranking.rank_change < 0 else 'same'),
                'changeAmount': str(abs(ranking.rank_change)),
                'isCurrentUser': ranking.user.id == request.user.id,
                'rank': i + 1
            }
            
            # Add school name to individual entries if school scope
            if scope == 'school' and ranking.school:
                entry['school'] = {
                    'id': ranking.school.id,
                    'name': ranking.school.name
                }
            
            leaderboard_data.append(entry)
        
        # Add current user's rank if not in top results
        current_user_in_results = any(entry['isCurrentUser'] for entry in leaderboard_data)
        
        if not current_user_in_results:
            try:
                user_ranking = UserRanking.objects.get(user=request.user)
                
                # For friends scope, calculate rank within friend group
                if scope == 'friends':
                    # Count how many friends have higher scores
                    friends_with_higher_scores = queryset.filter(
                        total_score__gt=user_ranking.total_score
                    ).count()
                    current_user_rank = friends_with_higher_scores + 1
                else:
                    # Use global rank for other scopes
                    current_user_rank = user_ranking.current_rank
                
                leaderboard_data.append({
                    'id': str(request.user.id),
                    'name': request.user.get_full_name() or request.user.username,
                    'username': request.user.username,
                    'avatar': request.build_absolute_uri(request.user.profile_pic) if request.user.profile_pic else None,
                    'score': str(user_ranking.total_score),  # Use score directly, no conversion needed
                    'change': 'up' if user_ranking.rank_change > 0 else ('down' if user_ranking.rank_change < 0 else 'same'),
                    'changeAmount': str(abs(user_ranking.rank_change)),
                    'isCurrentUser': True,
                    'rank': current_user_rank,
                    **(
                        {'school': {'id': request.user.school.id, 'name': request.user.school.name}} 
                        if scope == 'school' and request.user.school 
                        else {}
                    )
                })
            except UserRanking.DoesNotExist:
                pass
        
        # Get location info for local scope
        location_info = None
        if scope == 'local' and lat and lng:
            try:
                from geocoder import reverse
                location = reverse((lat, lng))
                if location and location.city:
                    location_info = location.city
                elif location and location.county:
                    location_info = location.county
            except Exception:
                pass  # Fail silently if geocoding fails
        
        response_data = {
            'leaderboard': leaderboard_data,
            'location': location_info,
            'scope': scope,
            'school_info': school_info
        }
        
        # Cache the response for reasonable time periods
        if cache_key:
            from django.core.cache import cache
            cache_timeout = 300  # 5 minutes for local/friends/school
            if scope == 'all':
                cache_timeout = 600  # 10 minutes for global rankings
            cache.set(cache_key, response_data, timeout=cache_timeout)
        
        return Response(response_data)
    
    @action(detail=False)
    def history(self, request):
        """Get ranking history for the current user"""
        history = RankingHistory.objects.filter(
            user=request.user
        ).order_by('-recorded_at')[:30]  # Last 30 records
        
        serializer = RankingHistorySerializer(history, many=True)
        return Response(serializer.data)
    
    @action(detail=False)
    def stats(self, request):
        """Get detailed ranking stats for the current user"""
        try:
            ranking = UserRanking.objects.get(user=request.user)
            return Response({
                'total_score': ranking.total_score,
                'current_rank': ranking.current_rank,
                'rank_change': ranking.rank_change,
                'components': {
                    'challenge_score': ranking.challenge_score,
                    'pin_score': ranking.pin_score,
                    'social_score': ranking.social_score,
                    'achievement_score': ranking.achievement_score
                }
            })
        except UserRanking.DoesNotExist:
            return Response(
                {"error": "Ranking not found"},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False)
    def badge(self, request):
        """Get current user's badge details and progress"""
        try:
            ranking = UserRanking.objects.get(user=request.user)
            
            # Get badge details
            badge_info = ranking.get_badge_details()
            
            # Calculate progress to next level
            current_xp = ranking.total_score * 10
            current_level_xp = ranking.level * 500
            next_level_xp = (ranking.level + 1) * 500 if ranking.level < 30 else current_level_xp
            
            progress = {
                'current_xp': current_xp,
                'level_xp': current_level_xp,
                'next_level_xp': next_level_xp,
                'progress_percent': min(100, ((current_xp - current_level_xp) / (next_level_xp - current_level_xp)) * 100) if ranking.level < 30 else 100
            }
            
            return Response({
                'badge': badge_info,
                'progress': progress,
                'total_score': ranking.total_score,
                'score_components': {
                    'challenge_score': ranking.challenge_score,
                    'pin_score': ranking.pin_score,
                    'social_score': ranking.social_score,
                    'achievement_score': ranking.achievement_score
                }
            })
            
        except UserRanking.DoesNotExist:
            return Response(
                {"error": "Ranking not found"},
                status=status.HTTP_404_NOT_FOUND
            )