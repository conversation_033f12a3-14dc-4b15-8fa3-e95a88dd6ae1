#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}BOPMaps OpenMapTiles AWS Deployment${NC}"
echo -e "${YELLOW}This script deploys OpenMapTiles planet data to AWS EFS and ECS${NC}"
echo ""

# Configuration
AWS_REGION=${AWS_REGION:-us-east-1}
ENVIRONMENT=${ENVIRONMENT:-prod}
PROJECT_NAME=${PROJECT_NAME:-bopmaps}

# Local paths
OPENMAPTILES_DIR="./docker/openmaptiles"
TILES_DIR="${OPENMAPTILES_DIR}/tiles"
STYLES_DIR="${OPENMAPTILES_DIR}/styles"
FONTS_DIR="${OPENMAPTILES_DIR}/fonts"
SPRITES_DIR="${OPENMAPTILES_DIR}/sprites"

# Functions
check_prerequisites() {
    echo -e "${YELLOW}Checking prerequisites...${NC}"
    
    # Check AWS CLI
    if ! command -v aws &> /dev/null; then
        echo -e "${RED}Error: AWS CLI is not installed${NC}"
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        echo -e "${RED}Error: AWS credentials not configured${NC}"
        exit 1
    fi
    
    # Check Terraform
    if ! command -v terraform &> /dev/null; then
        echo -e "${RED}Error: Terraform is not installed${NC}"
        exit 1
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}Error: Docker is not installed${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}Prerequisites check passed${NC}"
}

get_terraform_outputs() {
    echo -e "${YELLOW}Getting Terraform outputs...${NC}"
    
    cd terraform
    
    # Get EFS file system ID
    EFS_ID=$(terraform output -raw efs_id 2>/dev/null || echo "")
    if [ -z "$EFS_ID" ]; then
        echo -e "${RED}Error: Could not get EFS ID from Terraform outputs${NC}"
        exit 1
    fi
    
    # Get ECR repository URL
    ECR_REPO=$(terraform output -raw ecr_tileserver_repository_url 2>/dev/null || echo "")
    if [ -z "$ECR_REPO" ]; then
        echo -e "${RED}Error: Could not get ECR repository URL from Terraform outputs${NC}"
        exit 1
    fi
    
    # Get ECS cluster name
    ECS_CLUSTER=$(terraform output -raw ecs_cluster_name 2>/dev/null || echo "")
    if [ -z "$ECS_CLUSTER" ]; then
        echo -e "${RED}Error: Could not get ECS cluster name from Terraform outputs${NC}"
        exit 1
    fi
    
    cd ..
    
    echo -e "${GREEN}Retrieved infrastructure details:${NC}"
    echo "  EFS ID: $EFS_ID"
    echo "  ECR Repository: $ECR_REPO"
    echo "  ECS Cluster: $ECS_CLUSTER"
}

create_ec2_instance_for_upload() {
    echo -e "${YELLOW}Creating temporary EC2 instance for data upload...${NC}"
    
    # Get subnet ID (use first private subnet)
    SUBNET_ID=$(cd terraform && terraform output -json private_subnet_ids | jq -r '.[0]' && cd ..)
    
    # Get security group ID for ECS
    SECURITY_GROUP_ID=$(cd terraform && terraform output -json | jq -r '.security_groups.value.ecs' && cd ..)
    
    # Create EC2 instance
    INSTANCE_ID=$(aws ec2 run-instances \
        --image-id ami-0c02fb55956c7d316 \
        --instance-type t3.medium \
        --subnet-id "$SUBNET_ID" \
        --security-group-ids "$SECURITY_GROUP_ID" \
        --user-data file://scripts/ec2-userdata.sh \
        --tag-specifications "ResourceType=instance,Tags=[{Key=Name,Value=${PROJECT_NAME}-${ENVIRONMENT}-openmaptiles-upload},{Key=Project,Value=${PROJECT_NAME}},{Key=Environment,Value=${ENVIRONMENT}},{Key=Purpose,Value=OpenMapTiles Upload}]" \
        --query 'Instances[0].InstanceId' \
        --output text)
    
    echo "Created EC2 instance: $INSTANCE_ID"
    
    # Wait for instance to be running
    echo -e "${YELLOW}Waiting for instance to be running...${NC}"
    aws ec2 wait instance-running --instance-ids "$INSTANCE_ID"
    
    echo -e "${GREEN}EC2 instance is running${NC}"
    echo "$INSTANCE_ID"
}

create_efs_mount_script() {
    cat > /tmp/mount-efs.sh << 'EOF'
#!/bin/bash
set -e

# Install EFS utils
sudo yum update -y
sudo yum install -y amazon-efs-utils

# Create mount points
sudo mkdir -p /mnt/efs/{tiles,styles,fonts,sprites,region_bundles}

# Mount EFS
sudo mount -t efs -o tls,accesspoint=ACCESS_POINT_TILES EFS_ID:/ /mnt/efs/tiles
sudo mount -t efs -o tls,accesspoint=ACCESS_POINT_STYLES EFS_ID:/ /mnt/efs/styles
sudo mount -t efs -o tls,accesspoint=ACCESS_POINT_FONTS EFS_ID:/ /mnt/efs/fonts
sudo mount -t efs -o tls,accesspoint=ACCESS_POINT_SPRITES EFS_ID:/ /mnt/efs/sprites

# Set permissions
sudo chown -R ec2-user:ec2-user /mnt/efs
sudo chmod -R 755 /mnt/efs

echo "EFS mounted successfully"
EOF

    # Replace placeholders
    sed -i "s/EFS_ID/$EFS_ID/g" /tmp/mount-efs.sh
    
    # Get access point IDs from Terraform
    cd terraform
    ACCESS_POINT_TILES=$(terraform output -json | jq -r '.efs_access_points.value.tiles' 2>/dev/null || echo "")
    ACCESS_POINT_STYLES=$(terraform output -json | jq -r '.efs_access_points.value.styles' 2>/dev/null || echo "")
    ACCESS_POINT_FONTS=$(terraform output -json | jq -r '.efs_access_points.value.fonts' 2>/dev/null || echo "")
    ACCESS_POINT_SPRITES=$(terraform output -json | jq -r '.efs_access_points.value.sprites' 2>/dev/null || echo "")
    cd ..
    
    sed -i "s/ACCESS_POINT_TILES/$ACCESS_POINT_TILES/g" /tmp/mount-efs.sh
    sed -i "s/ACCESS_POINT_STYLES/$ACCESS_POINT_STYLES/g" /tmp/mount-efs.sh
    sed -i "s/ACCESS_POINT_FONTS/$ACCESS_POINT_FONTS/g" /tmp/mount-efs.sh
    sed -i "s/ACCESS_POINT_SPRITES/$ACCESS_POINT_SPRITES/g" /tmp/mount-efs.sh
}

upload_data_to_efs() {
    echo -e "${YELLOW}Uploading OpenMapTiles data to EFS...${NC}"
    
    # Validate local data exists
    if [ ! -f "${TILES_DIR}/planet.mbtiles" ]; then
        echo -e "${RED}Error: Planet tiles not found. Run setup-openmaptiles.sh first${NC}"
        exit 1
    fi
    
    # Create EC2 instance for upload
    INSTANCE_ID=$(create_ec2_instance_for_upload)
    
    # Create mount script
    create_efs_mount_script
    
    # Wait a bit more for the instance to be ready
    echo -e "${YELLOW}Waiting for instance to be ready for connections...${NC}"
    sleep 60
    
    # Get instance public IP
    INSTANCE_IP=$(aws ec2 describe-instances \
        --instance-ids "$INSTANCE_ID" \
        --query 'Reservations[0].Instances[0].PublicIpAddress' \
        --output text)
    
    # Copy mount script and execute
    echo -e "${YELLOW}Setting up EFS mounts...${NC}"
    scp -o StrictHostKeyChecking=no /tmp/mount-efs.sh ec2-user@${INSTANCE_IP}:~/
    ssh -o StrictHostKeyChecking=no ec2-user@${INSTANCE_IP} 'chmod +x ~/mount-efs.sh && ~/mount-efs.sh'
    
    # Upload planet tiles
    echo -e "${YELLOW}Uploading planet tiles (~50GB)...${NC}"
    echo -e "${RED}This will take a significant amount of time!${NC}"
    scp -o StrictHostKeyChecking=no "${TILES_DIR}/planet.mbtiles" ec2-user@${INSTANCE_IP}:/mnt/efs/tiles/
    
    # Upload styles
    echo -e "${YELLOW}Uploading styles...${NC}"
    scp -r -o StrictHostKeyChecking=no "${STYLES_DIR}/" ec2-user@${INSTANCE_IP}:/mnt/efs/styles/
    
    # Upload fonts
    echo -e "${YELLOW}Uploading fonts...${NC}"
    scp -r -o StrictHostKeyChecking=no "${FONTS_DIR}/" ec2-user@${INSTANCE_IP}:/mnt/efs/fonts/
    
    # Upload sprites
    echo -e "${YELLOW}Uploading sprites...${NC}"
    scp -r -o StrictHostKeyChecking=no "${SPRITES_DIR}/" ec2-user@${INSTANCE_IP}:/mnt/efs/sprites/
    
    # Upload config
    echo -e "${YELLOW}Uploading configuration...${NC}"
    scp -o StrictHostKeyChecking=no "${OPENMAPTILES_DIR}/config.json" ec2-user@${INSTANCE_IP}:/mnt/efs/
    
    # Verify upload
    echo -e "${YELLOW}Verifying upload...${NC}"
    ssh -o StrictHostKeyChecking=no ec2-user@${INSTANCE_IP} 'ls -la /mnt/efs/ && du -sh /mnt/efs/*'
    
    # Cleanup
    echo -e "${YELLOW}Cleaning up EC2 instance...${NC}"
    aws ec2 terminate-instances --instance-ids "$INSTANCE_ID"
    
    echo -e "${GREEN}Data upload completed successfully${NC}"
}

build_and_push_docker_image() {
    echo -e "${YELLOW}Building and pushing OpenMapTiles Docker image...${NC}"
    
    # Login to ECR
    aws ecr get-login-password --region "$AWS_REGION" | docker login --username AWS --password-stdin "$ECR_REPO"
    
    # Build image
    docker build -f docker/Dockerfile.openmaptiles -t "$ECR_REPO:latest" .
    docker build -f docker/Dockerfile.openmaptiles -t "$ECR_REPO:openmaptiles-$(date +%Y%m%d)" .
    
    # Push images
    docker push "$ECR_REPO:latest"
    docker push "$ECR_REPO:openmaptiles-$(date +%Y%m%d)"
    
    echo -e "${GREEN}Docker image built and pushed successfully${NC}"
}

deploy_to_ecs() {
    echo -e "${YELLOW}Deploying to ECS...${NC}"
    
    # Update ECS service to force new deployment
    aws ecs update-service \
        --cluster "$ECS_CLUSTER" \
        --service "${PROJECT_NAME}-${ENVIRONMENT}-tileserver" \
        --force-new-deployment \
        --region "$AWS_REGION"
    
    # Wait for deployment to complete
    echo -e "${YELLOW}Waiting for deployment to complete...${NC}"
    aws ecs wait services-stable \
        --cluster "$ECS_CLUSTER" \
        --services "${PROJECT_NAME}-${ENVIRONMENT}-tileserver" \
        --region "$AWS_REGION"
    
    echo -e "${GREEN}ECS deployment completed${NC}"
}

test_deployment() {
    echo -e "${YELLOW}Testing deployment...${NC}"
    
    # Get load balancer URL
    cd terraform
    LB_URL=$(terraform output -raw load_balancer_url 2>/dev/null || echo "")
    cd ..
    
    if [ -z "$LB_URL" ]; then
        echo -e "${RED}Error: Could not get load balancer URL${NC}"
        return 1
    fi
    
    # Test tile server endpoints
    echo "Testing tile server endpoints..."
    
    # Test health endpoint
    if curl -f "${LB_URL}/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Health endpoint working${NC}"
    else
        echo -e "${RED}✗ Health endpoint failed${NC}"
    fi
    
    # Test styles endpoint
    if curl -f "${LB_URL}/styles/" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Styles endpoint working${NC}"
    else
        echo -e "${RED}✗ Styles endpoint failed${NC}"
    fi
    
    # Test a sample tile
    if curl -f "${LB_URL}/data/planet/0/0/0.pbf" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Tile serving working${NC}"
    else
        echo -e "${RED}✗ Tile serving failed${NC}"
    fi
    
    echo -e "${GREEN}Deployment testing completed${NC}"
    echo -e "${GREEN}OpenMapTiles is now available at: ${LB_URL}${NC}"
}

# Main execution
main() {
    echo -e "${GREEN}Starting OpenMapTiles AWS deployment...${NC}"
    
    check_prerequisites
    get_terraform_outputs
    
    echo -e "${YELLOW}Deployment plan:${NC}"
    echo "1. Upload OpenMapTiles data to EFS"
    echo "2. Build and push Docker image"
    echo "3. Deploy to ECS"
    echo "4. Test deployment"
    echo ""
    
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Deployment cancelled${NC}"
        exit 0
    fi
    
    upload_data_to_efs
    build_and_push_docker_image
    deploy_to_ecs
    test_deployment
    
    echo -e "${GREEN}OpenMapTiles deployment completed successfully!${NC}"
}

# Show usage if help requested
if [[ "$1" == "--help" ]] || [[ "$1" == "-h" ]]; then
    echo "BOPMaps OpenMapTiles AWS Deployment"
    echo ""
    echo "This script deploys OpenMapTiles planet data to AWS infrastructure."
    echo ""
    echo "Prerequisites:"
    echo "  - AWS CLI configured with appropriate permissions"
    echo "  - Terraform infrastructure already deployed"
    echo "  - OpenMapTiles data prepared (run setup-openmaptiles.sh first)"
    echo "  - Docker installed"
    echo ""
    echo "Usage:"
    echo "  $0                 Run deployment"
    echo "  $0 --help         Show this help"
    echo ""
    echo "Environment variables:"
    echo "  AWS_REGION         AWS region (default: us-east-1)"
    echo "  ENVIRONMENT        Environment name (default: prod)"
    echo "  PROJECT_NAME       Project name (default: bopmaps)"
    echo ""
    exit 0
fi

# Run main function
main "$@" 