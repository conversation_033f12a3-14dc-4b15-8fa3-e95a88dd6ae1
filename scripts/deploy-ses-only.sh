#!/bin/bash

# Deploy only SES resources for testing
# Usage: ./scripts/deploy-ses-only.sh

set -e

echo "🎵 BOPMaps SES Deployment Script"
echo "================================="

# Check if we're in the right directory
if [ ! -f "terraform/ses.tf" ]; then
    echo "❌ Error: terraform/ses.tf not found. Please run this script from the project root."
    exit 1
fi

# Change to terraform directory
cd terraform

echo "📋 Checking current directory: $(pwd)"

# Initialize terraform if needed
if [ ! -d ".terraform" ]; then
    echo "🔧 Initializing Terraform..."
    terraform init
else
    echo "✅ Terraform already initialized"
fi

# Check if terraform.tfvars exists
if [ ! -f "terraform.tfvars" ]; then
    echo "❌ Error: terraform.tfvars not found."
    echo "Please create terraform.tfvars with your configuration variables."
    echo "Required variables for SES:"
    echo "  - cloudflare_api_token"
    echo "  - cloudflare_zone_id" 
    echo "  - cloudflare_account_id"
    echo "  - aws_region"
    exit 1
fi

# Plan deployment targeting only SES resources
echo "📋 Planning SES deployment..."
terraform plan \
    -target="aws_ses_email_identity.admin_email" \
    -target="aws_ses_domain_identity.bopmaps_domain" \
    -target="aws_ses_domain_dkim.bopmaps_dkim" \
    -target="aws_ses_configuration_set.bopmaps_emails" \
    -target="aws_ses_event_destination.cloudwatch" \
    -target="aws_ses_template.user_verification" \
    -target="aws_ses_template.school_verification" \
    -target="aws_ses_template.school_verification_success" \
    -target="aws_cloudwatch_log_group.ses_logs" \
    -target="aws_iam_policy.ses_send_policy" \
    -target="aws_iam_role_policy_attachment.ecs_task_ses_policy" \
    -out=ses-plan.tfplan

# Ask for confirmation
echo ""
read -p "🚀 Do you want to apply the SES deployment? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Deployment cancelled."
    rm -f ses-plan.tfplan
    exit 1
fi

# Apply the plan
echo "🚀 Deploying SES resources..."
terraform apply ses-plan.tfplan

# Clean up plan file
rm -f ses-plan.tfplan

echo ""
echo "✅ SES deployment completed!"
echo ""
echo "📋 Next Steps:"
echo "1. Verify <EMAIL> email in AWS SES Console"
echo "2. Add DKIM records to your DNS (see terraform output)"
echo "3. Test email sending with: python manage.py test_ses <EMAIL>"
echo ""
echo "🔍 SES Resources Created:"
echo "  - Email identity: <EMAIL>"
echo "  - Domain identity: bopmaps.com"
echo "  - DKIM authentication"
echo "  - Configuration set: bopmaps-emails"
echo "  - Email templates: user-verification, school-verification, school-verification-success"
echo "  - CloudWatch logging"
echo "  - IAM policies for email sending"
echo ""

# Show important outputs
echo "📤 Important Information:"
terraform output ses_admin_email 2>/dev/null || echo "  Admin Email: <EMAIL>"
terraform output ses_configuration_set_name 2>/dev/null || echo "  Configuration Set: bopmaps-emails"

echo ""
echo "🎯 Test Command:"
echo "python manage.py test_ses <EMAIL> --template=raw"
echo ""
echo "💡 Remember to:"
echo "  - Check your AWS SES dashboard for sending statistics"
echo "  - Verify the admin email address in SES console"
echo "  - Request production access if needed (remove sandbox)"
echo "  - Monitor CloudWatch logs for email delivery status" 