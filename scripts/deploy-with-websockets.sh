#!/bin/bash

# BOPMaps WebSocket-Enabled Deployment Script
# This script builds and deploys the Docker image with WebSocket support to ECS

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
PROJECT_NAME="bopmaps"
ENVIRONMENT="prod"
AWS_REGION="us-east-1"
DOCKERFILE="Dockerfile.prod"

print_status "Starting WebSocket-enabled deployment for BOPMaps..."

# Check if we're in the correct directory
if [ ! -f "manage.py" ]; then
    print_error "This script must be run from the project root directory"
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    print_error "AWS CLI is not configured. Please run 'aws configure' first."
    exit 1
fi

print_status "Checking Terraform outputs..."

# Get ECR repository URL from Terraform
cd terraform
if [ ! -f "terraform.tfstate" ]; then
    print_error "Terraform state not found. Please run 'terraform apply' first."
    exit 1
fi

ECR_REPO=$(terraform output -raw ecr_repository_url 2>/dev/null)
if [ -z "$ECR_REPO" ]; then
    print_error "Could not get ECR repository URL from Terraform outputs"
    exit 1
fi

print_status "ECR Repository: $ECR_REPO"

# Go back to project root
cd ..

print_status "Logging into ECR..."
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REPO

print_status "Building Docker image with WebSocket support for amd64..."
docker build --platform linux/amd64 -f $DOCKERFILE -t $ECR_REPO:latest .

print_status "Pushing Docker image to ECR..."
docker push $ECR_REPO:latest

print_status "Installing PostGIS on database..."

# Create a one-time task to install PostGIS
print_status "Creating PostGIS installation task..."

# Get the current task definition for PostGIS installation
POSTGIS_TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "${PROJECT_NAME}-${ENVIRONMENT}-app" --region "$AWS_REGION" --query 'taskDefinition' --output json)

# Create a modified task definition for PostGIS installation
NEW_POSTGIS_TASK_DEFINITION=$(echo "$POSTGIS_TASK_DEFINITION" | jq -c \
    --arg IMAGE "$ECR_REPO:latest" \
    'del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy) | 
     .family = "postgis-install-temp" |
     .containerDefinitions[0].image = $IMAGE |
     .containerDefinitions[0].name = "postgis-install" |
     .containerDefinitions[0].command = ["python", "/app/scripts/install_postgis.py"]')

# Register the PostGIS installation task definition
print_status "Registering PostGIS installation task definition..."
POSTGIS_TASK_INFO=$(aws ecs register-task-definition \
    --cli-input-json "$NEW_POSTGIS_TASK_DEFINITION" \
    --region "$AWS_REGION" \
    --output json)

# Extract PostGIS task revision
POSTGIS_REVISION=$(echo "$POSTGIS_TASK_INFO" | jq -r '.taskDefinition.revision // empty')
if [ -z "$POSTGIS_REVISION" ]; then
    print_warning "PostGIS task definition registration failed, continuing with migration (PostGIS may already be installed)"
else
    print_status "Running PostGIS installation task..."
    
    # Get network configuration from Terraform outputs
    cd terraform
    PRIVATE_SUBNET_ID=$(terraform output -json private_subnet_ids | jq -r '.[0]')
    ECS_SECURITY_GROUP_ID=$(terraform output -raw ecs_security_group_id)
    cd ..
    
    # Run the PostGIS installation task
    POSTGIS_TASK_ARN=$(aws ecs run-task \
        --cluster "$PROJECT_NAME-$ENVIRONMENT" \
        --task-definition "postgis-install-temp:$POSTGIS_REVISION" \
        --network-configuration "awsvpcConfiguration={subnets=[\"$PRIVATE_SUBNET_ID\"],securityGroups=[\"$ECS_SECURITY_GROUP_ID\"]}" \
        --region "$AWS_REGION" \
        --output text --query 'tasks[0].taskArn')
    
    if [ -n "$POSTGIS_TASK_ARN" ]; then
        print_status "Waiting for PostGIS installation to complete..."
        aws ecs wait tasks-stopped --cluster "$PROJECT_NAME-$ENVIRONMENT" --tasks "$POSTGIS_TASK_ARN" --region "$AWS_REGION"
        print_status "PostGIS installation completed"
    else
        print_warning "Failed to start PostGIS installation task"
    fi
    
    # Clean up temporary task definition
    aws ecs deregister-task-definition --task-definition "postgis-install-temp:$POSTGIS_REVISION" --region "$AWS_REGION" > /dev/null 2>&1
fi

print_status "Updating ECS services with new task definitions..."

# Update main app service
print_status "Updating main application service..."

# Get the current task definition
TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "${PROJECT_NAME}-${ENVIRONMENT}-app" --region "$AWS_REGION" --query 'taskDefinition' --output json)

# Debug: Print the raw task definition
print_status "Raw Task Definition:"
echo "$TASK_DEFINITION" | jq '.'

# Create a new task definition with the new image
# Use a more robust jq command with error handling
NEW_TASK_DEFINITION=$(echo "$TASK_DEFINITION" | jq -c \
    --arg IMAGE "$ECR_REPO:latest" \
    'del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy) | 
     .containerDefinitions[0].image = $IMAGE')

# Debug: Print the new task definition
print_status "New Task Definition:"
echo "$NEW_TASK_DEFINITION" | jq '.'

# Validate the new task definition
if ! echo "$NEW_TASK_DEFINITION" | jq empty > /dev/null 2>&1; then
    print_error "Invalid task definition JSON"
    exit 1
fi

# Register the new task definition
print_status "Registering new task definition..."
NEW_TASK_INFO=$(aws ecs register-task-definition \
    --cli-input-json "$NEW_TASK_DEFINITION" \
    --region "$AWS_REGION" \
    --output json)

# Debug: Print the new task info
print_status "New Task Info:"
echo "$NEW_TASK_INFO" | jq '.'

# Extract new revision with error handling
NEW_REVISION=$(echo "$NEW_TASK_INFO" | jq -r '.taskDefinition.revision // empty')
if [ -z "$NEW_REVISION" ]; then
    print_error "Failed to extract task definition revision"
    exit 1
fi

print_status "Registered new task definition revision: $NEW_REVISION"

# Update the service with the new task definition
aws ecs update-service \
    --cluster "$PROJECT_NAME-$ENVIRONMENT" \
    --service "$PROJECT_NAME-$ENVIRONMENT-app" \
    --task-definition "$PROJECT_NAME-$ENVIRONMENT-app:$NEW_REVISION" \
    --force-new-deployment \
    --region "$AWS_REGION"

# Update Celery service
print_status "Updating Celery service..."

# Get the current Celery task definition
CELERY_TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "${PROJECT_NAME}-${ENVIRONMENT}-celery" --region "$AWS_REGION" --query 'taskDefinition' --output json)

# Create a new Celery task definition with the new image
NEW_CELERY_TASK_DEFINITION=$(echo "$CELERY_TASK_DEFINITION" | jq -c \
    --arg IMAGE "$ECR_REPO:latest" \
    'del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy) | 
     .containerDefinitions[0].image = $IMAGE')

# Validate the new Celery task definition
if ! echo "$NEW_CELERY_TASK_DEFINITION" | jq empty > /dev/null 2>&1; then
    print_error "Invalid Celery task definition JSON"
    exit 1
fi

# Register the new Celery task definition
print_status "Registering new Celery task definition..."
NEW_CELERY_TASK_INFO=$(aws ecs register-task-definition \
    --cli-input-json "$NEW_CELERY_TASK_DEFINITION" \
    --region "$AWS_REGION" \
    --output json)

# Extract new Celery revision
NEW_CELERY_REVISION=$(echo "$NEW_CELERY_TASK_INFO" | jq -r '.taskDefinition.revision // empty')
if [ -z "$NEW_CELERY_REVISION" ]; then
    print_error "Failed to extract Celery task definition revision"
    exit 1
fi

print_status "Registered new Celery task definition revision: $NEW_CELERY_REVISION"

# Update the Celery service with the new task definition
aws ecs update-service \
    --cluster "$PROJECT_NAME-$ENVIRONMENT" \
    --service "$PROJECT_NAME-$ENVIRONMENT-celery" \
    --task-definition "$PROJECT_NAME-$ENVIRONMENT-celery:$NEW_CELERY_REVISION" \
    --force-new-deployment \
    --region "$AWS_REGION"

print_status "Waiting for deployments to complete..."

# Wait for main app service to stabilize
print_status "Waiting for main application service to stabilize..."
aws ecs wait services-stable \
    --cluster "$PROJECT_NAME-$ENVIRONMENT" \
    --services "$PROJECT_NAME-$ENVIRONMENT-app" \
    --region "$AWS_REGION"

# Wait for Celery service to stabilize
print_status "Waiting for Celery service to stabilize..."
aws ecs wait services-stable \
    --cluster "$PROJECT_NAME-$ENVIRONMENT" \
    --services "$PROJECT_NAME-$ENVIRONMENT-celery" \
    --region "$AWS_REGION"

print_status "All deployments completed successfully!"

# Get the load balancer URL
cd terraform
ALB_URL=$(terraform output -raw alb_dns_name 2>/dev/null)
if [ -n "$ALB_URL" ]; then
    print_status "Application URL: https://$ALB_URL"
fi

print_status "Testing WebSocket endpoints..."

# Test the health endpoint
print_status "Testing health endpoint..."
HEALTH_URL="https://$ALB_URL/health/"
if curl -f -s "$HEALTH_URL" > /dev/null; then
    print_status "✓ Health endpoint is working"
else
    print_warning "⚠ Health endpoint test failed - this is normal during deployment"
fi

print_status "WebSocket deployment completed!"
print_status "WebSocket URL: wss://$ALB_URL/ws/achievements/"

echo ""
echo "Next steps:"
echo "1. Test WebSocket connection: wss://$ALB_URL/ws/achievements/"
echo "2. Monitor deployment: aws ecs describe-services --cluster $PROJECT_NAME-$ENVIRONMENT --services $PROJECT_NAME-$ENVIRONMENT-app"
echo "3. Check logs: aws logs tail /ecs/$PROJECT_NAME-$ENVIRONMENT/app --follow"
echo "4. Monitor Celery: aws logs tail /ecs/$PROJECT_NAME-$ENVIRONMENT/celery --follow"
echo ""
echo "WebSocket features enabled:"
echo "- Gamification achievement notifications"
echo "- Real-time user updates"
echo "- Redis channel layer for distributed WebSocket support"
echo "- Celery background tasks for async processing"
echo "" 