#!/usr/bin/env python3
"""
Script to install PostGIS extension on the production database
"""
import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_postgis():
    """Install PostGIS extension on the database"""
    # Get database connection parameters from environment
    database_url = os.environ.get('DATABASE_URL')
    if not database_url:
        logger.error("DATABASE_URL environment variable not set")
        return False
    
    # Parse the database URL (format: postgis://user:password@host:port/database)
    try:
        # Remove the postgis:// prefix and handle it as postgres://
        if database_url.startswith('postgis://'):
            database_url = database_url.replace('postgis://', 'postgres://')
        
        # Connect to the database
        conn = psycopg2.connect(database_url)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        logger.info("Connected to database successfully")
        
        # Check if PostGIS is already installed
        cursor.execute("SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'postgis');")
        postgis_exists = cursor.fetchone()[0]
        
        if postgis_exists:
            logger.info("PostGIS extension is already installed")
        else:
            logger.info("Installing PostGIS extension...")
            
            # Install PostGIS extension
            cursor.execute("CREATE EXTENSION IF NOT EXISTS postgis;")
            logger.info("PostGIS extension installed successfully")
            
            # Install PostGIS topology extension (optional but recommended)
            cursor.execute("CREATE EXTENSION IF NOT EXISTS postgis_topology;")
            logger.info("PostGIS topology extension installed successfully")
        
        # Verify installation
        cursor.execute("SELECT PostGIS_Full_Version();")
        version = cursor.fetchone()[0]
        logger.info(f"PostGIS version: {version}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        logger.error(f"Error installing PostGIS: {str(e)}")
        return False

if __name__ == "__main__":
    success = install_postgis()
    sys.exit(0 if success else 1) 