#!/usr/bin/env python
import os
import django
from django.core.management import call_command

def main():
    """Run database migrations and create cache tables."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bopmaps.settings')
    django.setup()
    
    # Run migrations first
    call_command('migrate')
    
    # Create cache tables
    try:
        call_command('createcachetable')
        print("Successfully created cache tables")
    except Exception as e:
        print(f"Error creating cache tables: {e}")

if __name__ == '__main__':
    main() 