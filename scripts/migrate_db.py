#!/usr/bin/env python3
import os
import sys
import boto3
import json
import logging
import django
from urllib.parse import urlparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def get_secret(secret_name):
    """Get secret from AWS Secrets Manager"""
    try:
        session = boto3.session.Session()
        client = session.client(
            service_name='secretsmanager',
            region_name='us-east-1'
        )
        response = client.get_secret_value(SecretId=secret_name)
        return response['SecretString']
    except Exception as e:
        logger.error(f"Failed to get secret {secret_name}: {e}")
        raise

def setup_django_environment():
    """Setup Django environment with proper settings"""
    try:
        # Get secrets
        database_url = get_secret('bopmaps-prod-database-url')
        django_secret = get_secret('bopmaps-prod-django-secret-b2exfo')

        # Set environment variables
        os.environ['DATABASE_URL'] = database_url
        os.environ['DJANGO_SETTINGS_MODULE'] = 'bopmaps.settings'
        os.environ['SECRET_KEY'] = django_secret
        os.environ['ENVIRONMENT'] = 'prod'
        os.environ['DEBUG'] = 'False'

        # Initialize Django
        django.setup()
        logger.info("Django environment initialized successfully")
    except Exception as e:
        logger.error(f"Failed to setup Django environment: {e}")
        raise

def run_migrations():
    """Run Django migrations in the correct order"""
    try:
        from django.core.management import execute_from_command_line

        # Run migrations for built-in apps first
        base_apps = [
            'contenttypes',
            'auth',
            'admin',
            'sessions',
        ]

        for app in base_apps:
            logger.info(f"Running migrations for {app}")
            execute_from_command_line(['manage.py', 'migrate', app])

        # Run remaining migrations
        logger.info("Running migrations for remaining apps")
        execute_from_command_line(['manage.py', 'migrate'])

        logger.info("All migrations completed successfully")
    except Exception as e:
        logger.error(f"Failed to run migrations: {e}")
        raise

def main():
    try:
        logger.info("Starting database migration process")
        setup_django_environment()
        run_migrations()
        logger.info("Migration process completed successfully")
    except Exception as e:
        logger.error(f"Migration process failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main() 