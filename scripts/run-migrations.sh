#!/bin/bash

# Script to run Django migrations in ECS
set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${G<PERSON><PERSON>}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
PROJECT_NAME="bopmaps"
ENVIRONMENT="prod"
AWS_REGION="us-east-1"

print_status "Starting database setup in ECS..."

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    print_error "AWS CLI is not configured. Please run 'aws configure' first."
    exit 1
fi

# Get the current task definition
TASK_DEFINITION=$(aws ecs describe-task-definition \
    --task-definition "${PROJECT_NAME}-${ENVIRONMENT}-app" \
    --region "$AWS_REGION" \
    --query 'taskDefinition' \
    --output json)

# Get the subnet and security group from the existing service
SERVICE_INFO=$(aws ecs describe-services \
    --cluster "${PROJECT_NAME}-${ENVIRONMENT}" \
    --services "${PROJECT_NAME}-${ENVIRONMENT}-app" \
    --region "$AWS_REGION" \
    --output json)

SUBNET_ID=$(echo "$SERVICE_INFO" | jq -r '.services[0].networkConfiguration.awsvpcConfiguration.subnets[0]')
SECURITY_GROUP=$(echo "$SERVICE_INFO" | jq -r '.services[0].networkConfiguration.awsvpcConfiguration.securityGroups[0]')

if [ -z "$SUBNET_ID" ] || [ -z "$SECURITY_GROUP" ]; then
    print_error "Failed to get network configuration from existing service"
    exit 1
fi

print_status "Running database setup task..."
TASK_ARN=$(aws ecs run-task \
    --cluster "${PROJECT_NAME}-${ENVIRONMENT}" \
    --task-definition "${PROJECT_NAME}-${ENVIRONMENT}-app" \
    --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_ID],securityGroups=[$SECURITY_GROUP],assignPublicIp=ENABLED}" \
    --overrides '{"containerOverrides": [{"name": "app", "command": ["python", "manage.py", "setup_database"]}]}' \
    --launch-type FARGATE \
    --region "$AWS_REGION" \
    --output json \
    | jq -r '.tasks[0].taskArn')

if [ -z "$TASK_ARN" ]; then
    print_error "Failed to start setup task"
    exit 1
fi

print_status "Setup task started. Task ARN: $TASK_ARN"
print_status "Waiting for setup to complete..."

# Extract task ID from ARN
TASK_ID=$(echo $TASK_ARN | awk -F'/' '{print $3}')

# Wait for task to complete
aws ecs wait tasks-stopped \
    --cluster "${PROJECT_NAME}-${ENVIRONMENT}" \
    --tasks "$TASK_ID" \
    --region "$AWS_REGION"

# Check task status
TASK_STATUS=$(aws ecs describe-tasks \
    --cluster "${PROJECT_NAME}-${ENVIRONMENT}" \
    --tasks "$TASK_ID" \
    --region "$AWS_REGION" \
    --output json \
    | jq -r '.tasks[0].containers[0].exitCode')

if [ "$TASK_STATUS" = "0" ]; then
    print_status "Database setup completed successfully!"
else
    print_error "Setup task failed with exit code $TASK_STATUS"
    print_error "Check CloudWatch logs for details"
    exit 1
fi 