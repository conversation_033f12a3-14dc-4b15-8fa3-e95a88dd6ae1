#!/bin/bash

# Run production challenges and pin skins generation on ECS

# Get cluster info
CLUSTER_NAME="bopmaps-prod"
TASK_DEFINITION="bopmaps-prod-app:9"
SUBNET_1="subnet-0a5907b0913de8fcd"
SUBNET_2="subnet-0cbe7b7c8863dabbe"

# Get ECS security group
ECS_SG=$(aws ec2 describe-security-groups --filters "Name=group-name,Values=bopmaps-prod-ecs-*" --query 'SecurityGroups[0].GroupId' --output text)

echo "Running production challenges and pin skins generation on ECS..."
echo "Cluster: $CLUSTER_NAME"
echo "Task Definition: $TASK_DEFINITION"
echo "Security Group: $ECS_SG"

# First create cache table if it doesn't exist
echo "Step 1: Making and running migrations..."
TASK_OUTPUT=$(aws ecs run-task \
  --cluster $CLUSTER_NAME \
  --task-definition $TASK_DEFINITION \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_1,$SUBNET_2],securityGroups=[$ECS_SG],assignPublicIp=DISABLED}" \
  --overrides '{"containerOverrides":[{"name":"app","command":["python","manage.py","makemigrations"]}]}' \
  --output json)

TASK_ARN=$(echo "$TASK_OUTPUT" | jq -r '.tasks[0].taskArn')
echo "Make migrations task started. Task ARN: $TASK_ARN"

# Wait for make migrations to complete
echo "Waiting for make migrations task to complete..."
aws ecs wait tasks-stopped \
  --cluster $CLUSTER_NAME \
  --tasks "$TASK_ARN"

# Run migrations
echo "Running migrations..."
TASK_OUTPUT=$(aws ecs run-task \
  --cluster $CLUSTER_NAME \
  --task-definition $TASK_DEFINITION \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_1,$SUBNET_2],securityGroups=[$ECS_SG],assignPublicIp=DISABLED}" \
  --overrides '{"containerOverrides":[{"name":"app","command":["python","manage.py","migrate"]}]}' \
  --output json)

TASK_ARN=$(echo "$TASK_OUTPUT" | jq -r '.tasks[0].taskArn')
echo "Run migrations task started. Task ARN: $TASK_ARN"

# Wait for migrations to complete
echo "Waiting for migrations task to complete..."
aws ecs wait tasks-stopped \
  --cluster $CLUSTER_NAME \
  --tasks "$TASK_ARN"

# Then run challenges generation with clear-existing option
echo "Step 2: Generating production challenges with database clearing..."
TASK_OUTPUT=$(aws ecs run-task \
  --cluster $CLUSTER_NAME \
  --task-definition $TASK_DEFINITION \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_1,$SUBNET_2],securityGroups=[$ECS_SG],assignPublicIp=DISABLED}" \
  --overrides '{"containerOverrides":[{"name":"app","command":["python","manage.py","create_initial_challenges"]}]}' \
  --output json)

TASK_ARN=$(echo "$TASK_OUTPUT" | jq -r '.tasks[0].taskArn')
echo "Production challenges generation task started. Task ARN: $TASK_ARN"

# Wait for challenges generation to complete
echo "Waiting for challenges generation task to complete..."
aws ecs wait tasks-stopped \
  --cluster $CLUSTER_NAME \
  --tasks "$TASK_ARN"

# Finally run pin skins generation with clear-existing option
echo "Step 3: Generating production pin skins with database clearing..."
TASK_OUTPUT=$(aws ecs run-task \
  --cluster $CLUSTER_NAME \
  --task-definition $TASK_DEFINITION \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_1,$SUBNET_2],securityGroups=[$ECS_SG],assignPublicIp=DISABLED}" \
  --overrides '{"containerOverrides":[{"name":"app","command":["python","manage.py","generate_production_pins","--clear-existing"]}]}' \
  --output json)

TASK_ARN=$(echo "$TASK_OUTPUT" | jq -r '.tasks[0].taskArn')
echo "Production pin skins generation task started. Task ARN: $TASK_ARN"

# Wait for pin skins generation to complete
echo "Waiting for pin skins generation task to complete..."
aws ecs wait tasks-stopped \
  --cluster $CLUSTER_NAME \
  --tasks "$TASK_ARN"

echo "All tasks have completed! Check ECS console for detailed logs." 