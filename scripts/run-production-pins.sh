#!/bin/bash

# Run production pins generation on ECS

# Get cluster info
CLUSTER_NAME="bopmaps-prod"
TASK_DEFINITION="bopmaps-prod-app:36"
SUBNET_1="subnet-0a5907b0913de8fcd"
SUBNET_2="subnet-0cbe7b7c8863dabbe"

# Get ECS security group
ECS_SG=$(aws ec2 describe-security-groups --filters "Name=group-name,Values=bopmaps-prod-ecs-*" --query 'SecurityGroups[0].GroupId' --output text)

echo "Running production pins generation on ECS..."
echo "Cluster: $CLUSTER_NAME"
echo "Task Definition: $TASK_DEFINITION"
echo "Security Group: $ECS_SG"

# Run the task with cleanup-duplicates option
TASK_OUTPUT=$(aws ecs run-task \
  --cluster $CLUSTER_NAME \
  --task-definition $TASK_DEFINITION \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_1,$SUBNET_2],securityGroups=[$ECS_SG],assignPublicIp=DISABLED}" \
  --overrides '{"containerOverrides":[{"name":"app","command":["python","manage.py","generate_production_pins","--cleanup-duplicates"]}]}' \
  --output json)

TASK_ARN=$(echo "$TASK_OUTPUT" | jq -r '.tasks[0].taskArn')
echo "Production pins generation task started. Task ARN: $TASK_ARN"

# Wait for task to complete
echo "Waiting for pins generation task to complete..."
aws ecs wait tasks-stopped \
  --cluster $CLUSTER_NAME \
  --tasks "$TASK_ARN"

echo "Production pins generation task completed! Check ECS console for detailed logs." 