#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}BOPMaps OpenMapTiles Planet Setup${NC}"
echo -e "${YELLOW}This script will download and process OpenMapTiles planet data${NC}"
echo ""

# Configuration
OPENMAPTILES_DIR="./docker/openmaptiles"
DATA_DIR="${OPENMAPTILES_DIR}/data"
TILES_DIR="${OPENMAPTILES_DIR}/tiles"
STYLES_DIR="${OPENMAPTILES_DIR}/styles"
FONTS_DIR="${OPENMAPTILES_DIR}/fonts"
SPRITES_DIR="${OPENMAPTILES_DIR}/sprites"

# Create directories
echo -e "${YELLOW}Creating directories...${NC}"
mkdir -p "${DATA_DIR}" "${TILES_DIR}" "${STYLES_DIR}" "${FONTS_DIR}" "${SPRITES_DIR}"

# Function to check disk space
check_disk_space() {
    local required_gb=$1
    local available_gb=$(df . | tail -1 | awk '{print int($4/1024/1024)}')
    
    if [ $available_gb -lt $required_gb ]; then
        echo -e "${RED}Error: Insufficient disk space. Required: ${required_gb}GB, Available: ${available_gb}GB${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}Disk space check passed: ${available_gb}GB available${NC}"
}

# Function to download file with progress
download_with_progress() {
    local url=$1
    local output=$2
    local description=$3
    
    echo -e "${YELLOW}Downloading ${description}...${NC}"
    echo "URL: $url"
    echo "Output: $output"
    
    # Check if file already exists
    if [ -f "$output" ]; then
        echo -e "${GREEN}File already exists: $output${NC}"
        read -p "Do you want to re-download? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            return 0
        fi
    fi
    
    # Download with curl showing progress
    curl -L --progress-bar "$url" -o "$output"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Successfully downloaded ${description}${NC}"
    else
        echo -e "${RED}Failed to download ${description}${NC}"
        exit 1
    fi
}

# Function to setup OpenMapTiles repository
setup_openmaptiles_repo() {
    echo -e "${YELLOW}Setting up OpenMapTiles repository...${NC}"
    
    if [ ! -d "./openmaptiles" ]; then
        git clone https://github.com/openmaptiles/openmaptiles.git
        cd openmaptiles
        git checkout v3.14
        cd ..
    else
        echo -e "${GREEN}OpenMapTiles repository already exists${NC}"
    fi
}

# Function to download planet data
download_planet_data() {
    echo -e "${YELLOW}Downloading OpenMapTiles planet data...${NC}"
    echo -e "${RED}WARNING: This will download ~50GB of data!${NC}"
    
    # Check disk space (need at least 100GB for processing)
    check_disk_space 100
    
    read -p "Do you want to continue with planet download? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Skipping planet download. You can download manually later.${NC}"
        return 0
    fi
    
    # Download planet MBTiles
    download_with_progress \
        "https://data.maptiler.com/downloads/planet/v3.14/planet.mbtiles" \
        "${TILES_DIR}/planet.mbtiles" \
        "OpenMapTiles Planet (v3.14)"
}

# Function to download styles
download_styles() {
    echo -e "${YELLOW}Downloading map styles...${NC}"
    
    # OSM Bright
    mkdir -p "${STYLES_DIR}/osm-bright"
    download_with_progress \
        "https://raw.githubusercontent.com/openmaptiles/osm-bright-gl-style/master/style.json" \
        "${STYLES_DIR}/osm-bright/style.json" \
        "OSM Bright Style"
    
    # Positron
    mkdir -p "${STYLES_DIR}/positron"
    download_with_progress \
        "https://raw.githubusercontent.com/openmaptiles/positron-gl-style/master/style.json" \
        "${STYLES_DIR}/positron/style.json" \
        "Positron Style"
    
    # Dark Matter
    mkdir -p "${STYLES_DIR}/dark-matter"
    download_with_progress \
        "https://raw.githubusercontent.com/openmaptiles/dark-matter-gl-style/master/style.json" \
        "${STYLES_DIR}/dark-matter/style.json" \
        "Dark Matter Style"
}

# Function to download fonts
download_fonts() {
    echo -e "${YELLOW}Downloading map fonts...${NC}"
    
    # Download font PBFs
    mkdir -p "${FONTS_DIR}"
    
    # List of common fonts used in OpenMapTiles
    fonts=(
        "Open Sans Regular"
        "Open Sans Semibold"
        "Open Sans Bold"
        "Noto Sans Regular"
        "Noto Sans Bold"
    )
    
    for font in "${fonts[@]}"; do
        font_encoded=$(echo "$font" | sed 's/ /%20/g')
        mkdir -p "${FONTS_DIR}/${font}"
        
        # Download font range 0-255 (covers most common characters)
        download_with_progress \
            "https://fonts.openmaptiles.org/${font_encoded}/0-255.pbf" \
            "${FONTS_DIR}/${font}/0-255.pbf" \
            "Font: ${font}"
    done
}

# Function to download sprites
download_sprites() {
    echo -e "${YELLOW}Downloading map sprites...${NC}"
    
    mkdir -p "${SPRITES_DIR}/osm-bright"
    
    # Download sprite files
    download_with_progress \
        "https://raw.githubusercontent.com/openmaptiles/osm-bright-gl-style/master/sprite.json" \
        "${SPRITES_DIR}/osm-bright/sprite.json" \
        "OSM Bright Sprite JSON"
    
    download_with_progress \
        "https://raw.githubusercontent.com/openmaptiles/osm-bright-gl-style/master/sprite.png" \
        "${SPRITES_DIR}/osm-bright/sprite.png" \
        "OSM Bright Sprite PNG"
    
    download_with_progress \
        "https://raw.githubusercontent.com/openmaptiles/osm-bright-gl-style/master/<EMAIL>" \
        "${SPRITES_DIR}/osm-bright/<EMAIL>" \
        "OSM Bright Sprite 2x JSON"
    
    download_with_progress \
        "https://raw.githubusercontent.com/openmaptiles/osm-bright-gl-style/master/<EMAIL>" \
        "${SPRITES_DIR}/osm-bright/<EMAIL>" \
        "OSM Bright Sprite 2x PNG"
}

# Function to validate setup
validate_setup() {
    echo -e "${YELLOW}Validating setup...${NC}"
    
    local errors=0
    
    # Check if planet file exists and is reasonable size (should be > 1GB)
    if [ -f "${TILES_DIR}/planet.mbtiles" ]; then
        local size=$(stat -c%s "${TILES_DIR}/planet.mbtiles" 2>/dev/null || stat -f%z "${TILES_DIR}/planet.mbtiles" 2>/dev/null || echo 0)
        local size_gb=$((size / 1024 / 1024 / 1024))
        
        if [ $size_gb -lt 1 ]; then
            echo -e "${RED}Warning: Planet file seems too small (${size_gb}GB)${NC}"
            errors=$((errors + 1))
        else
            echo -e "${GREEN}Planet file validated (${size_gb}GB)${NC}"
        fi
    else
        echo -e "${RED}Error: Planet file not found${NC}"
        errors=$((errors + 1))
    fi
    
    # Check styles
    for style in "osm-bright" "positron" "dark-matter"; do
        if [ -f "${STYLES_DIR}/${style}/style.json" ]; then
            echo -e "${GREEN}Style validated: ${style}${NC}"
        else
            echo -e "${RED}Error: Style missing: ${style}${NC}"
            errors=$((errors + 1))
        fi
    done
    
    # Check fonts
    if [ -d "${FONTS_DIR}" ] && [ "$(ls -A ${FONTS_DIR})" ]; then
        echo -e "${GREEN}Fonts directory validated${NC}"
    else
        echo -e "${RED}Error: Fonts directory empty${NC}"
        errors=$((errors + 1))
    fi
    
    # Check sprites
    if [ -f "${SPRITES_DIR}/osm-bright/sprite.json" ]; then
        echo -e "${GREEN}Sprites validated${NC}"
    else
        echo -e "${RED}Error: Sprites missing${NC}"
        errors=$((errors + 1))
    fi
    
    if [ $errors -eq 0 ]; then
        echo -e "${GREEN}All components validated successfully!${NC}"
        return 0
    else
        echo -e "${RED}Validation failed with ${errors} errors${NC}"
        return 1
    fi
}

# Function to build and start containers
build_and_start() {
    echo -e "${YELLOW}Building and starting OpenMapTiles containers...${NC}"
    
    cd "${OPENMAPTILES_DIR}"
    
    # Build the tile server image
    echo -e "${YELLOW}Building tile server image...${NC}"
    docker build -f ../../docker/Dockerfile.openmaptiles -t bopmaps-openmaptiles ../../
    
    # Start the tile server
    echo -e "${YELLOW}Starting tile server...${NC}"
    docker-compose -f docker-compose.openmaptiles.yml --profile serve up -d
    
    # Wait for server to start
    echo -e "${YELLOW}Waiting for tile server to start...${NC}"
    sleep 10
    
    # Test the server
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        echo -e "${GREEN}Tile server is running successfully!${NC}"
        echo -e "${GREEN}Access the tile server at: http://localhost:8080${NC}"
        echo -e "${GREEN}Test tiles at: http://localhost:8080/data/planet/{z}/{x}/{y}.pbf${NC}"
    else
        echo -e "${RED}Tile server failed to start properly${NC}"
        echo -e "${YELLOW}Check logs with: docker-compose -f ${OPENMAPTILES_DIR}/docker-compose.openmaptiles.yml --profile serve logs${NC}"
    fi
    
    cd - > /dev/null
}

# Main execution
main() {
    echo -e "${GREEN}Starting OpenMapTiles setup...${NC}"
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}Error: Docker is not running. Please start Docker and try again.${NC}"
        exit 1
    fi
    
    # Check if Docker Compose is available
    if ! command -v docker-compose > /dev/null 2>&1; then
        echo -e "${RED}Error: docker-compose is not installed.${NC}"
        exit 1
    fi
    
    # Setup steps
    setup_openmaptiles_repo
    download_styles
    download_fonts
    download_sprites
    download_planet_data
    
    # Validate setup
    if validate_setup; then
        echo -e "${GREEN}Setup completed successfully!${NC}"
        
        read -p "Do you want to start the tile server now? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            build_and_start
        else
            echo -e "${YELLOW}You can start the tile server later with:${NC}"
            echo "cd ${OPENMAPTILES_DIR}"
            echo "docker-compose -f docker-compose.openmaptiles.yml --profile serve up -d"
        fi
    else
        echo -e "${RED}Setup validation failed. Please check the errors above.${NC}"
        exit 1
    fi
}

# Show usage if help requested
if [[ "$1" == "--help" ]] || [[ "$1" == "-h" ]]; then
    echo "BOPMaps OpenMapTiles Planet Setup"
    echo ""
    echo "This script downloads and sets up OpenMapTiles planet data for the BOPMaps project."
    echo ""
    echo "Requirements:"
    echo "  - Docker and Docker Compose"
    echo "  - At least 100GB free disk space"
    echo "  - Stable internet connection"
    echo ""
    echo "Usage:"
    echo "  $0                 Run full setup"
    echo "  $0 --help         Show this help"
    echo ""
    echo "The script will:"
    echo "  1. Download OpenMapTiles planet data (~50GB)"
    echo "  2. Download map styles, fonts, and sprites"
    echo "  3. Set up tile server configuration"
    echo "  4. Build and start Docker containers"
    echo ""
    exit 0
fi

# Run main function
main "$@" 