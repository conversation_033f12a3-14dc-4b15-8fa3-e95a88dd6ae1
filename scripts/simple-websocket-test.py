#!/usr/bin/env python3
"""
Simple WebSocket Test for BOPMaps
Tests basic WebSocket connectivity without authentication
"""
import asyncio
import websockets
import ssl

async def test_websocket_url(url):
    """Test a single WebSocket URL"""
    try:
        print(f"Testing: {url}")
        # Create SSL context that doesn't verify certificates for testing
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        async with websockets.connect(url, ssl=ssl_context, timeout=10) as websocket:
            print(f"✓ Connected to {url}")
            # Send a simple ping
            await websocket.ping()
            print(f"✓ Ping successful to {url}")
            return True
    except websockets.exceptions.WebSocketException as e:
        print(f"✗ WebSocket error for {url}: {e}")
        return False
    except Exception as e:
        print(f"✗ Connection error for {url}: {e}")
        return False

async def main():
    print("=" * 50)
    print("Simple WebSocket Connection Test")
    print("=" * 50)
    
    # Test different URL patterns
    test_urls = [
        "wss://api.bopmaps.com/ws/achievements/",
        "wss://api.bopmaps.com/achievements/",
        "ws://api.bopmaps.com/ws/achievements/",  # Try without SSL
        "ws://api.bopmaps.com/achievements/",
    ]
    
    results = []
    for url in test_urls:
        result = await test_websocket_url(url)
        results.append(result)
        print()
    
    print("=" * 50)
    if any(results):
        print("✓ At least one WebSocket URL is working!")
    else:
        print("✗ No WebSocket URLs are working")
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(main()) 