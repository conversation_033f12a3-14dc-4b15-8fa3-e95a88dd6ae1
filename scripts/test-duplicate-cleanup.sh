#!/bin/bash

# Test duplicate cleanup functionality locally

echo "🧪 Testing duplicate cleanup functionality..."

# Activate virtual environment
source venv/bin/activate

echo ""
echo "📋 Testing pin skins duplicate cleanup..."
echo "Running: python manage.py generate_production_pins --dry-run --cleanup-duplicates"
python manage.py generate_production_pins --dry-run --cleanup-duplicates

echo ""
echo "📋 Testing challenges duplicate cleanup..."
echo "Running: python manage.py generate_production_challenges --dry-run --cleanup-duplicates"
python manage.py generate_production_challenges --dry-run --cleanup-duplicates

echo ""
echo "✅ Duplicate cleanup test completed!"
echo ""
echo "To run actual cleanup (not dry-run), use:"
echo "  python manage.py generate_production_pins --cleanup-duplicates"
echo "  python manage.py generate_production_challenges --cleanup-duplicates" 