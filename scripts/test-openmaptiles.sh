#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}BOPMaps OpenMapTiles Testing Suite${NC}"
echo -e "${YELLOW}This script tests the OpenMapTiles implementation${NC}"
echo ""

# Configuration
BASE_URL=${BASE_URL:-"http://localhost:8080"}
TILES_URL=${TILES_URL:-"https://tiles.bopmaps.com"}
API_URL=${API_URL:-"https://api.bopmaps.com"}

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test function
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -e "${BLUE}Testing: $test_name${NC}"
    
    if eval "$test_command"; then
        echo -e "${GREEN}✓ PASS: $test_name${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}✗ FAIL: $test_name${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Basic connectivity tests
test_local_tileserver() {
    echo -e "${YELLOW}=== Testing Local Tile Server ===${NC}"
    
    run_test "Local tile server health check" \
        "curl -f -s http://localhost:8080/health > /dev/null" \
        "success"
    
    run_test "Local tile server root endpoint" \
        "curl -f -s http://localhost:8080/ > /dev/null" \
        "success"
    
    run_test "Local styles endpoint" \
        "curl -f -s http://localhost:8080/styles/ > /dev/null" \
        "success"
    
    run_test "Local fonts endpoint" \
        "curl -f -s http://localhost:8080/fonts/ > /dev/null" \
        "success"
}

# Test tile serving
test_tile_serving() {
    echo -e "${YELLOW}=== Testing Tile Serving ===${NC}"
    
    # Test different zoom levels and tile coordinates
    local test_tiles=(
        "0/0/0"     # World overview
        "1/0/0"     # Northern hemisphere
        "1/1/1"     # Southern hemisphere
        "2/1/1"     # Europe/Africa
        "10/512/341"  # London area
        "14/8192/5462"  # Central London
    )
    
    for tile in "${test_tiles[@]}"; do
        run_test "Tile $tile (PBF)" \
            "curl -f -s http://localhost:8080/data/planet/$tile.pbf > /dev/null" \
            "success"
    done
    
    # Test tile formats
    run_test "PNG tile format" \
        "curl -f -s http://localhost:8080/styles/osm-bright/0/0/0.png > /dev/null" \
        "success"
    
    run_test "WebP tile format" \
        "curl -f -s http://localhost:8080/styles/osm-bright/0/0/0.webp > /dev/null" \
        "success"
}

# Test styles
test_styles() {
    echo -e "${YELLOW}=== Testing Map Styles ===${NC}"
    
    local styles=("osm-bright" "positron" "dark-matter")
    
    for style in "${styles[@]}"; do
        run_test "Style '$style' JSON" \
            "curl -f -s http://localhost:8080/styles/$style.json > /dev/null" \
            "success"
        
        run_test "Style '$style' tile rendering" \
            "curl -f -s http://localhost:8080/styles/$style/0/0/0.png > /dev/null" \
            "success"
    done
}

# Test fonts
test_fonts() {
    echo -e "${YELLOW}=== Testing Fonts ===${NC}"
    
    local fonts=(
        "Open%20Sans%20Regular"
        "Open%20Sans%20Bold"
        "Noto%20Sans%20Regular"
    )
    
    for font in "${fonts[@]}"; do
        run_test "Font '$font'" \
            "curl -f -s http://localhost:8080/fonts/$font/0-255.pbf > /dev/null" \
            "success"
    done
}

# Test AWS deployment
test_aws_deployment() {
    echo -e "${YELLOW}=== Testing AWS Deployment ===${NC}"
    
    if [ "$TILES_URL" != "http://localhost:8080" ]; then
        run_test "AWS tile server health" \
            "curl -f -s $TILES_URL/health > /dev/null" \
            "success"
        
        run_test "AWS tile serving" \
            "curl -f -s $TILES_URL/data/planet/0/0/0.pbf > /dev/null" \
            "success"
        
        run_test "AWS styles endpoint" \
            "curl -f -s $TILES_URL/styles/ > /dev/null" \
            "success"
    else
        echo -e "${YELLOW}Skipping AWS tests (using local server)${NC}"
    fi
}

# Test BOPMaps integration
test_bopmaps_integration() {
    echo -e "${YELLOW}=== Testing BOPMaps Integration ===${NC}"
    
    if [ "$API_URL" != "http://localhost:8000" ]; then
        run_test "BOPMaps API health" \
            "curl -f -s $API_URL/health/ > /dev/null" \
            "success"
        
        run_test "BOPMaps tile proxy" \
            "curl -f -s $API_URL/api/geo/tiles/osm/0/0/0.png > /dev/null" \
            "success"
        
        run_test "BOPMaps admin interface" \
            "curl -f -s $API_URL/admin/ > /dev/null" \
            "success"
    else
        echo -e "${YELLOW}Skipping BOPMaps API tests (using local server)${NC}"
    fi
}

# Performance tests
test_performance() {
    echo -e "${YELLOW}=== Testing Performance ===${NC}"
    
    # Test response times for different zoom levels
    local zoom_levels=(0 5 10 14)
    
    for zoom in "${zoom_levels[@]}"; do
        local x=$((2**zoom / 2))
        local y=$((2**zoom / 2))
        
        echo "Testing performance for zoom level $zoom..."
        
        # Test 5 tiles and average the response time
        local total_time=0
        local successful_requests=0
        
        for i in {1..5}; do
            local start_time=$(date +%s%3N)
            if curl -f -s "http://localhost:8080/data/planet/$zoom/$x/$y.pbf" > /dev/null; then
                local end_time=$(date +%s%3N)
                local response_time=$((end_time - start_time))
                total_time=$((total_time + response_time))
                successful_requests=$((successful_requests + 1))
            fi
        done
        
        if [ $successful_requests -gt 0 ]; then
            local avg_time=$((total_time / successful_requests))
            echo -e "${GREEN}Average response time for zoom $zoom: ${avg_time}ms${NC}"
            
            if [ $avg_time -lt 1000 ]; then
                echo -e "${GREEN}✓ Performance acceptable (<1s)${NC}"
                PASSED_TESTS=$((PASSED_TESTS + 1))
            else
                echo -e "${YELLOW}⚠ Performance warning (>1s)${NC}"
            fi
        else
            echo -e "${RED}✗ All requests failed for zoom $zoom${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
        
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
    done
}

# Data validation tests
test_data_validation() {
    echo -e "${YELLOW}=== Testing Data Validation ===${NC}"
    
    # Test that tiles contain actual data
    run_test "Tile contains valid PBF data" \
        "curl -s http://localhost:8080/data/planet/0/0/0.pbf | file - | grep -q 'protocol buffer'" \
        "success"
    
    # Test tile size is reasonable
    local tile_size=$(curl -s http://localhost:8080/data/planet/10/512/341.pbf | wc -c)
    if [ $tile_size -gt 100 ] && [ $tile_size -lt 1000000 ]; then
        echo -e "${GREEN}✓ Tile size is reasonable (${tile_size} bytes)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ Tile size is suspicious (${tile_size} bytes)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Test that styles are valid JSON
    run_test "OSM Bright style is valid JSON" \
        "curl -s http://localhost:8080/styles/osm-bright.json | jq . > /dev/null" \
        "success"
}

# Stress test
test_stress() {
    echo -e "${YELLOW}=== Running Stress Test ===${NC}"
    
    echo "Testing concurrent tile requests..."
    
    # Create a temporary directory for test results
    local temp_dir=$(mktemp -d)
    
    # Start 10 concurrent requests
    local pids=()
    for i in {1..10}; do
        (
            for j in {1..10}; do
                curl -s -f "http://localhost:8080/data/planet/5/$((i))/$((j)).pbf" > /dev/null
            done
            echo "Worker $i completed"
        ) &
        pids+=($!)
    done
    
    # Wait for all background jobs
    local success_count=0
    for pid in "${pids[@]}"; do
        if wait $pid; then
            success_count=$((success_count + 1))
        fi
    done
    
    rm -rf "$temp_dir"
    
    if [ $success_count -eq 10 ]; then
        echo -e "${GREEN}✓ Stress test passed (${success_count}/10 workers completed)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ Stress test failed (${success_count}/10 workers completed)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
}

# Memory and disk usage
test_resource_usage() {
    echo -e "${YELLOW}=== Testing Resource Usage ===${NC}"
    
    # Check if docker is available and container is running
    if command -v docker &> /dev/null; then
        local container_name="openmaptiles_tileserver"
        
        if docker ps | grep -q "$container_name"; then
            # Get memory usage
            local memory_usage=$(docker stats --no-stream --format "{{.MemUsage}}" "$container_name" | cut -d'/' -f1)
            echo -e "${BLUE}Memory usage: $memory_usage${NC}"
            
            # Get CPU usage
            local cpu_usage=$(docker stats --no-stream --format "{{.CPUPerc}}" "$container_name")
            echo -e "${BLUE}CPU usage: $cpu_usage${NC}"
            
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${YELLOW}Container not found or not running${NC}"
        fi
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
    fi
}

# Generate test report
generate_report() {
    echo ""
    echo -e "${BLUE}=== Test Report ===${NC}"
    echo -e "Total tests: $TOTAL_TESTS"
    echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
    echo -e "${RED}Failed: $FAILED_TESTS${NC}"
    
    local success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo -e "Success rate: ${success_rate}%"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}🎉 All tests passed!${NC}"
        return 0
    else
        echo -e "${RED}❌ Some tests failed${NC}"
        return 1
    fi
}

# Main execution
main() {
    echo -e "${GREEN}Starting OpenMapTiles test suite...${NC}"
    
    # Check if jq is available for JSON validation
    if ! command -v jq &> /dev/null; then
        echo -e "${YELLOW}Warning: jq not found. JSON validation tests will be skipped.${NC}"
    fi
    
    # Run test suites
    test_local_tileserver
    test_tile_serving
    test_styles
    test_fonts
    test_data_validation
    test_performance
    test_stress
    test_resource_usage
    test_aws_deployment
    test_bopmaps_integration
    
    # Generate final report
    generate_report
}

# Show usage if help requested
if [[ "$1" == "--help" ]] || [[ "$1" == "-h" ]]; then
    echo "BOPMaps OpenMapTiles Testing Suite"
    echo ""
    echo "This script runs comprehensive tests on the OpenMapTiles setup."
    echo ""
    echo "Usage:"
    echo "  $0                 Run all tests"
    echo "  $0 --help         Show this help"
    echo ""
    echo "Environment variables:"
    echo "  BASE_URL          Local tile server URL (default: http://localhost:8080)"
    echo "  TILES_URL         Production tile server URL (default: https://tiles.bopmaps.com)"
    echo "  API_URL           BOPMaps API URL (default: https://api.bopmaps.com)"
    echo ""
    echo "Test categories:"
    echo "  - Local tile server connectivity"
    echo "  - Tile serving (various zoom levels and formats)"
    echo "  - Map styles (OSM Bright, Positron, Dark Matter)"
    echo "  - Font serving"
    echo "  - Data validation"
    echo "  - Performance testing"
    echo "  - Stress testing"
    echo "  - Resource usage monitoring"
    echo "  - AWS deployment validation"
    echo "  - BOPMaps integration"
    echo ""
    exit 0
fi

# Run main function
main "$@" 