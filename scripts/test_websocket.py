#!/usr/bin/env python3
"""
Simple WebSocket test script to verify gamification WebSocket is working
"""
import asyncio
import websockets
import json
import sys

async def test_achievement_websocket():
    """Test the achievement WebSocket connection"""
    uri = "wss://api.bopmaps.com/ws/achievements/"
    
    try:
        print(f"🔗 Connecting to {uri}...")
        
        # Note: In production, you'd need to pass authentication headers
        # For now, testing basic connection
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connected successfully!")
            
            # Send a test message
            test_message = {
                "type": "test",
                "message": "Hello from test client"
            }
            
            await websocket.send(json.dumps(test_message))
            print("📤 Sent test message")
            
            # Wait for any response or keep connection alive
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📥 Received: {response}")
            except asyncio.TimeoutError:
                print("⏰ No response received (this is expected for unauthenticated connection)")
            
            print("✅ WebSocket test completed successfully!")
            
    except websockets.exceptions.InvalidStatus as e:
        if e.response.status_code == 403:
            print("🔒 Connection rejected due to authentication (this is expected)")
            print("✅ WebSocket authentication is working properly!")
            return True
        else:
            print(f"❌ Unexpected status code: {e.response.status_code}")
            return False
    except websockets.exceptions.ConnectionClosed:
        print("❌ Connection closed unexpectedly")
        return False
    except Exception as e:
        print(f"❌ WebSocket error: {e}")
        return False
    
    return True

async def test_websocket_with_auth():
    """Test WebSocket with authentication simulation"""
    uri = "wss://api.bopmaps.com/ws/achievements/"
    
    try:
        print(f"\n🔐 Testing authenticated connection to {uri}...")
        
        # This will likely fail due to authentication, but we can see the error
        async with websockets.connect(uri) as websocket:
            print("✅ Authenticated WebSocket connected!")
            
            # Wait for any messages
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                print(f"📥 Received: {message}")
            except asyncio.TimeoutError:
                print("⏰ No messages received")
                
    except websockets.exceptions.ConnectionClosed:
        print("❌ Connection closed")
        return False
    except websockets.exceptions.InvalidStatus as e:
        if e.response.status_code == 403:
            print("🔒 Connection rejected due to authentication (this is expected)")
            print("✅ WebSocket authentication is working properly!")
            return True
        else:
            print(f"❌ Unexpected status code: {e.response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🧪 Testing BOPMaps Achievement WebSocket")
    print("=" * 50)
    
    # Test basic connection
    result1 = asyncio.run(test_achievement_websocket())
    
    # Test with auth simulation
    result2 = asyncio.run(test_websocket_with_auth())
    
    print("\n📋 Test Results:")
    print(f"Basic connection: {'✅ PASS' if result1 else '❌ FAIL'}")
    print(f"Auth handling: {'✅ PASS' if result2 else '❌ FAIL'}")
    
    if result1 or result2:
        print("\n🎉 WebSocket is working! Your frontend can connect to:")
        print("   wss://api.bopmaps.com/ws/achievements/")
        print("\n💡 Remember to include authentication headers in your frontend")
    else:
        print("\n❌ WebSocket is not working properly")
        sys.exit(1) 