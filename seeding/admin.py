from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import SeedContentDatabase, SeedingArea, SeedingMetrics, CuratorAccount


@admin.register(SeedContentDatabase)
class SeedContentDatabaseAdmin(admin.ModelAdmin):
    list_display = ['version', 'is_active', 'total_tracks', 'cities_covered', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['version']
    readonly_fields = ['created_at', 'content_preview']

    fieldsets = (
        ('Basic Information', {
            'fields': ('version', 'is_active', 'created_at')
        }),
        ('Content Statistics', {
            'fields': ('total_tracks', 'cities_covered', 'last_api_update')
        }),
        ('Content Data', {
            'fields': ('content_data', 'content_preview'),
            'classes': ('collapse',)
        }),
    )

    def content_preview(self, obj):
        """Show a preview of the content data structure"""
        if not obj.content_data:
            return "No content data"

        preview = "<strong>Content Structure:</strong><br>"
        for key, value in obj.content_data.items():
            if isinstance(value, dict):
                preview += f"• {key}: {len(value)} items<br>"
            elif isinstance(value, list):
                preview += f"• {key}: {len(value)} items<br>"
            else:
                preview += f"• {key}: {str(value)[:50]}...<br>"

        return mark_safe(preview)

    content_preview.short_description = "Content Preview"

    actions = ['activate_content', 'deactivate_content']

    def activate_content(self, request, queryset):
        # Deactivate all other content first
        SeedContentDatabase.objects.update(is_active=False)
        # Activate selected content
        queryset.update(is_active=True)
        self.message_user(request, f"Activated {queryset.count()} content database(s)")

    activate_content.short_description = "Activate selected content databases"

    def deactivate_content(self, request, queryset):
        queryset.update(is_active=False)
        self.message_user(request, f"Deactivated {queryset.count()} content database(s)")

    deactivate_content.short_description = "Deactivate selected content databases"


@admin.register(SeedingArea)
class SeedingAreaAdmin(admin.ModelAdmin):
    list_display = ['city_name', 'seed_count', 'organic_pin_count', 'strategy_used', 'created_at']
    list_filter = ['strategy_used', 'country_code', 'created_at']
    search_fields = ['city_name', 'country_code']
    readonly_fields = ['created_at', 'last_organic_check', 'location_link']

    fieldsets = (
        ('Location Information', {
            'fields': ('center_point', 'location_link', 'radius_km', 'city_name', 'country_code')
        }),
        ('Seeding Details', {
            'fields': ('seed_count', 'organic_pin_count', 'strategy_used', 'content_version')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'last_organic_check')
        }),
    )

    def location_link(self, obj):
        """Generate a Google Maps link for the seeded area"""
        if obj.center_point:
            lat, lng = obj.center_point.y, obj.center_point.x
            url = f"https://www.google.com/maps?q={lat},{lng}"
            return format_html('<a href="{}" target="_blank">View on Google Maps</a>', url)
        return "No location"

    location_link.short_description = "Map Link"


@admin.register(SeedingMetrics)
class SeedingMetricsAdmin(admin.ModelAdmin):
    list_display = ['date', 'areas_seeded', 'pins_generated', 'seed_pin_views', 'avg_user_engagement']
    list_filter = ['date']
    readonly_fields = ['date']

    fieldsets = (
        ('Date', {
            'fields': ('date',)
        }),
        ('Seeding Activity', {
            'fields': ('areas_seeded', 'pins_generated', 'seeds_expired', 'seeds_retired')
        }),
        ('User Engagement', {
            'fields': ('seed_pin_views', 'seed_pin_collects', 'seed_pin_likes', 'avg_user_engagement')
        }),
        ('System Performance', {
            'fields': ('avg_seeding_response_time', 'api_failures', 'cache_hit_rate')
        }),
    )

    def has_add_permission(self, request):
        """Prevent manual creation of metrics (should be auto-generated)"""
        return False


@admin.register(CuratorAccount)
class CuratorAccountAdmin(admin.ModelAdmin):
    list_display = ['user', 'persona_type', 'pins_created', 'last_used', 'is_active']
    list_filter = ['persona_type', 'is_active', 'created_at']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['pins_created', 'last_used', 'created_at', 'user_link']

    fieldsets = (
        ('User Information', {
            'fields': ('user', 'user_link', 'persona_type', 'is_active')
        }),
        ('Preferences', {
            'fields': ('preferred_genres', 'preferred_locations', 'assigned_cities')
        }),
        ('Activity', {
            'fields': ('pins_created', 'last_used', 'created_at')
        }),
    )

    def user_link(self, obj):
        """Link to the user admin page"""
        if obj.user:
            url = reverse('admin:users_user_change', args=[obj.user.pk])
            return format_html('<a href="{}">View User Profile</a>', url)
        return "No user"

    user_link.short_description = "User Profile"

    actions = ['activate_curators', 'deactivate_curators']

    def activate_curators(self, request, queryset):
        queryset.update(is_active=True)
        self.message_user(request, f"Activated {queryset.count()} curator(s)")

    activate_curators.short_description = "Activate selected curators"

    def deactivate_curators(self, request, queryset):
        queryset.update(is_active=False)
        self.message_user(request, f"Deactivated {queryset.count()} curator(s)")

    deactivate_curators.short_description = "Deactivate selected curators"
