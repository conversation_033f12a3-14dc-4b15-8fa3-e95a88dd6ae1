"""
Management command to assign profile pictures to curator accounts.

This command assigns appropriate aesthetic profile pictures to curator
accounts based on their persona types, using royalty-free placeholder
images that match their musical preferences and personality.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from seeding.models import CuratorAccount
from seeding.services.profile_picture_service import ProfilePictureService


class Command(BaseCommand):
    help = 'Assign profile pictures to curator accounts based on their personas'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--curator',
            type=str,
            help='Assign picture to specific curator username'
        )
        parser.add_argument(
            '--persona',
            type=str,
            help='Assign pictures only to curators with specific persona type'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force reassignment even if curator already has a profile picture'
        )
        parser.add_argument(
            '--rotate',
            action='store_true',
            help='Rotate existing profile pictures for freshness'
        )
        parser.add_argument(
            '--preview',
            action='store_true',
            help='Preview picture recommendations without assigning'
        )
    
    def handle(self, *args, **options):
        curator_username = options.get('curator')
        persona_type = options.get('persona')
        force = options['force']
        rotate = options['rotate']
        preview = options['preview']
        
        service = ProfilePictureService()
        
        if preview:
            self._preview_recommendations(service, persona_type)
            return
        
        if rotate:
            self._rotate_pictures(service, curator_username, persona_type)
            return
        
        self._assign_pictures(service, curator_username, persona_type, force)
    
    def _preview_recommendations(self, service, persona_type=None):
        """Preview picture recommendations for persona types"""
        if persona_type:
            personas = [persona_type]
        else:
            personas = list(service.PERSONA_CATEGORY_MAPPING.keys())
        
        self.stdout.write(self.style.SUCCESS('Profile Picture Recommendations:'))
        self.stdout.write('')
        
        for persona in personas:
            self.stdout.write(f"📱 {persona.replace('_', ' ').title()}:")
            
            recommendations = service.get_persona_recommendations(persona)
            
            for rec in recommendations:
                self.stdout.write(f"  • {rec['category'].replace('_', ' ').title()}")
                self.stdout.write(f"    {rec['description']}")
                self.stdout.write(f"    Colors: {', '.join(rec['colors'])}")
                self.stdout.write(f"    Sample: {rec['sample_url']}")
                self.stdout.write('')
    
    def _assign_pictures(self, service, curator_username=None, persona_type=None, force=False):
        """Assign profile pictures to curators"""
        try:
            # Build queryset
            queryset = CuratorAccount.objects.select_related('user')
            
            if curator_username:
                queryset = queryset.filter(user__username=curator_username)
            
            if persona_type:
                queryset = queryset.filter(persona_type=persona_type)
            
            if not force:
                # Only assign to curators without profile pictures
                queryset = queryset.filter(user__profile_pic__isnull=True)
            
            curators = list(queryset)
            
            if not curators:
                if curator_username:
                    self.stdout.write(
                        self.style.WARNING(f'No curator found with username: {curator_username}')
                    )
                elif not force:
                    self.stdout.write(
                        self.style.WARNING('No curators without profile pictures found. Use --force to reassign.')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING('No curators found matching criteria.')
                    )
                return
            
            self.stdout.write(f'Assigning profile pictures to {len(curators)} curator(s)...')
            
            assigned_count = 0
            failed_count = 0
            
            with transaction.atomic():
                for curator in curators:
                    try:
                        old_pic = curator.user.profile_pic
                        picture_url = service.assign_profile_picture(curator)
                        
                        if picture_url:
                            assigned_count += 1
                            category = service.get_category_for_persona(curator.persona_type)
                            
                            action = "Reassigned" if old_pic else "Assigned"
                            self.stdout.write(
                                f'  ✓ {action} {category} picture to {curator.user.username} '
                                f'({curator.get_persona_type_display()})'
                            )
                        else:
                            failed_count += 1
                            self.stdout.write(
                                self.style.ERROR(
                                    f'  ✗ Failed to assign picture to {curator.user.username}'
                                )
                            )
                            
                    except Exception as e:
                        failed_count += 1
                        self.stdout.write(
                            self.style.ERROR(
                                f'  ✗ Error assigning picture to {curator.user.username}: {str(e)}'
                            )
                        )
            
            # Summary
            self.stdout.write('')
            self.stdout.write(
                self.style.SUCCESS(
                    f'Profile picture assignment completed: '
                    f'{assigned_count} successful, {failed_count} failed'
                )
            )
            
            if assigned_count > 0:
                self.stdout.write('')
                self.stdout.write('Picture categories used:')
                for category, info in service.PICTURE_CATEGORIES.items():
                    self.stdout.write(f'  • {category.replace("_", " ").title()}: {info["description"]}')
                    
        except Exception as e:
            raise CommandError(f'Error assigning profile pictures: {str(e)}')
    
    def _rotate_pictures(self, service, curator_username=None, persona_type=None):
        """Rotate existing profile pictures for freshness"""
        try:
            # Build queryset for curators with existing pictures
            queryset = CuratorAccount.objects.select_related('user').filter(
                user__profile_pic__isnull=False,
                is_active=True
            )
            
            if curator_username:
                queryset = queryset.filter(user__username=curator_username)
            
            if persona_type:
                queryset = queryset.filter(persona_type=persona_type)
            
            curators = list(queryset)
            
            if not curators:
                self.stdout.write(
                    self.style.WARNING('No curators with existing profile pictures found.')
                )
                return
            
            self.stdout.write(f'Rotating profile pictures for {len(curators)} curator(s)...')
            
            rotated_count = service.rotate_profile_pictures(curators)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Profile picture rotation completed: {rotated_count} pictures rotated'
                )
            )
            
        except Exception as e:
            raise CommandError(f'Error rotating profile pictures: {str(e)}')
    
    def _display_curator_info(self, curator):
        """Display information about a curator"""
        pic_status = "✓" if curator.user.profile_pic else "✗"
        return (
            f'{pic_status} {curator.user.username} '
            f'({curator.get_persona_type_display()}) - '
            f'{curator.pins_created} pins created'
        )
