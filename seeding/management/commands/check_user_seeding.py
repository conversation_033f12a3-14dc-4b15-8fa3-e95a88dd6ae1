#!/usr/bin/env python
"""
Django management command to check specific user seeding states

This command helps debug personalized seeding issues by checking:
1. User seeding states
2. Seeding areas they're associated with
3. What seeding decision would be made for them at a location

Usage:
    python manage.py check_user_seeding --users username1,username2 --location LAT,LNG

Examples:
    python manage.py check_user_seeding --users mothus<PERSON>lunga,Mothusom68 --location -26.2041,28.0473
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.gis.geos import Point
from users.models import User
from seeding.models import UserSeedingState, SeedingArea
from seeding.services.user_seeding_state_service import UserSeedingStateService


class Command(BaseCommand):
    help = 'Check specific user seeding states and decisions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--users',
            type=str,
            required=True,
            help='Comma-separated list of usernames to check',
        )
        parser.add_argument(
            '--location',
            type=str,
            help='Location to check seeding decisions (format: lat,lng)',
        )

    def handle(self, *args, **options):
        usernames = [u.strip() for u in options['users'].split(',')]
        location_str = options.get('location')
        
        lat, lng = None, None
        if location_str:
            try:
                lat, lng = map(float, location_str.split(','))
            except ValueError:
                raise CommandError('Invalid location format. Use: lat,lng')

        self.stdout.write('\n' + '='*60)
        self.stdout.write('🔍 USER SEEDING STATE ANALYSIS')
        self.stdout.write('='*60)
        
        state_service = UserSeedingStateService()
        
        for username in usernames:
            try:
                user = User.objects.get(username=username)
                self.check_user_state(user, state_service, lat, lng)
            except User.DoesNotExist:
                self.stdout.write(f'\n❌ User "{username}" not found')
                continue
        
        if lat and lng:
            self.check_seeding_areas(lat, lng)

    def check_user_state(self, user, state_service, lat=None, lng=None):
        """Check a specific user's seeding state"""
        self.stdout.write(f'\n👤 USER: {user.username}')
        self.stdout.write('-' * 40)
        
        try:
            # Get user seeding state
            user_state = state_service.get_user_seeding_state(user)
            
            self.stdout.write(f'Current State: {user_state.current_state}')
            self.stdout.write(f'First Seeding: {user_state.first_seeding_at}')
            self.stdout.write(f'Personalized Seeding: {user_state.personalized_seeding_at}')
            self.stdout.write(f'Full Seeding: {user_state.full_seeding_at}')
            self.stdout.write(f'Personalized Areas: {user_state.personalized_seeding_areas}')
            self.stdout.write(f'Full Seeding Areas: {user_state.full_seeding_areas}')
            
            # Check seeding eligibility if location provided
            if lat and lng:
                self.stdout.write('\n📍 SEEDING ELIGIBILITY:')
                eligibility = state_service.check_area_seeding_eligibility(user, lat, lng, 5.0)
                
                self.stdout.write(f'Should Seed: {eligibility["should_seed"]}')
                self.stdout.write(f'Seeding Type: {eligibility["seeding_type"]}')
                self.stdout.write(f'Reason: {eligibility["reason"]}')
                self.stdout.write(f'User State: {eligibility["user_state"]}')
                
                if eligibility.get('existing_area'):
                    area = eligibility['existing_area']
                    self.stdout.write(f'Existing Area: {area.id} ({area.city_name})')
                    self.stdout.write(f'Area Created: {area.created_at}')
                    self.stdout.write(f'Area Seeds: {area.seed_count}')
                
        except Exception as e:
            self.stdout.write(f'❌ Error checking user state: {str(e)}')

    def check_seeding_areas(self, lat, lng):
        """Check seeding areas near the location"""
        self.stdout.write(f'\n🗺️  SEEDING AREAS NEAR ({lat}, {lng})')
        self.stdout.write('-' * 40)
        
        try:
            from django.contrib.gis.measure import D
            location_point = Point(lng, lat, srid=4326)
            
            # Find areas within 10km
            nearby_areas = SeedingArea.objects.filter(
                center_point__distance_lte=(location_point, D(km=10))
            ).order_by('created_at')
            
            if nearby_areas.exists():
                for area in nearby_areas:
                    distance = location_point.distance(area.center_point) * 111  # Rough km conversion
                    self.stdout.write(f'Area {area.id}: {area.city_name}')
                    self.stdout.write(f'  Distance: {distance:.1f}km')
                    self.stdout.write(f'  Radius: {area.radius_km}km')
                    self.stdout.write(f'  Seeds: {area.seed_count}')
                    self.stdout.write(f'  Created: {area.created_at}')
                    self.stdout.write('')
            else:
                self.stdout.write('No seeding areas found within 10km')
                
        except Exception as e:
            self.stdout.write(f'❌ Error checking seeding areas: {str(e)}')
