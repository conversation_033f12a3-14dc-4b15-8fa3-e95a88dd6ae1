"""
Comprehensive management command to clean up all seeding-related data.

This command implements the complete seed lifecycle management system:
- Removes expired seed pins (60-day TTL)
- Retires seed pins when areas reach 10+ organic pins
- Cleans up curator account data and syncs usage statistics
- Removes orphaned user seeding states
- Archives old inactive seed content databases
- Cleans up old seeding metrics to prevent database bloat
- Updates seeding metrics and analytics

Use --cleanup-all for comprehensive cleanup or individual flags for specific operations.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils import timezone
from django.contrib.gis.geos import Point
from django.contrib.gis.measure import D
from django.db.models import Q
from datetime import timedelta

from pins.models import Pin
from seeding.models import SeedingArea, SeedingMetrics, CuratorAccount, UserSeedingState, SeedContentDatabase
from users.models import User


class Command(BaseCommand):
    help = 'Comprehensive cleanup of all seeding-related data including pins, curators, user states, and metrics'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be cleaned up without actually doing it'
        )
        parser.add_argument(
            '--force-expire',
            action='store_true',
            help='Force expiration of all seed pins regardless of date'
        )
        parser.add_argument(
            '--retirement-threshold',
            type=int,
            default=10,
            help='Number of organic pins needed to retire seeds in an area (default: 10)'
        )
        parser.add_argument(
            '--cleanup-curators',
            action='store_true',
            help='Clean up curator account data (reset inactive curators, sync usage stats)'
        )
        parser.add_argument(
            '--cleanup-user-states',
            action='store_true',
            help='Clean up orphaned user seeding states'
        )
        parser.add_argument(
            '--cleanup-old-content',
            action='store_true',
            help='Clean up old inactive seed content databases'
        )
        parser.add_argument(
            '--cleanup-old-metrics',
            type=int,
            default=90,
            help='Clean up seeding metrics older than N days (default: 90, 0 to disable)'
        )
        parser.add_argument(
            '--cleanup-all',
            action='store_true',
            help='Run all cleanup operations'
        )
    
    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force_expire = options['force_expire']
        retirement_threshold = options['retirement_threshold']
        cleanup_curators = options['cleanup_curators'] or options['cleanup_all']
        cleanup_user_states = options['cleanup_user_states'] or options['cleanup_all']
        cleanup_old_content = options['cleanup_old_content'] or options['cleanup_all']
        cleanup_old_metrics = options['cleanup_old_metrics'] if not options['cleanup_all'] else 90

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))

        self.stdout.write('Starting comprehensive seed cleanup process...')

        try:
            with transaction.atomic():
                # Core seed pin cleanup
                expired_count = self._cleanup_expired_seeds(dry_run, force_expire)
                retired_count = self._retire_seeds_in_organic_areas(dry_run, retirement_threshold)

                # Extended cleanup operations
                curator_cleanup_count = 0
                user_state_cleanup_count = 0
                content_cleanup_count = 0
                metrics_cleanup_count = 0

                if cleanup_curators:
                    curator_cleanup_count = self._cleanup_curator_accounts(dry_run)

                if cleanup_user_states:
                    user_state_cleanup_count = self._cleanup_user_seeding_states(dry_run)

                if cleanup_old_content:
                    content_cleanup_count = self._cleanup_old_content_databases(dry_run)

                if cleanup_old_metrics > 0:
                    metrics_cleanup_count = self._cleanup_old_metrics(dry_run, cleanup_old_metrics)

                # Update metrics
                if not dry_run:
                    self._update_cleanup_metrics(expired_count, retired_count)

                # Summary
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Cleanup completed:\n'
                        f'  • {expired_count} expired seed pins\n'
                        f'  • {retired_count} retired seed pins\n'
                        f'  • {curator_cleanup_count} curator accounts cleaned\n'
                        f'  • {user_state_cleanup_count} user states cleaned\n'
                        f'  • {content_cleanup_count} old content databases removed\n'
                        f'  • {metrics_cleanup_count} old metrics records archived'
                    )
                )

        except Exception as e:
            raise CommandError(f'Error during seed cleanup: {str(e)}')
    
    def _cleanup_expired_seeds(self, dry_run=False, force_expire=False):
        """Remove expired seed pins"""
        self.stdout.write('Cleaning up expired seed pins...')
        
        if force_expire:
            # Force expire all seed pins
            expired_seeds = Pin.objects.filter(tags__contains=['seed'])
        else:
            # Only expired seed pins
            expired_seeds = Pin.objects.filter(
                tags__contains=['seed'],
                expiration_date__lt=timezone.now()
            )
        
        expired_count = expired_seeds.count()
        
        if expired_count == 0:
            self.stdout.write('No expired seed pins found.')
            return 0
        
        if dry_run:
            self.stdout.write(f'Would remove {expired_count} expired seed pins:')
            for pin in expired_seeds[:10]:  # Show first 10
                expiry_info = f" (expires: {pin.expiration_date})" if pin.expiration_date else " (no expiry)"
                self.stdout.write(f'  • {pin.title} by {pin.owner.username}{expiry_info}')
            if expired_count > 10:
                self.stdout.write(f'  ... and {expired_count - 10} more')
        else:
            # Actually delete the expired pins
            deleted_count, _ = expired_seeds.delete()
            self.stdout.write(f'Removed {deleted_count} expired seed pins.')
        
        return expired_count
    
    def _retire_seeds_in_organic_areas(self, dry_run=False, retirement_threshold=10):
        """Retire seed pins in areas that now have sufficient organic content"""
        self.stdout.write(f'Retiring seeds in areas with {retirement_threshold}+ organic pins...')
        
        retired_count = 0
        
        # Get all seeded areas
        seeded_areas = SeedingArea.objects.all()
        
        for area in seeded_areas:
            try:
                # Count organic pins in this area
                organic_count = Pin.objects.filter(
                    Q(location__distance_lte=(area.center_point, D(km=area.radius_km))) &
                    (Q(expiration_date__isnull=True) | Q(expiration_date__gt=timezone.now())) &
                    Q(is_private=False) &
                    ~Q(tags__contains=['seed'])
                ).count()
                
                # Update the area's organic pin count
                area.organic_pin_count = organic_count
                area.last_organic_check = timezone.now()
                if not dry_run:
                    area.save(update_fields=['organic_pin_count', 'last_organic_check'])
                
                # Check if area has enough organic content to retire seeds
                if organic_count >= retirement_threshold:
                    # Find seed pins in this area
                    area_seeds = Pin.objects.filter(
                        Q(location__distance_lte=(area.center_point, D(km=area.radius_km))) &
                        Q(tags__contains=['seed']) &
                        (Q(expiration_date__isnull=True) | Q(expiration_date__gt=timezone.now()))
                    )
                    
                    area_seed_count = area_seeds.count()
                    
                    if area_seed_count > 0:
                        city_info = f" in {area.city_name}" if area.city_name else ""
                        
                        if dry_run:
                            self.stdout.write(
                                f'Would retire {area_seed_count} seed pins{city_info} '
                                f'({organic_count} organic pins found)'
                            )
                        else:
                            # Retire the seed pins (delete them)
                            deleted_count, _ = area_seeds.delete()
                            self.stdout.write(
                                f'Retired {deleted_count} seed pins{city_info} '
                                f'({organic_count} organic pins found)'
                            )
                            retired_count += deleted_count
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error processing area {area.id}: {str(e)}')
                )
                continue
        
        if retired_count == 0 and not dry_run:
            self.stdout.write('No seed pins needed retirement.')
        
        return retired_count
    
    def _update_cleanup_metrics(self, expired_count, retired_count):
        """Update daily metrics with cleanup statistics"""
        try:
            today = timezone.now().date()
            metrics, created = SeedingMetrics.objects.get_or_create(
                date=today,
                defaults={
                    'seeds_expired': 0,
                    'seeds_retired': 0,
                }
            )
            
            metrics.seeds_expired += expired_count
            metrics.seeds_retired += retired_count
            metrics.save(update_fields=['seeds_expired', 'seeds_retired'])
            
            self.stdout.write(f'Updated metrics: +{expired_count} expired, +{retired_count} retired')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error updating cleanup metrics: {str(e)}')
            )
    
    def get_cleanup_summary(self):
        """Get a summary of what needs cleanup"""
        # Count expired seeds
        expired_count = Pin.objects.filter(
            tags__contains=['seed'],
            expiration_date__lt=timezone.now()
        ).count()
        
        # Count areas that might need seed retirement
        areas_with_potential_retirement = 0
        for area in SeedingArea.objects.all():
            try:
                organic_count = Pin.objects.filter(
                    Q(location__distance_lte=(area.center_point, D(km=area.radius_km))) &
                    (Q(expiration_date__isnull=True) | Q(expiration_date__gt=timezone.now())) &
                    Q(is_private=False) &
                    ~Q(tags__contains=['seed'])
                ).count()
                
                if organic_count >= 10:  # Default retirement threshold
                    areas_with_potential_retirement += 1
            except:
                continue
        
        return {
            'expired_seeds': expired_count,
            'areas_for_retirement': areas_with_potential_retirement
        }

    def _cleanup_curator_accounts(self, dry_run=False):
        """Clean up curator account data and sync usage statistics"""
        self.stdout.write('Cleaning up curator accounts...')

        cleanup_count = 0

        # Sync curator usage stats with actual pin counts
        curators = CuratorAccount.objects.all()

        for curator in curators:
            try:
                # Count actual seed pins created by this curator
                actual_pin_count = Pin.objects.filter(
                    owner=curator.user,
                    tags__contains=['seed']
                ).count()

                # Check if curator's stats need updating
                needs_update = False

                if curator.pins_created != actual_pin_count:
                    if dry_run:
                        self.stdout.write(
                            f'  Would sync {curator.user.username}: '
                            f'{curator.pins_created} -> {actual_pin_count} pins'
                        )
                    else:
                        curator.pins_created = actual_pin_count
                        needs_update = True

                # Reset inactive curators who haven't been used in 30+ days
                if curator.last_used and (timezone.now() - curator.last_used).days > 30:
                    if actual_pin_count == 0:  # Only reset if they have no active pins
                        if dry_run:
                            self.stdout.write(f'  Would reset inactive curator: {curator.user.username}')
                        else:
                            curator.last_used = None
                            needs_update = True

                if needs_update and not dry_run:
                    curator.save()
                    cleanup_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error processing curator {curator.user.username}: {str(e)}')
                )
                continue

        if not dry_run and cleanup_count > 0:
            self.stdout.write(f'Cleaned up {cleanup_count} curator accounts')
        elif dry_run:
            self.stdout.write(f'Would clean up curator account data')

        return cleanup_count

    def _cleanup_user_seeding_states(self, dry_run=False):
        """Clean up orphaned user seeding states"""
        self.stdout.write('Cleaning up user seeding states...')

        cleanup_count = 0

        # Find user seeding states for users that no longer exist
        orphaned_states = UserSeedingState.objects.filter(user__isnull=True)
        orphaned_count = orphaned_states.count()

        if orphaned_count > 0:
            if dry_run:
                self.stdout.write(f'  Would remove {orphaned_count} orphaned user seeding states')
            else:
                deleted_count, _ = orphaned_states.delete()
                cleanup_count += deleted_count
                self.stdout.write(f'  Removed {deleted_count} orphaned user seeding states')

        # Reset seeding states for users who have no seed pins in their areas
        stale_states = UserSeedingState.objects.exclude(current_state='NEVER_SEEDED')

        for state in stale_states:
            try:
                # Check if user still has seed pins in their recorded areas
                has_active_seeds = False

                all_areas = state.personalized_seeding_areas + state.full_seeding_areas
                for area_id in all_areas:
                    try:
                        area = SeedingArea.objects.get(id=area_id)
                        seeds_in_area = Pin.objects.filter(
                            Q(location__distance_lte=(area.center_point, D(km=area.radius_km))) &
                            Q(tags__contains=['seed']) &
                            (Q(expiration_date__isnull=True) | Q(expiration_date__gt=timezone.now()))
                        ).exists()

                        if seeds_in_area:
                            has_active_seeds = True
                            break
                    except SeedingArea.DoesNotExist:
                        continue

                # If no active seeds, reset the user's seeding state
                if not has_active_seeds:
                    if dry_run:
                        self.stdout.write(f'  Would reset seeding state for user {state.user.username}')
                    else:
                        state.current_state = 'NEVER_SEEDED'
                        state.personalized_seeding_areas = []
                        state.full_seeding_areas = []
                        state.save()
                        cleanup_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error processing user state {state.user.username}: {str(e)}')
                )
                continue

        if not dry_run and cleanup_count > 0:
            self.stdout.write(f'Cleaned up {cleanup_count} user seeding states')
        elif dry_run:
            self.stdout.write(f'Would clean up user seeding state data')

        return cleanup_count

    def _cleanup_old_content_databases(self, dry_run=False):
        """Clean up old inactive seed content databases"""
        self.stdout.write('Cleaning up old content databases...')

        cleanup_count = 0

        # Keep only the most recent active content database and 2 most recent inactive ones
        active_content = SeedContentDatabase.objects.filter(is_active=True).order_by('-created_at')
        inactive_content = SeedContentDatabase.objects.filter(is_active=False).order_by('-created_at')

        # Keep the most recent active content
        if active_content.count() > 1:
            old_active = active_content[1:]  # Keep first, remove rest
            old_active_count = old_active.count()

            if dry_run:
                self.stdout.write(f'  Would remove {old_active_count} old active content databases')
            else:
                for content in old_active:
                    content.delete()
                    cleanup_count += 1
                self.stdout.write(f'  Removed {old_active_count} old active content databases')

        # Keep only 2 most recent inactive content databases
        if inactive_content.count() > 2:
            old_inactive = inactive_content[2:]  # Keep first 2, remove rest
            old_inactive_count = old_inactive.count()

            if dry_run:
                self.stdout.write(f'  Would remove {old_inactive_count} old inactive content databases')
            else:
                for content in old_inactive:
                    content.delete()
                    cleanup_count += 1
                self.stdout.write(f'  Removed {old_inactive_count} old inactive content databases')

        if not dry_run and cleanup_count > 0:
            self.stdout.write(f'Cleaned up {cleanup_count} old content databases')
        elif dry_run:
            self.stdout.write(f'Would clean up old content database data')

        return cleanup_count

    def _cleanup_old_metrics(self, dry_run=False, days_old=90):
        """Archive old seeding metrics to prevent database bloat"""
        self.stdout.write(f'Cleaning up metrics older than {days_old} days...')

        cleanup_count = 0
        cutoff_date = timezone.now().date() - timedelta(days=days_old)

        # Find old metrics
        old_metrics = SeedingMetrics.objects.filter(date__lt=cutoff_date)
        old_count = old_metrics.count()

        if old_count > 0:
            if dry_run:
                self.stdout.write(f'  Would archive {old_count} old metrics records')
            else:
                # Instead of deleting, we could archive to a separate table or export
                # For now, we'll delete very old metrics (keeping recent history)
                deleted_count, _ = old_metrics.delete()
                cleanup_count = deleted_count
                self.stdout.write(f'  Archived {deleted_count} old metrics records')
        else:
            self.stdout.write('  No old metrics found to clean up')

        return cleanup_count
