"""
Management command to create realistic curator user accounts for seed pins.

This command generates diverse curator accounts with believable usernames,
profiles, and music preferences for different cities and regions.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.contrib.auth import get_user_model

from seeding.models import CuratorAccount
from seeding.services.user_generation_service import UserGenerationService
from seeding.services.profile_picture_service import ProfilePictureService

User = get_user_model()


class Command(BaseCommand):
    help = 'Create realistic curator user accounts for seed pins'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=15,
            help='Number of curator accounts to create (default: 15)'
        )
        parser.add_argument(
            '--city',
            type=str,
            help='Create curators for a specific city'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force creation even if curators already exist'
        )
    
    def handle(self, *args, **options):
        count = options['count']
        city = options.get('city')
        force = options['force']
        
        # Check if curators already exist
        existing_count = CuratorAccount.objects.count()
        if existing_count > 0 and not force:
            self.stdout.write(
                self.style.WARNING(
                    f'Found {existing_count} existing curator accounts. '
                    'Use --force to create additional curators.'
                )
            )
            return
        
        self.stdout.write(f'Creating {count} curator accounts...')
        
        try:
            with transaction.atomic():
                created_curators = self._create_curator_accounts(count, city)
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully created {len(created_curators)} curator accounts'
                    )
                )
                
                # Display created curators
                for curator in created_curators:
                    self.stdout.write(
                        f'  • {curator.user.username} ({curator.get_persona_type_display()})'
                    )
                    
        except Exception as e:
            raise CommandError(f'Error creating curator accounts: {str(e)}')
    
    def _create_curator_accounts(self, count, target_city=None):
        """Create diverse curator accounts with realistic profiles using new generation services"""

        # Initialize services
        user_gen_service = UserGenerationService()
        profile_pic_service = ProfilePictureService()

        # Enhanced curator persona configurations with more diversity
        curator_persona_configs = [
            {
                'persona_type': 'indie_explorer',
                'preferred_genres': ['indie', 'alternative', 'folk', 'shoegaze'],
                'preferred_locations': ['cafe', 'venue', 'campus', 'park'],
                'cities': ['seattle', 'portland', 'austin', 'brooklyn']
            },
            {
                'persona_type': 'coffee_enthusiast',
                'preferred_genres': ['jazz', 'acoustic', 'chill', 'bossa nova'],
                'preferred_locations': ['cafe', 'park'],
                'cities': ['san francisco', 'seattle', 'new york', 'chicago']
            },
            {
                'persona_type': 'venue_hopper',
                'preferred_genres': ['rock', 'electronic', 'hip-hop', 'punk'],
                'preferred_locations': ['venue', 'landmark'],
                'cities': ['nashville', 'austin', 'los angeles', 'chicago']
            },
            {
                'persona_type': 'campus_curator',
                'preferred_genres': ['lo-fi', 'indie', 'electronic', 'ambient'],
                'preferred_locations': ['campus', 'cafe', 'park'],
                'cities': ['boston', 'berkeley', 'ann arbor', 'cambridge']
            },
            {
                'persona_type': 'city_wanderer',
                'preferred_genres': ['pop', 'hip-hop', 'electronic', 'world'],
                'preferred_locations': ['landmark', 'transit', 'venue', 'cafe'],
                'cities': ['new york', 'los angeles', 'chicago', 'miami']
            },
            {
                'persona_type': 'music_historian',
                'preferred_genres': ['classic rock', 'blues', 'jazz', 'soul'],
                'preferred_locations': ['landmark', 'venue', 'park'],
                'cities': ['nashville', 'memphis', 'detroit', 'new orleans']
            },
            {
                'persona_type': 'genre_specialist',
                'preferred_genres': ['electronic', 'ambient', 'experimental', 'techno'],
                'preferred_locations': ['venue', 'campus', 'landmark'],
                'cities': ['berlin', 'detroit', 'london', 'montreal']
            },
            {
                'persona_type': 'local_guide',
                'preferred_genres': ['local', 'folk', 'country', 'americana'],
                'preferred_locations': ['landmark', 'park', 'venue'],
                'cities': ['nashville', 'austin', 'portland', 'asheville']
            },
            # Additional diverse personas for variety
            {
                'persona_type': 'venue_hopper',
                'preferred_genres': ['house', 'techno', 'ambient', 'downtempo'],
                'preferred_locations': ['venue', 'transit'],
                'cities': ['berlin', 'new york', 'los angeles', 'miami']
            },
            {
                'persona_type': 'city_wanderer',
                'preferred_genres': ['folk', 'ambient', 'acoustic', 'world'],
                'preferred_locations': ['park', 'landmark'],
                'cities': ['portland', 'denver', 'seattle', 'san francisco']
            },
            {
                'persona_type': 'city_wanderer',
                'preferred_genres': ['pop', 'indie', 'electronic', 'hip-hop'],
                'preferred_locations': ['transit', 'cafe'],
                'cities': ['new york', 'san francisco', 'chicago', 'washington dc']
            },
            {
                'persona_type': 'city_wanderer',
                'preferred_genres': ['electronic', 'hip-hop', 'rock', 'pop'],
                'preferred_locations': ['park', 'transit'],
                'cities': ['los angeles', 'miami', 'denver', 'austin']
            },
            {
                'persona_type': 'coffee_enthusiast',
                'preferred_genres': ['chill', 'lo-fi', 'ambient', 'jazz'],
                'preferred_locations': ['cafe', 'park', 'campus'],
                'cities': ['portland', 'seattle', 'san francisco', 'austin']
            },
            {
                'persona_type': 'local_guide',
                'preferred_genres': ['world', 'reggae', 'latin', 'afrobeat'],
                'preferred_locations': ['landmark', 'venue', 'cafe'],
                'cities': ['new york', 'los angeles', 'miami', 'toronto']
            },
            {
                'persona_type': 'music_historian',
                'preferred_genres': ['classic rock', 'soul', 'funk', 'disco'],
                'preferred_locations': ['venue', 'landmark', 'cafe'],
                'cities': ['detroit', 'memphis', 'nashville', 'chicago']
            },
            # International curators - Europe
            {
                'persona_type': 'electronic_pioneer',
                'preferred_genres': ['techno', 'house', 'minimal', 'ambient'],
                'preferred_locations': ['venue', 'landmark', 'transit'],
                'cities': ['berlin', 'amsterdam', 'london', 'barcelona']
            },
            {
                'persona_type': 'indie_explorer',
                'preferred_genres': ['indie', 'post-punk', 'shoegaze', 'britpop'],
                'preferred_locations': ['venue', 'cafe', 'park'],
                'cities': ['london', 'manchester', 'glasgow', 'dublin']
            },
            {
                'persona_type': 'coffee_enthusiast',
                'preferred_genres': ['jazz', 'bossa nova', 'acoustic', 'folk'],
                'preferred_locations': ['cafe', 'park', 'landmark'],
                'cities': ['paris', 'vienna', 'stockholm', 'copenhagen']
            },
            {
                'persona_type': 'venue_hopper',
                'preferred_genres': ['electronic', 'drum and bass', 'dubstep', 'garage'],
                'preferred_locations': ['venue', 'transit', 'landmark'],
                'cities': ['london', 'bristol', 'manchester', 'birmingham']
            },
            {
                'persona_type': 'local_guide',
                'preferred_genres': ['flamenco', 'latin', 'folk', 'world'],
                'preferred_locations': ['landmark', 'venue', 'park'],
                'cities': ['madrid', 'barcelona', 'seville', 'valencia']
            },
            # International curators - Asia Pacific
            {
                'persona_type': 'city_wanderer',
                'preferred_genres': ['j-pop', 'electronic', 'ambient', 'experimental'],
                'preferred_locations': ['transit', 'landmark', 'cafe'],
                'cities': ['tokyo', 'osaka', 'kyoto', 'yokohama']
            },
            {
                'persona_type': 'venue_hopper',
                'preferred_genres': ['k-pop', 'electronic', 'hip-hop', 'indie'],
                'preferred_locations': ['venue', 'transit', 'landmark'],
                'cities': ['seoul', 'busan', 'incheon', 'daegu']
            },
            {
                'persona_type': 'coffee_enthusiast',
                'preferred_genres': ['indie', 'folk', 'acoustic', 'chill'],
                'preferred_locations': ['cafe', 'park', 'campus'],
                'cities': ['melbourne', 'sydney', 'brisbane', 'adelaide']
            },
            {
                'persona_type': 'local_guide',
                'preferred_genres': ['bollywood', 'classical', 'folk', 'world'],
                'preferred_locations': ['landmark', 'venue', 'park'],
                'cities': ['mumbai', 'delhi', 'bangalore', 'kolkata']
            },
            # International curators - Africa & Middle East
            {
                'persona_type': 'local_guide',
                'preferred_genres': ['amapiano', 'afrobeat', 'house', 'kwaito'],
                'preferred_locations': ['venue', 'landmark', 'park'],
                'cities': ['johannesburg', 'cape town', 'durban', 'pretoria']
            },
            {
                'persona_type': 'venue_hopper',
                'preferred_genres': ['afrobeat', 'highlife', 'hip-hop', 'dancehall'],
                'preferred_locations': ['venue', 'landmark', 'cafe'],
                'cities': ['lagos', 'accra', 'nairobi', 'addis ababa']
            },
            {
                'persona_type': 'music_historian',
                'preferred_genres': ['oud', 'classical', 'folk', 'world'],
                'preferred_locations': ['landmark', 'venue', 'cafe'],
                'cities': ['cairo', 'istanbul', 'beirut', 'damascus']
            },
            # International curators - Latin America
            {
                'persona_type': 'local_guide',
                'preferred_genres': ['reggaeton', 'salsa', 'bachata', 'merengue'],
                'preferred_locations': ['venue', 'landmark', 'park'],
                'cities': ['mexico city', 'guadalajara', 'monterrey', 'puebla']
            },
            {
                'persona_type': 'venue_hopper',
                'preferred_genres': ['samba', 'bossa nova', 'funk', 'electronic'],
                'preferred_locations': ['venue', 'landmark', 'cafe'],
                'cities': ['rio de janeiro', 'sao paulo', 'salvador', 'brasilia']
            },
            {
                'persona_type': 'city_wanderer',
                'preferred_genres': ['tango', 'folk', 'rock', 'electronic'],
                'preferred_locations': ['landmark', 'cafe', 'park'],
                'cities': ['buenos aires', 'cordoba', 'rosario', 'mendoza']
            },
            {
                'persona_type': 'coffee_enthusiast',
                'preferred_genres': ['salsa', 'cumbia', 'vallenato', 'folk'],
                'preferred_locations': ['cafe', 'park', 'landmark'],
                'cities': ['bogota', 'medellin', 'cali', 'cartagena']
            },
            # International curators - Canada
            {
                'persona_type': 'indie_explorer',
                'preferred_genres': ['indie', 'folk', 'alternative', 'electronic'],
                'preferred_locations': ['venue', 'cafe', 'park'],
                'cities': ['toronto', 'montreal', 'vancouver', 'calgary']
            },
            {
                'persona_type': 'coffee_enthusiast',
                'preferred_genres': ['jazz', 'folk', 'acoustic', 'indie'],
                'preferred_locations': ['cafe', 'park', 'campus'],
                'cities': ['montreal', 'quebec city', 'ottawa', 'halifax']
            }
        ]
        
        # Filter by city if specified
        if target_city:
            curator_persona_configs = [
                config for config in curator_persona_configs
                if target_city.lower() in [city.lower() for city in config['cities']]
            ]

        # Limit to requested count and ensure we have enough configurations
        available_configs = curator_persona_configs * ((count // len(curator_persona_configs)) + 1)
        selected_configs = available_configs[:count]

        created_curators = []
        existing_usernames = set(User.objects.values_list('username', flat=True))

        for config in selected_configs:
            try:
                # Generate diverse user profile using the new service
                user_profile = user_gen_service.generate_user_profile(
                    persona_type=config['persona_type'],
                    preferred_genres=config['preferred_genres'],
                    preferred_locations=config['preferred_locations'],
                    assigned_cities=config['cities']
                )

                # Ensure username uniqueness
                unique_username = user_gen_service.ensure_username_uniqueness(
                    user_profile['username'],
                    existing_usernames
                )
                existing_usernames.add(unique_username)

                # Create user with generated profile
                curator_user = User.objects.create_user(
                    username=unique_username,
                    email=user_profile['email'],
                    first_name=user_profile['first_name'],
                    last_name=user_profile['last_name'],
                    bio=user_profile['bio'],
                    profile_pic=user_profile['profile_pic']
                )

                # Create curator profile
                curator = CuratorAccount.objects.create(
                    user=curator_user,
                    persona_type=config['persona_type'],
                    preferred_genres=config['preferred_genres'],
                    preferred_locations=config['preferred_locations'],
                    assigned_cities=config['cities']
                )

                created_curators.append(curator)

                # Log the created curator
                self.stdout.write(
                    f'Created diverse curator: {curator_user.username} ({curator_user.first_name} {curator_user.last_name}) - {config["persona_type"]}'
                )

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error creating curator with persona {config["persona_type"]}: {str(e)}')
                )
                continue

        return created_curators
