"""
Management command to create a basic seed content database for testing.

This command creates a sample seed content database with diverse tracks
organized by genre, mood, and city profiles for the seeding system.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from seeding.models import SeedContentDatabase
from seeding.services.content_generator import SeedContentGenerator


class Command(BaseCommand):
    help = 'Create a basic seed content database for testing'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--content-version',
            type=str,
            default='1.0.0',
            help='Version identifier for the seed content (default: 1.0.0)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force creation even if content already exists'
        )
        parser.add_argument(
            '--target-tracks',
            type=int,
            default=500,
            help='Target number of tracks to collect (default: 500)'
        )
        parser.add_argument(
            '--use-apis',
            action='store_true',
            help='Use real API integrations instead of sample data'
        )
    
    def handle(self, *args, **options):
        version = options['content_version']
        force = options['force']
        target_tracks = options['target_tracks']
        use_apis = options['use_apis']
        
        # Check if content already exists
        existing_content = SeedContentDatabase.objects.filter(is_active=True).first()
        if existing_content and not force:
            self.stdout.write(
                self.style.WARNING(
                    f'Active seed content database already exists (v{existing_content.version}). '
                    'Use --force to create new content.'
                )
            )
            return
        
        if use_apis:
            self.stdout.write(f'Creating seed content database v{version} using real APIs (target: {target_tracks} tracks)...')
        else:
            self.stdout.write(f'Creating seed content database v{version} using sample data...')

        try:
            with transaction.atomic():
                # Deactivate existing content
                SeedContentDatabase.objects.update(is_active=False)

                # Create new content database
                if use_apis:
                    content_data = self._create_api_content(target_tracks)
                else:
                    content_data = self._create_sample_content()

                # Extract metrics from content
                total_tracks = len(content_data.get('content', {}).get('global', []))
                cities_covered = len(content_data.get('content', {}).get('city_profiles', {}))

                seed_db = SeedContentDatabase.objects.create(
                    version=version,
                    content_data=content_data,
                    is_active=True,
                    total_tracks=total_tracks,
                    cities_covered=cities_covered
                )
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully created seed content database v{version} '
                        f'with {seed_db.total_tracks} tracks and {seed_db.cities_covered} city profiles'
                    )
                )
                    
        except Exception as e:
            raise CommandError(f'Error creating seed content database: {str(e)}')

    def _create_api_content(self, target_tracks: int):
        """Create seed content using real API integrations"""
        try:
            generator = SeedContentGenerator()
            content_data = generator.generate_comprehensive_content(target_tracks)

            self.stdout.write(
                f'Successfully generated content from APIs: '
                f'{content_data["total_tracks"]} tracks from {len(content_data["sources"])} sources'
            )

            return content_data

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error generating API content: {str(e)}')
            )
            self.stdout.write('Falling back to sample content...')
            return self._create_sample_content()
    
    def _create_sample_content(self):
        """Create sample seed content for testing"""
        return {
            "version": "1.0.0",
            "generated_at": "2025-01-20T10:00:00Z",
            "content": {
                "global": [
                    {
                        "track_id": "spotify:track:sample1",
                        "title": "Blinding Lights",
                        "artist": "The Weeknd",
                        "album": "After Hours",
                        "genre": "pop",
                        "mood": "energetic",
                        "popularity_score": 95,
                        "release_year": 2019,
                        "duration_ms": 200000,
                        "track_url": "https://open.spotify.com/track/sample1",
                        "service": "spotify",
                        "tags": ["trending", "popular", "upbeat"]
                    },
                    {
                        "track_id": "spotify:track:sample2",
                        "title": "Watermelon Sugar",
                        "artist": "Harry Styles",
                        "album": "Fine Line",
                        "genre": "pop",
                        "mood": "happy",
                        "popularity_score": 90,
                        "release_year": 2020,
                        "duration_ms": 174000,
                        "track_url": "https://open.spotify.com/track/sample2",
                        "service": "spotify",
                        "tags": ["summer", "feel-good"]
                    },
                    {
                        "track_id": "spotify:track:sample3",
                        "title": "Levitating",
                        "artist": "Dua Lipa",
                        "album": "Future Nostalgia",
                        "genre": "pop",
                        "mood": "party",
                        "popularity_score": 88,
                        "release_year": 2020,
                        "duration_ms": 203000,
                        "track_url": "https://open.spotify.com/track/sample3",
                        "service": "spotify",
                        "tags": ["dance", "disco"]
                    },
                    {
                        "track_id": "spotify:track:sample4",
                        "title": "Good 4 U",
                        "artist": "Olivia Rodrigo",
                        "album": "SOUR",
                        "genre": "pop",
                        "mood": "energetic",
                        "popularity_score": 92,
                        "release_year": 2021,
                        "duration_ms": 178000,
                        "track_url": "https://open.spotify.com/track/sample4",
                        "service": "spotify",
                        "tags": ["rock", "alternative"]
                    },
                    {
                        "track_id": "spotify:track:sample5",
                        "title": "Stay",
                        "artist": "The Kid LAROI & Justin Bieber",
                        "album": "F*CK LOVE 3: OVER YOU",
                        "genre": "hip-hop",
                        "mood": "chill",
                        "popularity_score": 89,
                        "release_year": 2021,
                        "duration_ms": 141000,
                        "track_url": "https://open.spotify.com/track/sample5",
                        "service": "spotify",
                        "tags": ["melodic", "rap"]
                    },
                    {
                        "track_id": "spotify:track:sample6",
                        "title": "Heat Waves",
                        "artist": "Glass Animals",
                        "album": "Dreamland",
                        "genre": "indie",
                        "mood": "chill",
                        "popularity_score": 87,
                        "release_year": 2020,
                        "duration_ms": 238000,
                        "track_url": "https://open.spotify.com/track/sample6",
                        "service": "spotify",
                        "tags": ["indie-pop", "dreamy"]
                    },
                    {
                        "track_id": "spotify:track:sample7",
                        "title": "Industry Baby",
                        "artist": "Lil Nas X & Jack Harlow",
                        "album": "MONTERO",
                        "genre": "hip-hop",
                        "mood": "energetic",
                        "popularity_score": 91,
                        "release_year": 2021,
                        "duration_ms": 212000,
                        "track_url": "https://open.spotify.com/track/sample7",
                        "service": "spotify",
                        "tags": ["rap", "trap"]
                    },
                    {
                        "track_id": "spotify:track:sample8",
                        "title": "Shivers",
                        "artist": "Ed Sheeran",
                        "album": "=",
                        "genre": "pop",
                        "mood": "romantic",
                        "popularity_score": 85,
                        "release_year": 2021,
                        "duration_ms": 207000,
                        "track_url": "https://open.spotify.com/track/sample8",
                        "service": "spotify",
                        "tags": ["acoustic", "love"]
                    },
                    {
                        "track_id": "spotify:track:sample9",
                        "title": "Bad Habits",
                        "artist": "Ed Sheeran",
                        "album": "=",
                        "genre": "pop",
                        "mood": "party",
                        "popularity_score": 86,
                        "release_year": 2021,
                        "duration_ms": 231000,
                        "track_url": "https://open.spotify.com/track/sample9",
                        "service": "spotify",
                        "tags": ["electronic", "dance"]
                    },
                    {
                        "track_id": "spotify:track:sample10",
                        "title": "Montero (Call Me By Your Name)",
                        "artist": "Lil Nas X",
                        "album": "MONTERO",
                        "genre": "hip-hop",
                        "mood": "energetic",
                        "popularity_score": 88,
                        "release_year": 2021,
                        "duration_ms": 137000,
                        "track_url": "https://open.spotify.com/track/sample10",
                        "service": "spotify",
                        "tags": ["pop-rap", "catchy"]
                    }
                ],
                "city_profiles": {
                    "nashville": {
                        "popular_genres": ["country", "americana", "folk", "rock"],
                        "genre_weights": {"country": 0.4, "americana": 0.2, "folk": 0.2, "rock": 0.2},
                        "cultural_context": "music_city",
                        "tracks": [
                            {
                                "track_id": "spotify:track:country1",
                                "title": "The Good Ones",
                                "artist": "Gabby Barrett",
                                "genre": "country",
                                "mood": "happy",
                                "track_url": "https://open.spotify.com/track/country1",
                                "service": "spotify"
                            }
                        ]
                    },
                    "new_york": {
                        "popular_genres": ["hip-hop", "indie", "electronic", "jazz"],
                        "genre_weights": {"hip-hop": 0.3, "indie": 0.25, "electronic": 0.25, "jazz": 0.2},
                        "cultural_context": "urban_diverse",
                        "tracks": [
                            {
                                "track_id": "spotify:track:hiphop1",
                                "title": "Empire State of Mind",
                                "artist": "Jay-Z ft. Alicia Keys",
                                "genre": "hip-hop",
                                "mood": "energetic",
                                "track_url": "https://open.spotify.com/track/hiphop1",
                                "service": "spotify"
                            }
                        ]
                    }
                },
                "genres": {
                    "pop": [
                        {
                            "track_id": "spotify:track:sample1",
                            "title": "Blinding Lights",
                            "artist": "The Weeknd",
                            "genre": "pop",
                            "mood": "energetic",
                            "track_url": "https://open.spotify.com/track/sample1",
                            "service": "spotify"
                        }
                    ],
                    "hip-hop": [
                        {
                            "track_id": "spotify:track:sample5",
                            "title": "Stay",
                            "artist": "The Kid LAROI & Justin Bieber",
                            "genre": "hip-hop",
                            "mood": "chill",
                            "track_url": "https://open.spotify.com/track/sample5",
                            "service": "spotify"
                        }
                    ],
                    "indie": [
                        {
                            "track_id": "spotify:track:sample6",
                            "title": "Heat Waves",
                            "artist": "Glass Animals",
                            "genre": "indie",
                            "mood": "chill",
                            "track_url": "https://open.spotify.com/track/sample6",
                            "service": "spotify"
                        }
                    ]
                }
            }
        }
