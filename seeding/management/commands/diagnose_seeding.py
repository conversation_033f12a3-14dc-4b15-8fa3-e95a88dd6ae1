#!/usr/bin/env python
"""
Django management command to diagnose seeding issues

This command helps diagnose why seeding isn't working by checking:
1. SeedingArea records that might prevent seeding
2. Cached seed pins
3. Organic pin counts
4. Seeding service configuration

Usage:
    python manage.py diagnose_seeding --location LAT,LNG [options]

Options:
    --location LAT,LNG  Location to diagnose (required)
    --radius KM         Radius to check (default: 10km)

Examples:
    python manage.py diagnose_seeding --location 37.7749,-122.4194
    python manage.py diagnose_seeding --location 40.7128,-74.0060 --radius 5
"""

from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.contrib.gis.geos import Point
from django.contrib.gis.measure import D
from django.db.models import Q
from django.utils import timezone
from pins.models import Pin
from seeding.models import SeedingArea
from seeding.services import SeedingService


class Command(BaseCommand):
    help = 'Diagnose seeding issues for a specific location'

    def add_arguments(self, parser):
        parser.add_argument(
            '--location',
            type=str,
            required=True,
            help='Location to diagnose (format: lat,lng)',
        )
        parser.add_argument(
            '--radius',
            type=float,
            default=10.0,
            help='Radius to check in km (default: 10)',
        )

    def handle(self, *args, **options):
        self.stdout.write('🔍 Diagnosing seeding issues...')
        
        # Parse location
        try:
            lat_str, lng_str = options['location'].split(',')
            lat = float(lat_str.strip())
            lng = float(lng_str.strip())
            location_point = Point(lng, lat, srid=4326)
            self.stdout.write(f'📍 Location: ({lat}, {lng}) with {options["radius"]}km radius')
            self.location_str = f"{lat},{lng}"  # Store for recommendations
        except (ValueError, TypeError) as e:
            self.stdout.write(self.style.ERROR(f'❌ Invalid location format: {e}'))
            return

        radius_km = options['radius']
        
        # Initialize seeding service
        seeding_service = SeedingService()
        
        self.stdout.write('\n' + '='*60)
        self.stdout.write('📊 SEEDING DIAGNOSIS REPORT')
        self.stdout.write('='*60)
        
        # 1. Check SeedingArea records
        self.check_seeding_areas(location_point, radius_km, seeding_service)
        
        # 2. Check cache
        self.check_cache(lat, lng)
        
        # 3. Check organic pins
        self.check_organic_pins(location_point, radius_km, seeding_service)
        
        # 4. Check seed pins
        self.check_seed_pins(location_point, radius_km)
        
        # 5. Check seeding decision
        self.check_seeding_decision(lat, lng, seeding_service)

        # 6. Check user seeding states
        self.check_user_seeding_states(lat, lng)

        self.stdout.write('\n' + '='*60)
        self.stdout.write('💡 RECOMMENDATIONS')
        self.stdout.write('='*60)
        self.provide_recommendations()

    def check_seeding_areas(self, location_point, radius_km, seeding_service):
        """Check for existing SeedingArea records"""
        self.stdout.write('\n🗺️  SEEDING AREA RECORDS:')
        
        # Check within duplicate prevention radius
        duplicate_prevention_radius = seeding_service.DUPLICATE_PREVENTION_RADIUS_KM
        existing_areas = SeedingArea.objects.filter(
            center_point__distance_lte=(location_point, D(km=duplicate_prevention_radius))
        )
        
        if existing_areas.exists():
            self.stdout.write(f'   ❌ Found {existing_areas.count()} SeedingArea records within {duplicate_prevention_radius}km')
            for area in existing_areas[:3]:  # Show first 3
                distance = location_point.distance(area.center_point) * 111  # Rough km conversion
                self.stdout.write(f'      - Created: {area.created_at}, Distance: {distance:.1f}km, Seeds: {area.seed_count}')
            if existing_areas.count() > 3:
                self.stdout.write(f'      ... and {existing_areas.count() - 3} more')
        else:
            self.stdout.write(f'   ✅ No SeedingArea records found within {duplicate_prevention_radius}km')
        
        # Check within specified radius
        all_areas = SeedingArea.objects.filter(
            center_point__distance_lte=(location_point, D(km=radius_km))
        )
        self.stdout.write(f'   📊 Total SeedingArea records within {radius_km}km: {all_areas.count()}')

    def check_cache(self, lat, lng):
        """Check for cached seed pins"""
        self.stdout.write('\n💾 CACHE STATUS:')
        
        cache_key = f"seed_pins:{lat:.3f}:{lng:.3f}"
        cached_seeds = cache.get(cache_key)
        
        if cached_seeds is not None:
            self.stdout.write(f'   ❌ Found cached seed pins: {len(cached_seeds)} pins')
            self.stdout.write(f'      Cache key: {cache_key}')
        else:
            self.stdout.write(f'   ✅ No cached seed pins found')
            self.stdout.write(f'      Cache key checked: {cache_key}')

    def check_organic_pins(self, location_point, radius_km, seeding_service):
        """Check organic pin count"""
        self.stdout.write('\n🎵 ORGANIC PINS:')
        
        # Count organic pins using same logic as seeding service
        organic_pins = Pin.objects.filter(
            Q(location__distance_lte=(location_point, D(km=radius_km))) &
            (Q(expiration_date__isnull=True) | Q(expiration_date__gt=timezone.now())) &
            Q(is_private=False) &
            ~Q(tags__contains=['seed'])
        )
        
        organic_count = organic_pins.count()
        threshold = seeding_service.MIN_ORGANIC_PINS_THRESHOLD
        
        self.stdout.write(f'   📊 Organic pins found: {organic_count}')
        self.stdout.write(f'   📊 Seeding threshold: {threshold}')
        
        if organic_count < threshold:
            self.stdout.write(f'   ✅ Organic pin count is below threshold (seeding should be triggered)')
        else:
            self.stdout.write(f'   ❌ Organic pin count is above threshold (seeding not needed)')

    def check_seed_pins(self, location_point, radius_km):
        """Check existing seed pins"""
        self.stdout.write('\n🌱 SEED PINS:')
        
        seed_pins = Pin.objects.filter(
            location__distance_lte=(location_point, D(km=radius_km)),
            tags__contains=['seed']
        )
        
        seed_count = seed_pins.count()
        self.stdout.write(f'   📊 Existing seed pins: {seed_count}')
        
        if seed_count > 0:
            # Show some details
            recent_seeds = seed_pins.order_by('-created_at')[:3]
            for pin in recent_seeds:
                self.stdout.write(f'      - {pin.title} (created: {pin.created_at})')

    def check_seeding_decision(self, lat, lng, seeding_service):
        """Check what the seeding service decides"""
        self.stdout.write('\n🤖 SEEDING SERVICE DECISION:')

        should_seed = seeding_service.should_seed_area(lat, lng)

        if should_seed:
            self.stdout.write('   ✅ Seeding service says: SHOULD SEED')
        else:
            self.stdout.write('   ❌ Seeding service says: SHOULD NOT SEED')

    def check_user_seeding_states(self, lat, lng):
        """Check user seeding states for debugging personalized seeding"""
        from seeding.models import UserSeedingState
        from users.models import User
        from seeding.services.user_seeding_state_service import UserSeedingStateService

        self.stdout.write('\n👥 USER SEEDING STATES:')

        # Get recent users (last 10)
        recent_users = User.objects.order_by('-date_joined')[:10]
        state_service = UserSeedingStateService()

        for user in recent_users:
            try:
                user_state = state_service.get_user_seeding_state(user)
                eligibility = state_service.check_area_seeding_eligibility(user, lat, lng, 5.0)

                self.stdout.write(f'   User: {user.username}')
                self.stdout.write(f'     State: {user_state.current_state}')
                self.stdout.write(f'     Should seed: {eligibility["should_seed"]}')
                self.stdout.write(f'     Seeding type: {eligibility["seeding_type"]}')
                self.stdout.write(f'     Reason: {eligibility["reason"]}')
                if eligibility.get('existing_area'):
                    self.stdout.write(f'     Existing area: {eligibility["existing_area"].id}')
                self.stdout.write('')

            except Exception as e:
                self.stdout.write(f'   User: {user.username} - Error: {str(e)}')
                self.stdout.write('')

    def provide_recommendations(self):
        """Provide recommendations based on diagnosis"""
        self.stdout.write('\n🔧 To fix seeding issues, try:')
        self.stdout.write('   1. Clear SeedingArea records:')
        self.stdout.write('      python manage.py reset_seeding --clear-areas')
        self.stdout.write('')
        self.stdout.write('   2. Clear cache:')
        self.stdout.write('      python manage.py reset_seeding --clear-cache')
        self.stdout.write('')
        self.stdout.write('   3. Delete existing seed pins (if needed):')
        self.stdout.write('      python manage.py reset_seeding --delete-seeds')
        self.stdout.write('')
        self.stdout.write('   4. Reset everything for this location:')
        self.stdout.write(f'      python manage.py reset_seeding --location {self.location_str} --clear-areas --clear-cache')
