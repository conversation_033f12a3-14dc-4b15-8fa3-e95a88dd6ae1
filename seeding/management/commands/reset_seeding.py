#!/usr/bin/env python
"""
Django management command to reset seeding system

This command helps reset the seeding system by:
1. Clearing SeedingArea records that prevent re-seeding
2. Clearing cached seed pins
3. Optionally deleting existing seed pins
4. Allowing fresh seeding to occur

Usage:
    python manage.py reset_seeding [options]

Options:
    --clear-areas       Clear SeedingArea records to allow re-seeding
    --clear-cache       Clear cached seed pins
    --delete-seeds      Delete existing seed pins from database
    --location LAT,LNG  Reset seeding for specific location (optional)
    --radius KM         Radius around location to reset (default: 10km)

Examples:
    # Reset everything globally
    python manage.py reset_seeding --clear-areas --clear-cache --delete-seeds

    # Reset for specific location
    python manage.py reset_seeding --location 37.7749,-122.4194 --radius 5 --clear-areas --clear-cache

    # Just clear cache and areas (keep existing seed pins)
    python manage.py reset_seeding --clear-areas --clear-cache
"""

from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.contrib.gis.geos import Point
from django.contrib.gis.measure import D
from django.db import transaction
from pins.models import Pin
from seeding.models import SeedingArea


class Command(BaseCommand):
    help = 'Reset seeding system to allow fresh seeding'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear-areas',
            action='store_true',
            help='Clear SeedingArea records to allow re-seeding',
        )
        parser.add_argument(
            '--clear-cache',
            action='store_true',
            help='Clear cached seed pins',
        )
        parser.add_argument(
            '--delete-seeds',
            action='store_true',
            help='Delete existing seed pins from database',
        )
        parser.add_argument(
            '--location',
            type=str,
            help='Specific location to reset (format: lat,lng)',
        )
        parser.add_argument(
            '--radius',
            type=float,
            default=10.0,
            help='Radius around location to reset in km (default: 10)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        
        if self.dry_run:
            self.stdout.write('🔍 DRY RUN MODE - No changes will be made')
        
        self.stdout.write('🔄 Resetting seeding system...')
        
        # Parse location if provided
        location_point = None
        if options['location']:
            try:
                lat_str, lng_str = options['location'].split(',')
                lat = float(lat_str.strip())
                lng = float(lng_str.strip())
                location_point = Point(lng, lat, srid=4326)
                self.stdout.write(f'📍 Targeting location: ({lat}, {lng}) with {options["radius"]}km radius')
            except (ValueError, TypeError) as e:
                self.stdout.write(self.style.ERROR(f'❌ Invalid location format: {e}'))
                return
        
        # Clear SeedingArea records
        if options['clear_areas']:
            self.clear_seeding_areas(location_point, options['radius'])
        
        # Clear cache
        if options['clear_cache']:
            self.clear_seed_cache(location_point, options['radius'])
        
        # Delete seed pins
        if options['delete_seeds']:
            self.delete_seed_pins(location_point, options['radius'])
        
        if not any([options['clear_areas'], options['clear_cache'], options['delete_seeds']]):
            self.stdout.write(self.style.WARNING('⚠️  No actions specified. Use --clear-areas, --clear-cache, or --delete-seeds'))
            return
        
        self.stdout.write('\n✅ Seeding system reset completed!')
        self.stdout.write('💡 You can now trigger fresh seeding by calling the nearby endpoint')

    def clear_seeding_areas(self, location_point=None, radius_km=10.0):
        """Clear SeedingArea records"""
        self.stdout.write('\n🗑️  Clearing SeedingArea records...')
        
        if location_point:
            # Clear areas within radius of specific location
            areas_query = SeedingArea.objects.filter(
                center_point__distance_lte=(location_point, D(km=radius_km))
            )
        else:
            # Clear all areas
            areas_query = SeedingArea.objects.all()
        
        areas_count = areas_query.count()
        
        if self.dry_run:
            self.stdout.write(f'   🔍 Would delete {areas_count} SeedingArea records')
        else:
            with transaction.atomic():
                deleted_count, _ = areas_query.delete()
                self.stdout.write(f'   ✅ Deleted {deleted_count} SeedingArea records')

    def clear_seed_cache(self, location_point=None, radius_km=10.0):
        """Clear cached seed pins"""
        self.stdout.write('\n🗑️  Clearing seed pin cache...')

        if location_point:
            # Clear cache for specific location area
            lat = location_point.y
            lng = location_point.x

            # Generate cache keys for area (approximate grid)
            cache_keys = []
            for lat_offset in [-0.1, -0.05, 0, 0.05, 0.1]:
                for lng_offset in [-0.1, -0.05, 0, 0.05, 0.1]:
                    cache_lat = lat + lat_offset
                    cache_lng = lng + lng_offset
                    cache_key = f"seed_pins:{cache_lat:.3f}:{cache_lng:.3f}"
                    cache_keys.append(cache_key)

            if self.dry_run:
                self.stdout.write(f'   🔍 Would clear {len(cache_keys)} cache keys around location')
            else:
                cleared_count = 0
                for cache_key in cache_keys:
                    if cache.get(cache_key) is not None:
                        cache.delete(cache_key)
                        cleared_count += 1
                self.stdout.write(f'   ✅ Cleared {cleared_count} cache entries')
        else:
            # Clear all cache entries
            if self.dry_run:
                self.stdout.write('   🔍 Would clear all cache entries')
            else:
                try:
                    cache.clear()
                    self.stdout.write('   ✅ Cleared all cache entries')
                except Exception as e:
                    self.stdout.write(f'   ⚠️  Could not clear cache: {str(e)}')
                    self.stdout.write('   💡 Try running: python manage.py shell -c "from django.core.cache import cache; cache.clear()"')

    def delete_seed_pins(self, location_point=None, radius_km=10.0):
        """Delete existing seed pins"""
        self.stdout.write('\n🗑️  Deleting seed pins...')
        
        # Build query for seed pins
        seed_pins_query = Pin.objects.filter(tags__contains=['seed'])
        
        if location_point:
            # Delete seed pins within radius of specific location
            seed_pins_query = seed_pins_query.filter(
                location__distance_lte=(location_point, D(km=radius_km))
            )
        
        pins_count = seed_pins_query.count()
        
        if self.dry_run:
            self.stdout.write(f'   🔍 Would delete {pins_count} seed pins')
        else:
            with transaction.atomic():
                deleted_count, _ = seed_pins_query.delete()
                self.stdout.write(f'   ✅ Deleted {deleted_count} seed pins')
