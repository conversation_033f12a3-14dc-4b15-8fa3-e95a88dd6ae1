#!/usr/bin/env python
"""
Django management command to test personalized seeding logic

This command simulates the scenario where:
1. User 1 enters a location and gets full seeding
2. User 2 enters the same location and should get personalized seeding

Usage:
    python manage.py test_personalized_seeding --users user1,user2 --location LAT,LNG

Examples:
    python manage.py test_personalized_seeding --users moth<PERSON><PERSON><PERSON><PERSON>,Mothusom68 --location -26.2041,28.0473
"""

from django.core.management.base import BaseCommand, CommandError
from users.models import User
from seeding.services.seeding_service import SeedingService
from seeding.services.user_seeding_state_service import UserSeedingStateService


class Command(BaseCommand):
    help = 'Test personalized seeding logic with two users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--users',
            type=str,
            required=True,
            help='Comma-separated list of two usernames to test',
        )
        parser.add_argument(
            '--location',
            type=str,
            required=True,
            help='Location to test seeding (format: lat,lng)',
        )

    def handle(self, *args, **options):
        usernames = [u.strip() for u in options['users'].split(',')]
        if len(usernames) != 2:
            raise CommandError('Please provide exactly two usernames')
        
        try:
            lat, lng = map(float, options['location'].split(','))
        except ValueError:
            raise CommandError('Invalid location format. Use: lat,lng')

        self.stdout.write('\n' + '='*60)
        self.stdout.write('🧪 PERSONALIZED SEEDING TEST')
        self.stdout.write('='*60)
        
        # Get users
        try:
            user1 = User.objects.get(username=usernames[0])
            user2 = User.objects.get(username=usernames[1])
        except User.DoesNotExist as e:
            raise CommandError(f'User not found: {e}')
        
        seeding_service = SeedingService()
        state_service = UserSeedingStateService()
        
        self.stdout.write(f'\n📍 Testing at location: ({lat}, {lng})')
        self.stdout.write(f'👤 User 1: {user1.username}')
        self.stdout.write(f'👤 User 2: {user2.username}')
        
        # Test User 1 - should get full seeding
        self.stdout.write(f'\n🔄 STEP 1: {user1.username} enters the area')
        self.stdout.write('-' * 40)
        
        eligibility1 = seeding_service.check_personalized_seeding_needed(user1, lat, lng, 5.0)
        self.stdout.write(f'Should seed: {eligibility1["should_seed"]}')
        self.stdout.write(f'Seeding type: {eligibility1["seeding_type"]}')
        self.stdout.write(f'Reason: {eligibility1["reason"]}')
        
        if eligibility1['should_seed'] and eligibility1['seeding_type'] == 'full':
            self.stdout.write('✅ User 1 correctly identified for full seeding')
            
            # Simulate full seeding
            self.stdout.write('🌍 Simulating full seeding...')
            try:
                seed_pins = seeding_service.generate_exploration_seed_pins(lat, lng, user=user1)
                self.stdout.write(f'✅ Generated {len(seed_pins)} seed pins for {user1.username}')
            except Exception as e:
                self.stdout.write(f'❌ Error generating seed pins: {e}')
                return
        else:
            self.stdout.write(f'❌ User 1 seeding decision incorrect. Expected full seeding.')
            return
        
        # Check User 1's state after seeding
        user1_state = state_service.get_user_seeding_state(user1)
        self.stdout.write(f'User 1 state after seeding: {user1_state.current_state}')
        self.stdout.write(f'User 1 full seeding areas: {user1_state.full_seeding_areas}')
        
        # Test User 2 - should get personalized seeding
        self.stdout.write(f'\n🔄 STEP 2: {user2.username} enters the same area')
        self.stdout.write('-' * 40)
        
        eligibility2 = seeding_service.check_personalized_seeding_needed(user2, lat, lng, 5.0)
        self.stdout.write(f'Should seed: {eligibility2["should_seed"]}')
        self.stdout.write(f'Seeding type: {eligibility2["seeding_type"]}')
        self.stdout.write(f'Reason: {eligibility2["reason"]}')
        
        if eligibility2['should_seed'] and eligibility2['seeding_type'] == 'personalized':
            self.stdout.write('✅ User 2 correctly identified for personalized seeding')
            
            # Check if there's an existing area
            if eligibility2.get('existing_area'):
                area = eligibility2['existing_area']
                self.stdout.write(f'🗺️  Found existing area: {area.id} ({area.city_name})')
                
                # Simulate personalized seeding
                self.stdout.write('🎵 Simulating personalized seeding...')
                try:
                    personalized_pins = seeding_service.generate_personalized_seeds_for_area(
                        user2, lat, lng, area
                    )
                    self.stdout.write(f'✅ Generated {len(personalized_pins)} personalized pins for {user2.username}')
                except Exception as e:
                    self.stdout.write(f'❌ Error generating personalized pins: {e}')
                    return
            else:
                self.stdout.write('⚠️  No existing area found, but personalized seeding was recommended')
        else:
            self.stdout.write(f'❌ User 2 seeding decision incorrect. Expected personalized seeding.')
            self.stdout.write(f'   Got: {eligibility2["seeding_type"]}')
            
            # Debug information
            self.stdout.write('\n🔍 DEBUG INFORMATION:')
            from seeding.models import SeedingArea
            areas = SeedingArea.objects.all()
            self.stdout.write(f'Total seeding areas: {areas.count()}')
            for area in areas:
                self.stdout.write(f'  Area {area.id}: {area.city_name}, created: {area.created_at}')
            
            return
        
        # Check User 2's state after seeding
        user2_state = state_service.get_user_seeding_state(user2)
        self.stdout.write(f'User 2 state after seeding: {user2_state.current_state}')
        self.stdout.write(f'User 2 personalized areas: {user2_state.personalized_seeding_areas}')
        
        self.stdout.write('\n🎉 TEST COMPLETED SUCCESSFULLY!')
        self.stdout.write('✅ User 1 received full seeding')
        self.stdout.write('✅ User 2 received personalized seeding')
        self.stdout.write('\nThe personalized seeding system is working correctly!')
