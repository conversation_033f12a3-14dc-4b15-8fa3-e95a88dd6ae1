"""
Management command to test pin constraints:
1. Spatial offset for pins at the same POI (5-15m)
2. Minimum distance from user when seeding pins (250m)

This command verifies that the pin generation and location filtering logic
works correctly for both constraints.
"""

import logging
from django.core.management.base import BaseCommand
from django.contrib.gis.geos import Point
from seeding.services.seeding_service import SeedingService
from seeding.services.pin_generation_service import PinGenerationService
from seeding.services.location_service import LocationService
from pins.models import Pin
from django.utils import timezone
from datetime import timedelta

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Test pin constraints for spatial offset and minimum distance from user"

    def add_arguments(self, parser):
        parser.add_argument(
            '--lat',
            type=float,
            default=37.7749,  # San Francisco by default
            help='User latitude for testing',
        )
        parser.add_argument(
            '--lng',
            type=float,
            default=-122.4194,  # San Francisco by default
            help='User longitude for testing',
        )
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up test pins after running',
        )

    def handle(self, *args, **options):
        user_lat = options['lat']
        user_lng = options['lng']
        cleanup = options['cleanup']
        
        self.stdout.write(self.style.SUCCESS(f'Testing pin constraints for user location: ({user_lat}, {user_lng})'))
        
        # Step 1: Generate test locations
        location_service = LocationService()
        
        self.stdout.write('Getting seed locations from location service...')
        seed_locations = location_service.get_seed_locations(user_lat, user_lng)
        
        # Check if we have locations
        location_counts = {zone: len(locs) for zone, locs in seed_locations.items()}
        self.stdout.write(f'Retrieved locations by zone: {location_counts}')
        
        # Verify minimum user distance in locations
        self._verify_location_distances(seed_locations, user_lat, user_lng, location_service.MIN_USER_DISTANCE_M)
        
        # Step 2: Generate test pins for a POI to check spatial offset
        pin_generation_service = PinGenerationService()
        seeding_service = SeedingService()
        
        # Select a location from the immediate zone if available
        test_location = None
        for zone in ['immediate', 'nearby', 'city']:
            if zone in seed_locations and seed_locations[zone]:
                test_location = seed_locations[zone][0]
                break
        
        if not test_location:
            self.stdout.write(self.style.ERROR('No test locations found'))
            return
            
        # Create test pins at the same POI
        self.stdout.write(f'\nCreating multiple test pins at POI: {test_location.get("name", "Unnamed")}')
        self.stdout.write(f'Original POI coordinates: ({test_location["lat"]}, {test_location["lng"]})')
        
        # Tag for identifying test pins
        test_tag = f'pin_constraint_test_{timezone.now().strftime("%Y%m%d%H%M%S")}'
        test_pins = []
        
        # Create multiple pins at the same POI
        for i in range(5):
            # Get curator using pin_generation_service instead
            # Using dummy content dict for curator selection
            dummy_content = {'title': 'Test Track', 'artist': 'Test Artist'}
            curator = pin_generation_service._select_curator_for_content(dummy_content)
            if not curator:
                self.stdout.write(self.style.ERROR('No curator available for testing'))
                return
                
            # Create complete test content with all required fields
            content = {'tracks': [{
                'title': 'Test Track', 
                'artist': 'Test Artist', 
                'id': 'test_id',
                'track_url': 'https://example.com/test_track',
                'album': 'Test Album',
                'service': 'spotify',
                'artwork_url': 'https://example.com/artwork.jpg',
                'duration_ms': 180000,
                'genre': 'Test Genre',
                'mood': 'Test Mood'
            }]}
            
            # Create a test pin - using pin_generation_service directly to apply spatial offset
            track = content['tracks'][0] if content['tracks'] else None
            if not track:
                self.stdout.write(self.style.ERROR('No tracks in seed content'))
                return
                
            # Create content for the pin (matching expected format)
            proper_content = {
                'general': [track],  # Structure content as expected by create_seed_pin
            }
            
            # Apply spatial offset in pin creation
            pin = pin_generation_service.create_seed_pin(
                location=test_location,
                content=proper_content,
                zone_type='immediate'
            )
            
            test_pins.append(pin)
            
            self.stdout.write(f'Created pin {i+1}: ({pin.location.y}, {pin.location.x})')
        
        # Step 3: Verify pin constraints
        self.stdout.write('\nVerifying pin constraints...')
        results = pin_generation_service.verify_pin_constraints(test_pins, user_lat, user_lng)
        
        # Format and print results
        self.stdout.write('\n=== PIN CONSTRAINT TEST RESULTS ===')
        self.stdout.write(f'Total pins: {results["total_pins"]}')
        self.stdout.write(f'Valid pins (meeting all constraints): {results["valid_pins"]}')
        self.stdout.write(f'Overlapping pins (too close to each other): {results["overlapping_pins"]}')
        
        if user_lat and user_lng:
            self.stdout.write(f'Pins too close to user: {results["pins_too_close_to_user"]}')
            self.stdout.write(f'Nearest pin to user: {results["nearest_pin_to_user"]:.2f}m (min required: {LocationService.MIN_USER_DISTANCE_M}m)')
        
        if "min_pin_distance" in results and results["min_pin_distance"]:
            self.stdout.write(f'Minimum distance between pins: {results["min_pin_distance"]:.2f}m (should be ≥ {pin_generation_service.MIN_OFFSET_METERS}m)')
        
        if "avg_pin_distance" in results and results["avg_pin_distance"]:
            self.stdout.write(f'Average distance between pins: {results["avg_pin_distance"]:.2f}m')
            
        # Summary
        all_constraints_met = (
            results["overlapping_pins"] == 0 and
            results["pins_too_close_to_user"] == 0
        )
        
        if all_constraints_met:
            self.stdout.write(self.style.SUCCESS('\n✓ All pin constraints are satisfied!'))
        else:
            self.stdout.write(self.style.ERROR('\n✗ Some pin constraints are not satisfied!'))
            
        # Cleanup test pins if requested
        if cleanup:
            self.stdout.write('\nCleaning up test pins...')
            deleted_count = Pin.objects.filter(testing_tag=test_tag).delete()[0]
            self.stdout.write(f'Deleted {deleted_count} test pins')

    def _verify_location_distances(self, locations, user_lat, user_lng, min_distance):
        """Helper method to verify all locations meet minimum distance requirement"""
        self.stdout.write('\nVerifying location distances from user...')
        
        # Initialize distance calculation service
        pin_service = PinGenerationService()
        
        all_valid = True
        for zone, zone_locations in locations.items():
            self.stdout.write(f'\nChecking {zone} zone locations:')
            
            for i, loc in enumerate(zone_locations):
                distance = pin_service.calculate_distance_meters(
                    user_lat, user_lng, 
                    loc['lat'], loc['lng']
                )
                
                status = "✓" if distance >= min_distance else "✗"
                self.stdout.write(f'  {status} Location {i+1}: {loc.get("name", "unnamed")} - {distance:.2f}m from user')
                
                if distance < min_distance:
                    all_valid = False
        
        if all_valid:
            self.stdout.write(self.style.SUCCESS(f'\nAll locations are at least {min_distance}m from user'))
        else:
            self.stdout.write(self.style.ERROR(f'\nSome locations are less than {min_distance}m from user'))
