"""
Management command to test the seeding system functionality.

This command tests the seeding system by checking if it correctly identifies
areas that need seeding and generates appropriate seed pins.
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model

from seeding.services import SeedingService

User = get_user_model()


class Command(BaseCommand):
    help = 'Test the seeding system functionality'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--lat',
            type=float,
            default=37.7749,  # San Francisco
            help='Latitude for testing (default: San Francisco)'
        )
        parser.add_argument(
            '--lng',
            type=float,
            default=-122.4194,  # San Francisco
            help='Longitude for testing (default: San Francisco)'
        )
        parser.add_argument(
            '--force-seed',
            action='store_true',
            help='Force seeding even if area already has pins'
        )
    
    def handle(self, *args, **options):
        lat = options['lat']
        lng = options['lng']
        force_seed = options['force_seed']
        
        self.stdout.write(f'Testing seeding system at coordinates ({lat}, {lng})...')
        
        try:
            seeding_service = SeedingService()
            
            # Check if area needs seeding
            needs_seeding = seeding_service.should_seed_area(lat, lng)
            self.stdout.write(f'Area needs seeding: {needs_seeding}')
            
            if needs_seeding or force_seed:
                self.stdout.write('Generating seed pins...')
                
                # Generate seed pins
                seed_pins = seeding_service.generate_exploration_seed_pins(lat, lng)
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully generated {len(seed_pins)} seed pins!'
                    )
                )
                
                # Display created pins
                for i, pin in enumerate(seed_pins, 1):
                    self.stdout.write(
                        f'  {i}. {pin.title} by {pin.owner.username} '
                        f'at {pin.location_name} ({pin.genre}/{pin.mood})'
                    )
                    
            else:
                self.stdout.write(
                    self.style.WARNING(
                        'Area does not need seeding. Use --force-seed to generate pins anyway.'
                    )
                )
                    
        except Exception as e:
            raise CommandError(f'Error testing seeding system: {str(e)}')
