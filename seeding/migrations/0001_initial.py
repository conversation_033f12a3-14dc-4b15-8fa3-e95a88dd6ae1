# Generated by Django 4.2.7 on 2025-07-23 16:36

from django.conf import settings
import django.contrib.gis.db.models.fields
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SeedingMetrics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(auto_now_add=True)),
                (
                    "areas_seeded",
                    models.IntegerField(
                        default=0, help_text="Number of new areas seeded today"
                    ),
                ),
                (
                    "pins_generated",
                    models.IntegerField(
                        default=0, help_text="Number of seed pins created today"
                    ),
                ),
                (
                    "seeds_expired",
                    models.IntegerField(
                        default=0, help_text="Number of seed pins expired today"
                    ),
                ),
                (
                    "seeds_retired",
                    models.IntegerField(
                        default=0,
                        help_text="Number of seed pins retired due to organic growth",
                    ),
                ),
                (
                    "seed_pin_views",
                    models.IntegerField(
                        default=0, help_text="Total views of seed pins today"
                    ),
                ),
                (
                    "seed_pin_collects",
                    models.IntegerField(
                        default=0, help_text="Total collects of seed pins today"
                    ),
                ),
                (
                    "seed_pin_likes",
                    models.IntegerField(
                        default=0, help_text="Total likes of seed pins today"
                    ),
                ),
                (
                    "avg_user_engagement",
                    models.FloatField(
                        default=0.0, help_text="Average engagement rate with seed pins"
                    ),
                ),
                (
                    "avg_seeding_response_time",
                    models.FloatField(
                        default=0.0, help_text="Average time to generate seeds (ms)"
                    ),
                ),
                (
                    "api_failures",
                    models.IntegerField(
                        default=0, help_text="Number of API failures during seeding"
                    ),
                ),
                (
                    "cache_hit_rate",
                    models.FloatField(
                        default=0.0, help_text="Cache hit rate for seeding operations"
                    ),
                ),
            ],
            options={
                "ordering": ["-date"],
                "indexes": [
                    models.Index(fields=["date"], name="seeding_see_date_1c4a3f_idx")
                ],
                "unique_together": {("date",)},
            },
        ),
        migrations.CreateModel(
            name="SeedingArea",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "center_point",
                    django.contrib.gis.db.models.fields.PointField(
                        geography=True,
                        help_text="Center point of the seeded area",
                        srid=4326,
                    ),
                ),
                (
                    "radius_km",
                    models.FloatField(
                        default=5.0, help_text="Radius of the seeded area in kilometers"
                    ),
                ),
                (
                    "seed_count",
                    models.IntegerField(
                        help_text="Number of seed pins generated in this area"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "last_organic_check",
                    models.DateTimeField(
                        auto_now=True,
                        help_text="Last time organic pin count was checked",
                    ),
                ),
                (
                    "city_name",
                    models.CharField(
                        blank=True,
                        help_text="Detected city name for this area",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "country_code",
                    models.CharField(
                        blank=True,
                        help_text="ISO country code",
                        max_length=2,
                        null=True,
                    ),
                ),
                (
                    "organic_pin_count",
                    models.IntegerField(
                        default=0, help_text="Number of organic pins in this area"
                    ),
                ),
                (
                    "content_version",
                    models.CharField(
                        blank=True,
                        help_text="Version of seed content used",
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "strategy_used",
                    models.CharField(
                        default="exploration_zones",
                        help_text="Seeding strategy applied",
                        max_length=50,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["created_at"], name="seeding_see_created_065080_idx"
                    ),
                    models.Index(
                        fields=["last_organic_check"],
                        name="seeding_see_last_or_2b0189_idx",
                    ),
                    models.Index(
                        fields=["city_name"], name="seeding_see_city_na_29f886_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="SeedContentDatabase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "version",
                    models.CharField(
                        help_text="Version identifier for the seed content",
                        max_length=20,
                    ),
                ),
                (
                    "content_data",
                    models.JSONField(
                        help_text="JSON structure containing seed tracks, city profiles, and regional data"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this version is currently active",
                    ),
                ),
                (
                    "total_tracks",
                    models.IntegerField(
                        default=0, help_text="Total number of tracks in this database"
                    ),
                ),
                (
                    "cities_covered",
                    models.IntegerField(
                        default=0, help_text="Number of cities with specific profiles"
                    ),
                ),
                (
                    "last_api_update",
                    models.DateTimeField(
                        blank=True,
                        help_text="When APIs were last queried for updates",
                        null=True,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["is_active", "-created_at"],
                        name="seeding_see_is_acti_b621d6_idx",
                    ),
                    models.Index(
                        fields=["version"], name="seeding_see_version_f5df75_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="CuratorAccount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "persona_type",
                    models.CharField(
                        choices=[
                            ("indie_explorer", "Indie Explorer"),
                            ("coffee_enthusiast", "Coffee Enthusiast"),
                            ("venue_hopper", "Venue Hopper"),
                            ("campus_curator", "Campus Curator"),
                            ("city_wanderer", "City Wanderer"),
                            ("music_historian", "Music Historian"),
                            ("genre_specialist", "Genre Specialist"),
                            ("local_guide", "Local Guide"),
                        ],
                        max_length=30,
                    ),
                ),
                (
                    "preferred_genres",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=50),
                        default=list,
                        help_text="Genres this curator typically shares",
                        size=None,
                    ),
                ),
                (
                    "preferred_locations",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=50),
                        default=list,
                        help_text="Location types this curator frequents",
                        size=None,
                    ),
                ),
                (
                    "pins_created",
                    models.IntegerField(
                        default=0,
                        help_text="Number of seed pins attributed to this curator",
                    ),
                ),
                (
                    "last_used",
                    models.DateTimeField(
                        blank=True,
                        help_text="Last time this curator was used",
                        null=True,
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this curator is available for use",
                    ),
                ),
                (
                    "assigned_cities",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=100),
                        blank=True,
                        default=list,
                        help_text="Cities where this curator is active",
                        size=None,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="curator_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["persona_type"], name="seeding_cur_persona_61fa97_idx"
                    ),
                    models.Index(
                        fields=["is_active"], name="seeding_cur_is_acti_e7bfbe_idx"
                    ),
                    models.Index(
                        fields=["last_used"], name="seeding_cur_last_us_ab5591_idx"
                    ),
                ],
            },
        ),
    ]
