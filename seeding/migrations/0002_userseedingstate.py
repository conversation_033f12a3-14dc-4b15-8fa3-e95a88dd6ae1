# Generated by Django 4.2.7 on 2025-07-24 06:17

from django.conf import settings
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("seeding", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserSeedingState",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "current_state",
                    models.CharField(
                        choices=[
                            ("NEVER_SEEDED", "Never Seeded"),
                            ("PERSONALIZED_ONLY", "Personalized Only"),
                            ("FULL_SEEDED", "Full Seeded"),
                        ],
                        default="NEVER_SEEDED",
                        help_text="Current seeding state for this user",
                        max_length=20,
                    ),
                ),
                (
                    "first_seeding_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When user first received any type of seeding",
                        null=True,
                    ),
                ),
                (
                    "personalized_seeding_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When user received personalized seeding",
                        null=True,
                    ),
                ),
                (
                    "full_seeding_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When user received full seeding",
                        null=True,
                    ),
                ),
                (
                    "personalized_seeding_areas",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=100),
                        blank=True,
                        default=list,
                        help_text="List of area IDs where user received personalized seeding",
                        size=None,
                    ),
                ),
                (
                    "full_seeding_areas",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=100),
                        blank=True,
                        default=list,
                        help_text="List of area IDs where user received full seeding",
                        size=None,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="seeding_state",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["current_state"], name="seeding_use_current_8c0694_idx"
                    ),
                    models.Index(
                        fields=["user", "current_state"],
                        name="seeding_use_user_id_910235_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="seeding_use_created_c47f38_idx"
                    ),
                ],
            },
        ),
    ]
