from django.db import models
from django.contrib.gis.db import models as gis_models
from django.conf import settings
from django.utils import timezone
from django.contrib.postgres.fields import ArrayField


class SeedContentDatabase(models.Model):
    """
    Stores the JSON seed content database with music tracks and metadata
    for generating seed pins across different regions and genres.
    """
    version = models.CharField(max_length=20, help_text="Version identifier for the seed content")
    content_data = models.JSONField(help_text="JSON structure containing seed tracks, city profiles, and regional data")
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True, help_text="Whether this version is currently active")

    # Metadata about the content
    total_tracks = models.IntegerField(default=0, help_text="Total number of tracks in this database")
    cities_covered = models.IntegerField(default=0, help_text="Number of cities with specific profiles")
    last_api_update = models.DateTimeField(null=True, blank=True, help_text="When APIs were last queried for updates")

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['is_active', '-created_at']),
            models.Index(fields=['version']),
        ]

    def __str__(self):
        return f"Seed Content v{self.version} ({'Active' if self.is_active else 'Inactive'})"

    @classmethod
    def get_active_content(cls):
        """Get the currently active seed content database"""
        return cls.objects.filter(is_active=True).first()


class SeedingArea(models.Model):
    """
    Tracks geographic areas that have been seeded to prevent duplicate
    seeding and manage seed pin lifecycle.
    """
    center_point = gis_models.PointField(geography=True, help_text="Center point of the seeded area")
    radius_km = models.FloatField(default=5.0, help_text="Radius of the seeded area in kilometers")
    seed_count = models.IntegerField(help_text="Number of seed pins generated in this area")
    created_at = models.DateTimeField(auto_now_add=True)
    last_organic_check = models.DateTimeField(auto_now=True, help_text="Last time organic pin count was checked")

    # Area metadata
    city_name = models.CharField(max_length=100, blank=True, null=True, help_text="Detected city name for this area")
    country_code = models.CharField(max_length=2, blank=True, null=True, help_text="ISO country code")
    organic_pin_count = models.IntegerField(default=0, help_text="Number of organic pins in this area")

    # Seeding strategy used
    content_version = models.CharField(max_length=20, blank=True, null=True, help_text="Version of seed content used")
    strategy_used = models.CharField(max_length=50, default='exploration_zones', help_text="Seeding strategy applied")

    class Meta:
        indexes = [
            models.Index(fields=['created_at']),
            models.Index(fields=['last_organic_check']),
            models.Index(fields=['city_name']),
        ]

    def __str__(self):
        city_info = f" in {self.city_name}" if self.city_name else ""
        return f"Seeded area{city_info} ({self.seed_count} pins)"


class SeedingMetrics(models.Model):
    """
    Daily analytics and metrics for the seeding system performance
    and user engagement tracking.
    """
    date = models.DateField(auto_now_add=True)

    # Seeding activity metrics
    areas_seeded = models.IntegerField(default=0, help_text="Number of new areas seeded today")
    pins_generated = models.IntegerField(default=0, help_text="Number of seed pins created today")
    seeds_expired = models.IntegerField(default=0, help_text="Number of seed pins expired today")
    seeds_retired = models.IntegerField(default=0, help_text="Number of seed pins retired due to organic growth")

    # User engagement metrics
    seed_pin_views = models.IntegerField(default=0, help_text="Total views of seed pins today")
    seed_pin_collects = models.IntegerField(default=0, help_text="Total collects of seed pins today")
    seed_pin_likes = models.IntegerField(default=0, help_text="Total likes of seed pins today")
    avg_user_engagement = models.FloatField(default=0.0, help_text="Average engagement rate with seed pins")

    # System performance metrics
    avg_seeding_response_time = models.FloatField(default=0.0, help_text="Average time to generate seeds (ms)")
    api_failures = models.IntegerField(default=0, help_text="Number of API failures during seeding")
    cache_hit_rate = models.FloatField(default=0.0, help_text="Cache hit rate for seeding operations")

    class Meta:
        unique_together = ['date']
        ordering = ['-date']
        indexes = [
            models.Index(fields=['date']),
        ]

    def __str__(self):
        return f"Seeding metrics for {self.date}"


class CuratorAccount(models.Model):
    """
    Manages curator user accounts that own seed pins to make them
    appear authentic and community-generated.
    """
    PERSONA_TYPES = [
        ('indie_explorer', 'Indie Explorer'),
        ('coffee_enthusiast', 'Coffee Enthusiast'),
        ('venue_hopper', 'Venue Hopper'),
        ('campus_curator', 'Campus Curator'),
        ('city_wanderer', 'City Wanderer'),
        ('music_historian', 'Music Historian'),
        ('genre_specialist', 'Genre Specialist'),
        ('local_guide', 'Local Guide'),
    ]

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='curator_profile'
    )
    persona_type = models.CharField(max_length=30, choices=PERSONA_TYPES)

    # Curator characteristics
    preferred_genres = ArrayField(
        models.CharField(max_length=50),
        default=list,
        help_text="Genres this curator typically shares"
    )
    preferred_locations = ArrayField(
        models.CharField(max_length=50),
        default=list,
        help_text="Location types this curator frequents"
    )

    # Activity tracking
    pins_created = models.IntegerField(default=0, help_text="Number of seed pins attributed to this curator")
    last_used = models.DateTimeField(null=True, blank=True, help_text="Last time this curator was used")
    is_active = models.BooleanField(default=True, help_text="Whether this curator is available for use")

    # Geographic assignment
    assigned_cities = ArrayField(
        models.CharField(max_length=100),
        default=list,
        blank=True,
        help_text="Cities where this curator is active"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['persona_type']),
            models.Index(fields=['is_active']),
            models.Index(fields=['last_used']),
        ]

    def __str__(self):
        return f"{self.user.username} ({self.get_persona_type_display()})"

    def update_usage(self):
        """Update usage statistics when this curator is used"""
        self.pins_created += 1
        self.last_used = timezone.now()
        self.save(update_fields=['pins_created', 'last_used'])


class UserSeedingState(models.Model):
    """
    Tracks each user's seeding history to enable personalized seeding logic.

    This model manages three seeding states:
    1. NEVER_SEEDED: User has never received any seeds (eligible for personalized or full seeding)
    2. PERSONALIZED_ONLY: User has received personalized seeding only
    3. FULL_SEEDED: User has received full seeding (no additional automatic seeding)
    """

    SEEDING_STATES = [
        ('NEVER_SEEDED', 'Never Seeded'),
        ('PERSONALIZED_ONLY', 'Personalized Only'),
        ('FULL_SEEDED', 'Full Seeded'),
    ]

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='seeding_state'
    )
    current_state = models.CharField(
        max_length=20,
        choices=SEEDING_STATES,
        default='NEVER_SEEDED',
        help_text="Current seeding state for this user"
    )

    # Seeding history tracking
    first_seeding_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When user first received any type of seeding"
    )
    personalized_seeding_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When user received personalized seeding"
    )
    full_seeding_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When user received full seeding"
    )

    # Geographic tracking for seeding areas
    personalized_seeding_areas = ArrayField(
        models.CharField(max_length=100),
        default=list,
        blank=True,
        help_text="List of area IDs where user received personalized seeding"
    )
    full_seeding_areas = ArrayField(
        models.CharField(max_length=100),
        default=list,
        blank=True,
        help_text="List of area IDs where user received full seeding"
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['current_state']),
            models.Index(fields=['user', 'current_state']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.get_current_state_display()}"

    @classmethod
    def get_or_create_for_user(cls, user):
        """Get or create seeding state for a user"""
        state, created = cls.objects.get_or_create(
            user=user,
            defaults={'current_state': 'NEVER_SEEDED'}
        )
        return state, created

    def can_receive_personalized_seeding(self):
        """Check if user can receive personalized seeding"""
        return self.current_state == 'NEVER_SEEDED'

    def can_receive_full_seeding(self):
        """Check if user can receive full seeding"""
        return self.current_state in ['NEVER_SEEDED', 'PERSONALIZED_ONLY']

    def should_get_personalized_in_area(self, area_id):
        """Check if user should get personalized seeding in a specific area"""
        return (
            self.current_state == 'NEVER_SEEDED' and
            area_id not in self.personalized_seeding_areas and
            area_id not in self.full_seeding_areas
        )

    def mark_personalized_seeding(self, area_id=None):
        """Mark that user has received personalized seeding"""
        now = timezone.now()

        if self.current_state == 'NEVER_SEEDED':
            self.current_state = 'PERSONALIZED_ONLY'
            self.first_seeding_at = self.first_seeding_at or now
            self.personalized_seeding_at = now

            if area_id and area_id not in self.personalized_seeding_areas:
                self.personalized_seeding_areas.append(area_id)

            self.save(update_fields=[
                'current_state', 'first_seeding_at', 'personalized_seeding_at',
                'personalized_seeding_areas', 'updated_at'
            ])

    def mark_full_seeding(self, area_id=None):
        """Mark that user has received full seeding"""
        now = timezone.now()

        if self.current_state in ['NEVER_SEEDED', 'PERSONALIZED_ONLY']:
            self.current_state = 'FULL_SEEDED'
            self.first_seeding_at = self.first_seeding_at or now
            self.full_seeding_at = now

            if area_id and area_id not in self.full_seeding_areas:
                self.full_seeding_areas.append(area_id)

            self.save(update_fields=[
                'current_state', 'first_seeding_at', 'full_seeding_at',
                'full_seeding_areas', 'updated_at'
            ])
