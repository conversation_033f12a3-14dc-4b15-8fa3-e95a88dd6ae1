"""
API integration services for fetching music data from external sources.

This module handles integration with:
- Spotify API for popular tracks and metadata
- Last.fm API for artist similarity and genre data  
- SoundCharts API for trending and chart data
"""

import logging
import requests
import time
from typing import List, Dict, Optional, Any
from django.conf import settings
from django.core.cache import cache
import base64

logger = logging.getLogger(__name__)


class SpotifyAPIClient:
    """Client for Spotify Web API integration"""
    
    def __init__(self):
        self.client_id = getattr(settings, 'SPOTIFY_CLIENT_ID', '')
        self.client_secret = getattr(settings, 'SPOTIFY_CLIENT_SECRET', '')
        self.base_url = 'https://api.spotify.com/v1'
        self.token_url = 'https://accounts.spotify.com/api/token'
        self._access_token = None
        self._token_expires_at = 0
    
    def _get_access_token(self) -> Optional[str]:
        """Get or refresh Spotify access token"""
        if self._access_token and time.time() < self._token_expires_at:
            return self._access_token
        
        try:
            # Encode client credentials
            credentials = base64.b64encode(
                f"{self.client_id}:{self.client_secret}".encode()
            ).decode()
            
            headers = {
                'Authorization': f'Basic {credentials}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            data = {'grant_type': 'client_credentials'}
            
            response = requests.post(self.token_url, headers=headers, data=data, timeout=10)
            response.raise_for_status()
            
            token_data = response.json()
            self._access_token = token_data['access_token']
            self._token_expires_at = time.time() + token_data['expires_in'] - 60  # 1 min buffer
            
            return self._access_token
            
        except Exception as e:
            logger.error(f"Error getting Spotify access token: {str(e)}")
            return None
    
    def get_featured_playlists(self, country: str = 'US', limit: int = 20) -> List[Dict]:
        """Get featured playlists from Spotify using correct endpoint"""
        token = self._get_access_token()
        if not token:
            return []

        try:
            headers = {'Authorization': f'Bearer {token}'}
            params = {
                'limit': limit
            }

            response = requests.get(
                f"{self.base_url}/browse/featured-playlists",
                headers=headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()

            data = response.json()
            return data.get('playlists', {}).get('items', [])

        except Exception as e:
            logger.error(f"Error fetching Spotify featured playlists: {str(e)}")
            return []
    
    def get_playlist_tracks(self, playlist_id: str, limit: int = 50) -> List[Dict]:
        """Get tracks from a Spotify playlist using correct endpoint"""
        token = self._get_access_token()
        if not token:
            return []

        try:
            headers = {'Authorization': f'Bearer {token}'}
            params = {
                'limit': limit,
                'fields': 'items(track(id,name,artists,album,duration_ms,popularity,external_urls,preview_url))',
                'market': 'US'
            }

            response = requests.get(
                f"{self.base_url}/playlists/{playlist_id}/tracks",
                headers=headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()

            data = response.json()
            tracks = []

            for item in data.get('items', []):
                track = item.get('track')
                if track and track.get('id') and track.get('popularity', 0) > 40:  # Higher popularity threshold
                    tracks.append(self._format_spotify_track(track))

            return tracks

        except Exception as e:
            logger.error(f"Error fetching Spotify playlist tracks: {str(e)}")
            return []
    
    def get_top_tracks_by_genre(self, genre: str, limit: int = 20) -> List[Dict]:
        """Search for top tracks by genre with enhanced search terms"""
        token = self._get_access_token()
        if not token:
            return []

        try:
            headers = {'Authorization': f'Bearer {token}'}

            # Enhanced search terms for better genre matching
            search_terms = [
                f'genre:"{genre}"',
                f'genre:{genre}',
                f'{genre}',
                f'tag:{genre}'
            ]

            all_tracks = []

            for search_term in search_terms:
                params = {
                    'q': search_term,
                    'type': 'track',
                    'limit': limit // len(search_terms) + 5,  # Get more per search
                    'market': 'US'
                }

                response = requests.get(
                    f"{self.base_url}/search",
                    headers=headers,
                    params=params,
                    timeout=10
                )
                response.raise_for_status()

                data = response.json()

                for track in data.get('tracks', {}).get('items', []):
                    if track.get('id') and track.get('popularity', 0) > 30:  # Filter for popular tracks
                        formatted_track = self._format_spotify_track(track)
                        formatted_track['genre'] = genre  # Ensure genre is set
                        all_tracks.append(formatted_track)

                # Small delay between requests
                time.sleep(0.1)

            # Remove duplicates and sort by popularity
            seen_ids = set()
            unique_tracks = []
            for track in all_tracks:
                track_id = track.get('track_id', '')
                if track_id not in seen_ids:
                    seen_ids.add(track_id)
                    unique_tracks.append(track)

            # Sort by popularity and return top tracks
            unique_tracks.sort(key=lambda x: x.get('popularity_score', 0), reverse=True)
            return unique_tracks[:limit]

        except Exception as e:
            logger.error(f"Error searching Spotify tracks by genre: {str(e)}")
            return []

    def get_new_releases(self, country: str = 'US', limit: int = 20) -> List[Dict]:
        """Get new music releases"""
        token = self._get_access_token()
        if not token:
            return []

        try:
            headers = {'Authorization': f'Bearer {token}'}
            params = {
                'country': country,
                'limit': limit
            }

            response = requests.get(
                f"{self.base_url}/browse/new-releases",
                headers=headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()

            data = response.json()
            tracks = []

            # Get tracks from new release albums
            for album in data.get('albums', {}).get('items', []):
                if album.get('id'):
                    album_tracks = self.get_album_tracks(album['id'], limit=3)  # Get top 3 tracks per album
                    tracks.extend(album_tracks)

            return tracks[:limit]

        except Exception as e:
            logger.error(f"Error fetching Spotify new releases: {str(e)}")
            return []

    def get_album_tracks(self, album_id: str, limit: int = 10) -> List[Dict]:
        """Get tracks from an album"""
        token = self._get_access_token()
        if not token:
            return []

        try:
            headers = {'Authorization': f'Bearer {token}'}
            params = {
                'limit': limit,
                'market': 'US'
            }

            response = requests.get(
                f"{self.base_url}/albums/{album_id}/tracks",
                headers=headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()

            data = response.json()
            tracks = []

            for track in data.get('items', []):
                if track.get('id'):
                    tracks.append(self._format_spotify_track(track))

            return tracks

        except Exception as e:
            logger.error(f"Error fetching Spotify album tracks: {str(e)}")
            return []
    
    def get_popular_artists_tracks(self, limit: int = 50) -> List[Dict]:
        """Get tracks from popular current artists"""
        token = self._get_access_token()
        if not token:
            return []

        # Current popular artists (updated list)
        popular_artists = [
            'Tate McRae', 'Drake', 'Future', 'Playboi Carti', 'Morgan Wallen',
            'Taylor Swift', 'Bad Bunny', 'The Weeknd', 'Ariana Grande', 'Post Malone',
            'Billie Eilish', 'Dua Lipa', 'Olivia Rodrigo', 'Harry Styles', 'Travis Scott',
            'Lil Baby', 'Doja Cat', 'SZA', 'Kendrick Lamar', 'Tyler, The Creator',
            'Lana Del Rey', 'Sabrina Carpenter', 'Chappell Roan', 'Benson Boone',
            'Gracie Abrams', 'Noah Kahan', 'Zach Bryan', 'Luke Combs', 'Chris Stapleton'
        ]

        all_tracks = []
        tracks_per_artist = max(2, limit // len(popular_artists))

        try:
            for artist_name in popular_artists:
                if len(all_tracks) >= limit:
                    break

                # Search for artist
                artist_tracks = self.get_artist_top_tracks(artist_name, limit=tracks_per_artist)
                all_tracks.extend(artist_tracks)

                # Small delay to respect rate limits
                time.sleep(0.1)

            return all_tracks[:limit]

        except Exception as e:
            logger.error(f"Error fetching popular artists tracks: {str(e)}")
            return []

    def get_artist_top_tracks(self, artist_name: str, limit: int = 10) -> List[Dict]:
        """Get top tracks for a specific artist"""
        token = self._get_access_token()
        if not token:
            return []

        try:
            headers = {'Authorization': f'Bearer {token}'}

            # First search for the artist
            search_params = {
                'q': artist_name,
                'type': 'artist',
                'limit': 1,
                'market': 'US'
            }

            response = requests.get(
                f"{self.base_url}/search",
                headers=headers,
                params=search_params,
                timeout=10
            )
            response.raise_for_status()

            search_data = response.json()
            artists = search_data.get('artists', {}).get('items', [])

            if not artists:
                return []

            artist_id = artists[0]['id']

            # Get artist's top tracks
            response = requests.get(
                f"{self.base_url}/artists/{artist_id}/top-tracks",
                headers=headers,
                params={'market': 'US'},
                timeout=10
            )
            response.raise_for_status()

            data = response.json()
            tracks = []

            for track in data.get('tracks', [])[:limit]:
                if track.get('id'):
                    tracks.append(self._format_spotify_track(track))

            return tracks

        except Exception as e:
            logger.error(f"Error fetching artist top tracks for {artist_name}: {str(e)}")
            return []

    def _make_spotify_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """Make a generic Spotify API request"""
        token = self._get_access_token()
        if not token:
            return None

        try:
            headers = {'Authorization': f'Bearer {token}'}
            url = f"{self.base_url}/{endpoint}"

            response = requests.get(url, headers=headers, params=params or {}, timeout=10)
            response.raise_for_status()

            return response.json()

        except Exception as e:
            logger.error(f"Error making Spotify request to {endpoint}: {str(e)}")
            return None
    
    def _format_spotify_track(self, track: Dict) -> Dict:
        """Format Spotify track data to our standard format"""
        return {
            'track_id': f"spotify:track:{track['id']}",
            'title': track['name'],
            'artist': ', '.join([artist['name'] for artist in track.get('artists', [])]),
            'album': track.get('album', {}).get('name', ''),
            'duration_ms': track.get('duration_ms'),
            'popularity_score': track.get('popularity', 0),
            'track_url': track.get('external_urls', {}).get('spotify', ''),
            'preview_url': track.get('preview_url'),
            'artwork_url': track.get('album', {}).get('images', [{}])[0].get('url') if track.get('album', {}).get('images') else None,
            'service': 'spotify',
            'release_year': None,  # Would need additional API call
            'genre': None,  # Will be determined by context or Last.fm
            'mood': None   # Will be determined by audio features
        }


class LastFmAPIClient:
    """Client for Last.fm API integration"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'LASTFM_API_KEY', '')
        self.base_url = 'https://ws.audioscrobbler.com/2.0/'
    
    def get_top_tracks_by_country(self, country: str = 'united states', limit: int = 50, page: int = None) -> List[Dict]:
        """Get top tracks by country from Last.fm with optional page randomization"""
        if not self.api_key:
            return []

        try:
            # Add randomization if no specific page is requested
            if page is None:
                import random
                # Use random page between 1-5 to get variety while staying in quality range
                page = random.randint(1, 5)

            params = {
                'method': 'geo.getTopTracks',
                'country': country,
                'api_key': self.api_key,
                'format': 'json',
                'limit': limit,
                'page': page
            }
            
            response = requests.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            tracks = []
            
            for track in data.get('tracks', {}).get('track', []):
                formatted_track = self._format_lastfm_track(track)
                if formatted_track:
                    tracks.append(formatted_track)
            
            return tracks
            
        except Exception as e:
            logger.error(f"Error fetching Last.fm top tracks: {str(e)}")
            return []
    
    def get_artist_top_tracks(self, artist: str, limit: int = 10, page: int = None) -> List[Dict]:
        """Get top tracks for an artist with optional page randomization"""
        if not self.api_key:
            return []

        try:
            # Add randomization if no specific page is requested
            if page is None:
                import random
                # Use random page between 1-3 for artist tracks (smaller range)
                page = random.randint(1, 3)

            params = {
                'method': 'artist.getTopTracks',
                'artist': artist,
                'api_key': self.api_key,
                'format': 'json',
                'limit': limit,
                'page': page
            }
            
            response = requests.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            tracks = []
            
            for track in data.get('toptracks', {}).get('track', []):
                formatted_track = self._format_lastfm_track(track)
                if formatted_track:
                    tracks.append(formatted_track)
            
            return tracks
            
        except Exception as e:
            logger.error(f"Error fetching Last.fm artist tracks: {str(e)}")
            return []
    
    def get_track_info(self, artist: str, track: str) -> Optional[Dict]:
        """Get detailed track information including tags"""
        if not self.api_key:
            return None
        
        try:
            params = {
                'method': 'track.getInfo',
                'artist': artist,
                'track': track,
                'api_key': self.api_key,
                'format': 'json'
            }
            
            response = requests.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            track_info = data.get('track', {})
            
            # Extract genre tags
            tags = []
            for tag in track_info.get('toptags', {}).get('tag', []):
                tags.append(tag.get('name', '').lower())
            
            return {
                'tags': tags[:5],  # Top 5 tags
                'listeners': int(track_info.get('listeners', 0)),
                'playcount': int(track_info.get('playcount', 0))
            }
            
        except Exception as e:
            logger.error(f"Error fetching Last.fm track info: {str(e)}")
            return None
    
    def _format_lastfm_track(self, track: Dict) -> Optional[Dict]:
        """Format Last.fm track data to our standard format"""
        if not track.get('name') or not track.get('artist', {}).get('name'):
            return None

        # Extract artwork URL from Last.fm images
        artwork_url = None
        if track.get('image'):
            # Last.fm provides images in different sizes, get the largest one
            for image in reversed(track['image']):  # Reverse to get largest first
                if image.get('#text'):
                    artwork_url = image['#text']
                    break

        return {
            'track_id': f"lastfm:track:{track.get('mbid', track['name'])}",
            'title': track['name'],
            'artist': track['artist']['name'] if isinstance(track['artist'], dict) else str(track['artist']),
            'album': track.get('album', {}).get('#text', '') if isinstance(track.get('album'), dict) else '',
            'duration_ms': None,
            'popularity_score': int(track.get('listeners', 0)) // 1000,  # Convert to 0-100 scale
            'track_url': track.get('url', ''),
            'artwork_url': artwork_url,
            'service': 'lastfm',
            'listeners': int(track.get('listeners', 0)),
            'playcount': int(track.get('playcount', 0))
        }

    def enhance_track_with_spotify(self, track: Dict, spotify_client: 'SpotifyAPIClient') -> Dict:
        """Enhance Last.fm track with Spotify metadata for better artwork and URLs"""
        try:
            # Search for the track on Spotify
            search_query = f"{track['title']} artist:{track['artist']}"
            token = spotify_client._get_access_token()

            if not token:
                logger.debug(f"No Spotify token available for enhancing {track['title']}")
                return track

            headers = {'Authorization': f'Bearer {token}'}
            params = {
                'q': search_query,
                'type': 'track',
                'limit': 1,
                'market': 'US'
            }

            response = requests.get(
                f"{spotify_client.base_url}/search",
                headers=headers,
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                spotify_tracks = data.get('tracks', {}).get('items', [])

                if spotify_tracks:
                    spotify_track = spotify_tracks[0]

                    # Enhance the track with Spotify data
                    enhanced_track = track.copy()
                    enhanced_track.update({
                        'track_url': spotify_track.get('external_urls', {}).get('spotify', track.get('track_url', '')),
                        'artwork_url': spotify_track.get('album', {}).get('images', [{}])[0].get('url') or track.get('artwork_url'),
                        'duration_ms': spotify_track.get('duration_ms') or track.get('duration_ms'),
                        'album': spotify_track.get('album', {}).get('name', '') or track.get('album', ''),
                        'spotify_id': spotify_track.get('id'),
                        'preview_url': spotify_track.get('preview_url')
                    })

                    logger.debug(f"Enhanced {track['title']} with Spotify metadata")
                    return enhanced_track

        except Exception as e:
            logger.debug(f"Could not enhance track {track['title']} with Spotify: {str(e)}")

        return track


class SoundChartsAPIClient:
    """Client for SoundCharts API integration"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'SOUNDCHARTS_API_KEY', '')
        self.base_url = 'https://customer.api.soundcharts.com/api/v2'
    
    def get_chart_tracks(self, platform: str = 'spotify', country: str = 'us', limit: int = 50) -> List[Dict]:
        """Get chart tracks from SoundCharts using correct API"""
        if not self.api_key:
            return []

        try:
            headers = {
                'x-app-id': self.api_key,
                'Content-Type': 'application/json'
            }

            # Use the correct SoundCharts endpoint for charts
            response = requests.get(
                f"{self.base_url}/charts",
                headers=headers,
                params={
                    'platform': platform,
                    'country': country,
                    'limit': limit
                },
                timeout=15
            )
            response.raise_for_status()

            data = response.json()
            tracks = []

            # Parse the chart data structure
            for chart_item in data.get('items', []):
                track_data = chart_item.get('song', {})
                if track_data.get('name') and track_data.get('artists'):
                    formatted_track = self._format_soundcharts_track(track_data)
                    if formatted_track:
                        formatted_track['chart_position'] = chart_item.get('position')
                        tracks.append(formatted_track)

            return tracks

        except Exception as e:
            logger.error(f"Error fetching SoundCharts chart tracks: {str(e)}")
            return []
    
    def get_trending_songs(self, platform: str = 'spotify', country: str = 'us', limit: int = 50) -> List[Dict]:
        """Get trending songs using alternative endpoint"""
        if not self.api_key:
            return []

        try:
            headers = {
                'x-app-id': self.api_key,
                'Content-Type': 'application/json'
            }

            # Try alternative endpoint structure
            response = requests.get(
                f"{self.base_url}/songs/trending",
                headers=headers,
                params={
                    'platform': platform,
                    'country': country,
                    'limit': limit
                },
                timeout=15
            )
            response.raise_for_status()

            data = response.json()
            tracks = []

            for song_item in data.get('data', []):
                if song_item.get('name') and song_item.get('artists'):
                    formatted_track = self._format_soundcharts_track(song_item)
                    if formatted_track:
                        tracks.append(formatted_track)

            return tracks

        except Exception as e:
            logger.error(f"Error fetching SoundCharts trending songs: {str(e)}")
            return []
    
    def _format_soundcharts_track(self, track: Dict) -> Optional[Dict]:
        """Format SoundCharts track data to our standard format"""
        if not track.get('name') or not track.get('artists'):
            return None
        
        return {
            'track_id': f"soundcharts:track:{track.get('id', track['name'])}",
            'title': track['name'],
            'artist': ', '.join([artist.get('name', '') for artist in track.get('artists', [])]),
            'album': track.get('album', {}).get('name', ''),
            'duration_ms': track.get('duration'),
            'popularity_score': track.get('popularity', 0),
            'track_url': track.get('external_urls', {}).get('spotify', ''),
            'service': 'soundcharts',
            'chart_data': {
                'peak_position': track.get('peak_position'),
                'current_position': track.get('current_position'),
                'trend': track.get('trend')
            }
        }
