"""
Caption generation service using Groq's API for human-like, contextual captions.

This service generates natural, varied captions for music pins based on:
- Song title and artist
- Location type (cafe, park, venue, etc.)
- Curator persona type
- Contextual factors like time of day, mood, etc.
"""

import logging
import random
from typing import Dict, Optional, List
from django.conf import settings
from django.core.cache import cache
import time

logger = logging.getLogger(__name__)

try:
    from groq import Groq
    GROQ_AVAILABLE = True
except ImportError:
    GROQ_AVAILABLE = False
    logger.warning("Groq library not available. Install with: pip install groq")


class CaptionGenerationService:
    """
    Service for generating human-like captions using Groq's API.
    
    Uses small, fast language models like llama-3.1-8b-instant or gemma2-9b-it
    to generate contextual, emoji-rich captions that feel authentic.
    """
    
    # Available models (prioritized by speed and quality)
    MODELS = [
        "llama-3.1-8b-instant",
        "gemma2-9b-it", 
        "llama3-8b-8192",
        "mixtral-8x7b-32768"
    ]
    
    # Cache settings
    CACHE_TTL = 60 * 10  # 10 minutes (shorter for more variety)
    RATE_LIMIT_WINDOW = 60  # 1 minute
    MAX_REQUESTS_PER_WINDOW = 100  # Increased for seeding operations (Groq allows much higher)
    
    def __init__(self):
        self.api_key = getattr(settings, 'GROQ_API_KEY', '')
        self.client = None

        if not self.api_key:
            logger.warning("GROQ_API_KEY not configured in settings")
        elif GROQ_AVAILABLE:
            try:
                # Initialize Groq client according to quickstart guide
                self.client = Groq(api_key=self.api_key)
                logger.info("Groq client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Groq client: {str(e)}")
                # Try alternative initialization without any extra parameters
                try:
                    self.client = Groq(api_key=self.api_key)
                    logger.info("Groq client initialized with fallback method")
                except Exception as e2:
                    logger.error(f"Fallback initialization also failed: {str(e2)}")
        else:
            logger.warning("Groq library not available")
    
    def generate_caption(self, track_title: str, track_artist: str,
                        location_name: str = None, location_type: str = None,
                        curator_persona: str = None, is_seeding: bool = False) -> str:
        """
        Generate a human-like caption for a music pin.

        Args:
            track_title: The song title
            track_artist: The artist name
            location_name: Name of the location (optional)
            location_type: Type of location (cafe, park, venue, etc.)
            curator_persona: Curator's persona type (music_lover, trendsetter, etc.)
            is_seeding: Whether this is part of a seeding operation (relaxes rate limits)

        Returns:
            Generated caption string
        """
        # Check rate limiting (more lenient for seeding operations)
        if not self._check_rate_limit(is_seeding=is_seeding):
            logger.warning("Rate limit exceeded, using fallback caption")
            return self._get_fallback_caption(track_title, track_artist, location_name)
        
        # Try cache first
        cache_key = self._generate_cache_key(track_title, track_artist, location_type, curator_persona)
        cached_caption = cache.get(cache_key)
        if cached_caption:
            logger.debug(f"Using cached caption for {track_title}")
            return cached_caption
        
        # Generate new caption
        if self.client and GROQ_AVAILABLE:
            try:
                caption = self._generate_with_groq(
                    track_title, track_artist, location_name, location_type, curator_persona
                )
                
                if caption:
                    # Cache the result
                    cache.set(cache_key, caption, self.CACHE_TTL)
                    self._increment_rate_limit()
                    return caption
                    
            except Exception as e:
                logger.error(f"Error generating caption with Groq: {str(e)}")
        
        # Fallback to template-based caption
        fallback = self._get_fallback_caption(track_title, track_artist, location_name)
        logger.debug(f"Using fallback caption for {track_title}")
        return fallback
    
    def _generate_with_groq(self, track_title: str, track_artist: str,
                           location_name: str = None, location_type: str = None,
                           curator_persona: str = None) -> Optional[str]:
        """Generate caption using Groq's API"""
        
        # We'll create a generic location context below instead of using specific names
        
        # Persona-based tone adjustment
        persona_tone = self._get_persona_tone(curator_persona)
        
        # Add randomization to prevent repetitive patterns
        import random

        style_options = [
            "Write about how the song makes you feel emotionally",
            "Describe the perfect moment/time for this song",
            "Share a simple, genuine appreciation",
            "Connect the song to an activity or mood",
            "Express surprise at discovering something good",
            "Describe the song's energy or vibe",
            "Make a brief, relatable observation",
            "Share how the song fits your current state"
        ]

        chosen_style = random.choice(style_options)

        # Create generic location context without specific names
        generic_location = ""
        if location_type:
            location_type_map = {
                'cafe': 'at this cozy spot',
                'restaurant': 'over dinner',
                'park': 'in the park',
                'hotel': 'at this place',
                'venue': 'at this venue',
                'bar': 'at this spot',
                'store': 'while shopping',
                'gym': 'during my workout',
                'library': 'while studying',
                'museum': 'at this cultural spot',
                'landmark': 'at this spot'
            }
            generic_location = location_type_map.get(location_type, 'at this spot')
        else:
            generic_location = 'right now'

        prompt = f"""Write a short, casual caption for someone sharing a song {generic_location}.

Focus: {chosen_style}
Tone: {persona_tone}

Rules:
- ONE sentence only, 5-10 words maximum
- Don't mention the song title or artist name
- Don't mention specific place names or business names
- Sound like a real person texting a friend
- Use 1-2 emojis naturally
- Avoid: "obsessed", "giving me life", "just discovered", "vibes", "this song"
- Be genuine and relatable

Examples:
- "perfect for rainy afternoons ☔"
- "can't stop hitting repeat 🔁"
- "exactly what I needed today 💙"
- "study playlist essential 📚"
- "cozy coffee shop feels ☕"
- "perfect workout energy 💪"

Write only the caption:"""

        try:
            # Try models in order of preference
            for model in self.MODELS:
                try:
                    response = self.client.chat.completions.create(
                        model=model,
                        messages=[{"role": "user", "content": prompt}],
                        max_tokens=100,
                        temperature=0.8,  # Higher temperature for more creativity
                        timeout=10
                    )
                    
                    caption = response.choices[0].message.content.strip()
                    
                    # Clean up the caption
                    caption = self._clean_caption(caption)
                    
                    if caption and len(caption) > 10:  # Basic validation
                        logger.info(f"Generated caption with {model}: {caption}")
                        return caption
                        
                except Exception as e:
                    logger.debug(f"Model {model} failed: {str(e)}")
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"Error in Groq API call: {str(e)}")
            return None
    
    def _clean_caption(self, caption: str) -> str:
        """Clean and validate the generated caption"""
        # Remove quotes if present
        caption = caption.strip('"\'')
        
        # Remove any prefixes like "Caption:" 
        prefixes = ["caption:", "caption -", "caption:"]
        for prefix in prefixes:
            if caption.lower().startswith(prefix):
                caption = caption[len(prefix):].strip()
        
        # Ensure it's not too long
        if len(caption) > 200:
            caption = caption[:200].rsplit(' ', 1)[0] + "..."
        
        return caption.strip()
    
    def _get_persona_tone(self, curator_persona: str = None) -> str:
        """Get tone description based on curator persona"""
        persona_tones = {
            'music_lover': 'deeply passionate, uses music terminology naturally',
            'trendsetter': 'effortlessly cool, minimal words but impactful',
            'casual_listener': 'laid-back and conversational, like talking to a friend',
            'party_enthusiast': 'high energy, uses exclamation points and party emojis',
            'indie_explorer': 'introspective and thoughtful, appreciates artistry',
            'local_expert': 'knows the scene, references local culture',
            'coffee_enthusiast': 'cozy and warm, mentions the atmosphere',
            'city_wanderer': 'observational, connects music to moments',
            'venue_hopper': 'experienced with live music, compares to concerts',
            'campus_curator': 'relatable to student life, mentions studying/hanging out'
        }

        return persona_tones.get(curator_persona, 'genuine and relatable, like a friend sharing music')
    
    def _get_fallback_caption(self, track_title: str, track_artist: str, 
                             location_name: str = None) -> str:
        """Generate fallback caption using templates"""
        templates = [
            "hits different 🎵",
            "can't stop listening ✨",
            "perfect for today's mood 🎶",
            "on repeat all morning 🔁",
            "such beautiful music 💙",
            "exactly what I needed 🎧",
            "found my new favorite 💎",
            "pure magic {location} ✨",
            "perfect soundtrack {location} 🎵",
            "coffee shop essential ☕",
            "study playlist addition 📚",
            "rainy day perfection ☔",
            "late night feels 🌙"
        ]
        
        location_text = f"at {location_name}" if location_name else "at this spot"
        template = random.choice(templates)
        
        if "{location}" in template:
            return template.format(location=location_text)
        else:
            return template
    
    def _generate_cache_key(self, track_title: str, track_artist: str,
                           location_type: str = None, curator_persona: str = None) -> str:
        """Generate cache key for caption with randomization for variety"""
        import random
        import time

        # Add time-based variation (changes every 5 minutes for variety)
        time_bucket = int(time.time()) // 300  # 5-minute buckets

        key_parts = [
            "caption",
            track_title.lower().replace(" ", "_")[:20],
            track_artist.lower().replace(" ", "_")[:20],
            location_type or "unknown",
            curator_persona or "default",
            str(time_bucket)  # This ensures variety over time
        ]
        return ":".join(key_parts)
    
    def _check_rate_limit(self, is_seeding: bool = False) -> bool:
        """Check if we're within rate limits"""
        current_time = int(time.time())
        window_start = current_time - self.RATE_LIMIT_WINDOW

        # Get request count for current window
        rate_limit_key = f"groq_rate_limit:{window_start // self.RATE_LIMIT_WINDOW}"
        request_count = cache.get(rate_limit_key, 0)

        # Use higher limit for seeding operations
        max_requests = self.MAX_REQUESTS_PER_WINDOW * 3 if is_seeding else self.MAX_REQUESTS_PER_WINDOW
        return request_count < max_requests
    
    def _increment_rate_limit(self):
        """Increment rate limit counter"""
        current_time = int(time.time())
        window_start = current_time - self.RATE_LIMIT_WINDOW
        
        rate_limit_key = f"groq_rate_limit:{window_start // self.RATE_LIMIT_WINDOW}"
        current_count = cache.get(rate_limit_key, 0)
        cache.set(rate_limit_key, current_count + 1, self.RATE_LIMIT_WINDOW)
