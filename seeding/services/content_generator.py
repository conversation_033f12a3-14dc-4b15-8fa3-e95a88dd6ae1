"""
Content generator service for creating comprehensive seed content databases.

This service orchestrates data collection from multiple music APIs to create
high-quality, diverse seed content with proper genre classification, mood
analysis, and regional preferences.
"""

import logging
import random
from typing import Dict, List, Set, Any, Optional
from collections import defaultdict
from datetime import datetime

from .api_integrations import SpotifyAPIClient, LastFmAPIClient, SoundChartsAPIClient
from .regional_music_service import RegionalMusicService

logger = logging.getLogger(__name__)


class SeedContentGenerator:
    """
    Generates comprehensive seed content databases by aggregating data
    from multiple music APIs and applying quality filtering and diversity algorithms.
    """
    
    # Genre mapping for mood classification
    GENRE_MOOD_MAPPING = {
        'pop': ['happy', 'energetic', 'party'],
        'rock': ['energetic', 'workout', 'party'],
        'hip-hop': ['energetic', 'party', 'workout'],
        'rap': ['energetic', 'party', 'workout'],
        'electronic': ['energetic', 'party', 'workout', 'focus'],
        'dance': ['party', 'energetic', 'workout'],
        'indie': ['chill', 'happy', 'focus'],
        'alternative': ['chill', 'energetic', 'focus'],
        'jazz': ['chill', 'romantic', 'focus'],
        'blues': ['chill', 'romantic'],
        'country': ['happy', 'chill', 'romantic'],
        'folk': ['chill', 'romantic', 'happy'],
        'classical': ['focus', 'romantic', 'chill'],
        'ambient': ['chill', 'focus', 'romantic'],
        'lo-fi': ['chill', 'focus'],
        'acoustic': ['chill', 'romantic', 'happy'],
        'r&b': ['romantic', 'chill', 'happy'],
        'soul': ['romantic', 'happy', 'chill'],
        'funk': ['energetic', 'party', 'happy'],
        'reggae': ['chill', 'happy'],
        'latin': ['party', 'energetic', 'happy'],
        'world': ['happy', 'chill', 'energetic']
    }
    
    # Audio features to mood mapping (for Spotify audio features)
    AUDIO_FEATURES_MOOD_MAPPING = {
        'happy': {'valence': (0.6, 1.0), 'energy': (0.5, 1.0)},
        'energetic': {'energy': (0.7, 1.0), 'danceability': (0.6, 1.0)},
        'chill': {'valence': (0.3, 0.7), 'energy': (0.0, 0.5)},
        'romantic': {'valence': (0.4, 0.8), 'energy': (0.0, 0.6), 'acousticness': (0.3, 1.0)},
        'party': {'danceability': (0.7, 1.0), 'energy': (0.7, 1.0), 'valence': (0.6, 1.0)},
        'focus': {'energy': (0.0, 0.5), 'instrumentalness': (0.3, 1.0)},
        'workout': {'energy': (0.8, 1.0), 'tempo': (120, 200)}
    }
    
    # Comprehensive city music profiles for regional content
    CITY_PROFILES = {
        # US Cities
        'nashville': {
            'genres': ['country', 'americana', 'folk', 'rock', 'alt country', 'bluegrass'],
            'weights': {'country': 0.35, 'americana': 0.2, 'folk': 0.15, 'rock': 0.15, 'alt country': 0.1, 'bluegrass': 0.05},
            'cultural_context': 'music_city'
        },
        'new york': {
            'genres': ['hip-hop', 'jazz', 'indie', 'electronic', 'r&b', 'punk', 'art pop'],
            'weights': {'hip-hop': 0.25, 'jazz': 0.2, 'indie': 0.2, 'electronic': 0.15, 'r&b': 0.1, 'punk': 0.05, 'art pop': 0.05},
            'cultural_context': 'urban_diverse'
        },
        'los angeles': {
            'genres': ['pop', 'hip-hop', 'electronic', 'indie', 'latin', 'surf rock', 'west coast rap'],
            'weights': {'pop': 0.25, 'hip-hop': 0.2, 'electronic': 0.15, 'indie': 0.15, 'latin': 0.1, 'surf rock': 0.08, 'west coast rap': 0.07},
            'cultural_context': 'entertainment_capital'
        },
        'austin': {
            'genres': ['indie', 'country', 'rock', 'electronic', 'psychedelic', 'blues', 'folk'],
            'weights': {'indie': 0.25, 'country': 0.2, 'rock': 0.15, 'electronic': 0.15, 'psychedelic': 0.1, 'blues': 0.08, 'folk': 0.07},
            'cultural_context': 'live_music_capital'
        },
        'seattle': {
            'genres': ['grunge', 'indie', 'alternative', 'electronic', 'post rock', 'indie folk'],
            'weights': {'grunge': 0.2, 'indie': 0.25, 'alternative': 0.2, 'electronic': 0.15, 'post rock': 0.1, 'indie folk': 0.1},
            'cultural_context': 'alternative_scene'
        },
        'chicago': {
            'genres': ['blues', 'hip-hop', 'house', 'jazz', 'drill', 'soul', 'gospel'],
            'weights': {'blues': 0.2, 'hip-hop': 0.2, 'house': 0.2, 'jazz': 0.15, 'drill': 0.1, 'soul': 0.08, 'gospel': 0.07},
            'cultural_context': 'blues_house_city'
        },
        'miami': {
            'genres': ['reggaeton', 'latin', 'electronic', 'hip-hop', 'trap latino', 'dance', 'afrobeat'],
            'weights': {'reggaeton': 0.25, 'latin': 0.2, 'electronic': 0.15, 'hip-hop': 0.15, 'trap latino': 0.1, 'dance': 0.08, 'afrobeat': 0.07},
            'cultural_context': 'latin_electronic'
        },
        'san francisco': {
            'genres': ['indie', 'electronic', 'alternative', 'pop', 'psychedelic', 'synthwave'],
            'weights': {'indie': 0.25, 'electronic': 0.2, 'alternative': 0.2, 'pop': 0.15, 'psychedelic': 0.1, 'synthwave': 0.1},
            'cultural_context': 'tech_indie'
        },
        'atlanta': {
            'genres': ['trap', 'hip-hop', 'r&b', 'southern hip-hop', 'crunk', 'gospel'],
            'weights': {'trap': 0.3, 'hip-hop': 0.25, 'r&b': 0.15, 'southern hip-hop': 0.15, 'crunk': 0.08, 'gospel': 0.07},
            'cultural_context': 'trap_capital'
        },
        'detroit': {
            'genres': ['techno', 'hip-hop', 'motown', 'electronic', 'house', 'soul'],
            'weights': {'techno': 0.25, 'hip-hop': 0.2, 'motown': 0.2, 'electronic': 0.15, 'house': 0.1, 'soul': 0.1},
            'cultural_context': 'motor_city'
        },

        # International Cities
        'london': {
            'genres': ['grime', 'uk drill', 'electronic', 'indie', 'dubstep', 'garage', 'drum-and-bass'],
            'weights': {'grime': 0.2, 'uk drill': 0.15, 'electronic': 0.15, 'indie': 0.15, 'dubstep': 0.12, 'garage': 0.12, 'drum-and-bass': 0.11},
            'cultural_context': 'uk_urban'
        },
        'manchester': {
            'genres': ['indie', 'alternative', 'electronic', 'madchester', 'post punk'],
            'weights': {'indie': 0.3, 'alternative': 0.25, 'electronic': 0.2, 'madchester': 0.15, 'post punk': 0.1},
            'cultural_context': 'madchester'
        },
        'berlin': {
            'genres': ['techno', 'electronic', 'minimal techno', 'house', 'industrial', 'krautrock'],
            'weights': {'techno': 0.3, 'electronic': 0.25, 'minimal techno': 0.15, 'house': 0.15, 'industrial': 0.08, 'krautrock': 0.07},
            'cultural_context': 'techno_capital'
        },
        'paris': {
            'genres': ['electronic', 'french pop', 'house', 'chanson', 'nu disco'],
            'weights': {'electronic': 0.25, 'french pop': 0.2, 'house': 0.2, 'chanson': 0.2, 'nu disco': 0.15},
            'cultural_context': 'french_electronic'
        },
        'tokyo': {
            'genres': ['j-pop', 'electronic', 'anime', 'city pop', 'vaporwave', 'noise'],
            'weights': {'j-pop': 0.25, 'electronic': 0.2, 'anime': 0.15, 'city pop': 0.15, 'vaporwave': 0.15, 'noise': 0.1},
            'cultural_context': 'japanese_pop'
        },
        'seoul': {
            'genres': ['k-pop', 'korean hip-hop', 'electronic', 'indie', 'k-r&b'],
            'weights': {'k-pop': 0.35, 'korean hip-hop': 0.2, 'electronic': 0.15, 'indie': 0.15, 'k-r&b': 0.15},
            'cultural_context': 'k_pop_capital'
        },
        'mumbai': {
            'genres': ['bollywood', 'indian classical', 'indie', 'electronic', 'fusion'],
            'weights': {'bollywood': 0.3, 'indian classical': 0.2, 'indie': 0.2, 'electronic': 0.15, 'fusion': 0.15},
            'cultural_context': 'bollywood_hub'
        },
        'lagos': {
            'genres': ['afrobeat', 'afropop', 'hip-hop', 'dancehall', 'highlife', 'amapiano'],
            'weights': {'afrobeat': 0.25, 'afropop': 0.2, 'hip-hop': 0.15, 'dancehall': 0.15, 'highlife': 0.15, 'amapiano': 0.1},
            'cultural_context': 'afrobeat_capital'
        },
        'johannesburg': {
            'genres': ['amapiano', 'afrobeat', 'hip-hop', 'house', 'kwaito', 'gqom'],
            'weights': {'amapiano': 0.3, 'afrobeat': 0.2, 'hip-hop': 0.15, 'house': 0.15, 'kwaito': 0.1, 'gqom': 0.1},
            'cultural_context': 'amapiano_hub'
        },
        'sao paulo': {
            'genres': ['brazilian pop', 'funk carioca', 'samba', 'bossa nova', 'electronic', 'hip-hop'],
            'weights': {'brazilian pop': 0.25, 'funk carioca': 0.2, 'samba': 0.15, 'bossa nova': 0.15, 'electronic': 0.15, 'hip-hop': 0.1},
            'cultural_context': 'brazilian_music'
        },
        'mexico city': {
            'genres': ['reggaeton', 'latin pop', 'mariachi', 'cumbia', 'hip-hop', 'electronic'],
            'weights': {'reggaeton': 0.25, 'latin pop': 0.2, 'mariachi': 0.15, 'cumbia': 0.15, 'hip-hop': 0.15, 'electronic': 0.1},
            'cultural_context': 'latin_america'
        },
        'toronto': {
            'genres': ['hip-hop', 'r&b', 'indie', 'electronic', 'canadian pop', 'drake-core'],
            'weights': {'hip-hop': 0.25, 'r&b': 0.2, 'indie': 0.2, 'electronic': 0.15, 'canadian pop': 0.1, 'drake-core': 0.1},
            'cultural_context': 'canadian_urban'
        }
    }
    
    def __init__(self):
        self.spotify_client = SpotifyAPIClient()
        self.lastfm_client = LastFmAPIClient()
        self.soundcharts_client = SoundChartsAPIClient()
        self.regional_service = RegionalMusicService()
    
    def generate_comprehensive_content(self, target_tracks: int = 500) -> Dict[str, Any]:
        """
        Generate a comprehensive seed content database by aggregating
        data from all available music APIs.
        
        Args:
            target_tracks: Target number of tracks to collect
            
        Returns:
            Structured content database with tracks organized by genre, mood, and region
        """
        logger.info(f"Starting comprehensive content generation (target: {target_tracks} tracks)")
        
        all_tracks = []
        
        # DISABLED: Content generation disabled - using Last.fm only
        all_tracks = []
        
        # 4. Enhance tracks with additional metadata
        logger.info("Enhancing tracks with metadata...")
        enhanced_tracks = self._enhance_tracks_metadata(all_tracks)
        
        # 5. Apply quality filtering and deduplication
        logger.info("Applying quality filtering and deduplication...")
        filtered_tracks = self._filter_and_deduplicate_tracks(enhanced_tracks)
        
        # 6. Classify moods using audio features and genre data
        logger.info("Classifying track moods...")
        classified_tracks = self._classify_track_moods(filtered_tracks)
        
        # 7. Organize content by structure
        logger.info("Organizing content structure...")
        organized_content = self._organize_content_structure(classified_tracks)
        
        logger.info(f"Content generation completed: {len(classified_tracks)} tracks processed")
        
        return {
            "version": f"api_generated_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "generated_at": datetime.now().isoformat(),
            "total_tracks": len(classified_tracks),
            "sources": ["spotify", "lastfm", "soundcharts"],
            "content": organized_content
        }
    
    def _collect_spotify_tracks(self, target_count: int) -> List[Dict]:
        """DISABLED: Spotify collection disabled - use Last.fm only"""
        return []
    
    def _collect_lastfm_tracks(self, target_count: int) -> List[Dict]:
        """Collect tracks from Last.fm"""
        tracks = []
        
        try:
            # Get top tracks from major countries
            countries = ['united states', 'united kingdom', 'canada', 'australia', 'germany']
            
            for country in countries:
                if len(tracks) >= target_count:
                    break
                    
                country_tracks = self.lastfm_client.get_top_tracks_by_country(
                    country, limit=target_count // len(countries)
                )
                tracks.extend(country_tracks)
            
            logger.info(f"Collected {len(tracks)} tracks from Last.fm")
            return tracks[:target_count]
            
        except Exception as e:
            logger.error(f"Error collecting Last.fm tracks: {str(e)}")
            return []
    
    def _collect_soundcharts_tracks(self, target_count: int) -> List[Dict]:
        """Collect trending tracks from SoundCharts using updated endpoints"""
        tracks = []

        try:
            # Get chart tracks using correct endpoint
            chart_tracks = self.soundcharts_client.get_chart_tracks(
                platform='spotify',
                country='us',
                limit=target_count // 2
            )
            tracks.extend(chart_tracks)

            # Get trending songs using alternative endpoint
            trending_tracks = self.soundcharts_client.get_trending_songs(
                platform='spotify',
                country='us',
                limit=target_count // 2
            )
            tracks.extend(trending_tracks)

            logger.info(f"Collected {len(tracks)} tracks from SoundCharts")
            return tracks[:target_count]

        except Exception as e:
            logger.error(f"Error collecting SoundCharts tracks: {str(e)}")
            return []
    
    def _enhance_tracks_metadata(self, tracks: List[Dict]) -> List[Dict]:
        """Enhance tracks with additional metadata from Last.fm"""
        enhanced_tracks = []
        
        for track in tracks:
            enhanced_track = track.copy()
            
            # Try to get additional info from Last.fm
            try:
                if track.get('service') != 'lastfm':
                    lastfm_info = self.lastfm_client.get_track_info(
                        track['artist'], track['title']
                    )
                    
                    if lastfm_info:
                        enhanced_track['lastfm_tags'] = lastfm_info.get('tags', [])
                        enhanced_track['listeners'] = lastfm_info.get('listeners', 0)
                        enhanced_track['playcount'] = lastfm_info.get('playcount', 0)
                        
                        # Extract genre from tags
                        if not enhanced_track.get('genre') and lastfm_info.get('tags'):
                            enhanced_track['genre'] = self._extract_genre_from_tags(
                                lastfm_info['tags']
                            )
                
            except Exception as e:
                logger.debug(f"Could not enhance track {track['title']}: {str(e)}")
            
            enhanced_tracks.append(enhanced_track)
        
        return enhanced_tracks

    def _collect_regional_content(self, target_count: int) -> List[Dict]:
        """Collect regional music content from various cultural regions"""
        regional_tracks = []

        try:
            # Define key regions for cultural diversity
            key_regions = [
                ('Japan', 'Tokyo'),
                ('South Korea', 'Seoul'),
                ('Nigeria', 'Lagos'),
                ('South Africa', 'Johannesburg'),
                ('Kenya', 'Nairobi'),
                ('Brazil', 'Rio de Janeiro'),
                ('Mexico', 'Mexico City'),
                ('Argentina', 'Buenos Aires'),
                ('United Kingdom', 'London'),
                ('Germany', 'Berlin'),
                ('Jamaica', 'Kingston'),
                ('India', 'Mumbai')
            ]

            tracks_per_region = max(2, target_count // len(key_regions))

            for country, city in key_regions:
                if len(regional_tracks) >= target_count:
                    break

                region_tracks = self.regional_service.get_regional_content(
                    country=country,
                    city=city,
                    target_count=tracks_per_region
                )
                regional_tracks.extend(region_tracks)

            logger.info(f"Collected {len(regional_tracks)} regional tracks from {len(key_regions)} regions")
            return regional_tracks[:target_count]

        except Exception as e:
            logger.error(f"Error collecting regional content: {str(e)}")
            return []

    def _filter_and_deduplicate_tracks(self, tracks: List[Dict]) -> List[Dict]:
        """Apply enhanced quality filtering and remove duplicates"""
        seen_tracks = set()
        seen_track_ids = set()
        filtered_tracks = []

        # Sort tracks by popularity to prefer higher quality versions
        sorted_tracks = sorted(tracks, key=lambda x: x.get('popularity_score', 0), reverse=True)

        for track in sorted_tracks:
            # Create multiple unique identifiers for better deduplication
            track_title = (track.get('title') or '').lower().strip()
            track_artist = (track.get('artist') or '').lower().strip()
            track_id = track.get('track_id') or ''

            # Skip if missing essential data
            if not track_title or not track_artist:
                continue

            if len(track_title) < 2 or len(track_artist) < 2:
                continue

            # Create normalized identifiers
            track_key = f"{track_artist}:{track_title}"

            # Clean up common variations for better deduplication
            clean_title = self._clean_track_title(track_title)
            clean_artist = self._clean_artist_name(track_artist)
            clean_key = f"{clean_artist}:{clean_title}"

            # Check for duplicates using multiple methods
            if (track_key in seen_tracks or
                clean_key in seen_tracks or
                track_id in seen_track_ids):
                continue

            # Quality filters - be more selective
            popularity = track.get('popularity_score', 0)
            listeners = track.get('listeners', 0)

            # Higher quality thresholds
            if popularity < 20 and listeners < 5000:
                continue

            # Add to seen sets
            seen_tracks.add(track_key)
            seen_tracks.add(clean_key)
            if track_id:
                seen_track_ids.add(track_id)

            filtered_tracks.append(track)

        logger.info(f"Enhanced filtering: {len(tracks)} -> {len(filtered_tracks)} tracks")
        return filtered_tracks

    def _clean_track_title(self, title: str) -> str:
        """Clean track title for better deduplication"""
        import re

        # Remove common variations
        title = re.sub(r'\s*\(.*?\)\s*', '', title)  # Remove parentheses content
        title = re.sub(r'\s*\[.*?\]\s*', '', title)  # Remove brackets content
        title = re.sub(r'\s*-\s*.*?remix.*', '', title, flags=re.IGNORECASE)  # Remove remix info
        title = re.sub(r'\s*feat\.?\s+.*', '', title, flags=re.IGNORECASE)  # Remove featuring
        title = re.sub(r'\s*ft\.?\s+.*', '', title, flags=re.IGNORECASE)  # Remove ft.
        title = re.sub(r'\s+', ' ', title).strip()  # Normalize whitespace

        return title

    def _clean_artist_name(self, artist: str) -> str:
        """Clean artist name for better deduplication"""
        import re

        # Take only the first artist if multiple
        artist = artist.split(',')[0].split('&')[0].split('feat')[0].split('ft')[0]
        artist = re.sub(r'\s+', ' ', artist).strip()

        return artist
    
    def _classify_track_moods(self, tracks: List[Dict]) -> List[Dict]:
        """Classify track moods using genre and metadata (audio features deprecated)"""
        classified_tracks = []

        for track in tracks:
            classified_track = track.copy()

            # Determine genre if not already set
            if not classified_track.get('genre'):
                classified_track['genre'] = self._determine_genre(track)

            # Determine mood based on genre and metadata
            mood = self._determine_mood_from_genre_and_metadata(track)
            classified_track['mood'] = mood

            classified_tracks.append(classified_track)

        return classified_tracks
    
    def _determine_genre(self, track: Dict) -> str:
        """Determine genre from available metadata"""
        # Check Last.fm tags
        if track.get('lastfm_tags'):
            for tag in track['lastfm_tags']:
                if tag in self.GENRE_MOOD_MAPPING:
                    return tag
        
        # Default genre based on service or fallback
        return 'pop'  # Safe default
    
    def _determine_mood_from_genre_and_metadata(self, track: Dict) -> str:
        """Determine mood from genre and track metadata"""
        genre = (track.get('genre') or 'pop').lower()
        title = (track.get('title') or '').lower()
        artist = (track.get('artist') or '').lower()

        # Mood keywords in track titles
        mood_keywords = {
            'happy': ['happy', 'joy', 'smile', 'sunshine', 'bright', 'celebration', 'party', 'dance'],
            'energetic': ['energy', 'power', 'fire', 'electric', 'pump', 'hype', 'wild', 'crazy'],
            'chill': ['chill', 'relax', 'calm', 'peaceful', 'slow', 'soft', 'gentle', 'quiet'],
            'romantic': ['love', 'heart', 'romance', 'kiss', 'baby', 'honey', 'sweet', 'beautiful'],
            'party': ['party', 'club', 'dance', 'night', 'weekend', 'turn up', 'lit', 'bounce'],
            'focus': ['focus', 'study', 'work', 'concentrate', 'meditation', 'ambient'],
            'workout': ['workout', 'gym', 'run', 'training', 'beast', 'strong', 'power']
        }

        # Check for mood keywords in title
        for mood, keywords in mood_keywords.items():
            if any(keyword in title for keyword in keywords):
                return mood

        # Genre-based mood mapping with more variety
        genre_mood_weights = {
            'pop': ['happy', 'energetic', 'party'],
            'hip-hop': ['energetic', 'party', 'workout'],
            'rap': ['energetic', 'party', 'workout'],
            'electronic': ['energetic', 'party', 'focus'],
            'dance': ['party', 'energetic', 'workout'],
            'indie': ['chill', 'happy', 'focus'],
            'alternative': ['chill', 'energetic'],
            'rock': ['energetic', 'workout', 'party'],
            'r&b': ['romantic', 'chill', 'happy'],
            'jazz': ['chill', 'romantic', 'focus'],
            'country': ['happy', 'chill', 'romantic'],
            'reggaeton': ['party', 'energetic', 'dance'],
            'latin': ['party', 'energetic', 'happy'],
            'afrobeat': ['party', 'energetic', 'happy'],
            'k-pop': ['happy', 'energetic', 'party'],
            'grime': ['energetic', 'workout', 'party'],
            'drill': ['energetic', 'workout', 'party'],
            'trap': ['energetic', 'party', 'workout'],
            'house': ['party', 'energetic', 'focus'],
            'techno': ['energetic', 'party', 'focus'],
            'ambient': ['chill', 'focus', 'romantic'],
            'folk': ['chill', 'romantic', 'happy']
        }

        # Get possible moods for genre
        possible_moods = genre_mood_weights.get(genre, ['happy', 'energetic'])

        # Add some randomness but prefer certain moods based on popularity
        mood_weights = {
            'happy': 0.25,
            'energetic': 0.25,
            'party': 0.2,
            'chill': 0.15,
            'romantic': 0.1,
            'focus': 0.03,
            'workout': 0.02
        }

        # Weight the possible moods
        weighted_moods = []
        for mood in possible_moods:
            weight = mood_weights.get(mood, 0.1)
            count = int(weight * 100)
            weighted_moods.extend([mood] * count)

        return random.choice(weighted_moods) if weighted_moods else random.choice(possible_moods)
    
    def _extract_genre_from_tags(self, tags: List[str]) -> str:
        """Extract genre from Last.fm tags"""
        for tag in tags:
            if tag in self.GENRE_MOOD_MAPPING:
                return tag
        return 'pop'
    
    def _organize_content_structure(self, tracks: List[Dict]) -> Dict[str, Any]:
        """Organize tracks into the final content structure"""
        # Group tracks by genre and mood
        by_genre = defaultdict(list)
        by_mood = defaultdict(list)
        
        for track in tracks:
            genre = track.get('genre', 'pop')
            mood = track.get('mood', 'happy')
            
            by_genre[genre].append(track)
            by_mood[mood].append(track)
        
        # Create city-specific content
        city_profiles = {}
        for city, profile in self.CITY_PROFILES.items():
            city_tracks = []
            
            for genre, weight in profile['weights'].items():
                genre_tracks = by_genre.get(genre, [])
                count = int(len(genre_tracks) * weight * 0.3)  # 30% of genre tracks for city
                city_tracks.extend(random.sample(genre_tracks, min(count, len(genre_tracks))))
            
            city_profiles[city] = {
                'popular_genres': profile['genres'],
                'genre_weights': profile['weights'],
                'cultural_context': profile['cultural_context'],
                'tracks': city_tracks[:50]  # Limit to 50 tracks per city
            }
        
        return {
            'global': tracks,
            'genres': dict(by_genre),
            'moods': dict(by_mood),
            'city_profiles': city_profiles
        }
