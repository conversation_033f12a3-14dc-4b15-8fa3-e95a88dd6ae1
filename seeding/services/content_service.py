"""
Content service for managing seed content generation and selection.

This service handles:
- Seed content database management and retrieval
- Genre diversity algorithms ensuring no single genre exceeds 60%
- Regional content weighting (60% local preferences, 40% discovery)
- Content-to-location matching for contextual placement
"""

import logging
import random
from typing import List, Dict, Optional, Any
from django.core.cache import cache
from django.conf import settings

# Removed database and Spotify imports - using Last.fm only
# from seeding.models import SeedContentDatabase
# from spotify.services import SpotifyService
from lastfm.services import LastFmService
from .regional_music_service import RegionalMusicService

logger = logging.getLogger(__name__)


class ContentService:
    """
    Service for managing seed content selection and diversity.
    
    Implements content strategy from design document:
    - 60% locally popular genres based on city music profiles
    - 40% diverse discovery content for variety
    - Genre diversity ensuring no single genre exceeds 60%
    - Mood-to-location matching for contextual placement
    """
    
    # Genre diversity constraints
    MAX_SINGLE_GENRE_PERCENTAGE = 0.6  # No genre should exceed 60%
    LOCAL_CONTENT_WEIGHT = 0.6  # 60% local preferences
    DISCOVERY_CONTENT_WEIGHT = 0.4  # 40% discovery content
    
    # City music profiles (will be expanded with API data)
    CITY_MUSIC_PROFILES = {
        # US Cities
        'nashville': {
            'popular_genres': ['country', 'americana', 'folk', 'rock', 'alt country', 'bluegrass'],
            'genre_weights': {'country': 0.35, 'americana': 0.2, 'folk': 0.15, 'rock': 0.15, 'alt country': 0.1, 'bluegrass': 0.05},
            'cultural_context': 'music_city'
        },
        'new york': {
            'popular_genres': ['hip-hop', 'jazz', 'indie', 'electronic', 'r&b', 'punk', 'art pop'],
            'genre_weights': {'hip-hop': 0.25, 'jazz': 0.2, 'indie': 0.2, 'electronic': 0.15, 'r&b': 0.1, 'punk': 0.05, 'art pop': 0.05},
            'cultural_context': 'urban_diverse'
        },
        'los angeles': {
            'popular_genres': ['pop', 'hip-hop', 'electronic', 'indie', 'latin', 'surf rock', 'west coast rap'],
            'genre_weights': {'pop': 0.25, 'hip-hop': 0.2, 'electronic': 0.15, 'indie': 0.15, 'latin': 0.1, 'surf rock': 0.08, 'west coast rap': 0.07},
            'cultural_context': 'entertainment_capital'
        },
        'austin': {
            'popular_genres': ['indie', 'country', 'rock', 'electronic', 'psychedelic', 'blues', 'folk'],
            'genre_weights': {'indie': 0.25, 'country': 0.2, 'rock': 0.15, 'electronic': 0.15, 'psychedelic': 0.1, 'blues': 0.08, 'folk': 0.07},
            'cultural_context': 'live_music_capital'
        },
        'seattle': {
            'popular_genres': ['grunge', 'indie', 'alternative', 'electronic', 'post rock', 'indie folk'],
            'genre_weights': {'grunge': 0.2, 'indie': 0.25, 'alternative': 0.2, 'electronic': 0.15, 'post rock': 0.1, 'indie folk': 0.1},
            'cultural_context': 'alternative_scene'
        },
        'chicago': {
            'popular_genres': ['blues', 'hip-hop', 'house', 'jazz', 'drill', 'soul', 'gospel'],
            'genre_weights': {'blues': 0.2, 'hip-hop': 0.2, 'house': 0.2, 'jazz': 0.15, 'drill': 0.1, 'soul': 0.08, 'gospel': 0.07},
            'cultural_context': 'blues_house_city'
        },
        'miami': {
            'popular_genres': ['reggaeton', 'latin', 'electronic', 'hip-hop', 'trap latino', 'dance', 'afrobeat'],
            'genre_weights': {'reggaeton': 0.25, 'latin': 0.2, 'electronic': 0.15, 'hip-hop': 0.15, 'trap latino': 0.1, 'dance': 0.08, 'afrobeat': 0.07},
            'cultural_context': 'latin_electronic'
        },
        'san francisco': {
            'popular_genres': ['indie', 'electronic', 'alternative', 'pop', 'psychedelic', 'synthwave'],
            'genre_weights': {'indie': 0.25, 'electronic': 0.2, 'alternative': 0.2, 'pop': 0.15, 'psychedelic': 0.1, 'synthwave': 0.1},
            'cultural_context': 'tech_indie'
        },
        'atlanta': {
            'popular_genres': ['trap', 'hip-hop', 'r&b', 'southern hip-hop', 'crunk', 'gospel'],
            'genre_weights': {'trap': 0.3, 'hip-hop': 0.25, 'r&b': 0.15, 'southern hip-hop': 0.15, 'crunk': 0.08, 'gospel': 0.07},
            'cultural_context': 'trap_capital'
        },
        'detroit': {
            'popular_genres': ['techno', 'hip-hop', 'motown', 'electronic', 'house', 'soul'],
            'genre_weights': {'techno': 0.25, 'hip-hop': 0.2, 'motown': 0.2, 'electronic': 0.15, 'house': 0.1, 'soul': 0.1},
            'cultural_context': 'motor_city'
        },

        # International Cities
        'london': {
            'popular_genres': ['grime', 'uk drill', 'electronic', 'indie', 'dubstep', 'garage', 'drum-and-bass'],
            'genre_weights': {'grime': 0.2, 'uk drill': 0.15, 'electronic': 0.15, 'indie': 0.15, 'dubstep': 0.12, 'garage': 0.12, 'drum-and-bass': 0.11},
            'cultural_context': 'uk_urban'
        },
        'berlin': {
            'popular_genres': ['techno', 'electronic', 'minimal techno', 'house', 'industrial', 'krautrock'],
            'genre_weights': {'techno': 0.3, 'electronic': 0.25, 'minimal techno': 0.15, 'house': 0.15, 'industrial': 0.08, 'krautrock': 0.07},
            'cultural_context': 'techno_capital'
        },
        'tokyo': {
            'popular_genres': ['j-pop', 'electronic', 'anime', 'city pop', 'vaporwave', 'noise'],
            'genre_weights': {'j-pop': 0.25, 'electronic': 0.2, 'anime': 0.15, 'city pop': 0.15, 'vaporwave': 0.15, 'noise': 0.1},
            'cultural_context': 'japanese_pop'
        },
        'seoul': {
            'popular_genres': ['k-pop', 'korean hip-hop', 'electronic', 'indie', 'k-r&b'],
            'genre_weights': {'k-pop': 0.35, 'korean hip-hop': 0.2, 'electronic': 0.15, 'indie': 0.15, 'k-r&b': 0.15},
            'cultural_context': 'k_pop_capital'
        },
        'lagos': {
            'popular_genres': ['afrobeat', 'afropop', 'hip-hop', 'dancehall', 'highlife', 'amapiano'],
            'genre_weights': {'afrobeat': 0.25, 'afropop': 0.2, 'hip-hop': 0.15, 'dancehall': 0.15, 'highlife': 0.15, 'amapiano': 0.1},
            'cultural_context': 'afrobeat_capital'
        },
        'johannesburg': {
            'popular_genres': ['amapiano', 'afrobeat', 'hip-hop', 'house', 'kwaito', 'gqom'],
            'genre_weights': {'amapiano': 0.3, 'afrobeat': 0.2, 'hip-hop': 0.15, 'house': 0.15, 'kwaito': 0.1, 'gqom': 0.1},
            'cultural_context': 'amapiano_hub'
        },
        'nairobi': {
            'popular_genres': ['afrobeat', 'gengetone', 'bongo flava', 'kenyan hip-hop', 'benga', 'kapuka'],
            'genre_weights': {'afrobeat': 0.25, 'gengetone': 0.2, 'bongo flava': 0.15, 'kenyan hip-hop': 0.15, 'benga': 0.15, 'kapuka': 0.1},
            'cultural_context': 'east_african_hub'
        },
        'buenos aires': {
            'popular_genres': ['tango', 'cumbia', 'rock nacional', 'latin pop', 'reggaeton', 'folklore'],
            'genre_weights': {'tango': 0.2, 'cumbia': 0.2, 'rock nacional': 0.2, 'latin pop': 0.15, 'reggaeton': 0.15, 'folklore': 0.1},
            'cultural_context': 'argentine_culture'
        },
        'rio de janeiro': {
            'popular_genres': ['bossa nova', 'samba', 'brazilian funk', 'mpb', 'pagode', 'forro'],
            'genre_weights': {'bossa nova': 0.2, 'samba': 0.2, 'brazilian funk': 0.2, 'mpb': 0.15, 'pagode': 0.15, 'forro': 0.1},
            'cultural_context': 'brazilian_rhythm'
        },
        'mumbai': {
            'popular_genres': ['bollywood', 'indian classical', 'bhangra', 'indie', 'punjabi pop', 'qawwali'],
            'genre_weights': {'bollywood': 0.3, 'indian classical': 0.15, 'bhangra': 0.15, 'indie': 0.15, 'punjabi pop': 0.15, 'qawwali': 0.1},
            'cultural_context': 'bollywood_hub'
        },
        'melbourne': {
            'popular_genres': ['indie', 'electronic', 'alternative', 'house', 'australian hip-hop', 'psych rock'],
            'genre_weights': {'indie': 0.25, 'electronic': 0.2, 'alternative': 0.2, 'house': 0.15, 'australian hip-hop': 0.1, 'psych rock': 0.1},
            'cultural_context': 'indie_electronic'
        },
        'reykjavik': {
            'popular_genres': ['indie', 'electronic', 'post rock', 'ambient', 'nordic folk', 'experimental'],
            'genre_weights': {'indie': 0.25, 'electronic': 0.2, 'post rock': 0.2, 'ambient': 0.15, 'nordic folk': 0.1, 'experimental': 0.1},
            'cultural_context': 'nordic_experimental'
        },
        'kingston': {
            'popular_genres': ['reggae', 'dancehall', 'ska', 'dub', 'jamaican hip-hop', 'roots reggae'],
            'genre_weights': {'reggae': 0.25, 'dancehall': 0.25, 'ska': 0.15, 'dub': 0.15, 'jamaican hip-hop': 0.1, 'roots reggae': 0.1},
            'cultural_context': 'reggae_birthplace'
        }
    }
    
    # Mood-to-location type mapping
    MOOD_LOCATION_MAPPING = {
        'cafe': ['chill', 'focus', 'happy'],
        'park': ['chill', 'happy', 'romantic'],
        'venue': ['energetic', 'party', 'workout'],
        'campus': ['focus', 'chill', 'energetic'],
        'landmark': ['happy', 'energetic', 'romantic'],
        'transit': ['energetic', 'happy', 'workout']
    }
    
    def __init__(self):
        # Removed Spotify service - using Last.fm only
        # self.spotify_service = SpotifyService()
        self.lastfm_service = LastFmService()
        # Removed regional service - it uses Spotify
        # self.regional_service = RegionalMusicService()

    def get_fresh_diverse_content(self, lat: float, lng: float, target_count: int = 50) -> List[Dict]:
        """
        Get fresh diverse content from APIs with dynamic offsets and regional intelligence.
        Uses different charts, playlists, and offsets to ensure variety.
        """
        try:
            # Get city music profile for regional intelligence
            city_profile = self._get_city_music_profile(lat, lng)
            country = city_profile.get('country', '')
            has_regional = city_profile.get('has_regional_content', False)

            all_tracks = []
            used_track_keys = set()  # Track title:artist combinations

            # Strategy 1: Regional content (40% if available)
            if has_regional and country:
                regional_target = int(target_count * 0.4)
                regional_tracks = self._fetch_fresh_regional_content(country, regional_target, used_track_keys)
                all_tracks.extend(regional_tracks)
                logger.info(f"Fetched {len(regional_tracks)} fresh regional tracks for {country}")

            # Strategy 2: Global trending content (30%)
            global_target = int(target_count * 0.3)
            trending_tracks = self._fetch_fresh_trending_content(global_target, used_track_keys)
            all_tracks.extend(trending_tracks)

            # Strategy 3: Genre-diverse content (30%)
            diverse_target = target_count - len(all_tracks)
            if diverse_target > 0:
                diverse_tracks = self._fetch_fresh_diverse_content(diverse_target, used_track_keys, city_profile)
                all_tracks.extend(diverse_tracks)

            logger.info(f"Fetched {len(all_tracks)} total fresh tracks with {len(used_track_keys)} unique songs")
            return all_tracks

        except Exception as e:
            logger.error(f"Error fetching fresh diverse content: {str(e)}")
            return []

    def get_fresh_popular_content(self, lat: float, lng: float, target_count: int = 50, user=None) -> List[Dict]:
        """
        Get fresh popular content using Last.fm with 75% popular + 25% personalized split
        """
        try:
            from .api_integrations import LastFmAPIClient
            import random

            # Initialize Last.fm client
            lastfm_client = LastFmAPIClient()

            # Calculate split: 75% popular, 25% personalized
            popular_count = int(target_count * 0.75)
            personalized_count = target_count - popular_count

            all_tracks = []

            # 1. Get 75% popular tracks from country/region
            country = self._get_country_from_coordinates(lat, lng)
            logger.info(f"🌍 Fetching {popular_count} popular tracks for country: {country}")

            # Add randomization to limit for more variety
            random_limit = random.randint(popular_count, popular_count * 2)

            # Get top tracks for the country (random page will be selected automatically)
            popular_tracks = lastfm_client.get_top_tracks_by_country(country=country, limit=random_limit)

            if not popular_tracks:
                logger.warning(f"No tracks found for country {country}, falling back to global")
                popular_tracks = lastfm_client.get_top_tracks_by_country(country='united states', limit=random_limit)

            # 2. Get 25% personalized tracks from user's top artists
            personalized_tracks = []
            if user and hasattr(user, 'top_artists') and user.top_artists:
                logger.info(f"🎵 Fetching {personalized_count} personalized tracks from user's top artists")
                personalized_tracks = self._get_personalized_tracks_from_artists(
                    lastfm_client, user.top_artists, personalized_count
                )
            else:
                logger.info("No user or top artists found, using all popular tracks")
                # If no personalization available, get more popular tracks from different page
                additional_popular = lastfm_client.get_top_tracks_by_country(
                    country=country, limit=personalized_count, page=random.randint(2, 4)
                )
                personalized_tracks = additional_popular or []

            # Combine tracks with personalization markers
            # Mark popular tracks
            for track in popular_tracks[:popular_count]:
                track['is_personalized'] = False

            # Mark personalized tracks
            for track in personalized_tracks[:personalized_count]:
                track['is_personalized'] = True

            all_tracks.extend(popular_tracks[:popular_count])
            all_tracks.extend(personalized_tracks[:personalized_count])
            tracks = all_tracks

            # Remove duplicates and ensure variety
            used_track_keys = set()
            used_artists = set()
            final_tracks = []

            # Shuffle for randomness
            random.shuffle(tracks)

            for track in tracks:
                if len(final_tracks) >= target_count:
                    break

                track_key = f"{track.get('artist', '')}:{track.get('title', '')}"
                artist_name = track.get('artist', '')

                # Limit to 2 tracks per artist for variety
                artist_count = sum(1 for t in final_tracks if t.get('artist') == artist_name)
                if artist_count >= 2:
                    continue

                if track_key not in used_track_keys:
                    final_tracks.append(track)
                    used_track_keys.add(track_key)
                    used_artists.add(artist_name)
                    logger.info(f"✅ ADDED: {track.get('title')} - {track.get('artist')}")

            # Enhance tracks with Spotify metadata for better artwork and URLs
            final_tracks = self._enhance_tracks_with_spotify(final_tracks)

            logger.info(f"📊 Returning {len(final_tracks)} tracks for {country}")
            return final_tracks

        except Exception as e:
            logger.error(f"Error getting fresh popular content: {str(e)}")
            return []

    def _get_personalized_tracks_from_artists(self, lastfm_client, top_artists: list, target_count: int) -> List[Dict]:
        """
        Get personalized tracks from user's top artists using Last.fm artist.getTopTracks

        Args:
            lastfm_client: LastFmAPIClient instance
            top_artists: List of user's top artists
            target_count: Number of personalized tracks to fetch

        Returns:
            List of track dictionaries from user's top artists
        """
        try:
            import random

            personalized_tracks = []
            used_track_keys = set()

            # Limit to top 5 artists to avoid too much repetition
            selected_artists = top_artists[:5] if len(top_artists) > 5 else top_artists

            # Calculate tracks per artist
            tracks_per_artist = max(1, target_count // len(selected_artists))

            logger.info(f"🎵 Getting personalized tracks from {len(selected_artists)} artists: {selected_artists}")

            for artist in selected_artists:
                if len(personalized_tracks) >= target_count:
                    break

                try:
                    # Get top tracks for this artist (random page for variety)
                    artist_tracks = lastfm_client.get_artist_top_tracks(
                        artist=artist,
                        limit=tracks_per_artist * 2  # Get extra to filter duplicates
                    )

                    # Add tracks from this artist
                    artist_added = 0
                    for track in artist_tracks:
                        if artist_added >= tracks_per_artist or len(personalized_tracks) >= target_count:
                            break

                        track_key = f"{track.get('artist', '')}:{track.get('title', '')}"
                        if track_key not in used_track_keys:
                            personalized_tracks.append(track)
                            used_track_keys.add(track_key)
                            artist_added += 1
                            logger.info(f"✅ PERSONALIZED: {track.get('title')} - {track.get('artist')}")

                except Exception as e:
                    logger.warning(f"Error fetching tracks for artist {artist}: {str(e)}")
                    continue

            logger.info(f"📊 Fetched {len(personalized_tracks)} personalized tracks from user's top artists")
            return personalized_tracks

        except Exception as e:
            logger.error(f"Error getting personalized tracks from artists: {str(e)}")
            return []

    def _get_country_from_coordinates(self, lat: float, lng: float) -> str:
        """Map coordinates to Last.fm country names"""
        try:
            # North America
            if 24.0 <= lat <= 71.0 and -168.0 <= lng <= -52.0:
                if 49.0 <= lat <= 71.0:  # Canada
                    return 'canada'
                elif 14.0 <= lat <= 33.0 and -118.0 <= lng <= -86.0:  # Mexico
                    return 'mexico'
                else:  # United States
                    return 'united states'

            # Europe
            elif 35.0 <= lat <= 71.0 and -10.0 <= lng <= 40.0:
                if 51.0 <= lat <= 61.0 and -8.0 <= lng <= 2.0:  # United Kingdom
                    return 'united kingdom'
                elif 41.0 <= lat <= 51.5 and -5.0 <= lng <= 10.0:  # France
                    return 'france'
                elif 47.0 <= lat <= 55.0 and 6.0 <= lng <= 15.0:  # Germany
                    return 'germany'
                elif 36.0 <= lat <= 47.0 and 6.0 <= lng <= 19.0:  # Italy
                    return 'italy'
                elif 36.0 <= lat <= 44.0 and -9.0 <= lng <= 4.0:  # Spain
                    return 'spain'
                else:
                    return 'united kingdom'  # Default for Europe

            # Asia
            elif 10.0 <= lat <= 50.0 and 70.0 <= lng <= 180.0:
                if 30.0 <= lat <= 46.0 and 123.0 <= lng <= 132.0:  # Japan
                    return 'japan'
                elif 18.0 <= lat <= 54.0 and 73.0 <= lng <= 135.0:  # China
                    return 'china'
                elif 33.0 <= lat <= 43.0 and 124.0 <= lng <= 132.0:  # South Korea
                    return 'south korea'
                else:
                    return 'japan'  # Default for Asia

            # Australia/Oceania
            elif -50.0 <= lat <= -10.0 and 110.0 <= lng <= 180.0:
                return 'australia'

            # South America
            elif -60.0 <= lat <= 15.0 and -82.0 <= lng <= -30.0:
                if -35.0 <= lat <= -22.0 and -58.0 <= lng <= -48.0:  # Brazil
                    return 'brazil'
                else:
                    return 'brazil'  # Default for South America

            # Africa
            elif -35.0 <= lat <= 37.0 and -20.0 <= lng <= 52.0:
                return 'south africa'

            # Default fallback
            else:
                return 'united states'

        except Exception as e:
            logger.warning(f"Error mapping coordinates to country: {str(e)}")
            return 'united states'

    def get_diverse_seed_content(self, lat: float, lng: float) -> Dict[str, List[Dict]]:
        """
        DEPRECATED: This method used database fallbacks. Use get_fresh_popular_content instead.

        Returns empty content to force use of Last.fm-only approach.
        """
        logger.warning("get_diverse_seed_content is deprecated - use get_fresh_popular_content with Last.fm only")
        return {
            'happy': [],
            'chill': [],
            'energetic': [],
            'focus': [],
            'party': [],
            'romantic': [],
            'workout': []
        }
    
    def _get_city_music_profile(self, lat: float, lng: float) -> Dict[str, Any]:
        """Get music profile for a specific city/region with regional intelligence"""
        from seeding.services.location_service import LocationService

        location_service = LocationService()
        location_info = location_service.determine_location_from_coordinates(lat, lng)

        if not location_info:
            # Fallback to old method
            city_name = location_service.determine_city_from_coordinates(lat, lng) or ''
            country_name = ''
        else:
            city_name = location_info.get('city', '')
            country_name = location_info.get('country', '')

        # Look up city-specific profile first
        if city_name:
            city_key = city_name.lower()
            if city_key in self.CITY_MUSIC_PROFILES:
                profile = self.CITY_MUSIC_PROFILES[city_key].copy()
                # Removed regional content availability check - using Last.fm only
                return profile

        # Removed regional content check - using Last.fm only

        # Return default diverse profile
        return {
            'popular_genres': ['pop', 'rock', 'hip-hop', 'indie', 'electronic'],
            'genre_weights': {'pop': 0.2, 'rock': 0.2, 'hip-hop': 0.2, 'indie': 0.2, 'electronic': 0.2},
            'cultural_context': 'diverse',
            'country': country_name
        }

    def _get_regional_profile(self, country_name: str) -> Dict[str, Any]:
        """Get regional music profile based on country"""
        # Regional genre mappings based on country
        regional_profiles = {
            'japan': {
                'popular_genres': ['j-pop', 'j-rock', 'city pop', 'electronic', 'anime'],
                'genre_weights': {'j-pop': 0.4, 'j-rock': 0.2, 'city pop': 0.15, 'electronic': 0.15, 'anime': 0.1},
                'cultural_context': 'japanese_pop'
            },
            'south korea': {
                'popular_genres': ['k-pop', 'k-hip-hop', 'k-r&b', 'electronic', 'indie'],
                'genre_weights': {'k-pop': 0.5, 'k-hip-hop': 0.2, 'k-r&b': 0.15, 'electronic': 0.1, 'indie': 0.05},
                'cultural_context': 'korean_wave'
            },
            'nigeria': {
                'popular_genres': ['afrobeats', 'afropop', 'afro-fusion', 'hip-hop', 'highlife'],
                'genre_weights': {'afrobeats': 0.4, 'afropop': 0.25, 'afro-fusion': 0.15, 'hip-hop': 0.1, 'highlife': 0.1},
                'cultural_context': 'afrobeats_capital'
            },
            'south africa': {
                'popular_genres': ['amapiano', 'afrobeats', 'house', 'gqom', 'kwaito'],
                'genre_weights': {'amapiano': 0.4, 'afrobeats': 0.25, 'house': 0.15, 'gqom': 0.1, 'kwaito': 0.1},
                'cultural_context': 'amapiano_hub'
            },
            'kenya': {
                'popular_genres': ['afrobeats', 'gengetone', 'bongo flava', 'hip-hop', 'benga'],
                'genre_weights': {'afrobeats': 0.3, 'gengetone': 0.25, 'bongo flava': 0.2, 'hip-hop': 0.15, 'benga': 0.1},
                'cultural_context': 'east_african_hub'
            },
            'brazil': {
                'popular_genres': ['brazilian funk', 'sertanejo', 'bossa nova', 'mpb', 'pagode'],
                'genre_weights': {'brazilian funk': 0.3, 'sertanejo': 0.25, 'bossa nova': 0.15, 'mpb': 0.15, 'pagode': 0.15},
                'cultural_context': 'brazilian_rhythm'
            },
            'mexico': {
                'popular_genres': ['reggaeton', 'latin trap', 'regional mexican', 'latin pop', 'corridos'],
                'genre_weights': {'reggaeton': 0.3, 'latin trap': 0.25, 'regional mexican': 0.2, 'latin pop': 0.15, 'corridos': 0.1},
                'cultural_context': 'latin_urban'
            },
            'united kingdom': {
                'popular_genres': ['grime', 'uk drill', 'garage', 'dubstep', 'electronic'],
                'genre_weights': {'grime': 0.25, 'uk drill': 0.2, 'garage': 0.2, 'dubstep': 0.15, 'electronic': 0.2},
                'cultural_context': 'uk_urban'
            }
        }

        country_key = country_name.lower()
        profile = regional_profiles.get(country_key, {
            'popular_genres': ['pop', 'rock', 'hip-hop', 'electronic'],
            'genre_weights': {'pop': 0.25, 'rock': 0.25, 'hip-hop': 0.25, 'electronic': 0.25},
            'cultural_context': 'international'
        })

        profile['has_regional_content'] = True
        profile['country'] = country_name
        return profile

    def _normalize_country_match(self, track_country: str, detected_country: str) -> bool:
        """Normalize country names for matching regional content"""
        if not track_country or not detected_country:
            return False

        # Normalize both to lowercase for comparison
        track_country_norm = track_country.lower().strip()
        detected_country_norm = detected_country.lower().strip()

        # Direct match
        if track_country_norm == detected_country_norm:
            return True

        # Handle common variations
        if 'korea' in track_country_norm and 'korea' in detected_country_norm:
            return True
        if 'united kingdom' in track_country_norm and ('uk' in detected_country_norm or 'britain' in detected_country_norm):
            return True
        if 'united states' in track_country_norm and ('usa' in detected_country_norm or 'america' in detected_country_norm):
            return True

        return False

    def _fetch_fresh_regional_content(self, country: str, target_count: int, used_track_keys: set) -> List[Dict]:
        """DEPRECATED: Removed regional content - use Last.fm only"""
        return []

    def _fetch_fresh_trending_content(self, target_count: int, used_track_keys: set) -> List[Dict]:
        """DEPRECATED: Removed Spotify trending content - use Last.fm only"""
        logger.warning("_fetch_fresh_trending_content is deprecated - use Last.fm only")
        return []

    # All old Spotify-based methods removed - now using only Last.fm geo.getTopTracks

    def _is_modern_popular_track(self, track: Dict) -> bool:
        """Filter out classical, jazz, and old tracks to ensure only modern popular music"""
        try:
            title = track.get('title', '').lower()
            artist = track.get('artist', '').lower()
            genre = track.get('genre', '').lower()

            # Exclude classical music indicators (expanded list)
            classical_indicators = [
                'suite', 'concerto', 'symphony', 'sonata', 'prelude', 'fugue', 'nocturne',
                'etude', 'waltz', 'minuet', 'adagio', 'allegro', 'andante', 'presto',
                'quartet', 'quintet', 'opus', 'bwv', 'k.', 'hob.', 'clair de lune',
                'moonlight sonata', 'für elise', 'canon in d', 'ave maria',
                'well-tempered clavier', 'goldberg variations', 'brandenburg',
                'on the nature of daylight', 'written on the sky', 'the blue notebooks',
                'solitude', 'unforgettable', 'dream a little dream', 'fly me to the moon',
                'nightbook', 'divenire', 'nuvole bianche', 'una mattina', 'primavera',
                'cello suite', 'violin concerto', 'piano concerto', 'string quartet'
            ]

            # Exclude old jazz/swing artists (expanded list)
            old_artists = [
                'frank sinatra', 'nat king cole', 'ella fitzgerald', 'louis armstrong',
                'duke ellington', 'count basie', 'billie holiday', 'miles davis',
                'john coltrane', 'charlie parker', 'dizzy gillespie', 'thelonious monk',
                'ray charles', 'b.b. king', 'muddy waters', 'howlin\' wolf',
                'bing crosby', 'dean martin', 'sammy davis jr', 'tony bennett',
                'sarah vaughan', 'dinah washington', 'lena horne', 'peggy lee'
            ]

            # Exclude classical composers (expanded list)
            classical_composers = [
                'bach', 'mozart', 'beethoven', 'chopin', 'debussy', 'vivaldi',
                'tchaikovsky', 'brahms', 'schubert', 'liszt', 'rachmaninoff',
                'stravinsky', 'prokofiev', 'ravel', 'satie', 'handel', 'haydn',
                'ligeti', 'einaudi', 'ludovico einaudi', 'max richter', 'ólafur arnalds',
                'johann sebastian bach', 'yo-yo ma', 'itzhak perlman', 'lang lang',
                'daniel hope', 'hilary hahn', 'joshua bell', 'anne-sophie mutter',
                'mahan esfahani', 'alice sara ott', 'mao fujita', 'daniil trifonov'
            ]

            # Check for exclusions
            for indicator in classical_indicators:
                if indicator in title or indicator in artist:
                    return False

            for old_artist in old_artists:
                if old_artist in artist:
                    return False

            for composer in classical_composers:
                if composer in artist:
                    return False

            # Exclude classical/jazz genres
            excluded_genres = ['classical', 'jazz', 'blues', 'swing', 'big band', 'bebop']
            for excluded_genre in excluded_genres:
                if excluded_genre in genre:
                    return False

            return True

        except Exception as e:
            logger.warning(f"Error filtering track: {str(e)}")
            return True  # Default to including if error

    def _fetch_fresh_diverse_content(self, target_count: int, used_track_keys: set, city_profile: Dict) -> List[Dict]:
        """DEPRECATED: Removed Spotify diverse content - use Last.fm only"""
        return []

    def _select_diverse_content(self, content_data: Dict, city_profile: Dict, target_count: int) -> List[Dict]:
        """Select content ensuring genre diversity and local preferences"""
        try:
            # Calculate target counts per genre based on city profile
            local_count = int(target_count * self.LOCAL_CONTENT_WEIGHT)
            discovery_count = target_count - local_count
            
            selected_tracks = []
            
            # Select local preference tracks
            local_tracks = self._select_local_preference_tracks(
                content_data, city_profile, local_count
            )
            selected_tracks.extend(local_tracks)
            
            # Select discovery tracks (diverse genres)
            discovery_tracks = self._select_discovery_tracks(
                content_data, discovery_count, exclude_genres=set()
            )
            selected_tracks.extend(discovery_tracks)
            
            # Ensure genre diversity constraints
            final_tracks = self._enforce_genre_diversity(selected_tracks, target_count)
            
            return final_tracks
            
        except Exception as e:
            logger.error(f"Error selecting diverse content: {str(e)}")
            return []
    
    def _select_local_preference_tracks(self, content_data: Dict, city_profile: Dict, count: int) -> List[Dict]:
        """Enhanced local preference selection with regional content prioritization"""
        local_tracks = []

        try:
            # PRIORITY 1: Regional content for this country
            country = city_profile.get('country', '').lower()
            has_regional = city_profile.get('has_regional_content', False)

            if has_regional and country:
                # Get all available content
                all_content = content_data.get('global', [])

                # Filter regional content for this country (with normalization)
                regional_content = [
                    track for track in all_content
                    if track.get('regional') and self._normalize_country_match(track.get('country', ''), country)
                ]

                if regional_content:
                    # Use 70% of local preference slots for regional content
                    regional_count = min(int(count * 0.7), len(regional_content))
                    selected_regional = random.sample(regional_content, regional_count)
                    local_tracks.extend(selected_regional)

                    logger.info(f"Selected {len(selected_regional)} regional tracks for {country}")

            # PRIORITY 2: City-specific tracks if available
            remaining_count = count - len(local_tracks)
            if remaining_count > 0:
                city_tracks = content_data.get('city_profiles', {}).get(
                    city_profile.get('cultural_context', ''), {}
                ).get('tracks', [])

                if city_tracks:
                    city_selection_count = min(remaining_count, len(city_tracks))
                    selected_city = random.sample(city_tracks, city_selection_count)
                    local_tracks.extend(selected_city)
                    remaining_count -= len(selected_city)

            # PRIORITY 3: Genre-based selection for remaining slots
            if remaining_count > 0:
                genre_tracks = self._select_by_genre_weights(
                    content_data, city_profile.get('genre_weights', {}), remaining_count
                )
                local_tracks.extend(genre_tracks)

        except Exception as e:
            logger.error(f"Error selecting enhanced local preference tracks: {str(e)}")

        return local_tracks[:count]
    
    def _select_by_genre_weights(self, content_data: Dict, genre_weights: Dict, count: int) -> List[Dict]:
        """Select tracks based on genre weights"""
        selected_tracks = []
        
        try:
            genres_content = content_data.get('genres', {})
            
            for genre, weight in genre_weights.items():
                genre_count = int(count * weight)
                if genre in genres_content and genre_count > 0:
                    genre_tracks = genres_content[genre]
                    selected_tracks.extend(random.sample(
                        genre_tracks, min(genre_count, len(genre_tracks))
                    ))
            
        except Exception as e:
            logger.error(f"Error selecting tracks by genre weights: {str(e)}")
        
        return selected_tracks
    
    def _select_discovery_tracks(self, content_data: Dict, count: int, exclude_genres: set) -> List[Dict]:
        """Select diverse discovery tracks"""
        discovery_tracks = []
        
        try:
            # Get global popular tracks
            global_tracks = content_data.get('global', [])
            
            # Filter out excluded genres and select diverse tracks
            available_tracks = [
                track for track in global_tracks
                if track.get('genre') not in exclude_genres
            ]
            
            if available_tracks:
                discovery_tracks = random.sample(
                    available_tracks, min(count, len(available_tracks))
                )
            
        except Exception as e:
            logger.error(f"Error selecting discovery tracks: {str(e)}")
        
        return discovery_tracks
    
    def _enforce_genre_diversity(self, tracks: List[Dict], target_count: int) -> List[Dict]:
        """Ensure no single genre exceeds the maximum percentage"""
        if not tracks:
            return tracks
        
        # Count tracks by genre
        genre_counts = {}
        for track in tracks:
            genre = track.get('genre', 'unknown')
            genre_counts[genre] = genre_counts.get(genre, 0) + 1
        
        # Check if any genre exceeds the limit
        max_allowed_per_genre = int(target_count * self.MAX_SINGLE_GENRE_PERCENTAGE)
        
        final_tracks = []
        genre_used_counts = {}
        
        # Shuffle tracks to ensure random selection within constraints
        shuffled_tracks = tracks.copy()
        random.shuffle(shuffled_tracks)
        
        for track in shuffled_tracks:
            genre = track.get('genre', 'unknown')
            current_count = genre_used_counts.get(genre, 0)
            
            if current_count < max_allowed_per_genre:
                final_tracks.append(track)
                genre_used_counts[genre] = current_count + 1
                
                if len(final_tracks) >= target_count:
                    break
        
        return final_tracks
    
    def _organize_content_by_mood(self, tracks: List[Dict]) -> Dict[str, List[Dict]]:
        """Organize content by mood for location matching"""
        organized = {
            'chill': [],
            'energetic': [],
            'happy': [],
            'focus': [],
            'party': [],
            'romantic': [],
            'workout': []
        }
        
        for track in tracks:
            mood = track.get('mood', 'happy')
            if mood in organized:
                organized[mood].append(track)
            else:
                # Default to happy if mood not recognized
                organized['happy'].append(track)
        
        return organized
    
    def get_content_for_location_type(self, content: Dict[str, List[Dict]], location_type: str) -> List[Dict]:
        """Get appropriate content for a specific location type"""
        suitable_moods = self.MOOD_LOCATION_MAPPING.get(location_type, ['happy'])
        
        suitable_tracks = []
        for mood in suitable_moods:
            suitable_tracks.extend(content.get(mood, []))
        
        # If no suitable tracks found, return any available tracks
        if not suitable_tracks:
            for mood_tracks in content.values():
                suitable_tracks.extend(mood_tracks)
        
        return suitable_tracks
    
    def get_current_content_version(self) -> Optional[str]:
        """Get the version of the currently active seed content database"""
        # Since we're using Last.fm API instead of database content, return a static version
        return "lastfm_api_v1.0"
    
    def _enhance_tracks_with_spotify(self, tracks: List[Dict]) -> List[Dict]:
        """
        Enhance Last.fm tracks with Spotify metadata for better artwork URLs and track URLs
        """
        try:
            from .api_integrations import SpotifyAPIClient, LastFmAPIClient

            spotify_client = SpotifyAPIClient()
            lastfm_client = LastFmAPIClient()
            enhanced_tracks = []

            for track in tracks:
                # Only enhance Last.fm tracks to get better Spotify metadata
                if (track.get('service') == 'lastfm'):

                    enhanced_track = lastfm_client.enhance_track_with_spotify(track, spotify_client)
                    enhanced_tracks.append(enhanced_track)

                    # Log enhancement status
                    if enhanced_track.get('spotify_id'):
                        logger.info(f"🎨 Enhanced {track['title']} with Spotify artwork and URL")
                    else:
                        logger.info(f"⚠️ Could not enhance {track['title']} with Spotify")
                else:
                    enhanced_tracks.append(track)

            return enhanced_tracks

        except Exception as e:
            logger.error(f"Error enhancing tracks with Spotify: {str(e)}")
            return tracks  # Return original tracks if enhancement fails

    def _get_fallback_content(self) -> Dict[str, List[Dict]]:
        """DEPRECATED: Return empty content to force Last.fm-only approach"""
        logger.warning("_get_fallback_content is deprecated - should use Last.fm only")
        return {
            'happy': [],
            'chill': [],
            'energetic': [],
            'focus': [],
            'party': [],
            'romantic': [],
            'workout': []
        }
