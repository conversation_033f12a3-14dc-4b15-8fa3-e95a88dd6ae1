"""
Location service for identifying points of interest and managing geographic
distribution of seed pins.

This service handles:
- POI discovery using multiple data sources (Google Places, OpenStreetMap)
- Geographic distribution logic for realistic pin placement
- City identification and reverse geocoding
- Fallback location generation when POI APIs fail
"""

import logging
import random
import math
import time
from typing import List, Dict, Optional, Tuple
from django.contrib.gis.geos import Point
from django.core.cache import cache
from django.conf import settings
import requests

logger = logging.getLogger(__name__)


class LocationService:
    """
    Service for handling location intelligence and POI discovery for seed pins.
    
    Implements the three-tier exploration pattern:
    - Immediate zone (0-500m): cafes, parks, nearby venues
    - Nearby zone (500m-2km): libraries, campuses, cultural spots  
    - City zone (2km+): major landmarks, popular districts
    """
    
    # Minimum distance from user for any seeded pin (in meters)
    MIN_USER_DISTANCE_M = 250
    
    # POI type mappings for different data sources
    GOOGLE_PLACES_TYPES = {
        'immediate': ['cafe', 'restaurant', 'park', 'store'],
        'nearby': ['library', 'university', 'school', 'museum', 'tourist_attraction'],
        'city': ['tourist_attraction', 'establishment', 'point_of_interest']
    }
    
    OSM_AMENITY_TYPES = {
        'immediate': ['cafe', 'restaurant', 'fast_food', 'pub', 'bar'],
        'nearby': ['library', 'university', 'school', 'college', 'museum'],
        'city': ['theatre', 'cinema', 'arts_centre', 'community_centre']
    }
    
    # Location type categorization
    LOCATION_CATEGORIES = {
        'cafe': ['cafe', 'restaurant', 'fast_food', 'coffee_shop'],
        'park': ['park', 'garden', 'recreation_ground'],
        'venue': ['bar', 'pub', 'night_club', 'music_venue', 'theatre'],
        'campus': ['library', 'university', 'school', 'college'],
        'landmark': ['tourist_attraction', 'museum', 'monument'],
        'transit': ['transit_station', 'subway_station', 'bus_station']
    }
    
    def __init__(self):
        self.google_api_key = getattr(settings, 'GOOGLE_PLACES_API_KEY', '')
        
    def _calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """
        Calculate distance between two coordinates in meters using the haversine formula.
        
        Args:
            lat1: Latitude of first point
            lng1: Longitude of first point
            lat2: Latitude of second point
            lng2: Longitude of second point
            
        Returns:
            Distance in meters
        """
        # Earth radius in meters
        R = 6371000
        
        # Convert to radians
        lat1_rad = math.radians(lat1)
        lng1_rad = math.radians(lng1)
        lat2_rad = math.radians(lat2)
        lng2_rad = math.radians(lng2)
        
        # Haversine formula
        dlat = lat2_rad - lat1_rad
        dlng = lng2_rad - lng1_rad
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlng/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = R * c
        
        return distance
    
    def _filter_locations_by_distance(self, locations: List[Dict], user_lat: float, user_lng: float) -> List[Dict]:
        """
        Filter out locations that are too close to the user.
        
        Args:
            locations: List of location dictionaries with 'lat' and 'lng' keys
            user_lat: User's latitude
            user_lng: User's longitude
            
        Returns:
            Filtered list of locations at least MIN_USER_DISTANCE_M away from user
        """
        filtered_locations = []
        
        for location in locations:
            distance = self._calculate_distance(
                user_lat, user_lng, 
                location['lat'], location['lng']
            )
            
            # Only include locations that are at least MIN_USER_DISTANCE_M meters away
            if distance >= self.MIN_USER_DISTANCE_M:
                filtered_locations.append(location)
            else:
                logger.debug(f"Excluding POI {location.get('name', 'unnamed')} at distance {distance:.1f}m (less than {self.MIN_USER_DISTANCE_M}m from user)" )
                
        return filtered_locations
        
    def get_seed_locations(self, center_lat: float, center_lng: float) -> Dict[str, List[Dict]]:
        """
        Generate realistic locations for seed pins following exploration pattern.

        Returns locations organized by exploration zones:
        - immediate: 5-8 locations within 2km walking/cycling distance
        - nearby: 5-10 locations within 8km for short exploration trips
        - city: 3-5 locations across broader city area (20km)
        - extended: 8-12 locations across extended coverage area (30km)

        Args:
            center_lat: Latitude of center point (user's location)
            center_lng: Longitude of center point (user's location)

        Returns:
            Dict with zone names as keys and lists of location dicts as values
        """
        try:
            # Import seeding service constants for consistent distance parameters
            from seeding.services.seeding_service import SeedingService

            # Try to get POI data from APIs using updated distances for city-wide distribution
            raw_locations = {
                'immediate': self._get_zone_locations(center_lat, center_lng, SeedingService.IMMEDIATE_ZONE_RADIUS_M, 'immediate'),
                'nearby': self._get_zone_locations(center_lat, center_lng, SeedingService.NEARBY_ZONE_RADIUS_M, 'nearby'),
                'city': self._get_zone_locations(center_lat, center_lng, SeedingService.CITY_ZONE_RADIUS_M, 'city'),
                'extended': self._get_zone_locations(center_lat, center_lng, SeedingService.EXTENDED_ZONE_RADIUS_M, 'extended')
            }
            
            # Filter out locations that are too close to the user's location
            locations = {}
            for zone, zone_locations in raw_locations.items():
                filtered_locations = self._filter_locations_by_distance(
                    zone_locations, center_lat, center_lng
                )
                logger.info(f"[DISTANCE_FILTER] Zone {zone}: filtered {len(zone_locations)} -> {len(filtered_locations)} locations (min distance: {self.MIN_USER_DISTANCE_M}m)")
                locations[zone] = filtered_locations
            
            # Ensure minimum location counts with fallbacks
            for zone, target_count in [('immediate', 8), ('nearby', 10), ('city', 5)]:
                if len(locations[zone]) < target_count:
                    # Generate fallback locations and filter them too
                    fallback_locations = self._generate_fallback_locations(
                        center_lat, center_lng, zone, target_count - len(locations[zone])
                    )
                    filtered_fallback = self._filter_locations_by_distance(
                        fallback_locations, center_lat, center_lng
                    )
                    locations[zone].extend(filtered_fallback)
            
            return locations
            
        except Exception as e:
            logger.error(f"Error getting seed locations: {str(e)}")
            # Return fallback locations for all zones
            return {
                'immediate': self._generate_fallback_locations(center_lat, center_lng, 'immediate', 8),
                'nearby': self._generate_fallback_locations(center_lat, center_lng, 'nearby', 10),
                'city': self._generate_fallback_locations(center_lat, center_lng, 'city', 5),
                'extended': self._generate_fallback_locations(center_lat, center_lng, 'extended', 10)
            }
    
    def _get_zone_locations(self, lat: float, lng: float, radius_m: int, zone_type: str) -> List[Dict]:
        """Get POI locations for a specific zone using multiple data sources"""
        cache_key = f"poi_locations:{lat:.4f}:{lng:.4f}:{radius_m}:{zone_type}"
        cached_locations = cache.get(cache_key)
        
        if cached_locations is not None:
            return cached_locations
        
        locations = []
        
        # Try Google Places API first
        if self.google_api_key:
            google_locations = self._query_google_places(lat, lng, radius_m, zone_type)
            locations.extend(google_locations)
        
        # If we don't have enough locations, try OpenStreetMap
        if len(locations) < 5:
            osm_locations = self._query_openstreetmap(lat, lng, radius_m, zone_type)
            locations.extend(osm_locations)
        
        # Remove duplicates and limit results
        locations = self._deduplicate_locations(locations)[:15]
        
        # Cache for 24 hours
        cache.set(cache_key, locations, 60 * 60 * 24)
        
        return locations
    
    def _query_google_places(self, lat: float, lng: float, radius_m: int, zone_type: str) -> List[Dict]:
        """Query Google Places API for POIs"""
        if not self.google_api_key:
            logger.warning("Google Places API key not configured")
            return []

        try:
            place_types = self.GOOGLE_PLACES_TYPES.get(zone_type, ['establishment'])
            locations = []

            for place_type in place_types:
                url = "https://maps.googleapis.com/maps/api/place/nearbysearch/json"
                params = {
                    'location': f"{lat},{lng}",
                    'radius': radius_m,
                    'type': place_type,
                    'key': self.google_api_key,
                    'fields': 'place_id,name,geometry,types,rating,price_level'
                }

                response = requests.get(url, params=params, timeout=15)
                response.raise_for_status()

                data = response.json()

                if data.get('status') == 'OK':
                    for place in data.get('results', []):
                        # Filter out low-quality places
                        if place.get('rating', 0) < 3.0:
                            continue

                        location = {
                            'lat': place['geometry']['location']['lat'],
                            'lng': place['geometry']['location']['lng'],
                            'name': place.get('name', 'Unknown Location'),
                            'type': self._categorize_location_type(place.get('types', [])),
                            'source': 'google_places',
                            'place_id': place.get('place_id'),
                            'rating': place.get('rating'),
                            'price_level': place.get('price_level'),
                            'types': place.get('types', [])
                        }
                        locations.append(location)
                elif data.get('status') == 'ZERO_RESULTS':
                    logger.debug(f"No {place_type} places found near {lat},{lng}")
                elif data.get('status') == 'OVER_QUERY_LIMIT':
                    logger.error("Google Places API quota exceeded")
                    break
                else:
                    logger.warning(f"Google Places API returned status: {data.get('status')}")

                # Limit API calls and respect rate limits
                if len(locations) >= 15:
                    break

                # Small delay to respect rate limits
                time.sleep(0.1)

            logger.info(f"Found {len(locations)} places from Google Places API")
            return locations

        except requests.exceptions.RequestException as e:
            logger.error(f"Network error querying Google Places API: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Error querying Google Places API: {str(e)}")
            return []
    
    def _query_openstreetmap(self, lat: float, lng: float, radius_m: int, zone_type: str) -> List[Dict]:
        """Query OpenStreetMap Overpass API as fallback POI source"""
        try:
            amenity_types = self.OSM_AMENITY_TYPES.get(zone_type, ['cafe'])
            
            # Build Overpass query
            amenity_filter = '|'.join([f'amenity={amenity}' for amenity in amenity_types])
            query = f"""
            [out:json][timeout:10];
            (
              node[{amenity_filter}](around:{radius_m},{lat},{lng});
              way[{amenity_filter}](around:{radius_m},{lat},{lng});
            );
            out center;
            """
            
            url = "https://overpass-api.de/api/interpreter"
            response = requests.post(url, data=query, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                locations = []
                
                for element in data.get('elements', []):
                    if element['type'] == 'node':
                        location_lat, location_lng = element['lat'], element['lon']
                    elif element['type'] == 'way' and 'center' in element:
                        location_lat, location_lng = element['center']['lat'], element['center']['lon']
                    else:
                        continue
                    
                    location = {
                        'lat': location_lat,
                        'lng': location_lng,
                        'name': element.get('tags', {}).get('name', 'Local Spot'),
                        'type': self._categorize_osm_amenity(element.get('tags', {}).get('amenity')),
                        'source': 'openstreetmap',
                        'osm_id': element.get('id')
                    }
                    locations.append(location)
                
                return locations[:10]  # Limit results
            
        except Exception as e:
            logger.error(f"Error querying OpenStreetMap: {str(e)}")
        
        return []
    
    def _generate_fallback_locations(self, center_lat: float, center_lng: float,
                                   zone_type: str, count: int) -> List[Dict]:
        """Generate geometric distribution when POI APIs fail"""
        locations = []

        # Import seeding service constants for consistent distance parameters
        from seeding.services.seeding_service import SeedingService

        # Define radius ranges for each zone using updated distances
        radius_ranges = {
            'immediate': (200, SeedingService.IMMEDIATE_ZONE_RADIUS_M),    # 200m to 2km
            'nearby': (SeedingService.IMMEDIATE_ZONE_RADIUS_M, SeedingService.NEARBY_ZONE_RADIUS_M),     # 2km to 8km
            'city': (SeedingService.NEARBY_ZONE_RADIUS_M, SeedingService.CITY_ZONE_RADIUS_M),       # 8km to 20km
            'extended': (SeedingService.CITY_ZONE_RADIUS_M, SeedingService.EXTENDED_ZONE_RADIUS_M)  # 20km to 30km
        }
        
        min_radius, max_radius = radius_ranges.get(zone_type, (100, 1000))
        
        # Ensure min_radius respects the minimum distance from user
        if min_radius < self.MIN_USER_DISTANCE_M:
            logger.info(f"Adjusting minimum radius for {zone_type} zone from {min_radius}m to {self.MIN_USER_DISTANCE_M}m to respect minimum user distance")
            min_radius = self.MIN_USER_DISTANCE_M
        
        for i in range(count):
            # Generate random point within radius range, always at least MIN_USER_DISTANCE_M away
            angle = random.uniform(0, 2 * math.pi)
            radius = random.uniform(min_radius, max_radius)
            
            # Convert to lat/lng offset
            lat_offset = (radius * math.cos(angle)) / 111000  # ~111km per degree
            lng_offset = (radius * math.sin(angle)) / (111000 * math.cos(math.radians(center_lat)))
            
            location = {
                'lat': center_lat + lat_offset,
                'lng': center_lng + lng_offset,
                'name': f"Discovery Spot {i+1}",
                'type': self._get_fallback_location_type(zone_type),
                'source': 'generated',
                'generated': True
            }
            locations.append(location)
        
        return locations
    
    def _categorize_location_type(self, google_types: List[str]) -> str:
        """Categorize Google Places types into our location categories"""
        for category, types in self.LOCATION_CATEGORIES.items():
            if any(gtype in types for gtype in google_types):
                return category
        return 'landmark'  # Default category
    
    def _categorize_osm_amenity(self, amenity: str) -> str:
        """Categorize OSM amenity types into our location categories"""
        if not amenity:
            return 'landmark'
        
        for category, types in self.LOCATION_CATEGORIES.items():
            if amenity in types:
                return category
        return 'landmark'  # Default category
    
    def _get_fallback_location_type(self, zone_type: str) -> str:
        """Get appropriate location type for fallback locations"""
        type_mapping = {
            'immediate': 'cafe',
            'nearby': 'campus', 
            'city': 'landmark'
        }
        return type_mapping.get(zone_type, 'landmark')
    
    def _deduplicate_locations(self, locations: List[Dict]) -> List[Dict]:
        """Remove duplicate locations based on proximity"""
        if not locations:
            return []
        
        unique_locations = []
        min_distance = 50  # Minimum 50 meters between locations
        
        for location in locations:
            is_duplicate = False
            for existing in unique_locations:
                distance = self._calculate_distance(
                    location['lat'], location['lng'],
                    existing['lat'], existing['lng']
                )
                if distance < min_distance:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_locations.append(location)
        
        return unique_locations
    
    def _calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """Calculate distance between two points in meters"""
        # Haversine formula
        R = 6371000  # Earth's radius in meters
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lng = math.radians(lng2 - lng1)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lng / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def determine_city_from_coordinates(self, lat: float, lng: float) -> Optional[str]:
        """Reverse geocode to determine city for music profile lookup"""
        cache_key = f"city_name:{lat:.3f}:{lng:.3f}"
        cached_city = cache.get(cache_key)
        
        if cached_city is not None:
            return cached_city
        
        try:
            if self.google_api_key:
                url = "https://maps.googleapis.com/maps/api/geocode/json"
                params = {
                    'latlng': f"{lat},{lng}",
                    'key': self.google_api_key,
                    'result_type': 'locality'
                }
                
                response = requests.get(url, params=params, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('results'):
                        for component in data['results'][0].get('address_components', []):
                            if 'locality' in component.get('types', []):
                                city_name = component['long_name']
                                # Cache for 24 hours
                                cache.set(cache_key, city_name, 60 * 60 * 24)
                                return city_name
            
        except Exception as e:
            logger.error(f"Error determining city from coordinates: {str(e)}")
        
        # Cache None result for shorter time
        cache.set(cache_key, None, 60 * 60)
        return None

    def determine_location_from_coordinates(self, lat: float, lng: float) -> Optional[Dict[str, str]]:
        """Determine city and country from coordinates using reverse geocoding"""
        cache_key = f"location_info:{lat:.3f}:{lng:.3f}"
        cached_location = cache.get(cache_key)

        if cached_location is not None:
            return cached_location

        try:
            if self.google_api_key:
                url = "https://maps.googleapis.com/maps/api/geocode/json"
                params = {
                    'latlng': f"{lat},{lng}",
                    'key': self.google_api_key,
                    'result_type': 'country|locality|administrative_area_level_1'
                }

                response = requests.get(url, params=params, timeout=10)
                response.raise_for_status()

                data = response.json()
                if data.get('status') == 'OK' and data.get('results'):
                    location_info = self._parse_google_geocoding_result(data['results'])
                    logger.info(f"Reverse geocoding for ({lat}, {lng}): {location_info}")
                    # Cache for 24 hours
                    cache.set(cache_key, location_info, 60 * 60 * 24)
                    return location_info
                else:
                    logger.warning(f"Google Geocoding API returned status: {data.get('status')}")

        except Exception as e:
            logger.error(f"Error determining location from coordinates: {str(e)}")

        # Cache None result for shorter time
        cache.set(cache_key, None, 60 * 60)
        return None

    def _parse_google_geocoding_result(self, results: List[Dict]) -> Dict[str, str]:
        """Parse Google Geocoding API results to extract city and country"""
        location_info = {'city': None, 'country': None}

        # Process all results to find the best city and country information
        for result in results:
            address_components = result.get('address_components', [])

            for component in address_components:
                types = component.get('types', [])
                long_name = component.get('long_name', '')

                # Extract country (highest priority)
                if 'country' in types and not location_info['country']:
                    location_info['country'] = long_name
                    logger.debug(f"Found country: {long_name}")

                # Extract city (locality preferred, then administrative areas)
                if not location_info['city']:
                    if 'locality' in types:
                        location_info['city'] = long_name
                        logger.debug(f"Found city (locality): {long_name}")
                    elif 'administrative_area_level_2' in types:
                        location_info['city'] = long_name
                        logger.debug(f"Found city (admin_level_2): {long_name}")
                    elif 'administrative_area_level_1' in types:
                        location_info['city'] = long_name
                        logger.debug(f"Found city (admin_level_1): {long_name}")

            # If we have both city and country, we can stop
            if location_info['city'] and location_info['country']:
                break

        logger.info(f"Parsed location: {location_info}")
        return location_info
