"""
Personalized seeding service that creates seed pins based on user's top artists.

This service analyzes user's top_artists and creates 100% personalized seed pins
using tracks from those artists via Last.fm API, ensuring users get content
from their favorite artists rather than generic popular music.
"""

import logging
import random
from typing import List, Dict, Optional, Any, Tuple
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db.models import F

from seeding.models import SeedContentDatabase, CuratorAccount
from seeding.services.content_service import ContentService

User = get_user_model()
logger = logging.getLogger(__name__)


class PersonalizedSeedingService:
    """
    Service for creating personalized seed pins based on user's top artists.

    Implements personalization strategy:
    - 100% personalized content based on user's top_artists only
    - Uses Last.fm API to get tracks from user's favorite artists
    - Fallback to general seeding if no top artists available
    """
    
    def __init__(self):
        self.content_service = ContentService()
    
    def generate_personalized_seed_pins(
        self,
        user: User,
        locations: List[Dict],
        city_profile: Dict,
        lat: float,
        lng: float,
        target_count: int = 18
    ) -> List[Dict]:
        """
        Generate personalized seed pins with fresh API content and regional intelligence.

        Args:
            user: User requesting pins
            locations: List of location dictionaries for pin placement
            city_profile: City music profile for regional context
            lat: Latitude for regional content
            lng: Longitude for regional content
            target_count: Target number of pins to generate

        Returns:
            List of personalized seed pin data
        """
        try:
            # Get user preferences
            user_preferences = self._get_user_preferences(user)

            if not user_preferences['has_preferences']:
                logger.info(f"No preferences found for user {user.username}, using general seeding")
                return self._generate_general_seed_pins(locations, city_profile, target_count)

            # For personalized-only seeding: 100% personalized content from user's top artists
            personalized_count = target_count
            regional_count = 0
            discovery_count = 0

            logger.info(
                f"Generating personalized-only content for {user.username}: "
                f"{personalized_count} personalized tracks from top artists"
            )

            # Get fresh personalized content from user's top artists
            all_fresh_content = self._get_fresh_personalized_content(
                user_preferences, city_profile, lat, lng, target_count
            )

            # Get personalized content
            personalized_content = all_fresh_content.get('personalized', [])

            if not personalized_content:
                logger.warning("No personalized content available from user's top artists, falling back to database")
                return self._generate_fallback_pins(user_preferences, locations, city_profile, target_count)

            # Generate pins from personalized content only
            all_pins = []

            # Create personalized pins from user's top artists
            if personalized_content:
                # Use all available personalized content up to target count
                content_to_use = personalized_content[:target_count]
                locations_to_use = locations[:len(content_to_use)]

                personalized_pins = self._create_pins_from_content(
                    content_to_use,
                    locations_to_use,
                    is_personalized=True
                )
                all_pins.extend(personalized_pins)

                logger.info(f"Created {len(personalized_pins)} personalized pins from user's top artists")

            # Shuffle for natural distribution
            random.shuffle(all_pins)

            logger.info(f"Generated {len(all_pins)} personalized seed pins for user {user.username}")
            return all_pins

        except Exception as e:
            logger.error(f"Error generating personalized seed pins: {str(e)}")
            return self._generate_general_seed_pins(locations, city_profile, target_count)
    
    def _get_user_preferences(self, user: User) -> Dict[str, Any]:
        """Extract and analyze user music preferences - focusing only on top artists"""
        preferences = {
            'has_preferences': False,
            'top_artists': [],
            'user': user  # Add user object for ContentService
        }

        # Get user's top artists - this is the only personalization we use
        if user.top_artists:
            preferences['top_artists'] = user.top_artists  # Keep original case for API calls
            preferences['has_preferences'] = True

        logger.info(
            f"User {user.username} preferences: "
            f"{len(preferences['top_artists'])} top artists: {preferences['top_artists']}"
        )

        return preferences

    def _get_fresh_personalized_content(
        self,
        user_preferences: Dict,
        city_profile: Dict,
        lat: float,
        lng: float,
        target_count: int
    ) -> Dict[str, List[Dict]]:
        """Get fresh content from APIs - 100% personalized from user's top artists"""
        try:
            from .api_integrations import LastFmAPIClient

            # Get user object from preferences
            user = user_preferences.get('user')
            top_artists = user_preferences.get('top_artists', [])

            if not top_artists:
                logger.warning("No top artists found for personalized seeding")
                return {'personalized': [], 'regional': [], 'discovery': []}

            # Initialize Last.fm client
            lastfm_client = LastFmAPIClient()

            # Get 100% personalized tracks from user's top artists
            logger.info(f"🎵 Getting {target_count} personalized tracks from user's top artists: {top_artists}")

            # Use ContentService method to get personalized tracks from artists
            content_service = ContentService()
            personalized_tracks = content_service._get_personalized_tracks_from_artists(
                lastfm_client, top_artists, target_count
            )

            if personalized_tracks:
                # Enhance tracks with Spotify metadata
                enhanced_tracks = content_service._enhance_tracks_with_spotify(personalized_tracks)

                logger.info(f"✅ Got {len(enhanced_tracks)} personalized tracks from user's top artists")

                return {
                    'personalized': enhanced_tracks,
                    'regional': [],
                    'discovery': []  # No discovery content in personalized-only seeding
                }
            else:
                logger.warning("No personalized tracks available from user's top artists")
                return {'personalized': [], 'regional': [], 'discovery': []}

        except Exception as e:
            logger.error(f"Error getting fresh personalized content: {str(e)}")
            return {'personalized': [], 'regional': [], 'discovery': []}



    def _create_pins_from_content(
        self,
        content_list: List[Dict],
        locations: List[Dict],
        is_personalized: bool = False,
        is_regional: bool = False
    ) -> List[Dict]:
        """Create pin data from content and locations"""
        pins = []

        for i, content in enumerate(content_list):
            if i >= len(locations):
                break

            location = locations[i]
            curator = self._select_curator_for_content(content)

            tags = ['seed']
            if is_personalized:
                tags.append('personalized')
            if is_regional:
                tags.append('regional')

            pin_data = {
                'content': content,
                'location': location,
                'curator': curator,
                'tags': tags,
                'is_personalized': is_personalized,
                'is_regional': is_regional
            }
            pins.append(pin_data)

        return pins

    def _generate_fallback_pins(
        self,
        user_preferences: Dict,
        locations: List[Dict],
        city_profile: Dict,
        target_count: int
    ) -> List[Dict]:
        """Fallback to general seeding when personalized content fails"""
        try:
            logger.warning("Personalized content failed, falling back to general seeding")

            # If personalized seeding fails, fall back to general seeding
            return self._generate_general_seed_pins(locations, city_profile, target_count)

        except Exception as e:
            logger.error(f"Error in fallback pin generation: {str(e)}")
            return []


    

    

    
    def _select_curator_for_content(self, content: Dict) -> Optional[CuratorAccount]:
        """Select curator randomly with proper rotation (no genre matching)"""
        try:
            # Get all active curators and select randomly with rotation
            all_curators = CuratorAccount.objects.filter(is_active=True)

            if all_curators.exists():
                # Select curator with least recent usage for rotation
                # Use nulls_first to prioritize curators who have never been used
                return all_curators.order_by(F('last_used').asc(nulls_first=True)).first()

            return None

        except Exception as e:
            logger.error(f"Error selecting curator: {str(e)}")
            return None
    
    def _create_pin_data(
        self, 
        content: Dict, 
        location: Dict, 
        curator: Optional[CuratorAccount],
        is_personalized: bool = False
    ) -> Dict:
        """Create pin data dictionary"""
        tags = ['seed']
        if is_personalized:
            tags.append('personalized')
        
        return {
            'content': content,
            'location': location,
            'curator': curator,
            'tags': tags,
            'is_personalized': is_personalized
        }
    
    def _generate_general_seed_pins(
        self,
        locations: List[Dict],
        city_profile: Dict,
        target_count: int
    ) -> List[Dict]:
        """Fallback to general seeding when no user preferences"""
        try:
            from .content_service import ContentService

            # Use the ContentService to get fresh content with Spotify enhancement
            content_service = ContentService()

            # Get fresh popular content with Spotify enhancement (no user for personalization)
            # Use default NYC coordinates if not available in city_profile
            lat = 40.7128  # Default to NYC
            lng = -74.006  # Default to NYC

            fresh_tracks = content_service.get_fresh_popular_content(
                lat=lat,
                lng=lng,
                target_count=target_count,
                user=None  # No user for general seeding
            )

            if fresh_tracks:
                logger.info(f"Got {len(fresh_tracks)} fresh tracks for general seeding with Spotify enhancement")
                # Create pin data from the enhanced tracks
                return self._create_pins_from_content(
                    fresh_tracks[:target_count],
                    locations[:len(fresh_tracks)],
                    is_personalized=False,
                    is_regional=False
                )
            else:
                logger.warning("No fresh content available for general seeding")
                return []

        except Exception as e:
            logger.error(f"Error in general seed pin generation: {str(e)}")
            return []
    
    def get_personalization_stats(self, user: User) -> Dict[str, Any]:
        """Get personalization statistics for a user"""
        preferences = self._get_user_preferences(user)

        return {
            'has_preferences': preferences['has_preferences'],
            'top_artists_count': len(preferences['top_artists']),
            'personalization_enabled': preferences['has_preferences']
        }
