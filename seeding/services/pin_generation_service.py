"""
Pin generation service for converting seed content into Pin objects.

This service handles:
- Converting seed content to Pin objects with proper metadata
- Curator account selection and rotation for seed pin ownership
- Contextual description generation using location-specific templates
- Ensuring seed pins are properly tagged and have expiration dates
- Assigning appropriate pin skins to seeded pins
"""

import logging
import random
import math
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Optional, List
from django.contrib.gis.geos import Point
from django.utils import timezone
from django.conf import settings
from django.db.models import F

from pins.models import Pin, PinSkin
from seeding.models import CuratorAccount
from users.models import User
from seeding.services.user_generation_service import UserGenerationService
from seeding.services.caption_generation_service import CaptionGenerationService

logger = logging.getLogger(__name__)


class PinGenerationService:
    """
    Service for generating Pin objects from seed content.
    
    Handles the conversion of seed content into realistic Pin objects
    with appropriate curator ownership, contextual descriptions, and
    proper metadata for the seeding system.
    """
    
    # Pin expiration settings
    SEED_PIN_EXPIRATION_DAYS = 60  # Seed pins expire after 60 days
    
    # Spatial offset settings to prevent pin overlap
    MIN_OFFSET_METERS = 5  # Minimum offset in meters
    MAX_OFFSET_METERS = 15  # Maximum offset in meters
    
    # Description templates for different location types and moods
    DESCRIPTION_TEMPLATES = {
        'cafe': {
            'chill': [
                "Perfect coffee shop vibes for this chill track",
                "Discovered this gem during a quiet afternoon here",
                "The perfect soundtrack for your morning coffee",
                "This track captures the cozy atmosphere perfectly"
            ],
            'focus': [
                "Great study music for this spot",
                "Perfect focus track for getting work done here",
                "This helps me concentrate during long study sessions",
                "Ideal background music for productivity"
            ],
            'happy': [
                "This upbeat track brightens up the whole cafe",
                "Can't help but smile when this plays here",
                "Perfect feel-good music for this welcoming space",
                "This track matches the positive energy here"
            ]
        },
        'park': {
            'chill': [
                "Peaceful vibes for a relaxing moment in nature",
                "This track complements the tranquil park setting",
                "Perfect for unwinding among the trees",
                "Nature and music in perfect harmony"
            ],
            'romantic': [
                "Beautiful track for a romantic walk in the park",
                "This song makes the sunset even more magical",
                "Perfect for sharing a quiet moment together",
                "Love this track for peaceful park strolls"
            ],
            'happy': [
                "Uplifting music for a beautiful day outdoors",
                "This track captures the joy of being in nature",
                "Perfect soundtrack for park adventures",
                "Feel-good vibes for outdoor relaxation"
            ]
        },
        'venue': {
            'energetic': [
                "This track was made for live music venues like this",
                "Perfect energy for this amazing venue",
                "Can imagine this pumping through the sound system",
                "This place deserves high-energy music like this"
            ],
            'party': [
                "Party anthem for this legendary venue",
                "This track gets the crowd moving every time",
                "Perfect for the electric atmosphere here",
                "Dance floor energy captured in this song"
            ]
        },
        'campus': {
            'focus': [
                "Study session anthem discovered at the library",
                "Perfect concentration music for academic work",
                "This track helps me power through assignments",
                "Great background music for learning"
            ],
            'energetic': [
                "Campus energy captured in this upbeat track",
                "Perfect for walking between classes",
                "This track motivates me during busy school days",
                "Great energy for campus life"
            ]
        },
        'landmark': {
            'happy': [
                "This track captures the excitement of discovering this place",
                "Perfect soundtrack for exploring local landmarks",
                "Music that matches the wonder of this location",
                "Great vibes for sightseeing and exploration"
            ],
            'romantic': [
                "Beautiful music for this romantic location",
                "This track makes the view even more breathtaking",
                "Perfect for creating special memories here",
                "Romantic vibes for this stunning spot"
            ]
        },
        'transit': {
            'energetic': [
                "Perfect commute music for busy travelers",
                "This track makes the journey more enjoyable",
                "Great energy for people on the move",
                "Upbeat vibes for transit adventures"
            ],
            'workout': [
                "Motivational music for active commuters",
                "Perfect energy for walking to your destination",
                "This track keeps you moving",
                "Great rhythm for staying active"
            ]
        }
    }
    
    def __init__(self):
        """Initialize the pin generation service"""
        self._available_skins_cache = None
        self.cache = {}
        # Track pin positions per POI to avoid overlaps
        self.pin_positions = {}
        self.caption_service = CaptionGenerationService()
    
    def calculate_distance_meters(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """
        Calculate distance between two coordinates in meters using the haversine formula.
        
        Args:
            lat1: Latitude of first point
            lng1: Longitude of first point
            lat2: Latitude of second point
            lng2: Longitude of second point
            
        Returns:
            Distance in meters
        """
        # Convert decimal degrees to radians
        lat1, lng1, lat2, lng2 = map(math.radians, [lat1, lng1, lat2, lng2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlng = lng2 - lng1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlng/2)**2
        c = 2 * math.asin(math.sqrt(a))
        # Radius of earth in meters
        r = 6371000
        return c * r
    
    def _apply_spatial_offset(self, lat: float, lng: float, location_name: str = None) -> tuple[float, float]:
        """
        Apply a spatial offset to coordinates to prevent pin overlap.
        
        Args:
            lat: Original latitude
            lng: Original longitude
            location_name: Name of the POI for tracking positions
            
        Returns:
            tuple: (offset_lat, offset_lng) with random offset applied and minimum distance enforced
        """
        # Initialize location tracking if not exists
        if location_name not in self.pin_positions:
            self.pin_positions[location_name] = []
        
        # Maximum attempts to find a suitable position
        max_attempts = 20
        attempts = 0
        
        while attempts < max_attempts:
            # Generate a random offset
            offset_distance = random.uniform(self.MIN_OFFSET_METERS, self.MAX_OFFSET_METERS)
            direction = random.uniform(0, 2 * math.pi)
            lat_offset = (offset_distance * math.cos(direction)) / 111000
            lng_offset = (offset_distance * math.sin(direction)) / (111000 * math.cos(math.radians(lat)))
            
            # Calculate new position
            offset_lat = lat + lat_offset
            offset_lng = lng + lng_offset
            
            # Check if position is valid (at least MIN_OFFSET_METERS from all existing pins)
            valid_position = True
            
            for existing_lat, existing_lng in self.pin_positions.get(location_name, []):
                distance = self.calculate_distance_meters(
                    offset_lat, offset_lng, existing_lat, existing_lng
                )
                
                if distance < self.MIN_OFFSET_METERS:
                    valid_position = False
                    break
            
            if valid_position:
                # Add position to tracked positions for this location
                self.pin_positions[location_name].append((offset_lat, offset_lng))
                return offset_lat, offset_lng
            
            attempts += 1
        
        # If we couldn't find a valid position after max attempts, use larger offset
        offset_distance = self.MAX_OFFSET_METERS * 2
        direction = random.uniform(0, 2 * math.pi)
        lat_offset = (offset_distance * math.cos(direction)) / 111000
        lng_offset = (offset_distance * math.sin(direction)) / (111000 * math.cos(math.radians(lat)))
        offset_lat = lat + lat_offset
        offset_lng = lng + lng_offset
        
        # Track this position
        self.pin_positions[location_name].append((offset_lat, offset_lng))
        return offset_lat, offset_lng
        # Earth radius in meters
        R = 6371000
        
        # Convert to radians
        lat1_rad = math.radians(lat1)
        lng1_rad = math.radians(lng1)
        lat2_rad = math.radians(lat2)
        lng2_rad = math.radians(lng2)
        
        # Haversine formula
        dlat = lat2_rad - lat1_rad
        dlng = lng2_rad - lng1_rad
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlng/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = R * c
        
        return distance
        
    def verify_pin_constraints(self, pins: List[Pin], user_lat: float = None, user_lng: float = None) -> Dict:
        """
        Test utility to verify spatial constraints for a list of pins:
        1. Pins at the same POI have spatial offset (5-15m)
        2. All pins are at least 250m away from user (if user coordinates provided)
        
        Args:
            pins: List of Pin objects to verify
            user_lat: User latitude (optional)
            user_lng: User longitude (optional)
            
        Returns:
            Dict with verification results
        """
        # Import LocationService for access to MIN_USER_DISTANCE_M constant
        from seeding.services.location_service import LocationService
        
        results = {
            "total_pins": len(pins),
            "overlapping_pins": 0,
            "pins_too_close_to_user": 0,
            "valid_pins": 0,
            "nearest_pin_to_user": None,
            "pin_distances": []
        }
        
        # Check for overlapping pins (less than MIN_OFFSET_METERS apart)
        for i, pin1 in enumerate(pins):
            valid_pin = True
            
            # Get lat/lng from pin location
            pin1_lat = pin1.location.y
            pin1_lng = pin1.location.x
            
            # Check distance from user if coordinates provided
            if user_lat is not None and user_lng is not None:
                distance_to_user = self.calculate_distance_meters(
                    user_lat, user_lng, pin1_lat, pin1_lng
                )
                
                # Track the nearest pin to user
                if results["nearest_pin_to_user"] is None or distance_to_user < results["nearest_pin_to_user"]:
                    results["nearest_pin_to_user"] = distance_to_user
                
                # Check if pin is too close to user
                if distance_to_user < LocationService.MIN_USER_DISTANCE_M:
                    results["pins_too_close_to_user"] += 1
                    valid_pin = False
            
            # Check distance to other pins
            for j, pin2 in enumerate(pins):
                if i == j:  # Skip comparing pin to itself
                    continue
                    
                pin2_lat = pin2.location.y
                pin2_lng = pin2.location.x
                
                distance = self.calculate_distance_meters(
                    pin1_lat, pin1_lng, pin2_lat, pin2_lng
                )
                
                # Track all pin distances for analysis
                results["pin_distances"].append(distance)
                
                # Check if pins are overlapping (closer than minimum offset)
                if distance < self.MIN_OFFSET_METERS:
                    results["overlapping_pins"] += 1
                    valid_pin = False
                    break
            
            if valid_pin:
                results["valid_pins"] += 1
        
        # Calculate average and minimum distance between pins if we have distances
        if results["pin_distances"]:
            results["avg_pin_distance"] = sum(results["pin_distances"]) / len(results["pin_distances"])
            results["min_pin_distance"] = min(results["pin_distances"]) if results["pin_distances"] else None
        
        return results

    def _generate_caption(self, track_title: str, track_artist: str,
                         location_name: str = None, location_type: str = None,
                         curator_persona: str = None) -> str:
        """
        Generate a human-like caption for a music pin using AI or fallback templates.

        Args:
            track_title: The song title
            track_artist: The artist name
            location_name: Name of the location
            location_type: Type of location (cafe, park, venue, etc.)
            curator_persona: Curator's persona type

        Returns:
            Generated caption string
        """
        try:
            return self.caption_service.generate_caption(
                track_title=track_title,
                track_artist=track_artist,
                location_name=location_name,
                location_type=location_type,
                curator_persona=curator_persona,
                is_seeding=True  # This is always called during seeding
            )
        except Exception as e:
            logger.error(f"Error generating caption: {str(e)}")
            # Fallback to simple template
            location_text = f"at {location_name}" if location_name else "at this spot"
            return f"Found this gem {location_text}! 🎵"

    def _get_appropriate_pin_skin(self) -> Optional[PinSkin]:
        """
        Get an appropriate pin skin for seeded pins.

        Returns a mix of default and available skins to provide visual variety
        while ensuring most pins use the default skin that all users have.
        """
        try:
            # Cache available skins to avoid repeated database queries
            if self._available_skins_cache is None:
                # Get available skins (default + some common unlockable ones)
                available_skins = PinSkin.objects.filter(
                    skin_type=PinSkin.HOUSE,
                    is_premium=False
                ).exclude(
                    # Exclude limited-time and special skins
                    metadata__unlock_type__in=['LIMITED_TIME', 'SPECIAL_EVENT', 'SECRET']
                ).order_by('created_at')

                self._available_skins_cache = list(available_skins)

            if not self._available_skins_cache:
                # Fallback to default skin if no skins available
                default_skin, _ = PinSkin.objects.get_or_create(
                    slug='default-pin',
                    defaults={
                        'name': 'Default',
                        'description': 'The standard pin for all BOPMaps users',
                        'image': 'https://bopmaps-prod-media-67ba6151.s3.us-east-005.backblazeb2.com/pin_skins/default.png',
                        'skin_type': PinSkin.HOUSE,
                        'is_premium': False,
                        'metadata': {
                            'unlock_type': 'DEFAULT',
                            'category': 'base',
                            'theme_color': '#4A90E2',
                            'color_theme': 'blue',
                            'rarity': 'common'
                        }
                    }
                )
                return default_skin

            # Use weighted selection: 70% default, 30% other available skins
            if random.random() < 0.7:
                # Try to get default skin
                default_skin = next((skin for skin in self._available_skins_cache if skin.slug == 'default-pin'), None)
                if default_skin:
                    return default_skin

            # Return a random available skin
            return random.choice(self._available_skins_cache)

        except Exception as e:
            logger.error(f"Error getting pin skin: {str(e)}")
            # Return None to use the model's default (ID 1)
            return None

    def create_seed_pin(self, location: Dict, content: Dict[str, List[Dict]], zone_type: str) -> Optional[Pin]:
        """
        Create a seed pin from location and content data.
        
        Args:
            location: Location dictionary with lat, lng, name, type
            content: Content organized by mood/genre
            zone_type: Zone type (immediate, nearby, city)
            
        Returns:
            Created Pin object or None if creation failed
        """
        try:
            # Get appropriate content for this location type
            from seeding.services.content_service import ContentService
            content_service = ContentService()
            
            suitable_tracks = content_service.get_content_for_location_type(
                content, location.get('type', 'landmark')
            )
            
            if not suitable_tracks:
                logger.warning(f"No suitable tracks found for location type: {location.get('type')}")
                return None
            
            # Select a random track
            track = random.choice(suitable_tracks)
            
            # Get or create curator account
            curator = self._get_curator_for_location(location, track)
            if not curator:
                logger.error("No curator account available for seed pin")
                return None
            
            # Generate contextual description
            description = self._generate_contextual_description(
                track, location.get('type', 'landmark'), location.get('name', 'Unknown Location')
            )

            # Generate AI-powered caption
            caption = self._generate_caption(
                track_title=track['title'],
                track_artist=track['artist'],
                location_name=location.get('name'),
                location_type=location.get('type', 'landmark'),
                curator_persona=curator.persona_type if hasattr(curator, 'persona_type') else None
            )

            # Get appropriate pin skin
            pin_skin = self._get_appropriate_pin_skin()
            
            # Apply spatial offset to prevent overlap
            offset_lat, offset_lng = self._apply_spatial_offset(location['lat'], location['lng'])

            # Create the pin
            pin_data = {
                'owner': curator.user,
                'location': Point(offset_lng, offset_lat, srid=4326),
                'location_name': location.get('name', 'Discovery Spot'),
                'title': f"{track['title']}",
                'description': description,
                'caption': caption,

                # Music data
                'track_title': track['title'],
                'track_artist': track['artist'],
                'album': track.get('album', ''),
                'track_url': track['track_url'],
                'service': track.get('service', 'spotify'),
                'artwork_url': track.get('artwork_url'),
                'duration_ms': track.get('duration_ms'),

                # Enhanced discovery fields
                'genre': track.get('genre'),
                'mood': track.get('mood'),
                'tags': self._get_pin_tags(track, zone_type, location.get('type', 'landmark')),

                # Discovery settings
                'aura_radius': random.randint(50, 100),  # Varied aura radius 50-100m
                'is_private': False,
                'expiration_date': timezone.now() + timedelta(days=self.SEED_PIN_EXPIRATION_DAYS),
            }

            # Add pin skin if available
            if pin_skin:
                pin_data['skin'] = pin_skin

            pin = Pin.objects.create(**pin_data)
            
            # Update curator usage
            curator.update_usage()
            
            logger.info(f"Created seed pin: {pin.title} at {location.get('name')} by {curator.user.username}")
            return pin
            
        except Exception as e:
            logger.error(f"Error creating seed pin: {str(e)}")
            return None

    def _get_pin_tags(self, track: Dict, zone_type: str, location_type: str) -> List[str]:
        """Generate appropriate tags for a pin based on track and location"""
        tags = ['seed', zone_type, location_type]

        # Add personalized tag if this is a personalized track
        if track.get('is_personalized', False):
            tags.append('personalized')

        return tags

    def create_seed_pin_with_dedup(self, location: Dict, content: Dict, zone_type: str, used_tracks: set) -> Optional[Pin]:
        """Create a seed pin with track deduplication"""
        try:
            # Select content that hasn't been used
            selected_content = self._select_unused_content(content, used_tracks)
            if not selected_content:
                # Fallback to any content if all tracks are used, but still avoid session duplicates
                selected_content = self._select_content_with_session_dedup(location, content, zone_type, used_tracks)

            # Select appropriate curator
            curator = self._select_curator_for_content(selected_content)

            # Generate AI-powered caption
            caption = self._generate_caption(
                track_title=selected_content['title'],
                track_artist=selected_content['artist'],
                location_name=location.get('name'),
                location_type=location.get('type', 'landmark'),
                curator_persona=curator.persona_type if hasattr(curator, 'persona_type') else None
            )

            # Get appropriate pin skin
            pin_skin = self._get_appropriate_pin_skin()
            
            # Apply spatial offset to prevent overlap
            offset_lat, offset_lng = self._apply_spatial_offset(location['lat'], location['lng'])

            # Create the pin
            pin_data = {
                'owner': curator.user,
                'location': Point(offset_lng, offset_lat, srid=4326),
                'location_name': location.get('name', 'Unknown Location'),
                'title': f"{selected_content['title']}",
                'description': f"Great music discovered at {location.get('name', 'this location')}!",
                'caption': caption,
                'track_title': selected_content['title'],
                'track_artist': selected_content['artist'],
                'track_url': selected_content.get('track_url', ''),
                'artwork_url': selected_content.get('artwork_url', ''),
                'duration_ms': selected_content.get('duration_ms', 0),
                'service': selected_content.get('service', 'spotify'),
                'genre': selected_content.get('genre', 'pop'),
                'mood': selected_content.get('mood', 'happy'),
                'tags': ['seed', zone_type],
                'aura_radius': random.randint(50, 100),  # Varied aura radius 50-100m
                'expiration_date': timezone.now() + timedelta(days=self.SEED_PIN_EXPIRATION_DAYS)
            }

            # Add pin skin if available
            if pin_skin:
                pin_data['skin'] = pin_skin

            pin = Pin.objects.create(**pin_data)

            # Update curator usage
            curator.update_usage()

            logger.debug(f"Created seed pin: {pin.track_title} by {pin.track_artist} at {location.get('name')} by {curator.user.username}")
            return pin

        except Exception as e:
            logger.error(f"Error creating seed pin with dedup: {str(e)}")
            return None

    def _select_unused_content(self, content: Dict, used_tracks: set) -> Optional[Dict]:
        """Select content that hasn't been used yet"""
        import random

        # Get all available tracks
        all_tracks = []
        if isinstance(content, dict):
            for category, tracks in content.items():
                if isinstance(tracks, list):
                    all_tracks.extend(tracks)
        elif isinstance(content, list):
            all_tracks = content

        # Filter out used tracks
        unused_tracks = []
        for track in all_tracks:
            track_key = f"{track.get('artist', '')}:{track.get('title', '')}"
            if track_key not in used_tracks:
                unused_tracks.append(track)
            else:
                logger.debug(f"Skipping used track: {track_key}")

        # Return random unused track
        logger.debug(f"Used tracks: {len(used_tracks)}, Available tracks: {len(all_tracks)}, Unused tracks: {len(unused_tracks)}")

        if unused_tracks:
            selected = random.choice(unused_tracks)
            logger.debug(f"Selected unused track: {selected.get('artist', '')}:{selected.get('title', '')}")
            return selected

        logger.warning("No unused tracks available, falling back to any track")
        return None

    def _select_content_with_session_dedup(self, location: Dict, content: Dict, zone_type: str, used_tracks: set) -> Optional[Dict]:
        """Select content with session-level deduplication even when all tracks are 'used' from existing pins"""
        import random

        # Get all available tracks
        all_tracks = []
        if isinstance(content, dict):
            for category, tracks in content.items():
                if isinstance(tracks, list):
                    all_tracks.extend(tracks)
        elif isinstance(content, list):
            all_tracks = content

        if not all_tracks:
            return None

        # Separate tracks that exist in database vs tracks used in this session
        session_used_tracks = set()
        for track_key in used_tracks:
            # Check if this track was used in current session (not just existing in DB)
            # We can identify session tracks by checking if they're in the current session's used_tracks
            # For now, we'll use a simpler approach: prefer tracks that haven't been used recently
            session_used_tracks.add(track_key)

        # Try to find tracks that haven't been used in this session
        session_unused_tracks = []
        for track in all_tracks:
            track_key = f"{track.get('artist', '')}:{track.get('title', '')}"
            # Count how many times this track appears in used_tracks (session usage)
            if track_key not in session_used_tracks:
                session_unused_tracks.append(track)

        # If we have session-unused tracks, prefer them
        if session_unused_tracks:
            logger.debug(f"Found {len(session_unused_tracks)} session-unused tracks for fallback")
            return random.choice(session_unused_tracks)

        # Last resort: return any track (this should rarely happen)
        logger.warning("All tracks used in session, selecting any track as last resort")
        return random.choice(all_tracks)

    def create_seed_pin_with_enhanced_dedup(self, location: Dict, content: Dict, zone_type: str,
                                          existing_tracks: set, session_used_tracks: set) -> Optional[Pin]:
        """Create a seed pin with enhanced deduplication (database + session level)"""
        try:
            # First priority: Select content not in database and not used in session
            selected_content = self._select_content_with_enhanced_dedup(content, existing_tracks, session_used_tracks)

            if not selected_content:
                logger.warning("No available tracks after enhanced deduplication")
                return None

            # Select appropriate curator
            curator = self._select_curator_for_content(selected_content)

            # Generate AI-powered caption
            caption = self._generate_caption(
                track_title=selected_content['title'],
                track_artist=selected_content['artist'],
                location_name=location.get('name'),
                location_type=location.get('type', 'landmark'),
                curator_persona=curator.persona_type if hasattr(curator, 'persona_type') else None
            )

            # Get appropriate pin skin
            pin_skin = self._get_appropriate_pin_skin()
            
            # Apply spatial offset to prevent overlap
            offset_lat, offset_lng = self._apply_spatial_offset(location['lat'], location['lng'])

            # Create the pin
            pin_tags = self._get_pin_tags(selected_content, zone_type, 'music_discovery')

            pin_data = {
                'owner': curator.user,
                'location': Point(offset_lng, offset_lat, srid=4326),
                'location_name': location.get('name', 'Unknown Location'),
                'title': f"{selected_content['title']}",
                'description': f"Great music discovered at {location.get('name', 'this location')}!",
                'caption': caption,
                'track_title': selected_content['title'],
                'track_artist': selected_content['artist'],
                'track_url': selected_content.get('track_url', ''),
                'artwork_url': selected_content.get('artwork_url'),
                'service': selected_content.get('service', 'lastfm'),
                'genre': selected_content.get('genre', 'pop'),
                'mood': selected_content.get('mood', 'happy'),
                'tags': pin_tags,
                'aura_radius': random.randint(50, 100),  # Varied aura radius 50-100m
                'expiration_date': timezone.now() + timedelta(days=self.SEED_PIN_EXPIRATION_DAYS)
            }

            # Add pin skin if available
            if pin_skin:
                pin_data['skin'] = pin_skin

            pin = Pin.objects.create(**pin_data)

            # Update curator usage
            curator.update_usage()

            logger.debug(f"Created seed pin with enhanced dedup: {selected_content['title']} by {selected_content['artist']} at {location.get('name')} by {curator.user.username}")
            return pin

        except Exception as e:
            logger.error(f"Error creating seed pin with enhanced dedup: {str(e)}")
            return None

    def create_pin_from_data(self, pin_data: Dict) -> Optional[Pin]:
        """
        Create a Pin object from personalized pin data dictionary.

        This method is used specifically for creating pins from personalized
        seeding data that comes from the PersonalizedSeedingService.

        Args:
            pin_data: Dictionary containing pin data with content, location, curator, etc.

        Returns:
            Created Pin object or None if creation failed
        """
        try:
            content = pin_data.get('content', {})
            location = pin_data.get('location', {})
            curator = pin_data.get('curator')
            tags = pin_data.get('tags', ['seed'])

            if not content or not location:
                logger.error("Missing content or location in pin data")
                return None

            if not curator or not hasattr(curator, 'user'):
                logger.error("Invalid curator in pin data")
                return None

            # Get appropriate pin skin
            pin_skin = self._get_appropriate_pin_skin()

            # Create contextual description
            description = self._generate_contextual_description(
                content,
                location.get('type', 'landmark'),
                location.get('name', 'Unknown Location')
            )

            # Generate AI-powered caption
            caption = self._generate_caption(
                track_title=content['title'],
                track_artist=content['artist'],
                location_name=location.get('name'),
                location_type=location.get('type', 'landmark'),
                curator_persona=curator.persona_type if hasattr(curator, 'persona_type') else None
            )

            # Apply spatial offset to prevent overlap
            offset_lat, offset_lng = self._apply_spatial_offset(location['lat'], location['lng'])
            
            # Create the pin
            pin_create_data = {
                'owner': curator.user,
                'location': Point(offset_lng, offset_lat, srid=4326),
                'location_name': location.get('name', 'Discovery Spot'),
                'title': f"{content['title']}",
                'description': description,
                'caption': caption,

                # Music data
                'track_title': content['title'],
                'track_artist': content['artist'],
                'album': content.get('album', ''),
                'track_url': content.get('track_url', ''),
                'service': content.get('service', 'spotify'),
                'artwork_url': content.get('artwork_url'),
                'duration_ms': content.get('duration_ms'),

                # Enhanced discovery fields
                'genre': content.get('genre'),
                'mood': content.get('mood'),
                'tags': tags,

                # Discovery settings
                'aura_radius': random.randint(50, 100),  # Varied aura radius 50-100m
                'is_private': False,
                'expiration_date': timezone.now() + timedelta(days=self.SEED_PIN_EXPIRATION_DAYS),
            }

            # Add pin skin if available
            if pin_skin:
                pin_create_data['skin'] = pin_skin

            pin = Pin.objects.create(**pin_create_data)

            # Update curator usage
            curator.update_usage()

            logger.info(f"Created personalized pin: {pin.title} at {location.get('name')} by {curator.user.username}")
            return pin

        except Exception as e:
            logger.error(f"Error creating pin from data: {str(e)}")
            return None

    def _select_content_with_enhanced_dedup(self, content: Dict, existing_tracks: set, session_used_tracks: set) -> Optional[Dict]:
        """Select content avoiding both database duplicates and session duplicates"""
        import random

        # Debug: Log content structure
        logger.debug(f"Content type: {type(content)}")
        if isinstance(content, dict):
            logger.debug(f"Content keys: {list(content.keys())}")

        # Get all available tracks
        all_tracks = []
        if isinstance(content, dict):
            # Handle both fresh content structure and database structure
            if 'tracks' in content and isinstance(content['tracks'], list):
                all_tracks = content['tracks']
                logger.debug(f"Using fresh tracks: {len(all_tracks)} tracks")
            else:
                for category, tracks in content.items():
                    if isinstance(tracks, list):
                        all_tracks.extend(tracks)
                        logger.debug(f"Added {len(tracks)} tracks from category '{category}'")
        elif isinstance(content, list):
            all_tracks = content

        logger.debug(f"Total tracks available: {len(all_tracks)}")
        logger.debug(f"Existing tracks to avoid: {len(existing_tracks)}")
        logger.debug(f"Session tracks to avoid: {len(session_used_tracks)}")

        if not all_tracks:
            logger.warning("No tracks available in content")
            return None

        # Priority 1: Tracks not in database and not used in session
        fresh_tracks = []
        for track in all_tracks:
            track_key = f"{track.get('artist', '')}:{track.get('title', '')}"
            if track_key not in existing_tracks and track_key not in session_used_tracks:
                fresh_tracks.append(track)

        if fresh_tracks:
            # Prioritize personalized tracks to reach 25% target
            personalized_fresh = [track for track in fresh_tracks if track.get('is_personalized', False)]
            popular_fresh = [track for track in fresh_tracks if not track.get('is_personalized', False)]

            # Use weighted selection: 60% chance for personalized tracks (to reach 25% target)
            if personalized_fresh and random.random() < 0.6:
                selected = random.choice(personalized_fresh)
                logger.debug(f"Selected personalized fresh track: {selected.get('artist', '')}:{selected.get('title', '')} (personalized)")
            else:
                # Select from all fresh tracks if no personalized or random choice favors popular
                selected = random.choice(fresh_tracks)
                is_personalized = selected.get('is_personalized', False)
                logger.debug(f"Selected fresh track: {selected.get('artist', '')}:{selected.get('title', '')} ({'personalized' if is_personalized else 'popular'})")

            return selected

        # Priority 2: Tracks not used in session (even if in database)
        session_fresh_tracks = []
        for track in all_tracks:
            track_key = f"{track.get('artist', '')}:{track.get('title', '')}"
            if track_key not in session_used_tracks:
                session_fresh_tracks.append(track)

        if session_fresh_tracks:
            # Prioritize personalized tracks in session-fresh selection too
            personalized_session_fresh = [track for track in session_fresh_tracks if track.get('is_personalized', False)]

            # Use weighted selection: 60% chance for personalized tracks
            if personalized_session_fresh and random.random() < 0.6:
                selected = random.choice(personalized_session_fresh)
                logger.debug(f"Selected personalized session-fresh track: {selected.get('artist', '')}:{selected.get('title', '')} (personalized)")
            else:
                selected = random.choice(session_fresh_tracks)
                is_personalized = selected.get('is_personalized', False)
                logger.debug(f"Selected session-fresh track: {selected.get('artist', '')}:{selected.get('title', '')} ({'personalized' if is_personalized else 'popular'})")

            return selected

        # Priority 3: Any track (last resort)
        logger.warning("No fresh tracks available, selecting any track")
        return random.choice(all_tracks)

    def _select_content_for_location(self, location: Dict, content: Dict, zone_type: str) -> Optional[Dict]:
        """Select appropriate content for a location"""
        import random

        # Get all available tracks
        all_tracks = []
        if isinstance(content, dict):
            for category, tracks in content.items():
                if isinstance(tracks, list):
                    all_tracks.extend(tracks)
        elif isinstance(content, list):
            all_tracks = content

        # Return random track if available
        if all_tracks:
            return random.choice(all_tracks)

        return None

    def _select_curator_for_content(self, content: Dict) -> Optional[CuratorAccount]:
        """Select curator randomly with proper rotation (no genre matching)"""
        try:
            # Get all active curators and select randomly with rotation
            all_curators = CuratorAccount.objects.filter(is_active=True)

            if all_curators.exists():
                # Select curator with least recent usage for rotation
                # Use nulls_first to prioritize curators who have never been used
                return all_curators.order_by(F('last_used').asc(nulls_first=True)).first()

            # Create default curator if none exist
            return self._create_default_curator()

        except Exception as e:
            logger.error(f"Error selecting curator: {str(e)}")
            return self._create_default_curator()

    def _get_curator_for_location(self, location: Dict, track: Dict) -> Optional[CuratorAccount]:
        """Get curator account randomly with proper rotation (no location/genre matching)"""
        try:
            # Get all active curators and select randomly with rotation
            all_curators = CuratorAccount.objects.filter(is_active=True)

            if all_curators.exists():
                # Select curator with least recent usage for rotation
                # Use nulls_first to prioritize curators who have never been used
                return all_curators.order_by(F('last_used').asc(nulls_first=True)).first()

            # Create a default curator if none exist
            return self._create_default_curator()

        except Exception as e:
            logger.error(f"Error getting curator for location: {str(e)}")
            return None
    
    def _create_default_curator(self) -> Optional[CuratorAccount]:
        """Create a default curator account if none exist using diverse generation"""
        try:
            # Initialize user generation service
            user_gen_service = UserGenerationService()

            # Generate diverse user profile for default curator
            user_profile = user_gen_service.generate_user_profile(
                persona_type='city_wanderer',
                preferred_genres=['pop', 'rock', 'indie', 'electronic'],
                preferred_locations=['cafe', 'park', 'venue', 'landmark'],
                assigned_cities=['general']
            )

            # Ensure username uniqueness
            existing_usernames = set(User.objects.values_list('username', flat=True))
            unique_username = user_gen_service.ensure_username_uniqueness(
                user_profile['username'],
                existing_usernames
            )

            # Create default curator user with diverse profile
            curator_user = User.objects.create_user(
                username=unique_username,
                email=user_profile['email'],
                first_name=user_profile['first_name'],
                last_name=user_profile['last_name'],
                bio=user_profile['bio'],
                profile_pic=user_profile['profile_pic']
            )

            # Create curator profile
            curator = CuratorAccount.objects.create(
                user=curator_user,
                persona_type='city_wanderer',
                preferred_genres=['pop', 'rock', 'indie', 'electronic'],
                preferred_locations=['cafe', 'park', 'venue', 'landmark']
            )

            logger.info(f"Created diverse default curator: {curator_user.username} ({curator_user.first_name} {curator_user.last_name})")
            return curator

        except Exception as e:
            logger.error(f"Error creating default curator: {str(e)}")
            return None
    
    def _generate_contextual_description(self, track: Dict, location_type: str, location_name: str) -> str:
        """Generate location-specific descriptions for authentic discovery feel"""
        try:
            track_mood = track.get('mood', 'happy')
            
            # Get templates for this location type and mood
            location_templates = self.DESCRIPTION_TEMPLATES.get(location_type, {})
            mood_templates = location_templates.get(track_mood, [])
            
            # If no specific templates, use generic ones
            if not mood_templates:
                mood_templates = [
                    f"Great {track_mood} vibes for this location",
                    f"Perfect {track_mood} track discovered here",
                    f"This {track_mood} song fits the atmosphere perfectly",
                    f"Love the {track_mood} energy of this track"
                ]
            
            # Select random template
            template = random.choice(mood_templates)
            
            # Add some variation
            variations = [
                template,
                f"{template}",
                f"🎶 {template}",
                f"{template} Perfect for {location_name}!",
            ]
            
            return random.choice(variations)
            
        except Exception as e:
            logger.error(f"Error generating contextual description: {str(e)}")
            return f"Great music discovered at {location_name}!"
    
    def create_curator_accounts(self, count: int = 10) -> List[CuratorAccount]:
        """Create multiple curator accounts with diverse personas using improved generation"""
        created_curators = []
        user_gen_service = UserGenerationService()

        persona_configs = [
            {
                'persona_type': 'indie_explorer',
                'preferred_genres': ['indie', 'alternative', 'folk'],
                'preferred_locations': ['cafe', 'venue', 'campus']
            },
            {
                'persona_type': 'coffee_enthusiast',
                'preferred_genres': ['chill', 'jazz', 'acoustic'],
                'preferred_locations': ['cafe', 'park']
            },
            {
                'persona_type': 'venue_hopper',
                'preferred_genres': ['rock', 'electronic', 'hip-hop'],
                'preferred_locations': ['venue', 'landmark']
            },
            {
                'persona_type': 'campus_curator',
                'preferred_genres': ['focus', 'indie', 'electronic'],
                'preferred_locations': ['campus', 'cafe']
            },
            {
                'persona_type': 'city_wanderer',
                'preferred_genres': ['pop', 'hip-hop', 'electronic'],
                'preferred_locations': ['landmark', 'transit', 'venue']
            }
        ]

        # Get existing usernames to ensure uniqueness
        existing_usernames = set(User.objects.values_list('username', flat=True))

        for config in persona_configs[:count]:
            try:
                # Generate diverse user profile
                user_profile = user_gen_service.generate_user_profile(
                    persona_type=config['persona_type'],
                    preferred_genres=config['preferred_genres'],
                    preferred_locations=config['preferred_locations']
                )

                # Ensure username uniqueness
                unique_username = user_gen_service.ensure_username_uniqueness(
                    user_profile['username'],
                    existing_usernames
                )
                existing_usernames.add(unique_username)

                # Create user with generated profile
                curator_user = User.objects.create_user(
                    username=unique_username,
                    email=user_profile['email'],
                    first_name=user_profile['first_name'],
                    last_name=user_profile['last_name'],
                    bio=user_profile['bio'],
                    profile_pic=user_profile['profile_pic']
                )

                # Create curator profile
                curator = CuratorAccount.objects.create(
                    user=curator_user,
                    persona_type=config['persona_type'],
                    preferred_genres=config['preferred_genres'],
                    preferred_locations=config['preferred_locations']
                )

                created_curators.append(curator)
                logger.info(f"Created diverse curator: {curator_user.username} ({curator_user.first_name} {curator_user.last_name})")

            except Exception as e:
                logger.error(f"Error creating curator with persona {config['persona_type']}: {str(e)}")
                continue

        return created_curators
