"""
Profile picture service for curator accounts.

This service manages profile pictures for curator accounts, providing
appropriate aesthetic images that match curator personas without using
real faces. Uses placeholder services and can be extended with real
image storage.
"""

import logging
import random
import hashlib
import uuid
from typing import Dict, List, Optional
from django.conf import settings
from django.core.files.base import ContentFile
import requests

logger = logging.getLogger(__name__)


class ProfilePictureService:
    """
    Service for managing curator profile pictures with aesthetic,
    non-face images that match curator personas.
    """
    
    # Profile picture categories and their characteristics
    PICTURE_CATEGORIES = {
        'abstract_art': {
            'description': 'Abstract artistic patterns and designs',
            'colors': ['vibrant', 'pastel', 'monochrome', 'gradient'],
            'suitable_personas': ['indie_explorer', 'genre_specialist', 'music_historian']
        },
        'music_themed': {
            'description': 'Music instruments, vinyl records, studio equipment',
            'colors': ['warm', 'vintage', 'modern'],
            'suitable_personas': ['venue_hopper', 'music_historian', 'genre_specialist']
        },
        'nature': {
            'description': 'Natural landscapes, plants, sky, water',
            'colors': ['earth_tones', 'green', 'blue', 'sunset'],
            'suitable_personas': ['coffee_enthusiast', 'city_wanderer', 'local_guide']
        },
        'urban_photography': {
            'description': 'City scenes, architecture, street art, neon',
            'colors': ['urban', 'neon', 'black_white', 'golden_hour'],
            'suitable_personas': ['city_wanderer', 'venue_hopper', 'campus_curator']
        },
        'minimalist': {
            'description': 'Clean, simple designs with geometric shapes',
            'colors': ['monochrome', 'pastel', 'single_color'],
            'suitable_personas': ['campus_curator', 'coffee_enthusiast', 'indie_explorer']
        },
        'vintage': {
            'description': 'Retro aesthetics, old textures, vintage objects',
            'colors': ['sepia', 'vintage', 'warm'],
            'suitable_personas': ['music_historian', 'local_guide', 'coffee_enthusiast']
        }
    }
    
    # Persona to category mapping
    PERSONA_CATEGORY_MAPPING = {
        'indie_explorer': ['abstract_art', 'minimalist', 'urban_photography'],
        'coffee_enthusiast': ['nature', 'minimalist', 'vintage'],
        'venue_hopper': ['music_themed', 'urban_photography', 'abstract_art'],
        'campus_curator': ['minimalist', 'urban_photography', 'nature'],
        'city_wanderer': ['urban_photography', 'nature', 'abstract_art'],
        'music_historian': ['vintage', 'music_themed', 'abstract_art'],
        'genre_specialist': ['abstract_art', 'music_themed', 'minimalist'],
        'local_guide': ['nature', 'vintage', 'urban_photography']
    }
    
    # Placeholder image services (royalty-free)
    PLACEHOLDER_SERVICES = {
        'unsplash': {
            'base_url': 'https://source.unsplash.com',
            'categories': {
                'abstract_art': ['abstract', 'art', 'pattern'],
                'music_themed': ['music', 'vinyl', 'instrument'],
                'nature': ['nature', 'landscape', 'plant'],
                'urban_photography': ['city', 'architecture', 'urban'],
                'minimalist': ['minimal', 'geometric', 'simple'],
                'vintage': ['vintage', 'retro', 'old']
            }
        },
        'picsum': {
            'base_url': 'https://picsum.photos',
            'blur_option': True,
            'grayscale_option': True
        }
    }
    
    def __init__(self):
        self.image_size = getattr(settings, 'CURATOR_PROFILE_PIC_SIZE', 400)
    
    def generate_pravatar_url(self, unique_identifier: str) -> str:
        """
        Generate a unique avatar URL using pravatar.cc.

        Args:
            unique_identifier: Unique identifier for consistent avatar generation

        Returns:
            Avatar URL from pravatar.cc
        """
        # Create a hash of the identifier to ensure consistency
        hash_object = hashlib.md5(unique_identifier.encode())
        hash_hex = hash_object.hexdigest()

        # Use the hash as the unique parameter for i.pravatar.cc
        avatar_url = f"https://i.pravatar.cc/150?u={hash_hex}"

        return avatar_url

    def assign_profile_picture(self, curator_account) -> Optional[str]:
        """
        Assign an appropriate profile picture to a curator account using pravatar.cc.

        Args:
            curator_account: CuratorAccount instance

        Returns:
            URL of the assigned profile picture or None if failed
        """
        try:
            # Generate a unique identifier for this curator
            unique_id = f"{curator_account.user.username}_{curator_account.persona_type}_{curator_account.id}"

            # Generate profile picture URL using pravatar.cc
            picture_url = self.generate_pravatar_url(unique_id)

            # Update the user's profile picture
            curator_account.user.profile_pic = picture_url
            curator_account.user.save(update_fields=['profile_pic'])

            logger.info(
                f"Assigned pravatar.cc profile picture to curator {curator_account.user.username}: {picture_url}"
            )

            return picture_url

        except Exception as e:
            logger.error(f"Error assigning profile picture to curator {curator_account.user.username}: {str(e)}")

        return None
    
    def _generate_picture_url(self, category: str) -> Optional[str]:
        """Generate a profile picture URL for the given category"""
        try:
            # Use Unsplash as primary source
            unsplash_categories = self.PLACEHOLDER_SERVICES['unsplash']['categories']
            
            if category in unsplash_categories:
                # Select random keyword from category
                keywords = unsplash_categories[category]
                keyword = random.choice(keywords)
                
                # Generate Unsplash URL
                url = f"{self.PLACEHOLDER_SERVICES['unsplash']['base_url']}/{self.image_size}x{self.image_size}/?{keyword}"
                
                # Add some randomization to avoid duplicate images
                seed = random.randint(1, 1000)
                url += f"&sig={seed}"
                
                return url
            
            # Fallback to Picsum with blur effect for aesthetic look
            picsum_url = f"{self.PLACEHOLDER_SERVICES['picsum']['base_url']}/{self.image_size}/{self.image_size}"
            
            # Add blur for artistic effect
            picsum_url += "?blur=2"
            
            # Add randomization
            seed = random.randint(1, 1000)
            picsum_url += f"&random={seed}"
            
            return picsum_url
            
        except Exception as e:
            logger.error(f"Error generating picture URL for category {category}: {str(e)}")
            return None
    
    def rotate_profile_pictures(self, curator_accounts: List) -> int:
        """
        Rotate profile pictures for a list of curator accounts to maintain freshness.
        
        Args:
            curator_accounts: List of CuratorAccount instances
            
        Returns:
            Number of profile pictures successfully rotated
        """
        rotated_count = 0
        
        for curator in curator_accounts:
            try:
                # Only rotate if curator has been used recently
                if curator.last_used and curator.pins_created > 5:
                    new_url = self.assign_profile_picture(curator)
                    if new_url:
                        rotated_count += 1
                        
            except Exception as e:
                logger.error(f"Error rotating profile picture for {curator.user.username}: {str(e)}")
                continue
        
        logger.info(f"Rotated {rotated_count} curator profile pictures")
        return rotated_count
    
    def get_category_for_persona(self, persona_type: str) -> str:
        """Get the most suitable picture category for a persona type"""
        suitable_categories = self.PERSONA_CATEGORY_MAPPING.get(persona_type, ['abstract_art'])
        return suitable_categories[0]  # Return primary category
    
    def validate_picture_url(self, url: str) -> bool:
        """
        Validate that a profile picture URL is accessible.
        
        Args:
            url: Profile picture URL to validate
            
        Returns:
            True if URL is accessible, False otherwise
        """
        try:
            response = requests.head(url, timeout=10)
            return response.status_code == 200
        except Exception as e:
            logger.debug(f"Profile picture URL validation failed for {url}: {str(e)}")
            return False
    
    def get_picture_info(self, category: str) -> Dict:
        """Get information about a picture category"""
        return self.PICTURE_CATEGORIES.get(category, {})
    
    def list_available_categories(self) -> List[str]:
        """List all available picture categories"""
        return list(self.PICTURE_CATEGORIES.keys())
    
    def get_persona_recommendations(self, persona_type: str) -> List[Dict]:
        """
        Get recommended picture categories for a persona type with details.
        
        Args:
            persona_type: Curator persona type
            
        Returns:
            List of recommended categories with details
        """
        suitable_categories = self.PERSONA_CATEGORY_MAPPING.get(persona_type, ['abstract_art'])
        
        recommendations = []
        for category in suitable_categories:
            category_info = self.PICTURE_CATEGORIES.get(category, {})
            recommendations.append({
                'category': category,
                'description': category_info.get('description', ''),
                'colors': category_info.get('colors', []),
                'sample_url': self._generate_picture_url(category)
            })
        
        return recommendations


# Utility functions for management commands
def assign_pictures_to_all_curators():
    """Assign profile pictures to all curator accounts that don't have them"""
    from seeding.models import CuratorAccount
    
    service = ProfilePictureService()
    assigned_count = 0
    
    curators_without_pictures = CuratorAccount.objects.filter(
        user__profile_pic__isnull=True
    ).select_related('user')
    
    for curator in curators_without_pictures:
        if service.assign_profile_picture(curator):
            assigned_count += 1
    
    return assigned_count


def rotate_all_curator_pictures():
    """Rotate profile pictures for all active curators"""
    from seeding.models import CuratorAccount
    
    service = ProfilePictureService()
    active_curators = CuratorAccount.objects.filter(
        is_active=True
    ).select_related('user')
    
    return service.rotate_profile_pictures(list(active_curators))
