"""
Regional Music Service for culturally intelligent music discovery.

This service provides region-specific music content that matches local culture:
- J-pop and K-pop in Asia
- Afrobeats and Amapiano in Africa  
- Reggaeton and Latin music in Latin America
- Regional hip-hop and electronic scenes globally
"""

import logging
import random
from typing import List, Dict, Optional, Any
from django.core.cache import cache

from seeding.services.api_integrations import SpotifyAPIClient

logger = logging.getLogger(__name__)


class RegionalMusicService:
    """
    Service for generating culturally appropriate regional music content.
    
    Provides region-specific artists and genres based on geographic location
    and local music culture.
    """
    
    # Enhanced regional artist mapping with market codes and search strategies
    REGIONAL_ARTISTS = {
        # Asian Markets - J-pop, K-pop, C-pop
        'japan': {
            'artists': ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON>nu', '<PERSON><PERSON>', 'Official HIGE DANdism', 'Li<PERSON>', '<PERSON><PERSON>',
                       'Perfume', 'Hikaru <PERSON>tad<PERSON>', 'ONE OK ROCK', 'Babymetal', '<PERSON><PERSON><PERSON>',
                       'Mrs. GREEN APPLE', 'Vaundy', 'back number', 'Creepy Nuts', '<PERSON><PERSON>'],
            'genres': ['j-pop', 'j-rock', 'city pop', 'visual kei', 'shibuya-kei', 'anime'],
            'search_terms': ['j-pop', 'jpop', 'japanese pop', 'j-rock', 'jrock', 'city pop', 'anime music'],
            'market': 'JP',
            'playlist_queries': ['j-pop hits', 'japanese music', 'jpop playlist', 'city pop', 'anime songs'],
            'is_niche': True
        },
        'south_korea': {
            'artists': ['NewJeans', 'aespa', 'ITZY', 'Stray Kids', 'TWICE', 'BLACKPINK', 'BTS', 'IU',
                       '(G)I-DLE', 'Red Velvet', 'SEVENTEEN', 'ENHYPEN', 'LE SSERAFIM', 'NMIXX', 'IVE'],
            'genres': ['k-pop', 'k-hip-hop', 'k-r&b', 'k-indie', 'korean ballad'],
            'search_terms': ['k-pop', 'kpop', 'korean pop', 'k-hip-hop', 'korean music'],
            'market': 'KR',
            'playlist_queries': ['k-pop hits', 'kpop playlist', 'korean music', 'k-pop 2024'],
            'is_niche': True
        },
        
        # African Markets - Afrobeats, Amapiano, Regional styles
        'nigeria': {
            'artists': ['Burna Boy', 'Wizkid', 'Davido', 'Tems', 'Rema', 'Asake', 'Ayra Starr',
                       'Fireboy DML', 'Joeboy', 'Omah Lay', 'Tiwa Savage', 'Yemi Alade', 'Oxlade',
                       'Ckay', 'Kizz Daniel', 'Ruger', 'Zinoleesky', 'Seyi Vibez'],
            'genres': ['afrobeats', 'afropop', 'afro-fusion', 'nigerian hip-hop', 'highlife'],
            'search_terms': ['afrobeats', 'afro beats', 'nigerian music', 'afropop', 'afro fusion', 'naija music'],
            'market': 'NG',
            'playlist_queries': ['afrobeats hits', 'nigerian music', 'afrobeats 2024', 'naija hits'],
            'is_niche': True
        },
        'south_africa': {
            'artists': ['Tyla', 'Focalistic', 'Kabza De Small', 'DJ Maphorisa', 'Sha Sha', 'Master KG',
                       'Black Coffee', 'Nasty C', 'AKA', 'Cassper Nyovest', 'Sho Madjozi', 'Uncle Waffles',
                       'Major League DJz', 'DBN Gogo', 'Musa Keys', 'Kelvin Momo'],
            'genres': ['amapiano', 'afrobeats', 'south african house', 'gqom', 'kwaito'],
            'search_terms': ['amapiano', 'south african music', 'gqom', 'sa house', 'kwaito'],
            'market': 'ZA',
            'playlist_queries': ['amapiano hits', 'south african music', 'amapiano 2024', 'gqom'],
            'is_niche': True
        },
        'kenya': {
            'artists': ['Sauti Sol', 'Nyashinski', 'Khaligraph Jones', 'Bien', 'Otile Brown',
                       'Nadia Mukami', 'Bahati', 'Akothee', 'Willy Paul', 'Ethic Entertainment',
                       'Boondocks Gang', 'Sailors', 'Mejja', 'Kristoff'],
            'genres': ['afrobeats', 'gengetone', 'bongo flava', 'kenyan hip-hop', 'benga'],
            'search_terms': ['kenyan music', 'gengetone', 'bongo flava', 'east african music'],
            'market': 'KE',
            'playlist_queries': ['kenyan music', 'gengetone hits', 'east african music'],
            'is_niche': False
        },
        
        # Latin American Markets
        'mexico': {
            'artists': ['Bad Bunny', 'Peso Pluma', 'Karol G', 'Feid', 'Grupo Frontera', 'Eslabon Armado',
                       'Christian Nodal', 'Becky G', 'Rauw Alejandro', 'Myke Towers'],
            'genres': ['reggaeton', 'latin trap', 'regional mexican', 'corridos tumbados', 'latin pop'],
            'search_terms': ['reggaeton', 'latin music', 'musica latina', 'trap latino', 'corridos']
        },
        'brazil': {
            'artists': ['Anitta', 'Ludmilla', 'Pabllo Vittar', 'IZA', 'Luisa Sonza', 'Kevinho', 
                       'MC Hariel', 'Tropkillaz', 'Alok', 'Vintage Culture'],
            'genres': ['brazilian funk', 'sertanejo', 'bossa nova', 'mpb', 'pagode', 'forro'],
            'search_terms': ['brazilian music', 'funk brasileiro', 'sertanejo', 'musica brasileira']
        },
        'argentina': {
            'artists': ['Tini', 'Maria Becerra', 'Nicki Nicole', 'Duki', 'Bizarrap', 'Paulo Londra',
                       'Lali', 'Ca7riel', 'Trueno', 'Lit killah'],
            'genres': ['reggaeton', 'latin trap', 'cumbia', 'rock nacional', 'latin pop'],
            'search_terms': ['musica argentina', 'trap argentino', 'cumbia', 'rock nacional']
        },
        
        # European Markets
        'uk': {
            'artists': ['Central Cee', 'Dave', 'Little Simz', 'Stormzy', 'AJ Tracey', 'Skepta',
                       'Dua Lipa', 'Ed Sheeran', 'Harry Styles', 'Adele', 'Sam Smith'],
            'genres': ['grime', 'uk drill', 'uk garage', 'drum and bass', 'dubstep', 'british pop'],
            'search_terms': ['uk music', 'grime', 'uk drill', 'british music', 'uk garage']
        },
        'germany': {
            'artists': ['Robin Schulz', 'Zedd', 'Paul Kalkbrenner', 'Moderat', 'Apparat', 'Kraftwerk',
                       'Capital Bra', 'Apache 207', 'Bonez MC', 'RAF Camora'],
            'genres': ['techno', 'electronic', 'minimal techno', 'german hip-hop', 'krautrock'],
            'search_terms': ['german music', 'techno', 'deutsche musik', 'german electronic']
        },
        
        # Caribbean
        'jamaica': {
            'artists': ['Popcaan', 'Koffee', 'Chronixx', 'Protoje', 'Spice', 'Shenseea',
                       'Sean Paul', 'Shaggy', 'Damian Marley', 'Skip Marley'],
            'genres': ['reggae', 'dancehall', 'ska', 'dub', 'roots reggae'],
            'search_terms': ['reggae', 'dancehall', 'jamaican music', 'ska', 'dub music']
        },
        
        # Middle East & India
        'india': {
            'artists': ['Divine', 'Nucleya', 'Prateek Kuhad', 'Ritviz', 'Badshah', 'Honey Singh',
                       'A.R. Rahman', 'Shreya Ghoshal', 'Arijit Singh'],
            'genres': ['bollywood', 'indian hip-hop', 'bhangra', 'indian classical', 'punjabi pop'],
            'search_terms': ['bollywood music', 'indian music', 'bhangra', 'hindi music', 'punjabi music']
        }
    }
    
    # Geographic region mapping
    COUNTRY_REGIONS = {
        # Asia-Pacific
        'japan': 'japan',
        'south korea': 'south_korea', 
        'korea': 'south_korea',
        'china': 'china',
        'australia': 'australia',
        'new zealand': 'australia',
        
        # Africa
        'nigeria': 'nigeria',
        'south africa': 'south_africa',
        'kenya': 'kenya',
        'ghana': 'nigeria',  # Similar music scene
        'uganda': 'kenya',   # East African scene
        'tanzania': 'kenya',
        
        # Latin America
        'mexico': 'mexico',
        'brazil': 'brazil',
        'argentina': 'argentina',
        'colombia': 'mexico',  # Similar reggaeton scene
        'puerto rico': 'mexico',
        'chile': 'argentina',
        'peru': 'argentina',
        
        # Europe
        'united kingdom': 'uk',
        'uk': 'uk',
        'england': 'uk',
        'germany': 'germany',
        'france': 'germany',  # Similar electronic scene
        'netherlands': 'germany',
        
        # Caribbean
        'jamaica': 'jamaica',
        'trinidad and tobago': 'jamaica',
        'barbados': 'jamaica',
        
        # Middle East & South Asia
        'india': 'india',
        'pakistan': 'india',
        'bangladesh': 'india'
    }
    
    def __init__(self):
        self.spotify_client = SpotifyAPIClient()
    
    def get_regional_content(self, country: str, city: str = None, target_count: int = 30, offset: int = 0) -> List[Dict]:
        """
        Get regional music content for a specific country/city.
        
        Args:
            country: Country name (e.g., 'Japan', 'Nigeria', 'Brazil')
            city: City name for additional context
            target_count: Number of tracks to retrieve
            
        Returns:
            List of regional track dictionaries
        """
        # DISABLED: Regional content disabled - use Last.fm only
        return []
    
    def _collect_regional_tracks(self, regional_data: Dict, target_count: int) -> List[Dict]:
        """Enhanced regional track collection using multiple strategies"""
        tracks = []
        market = regional_data.get('market', 'US')
        is_niche = regional_data.get('is_niche', False)

        # Strategy 1: Playlist-based discovery (especially for niche genres)
        if is_niche:
            playlist_tracks = self._get_tracks_from_regional_playlists(regional_data, target_count // 2)
            tracks.extend(playlist_tracks)
            logger.info(f"Collected {len(playlist_tracks)} tracks from regional playlists")

        # Strategy 2: Artist-based collection
        artist_tracks = self._get_tracks_from_regional_artists(regional_data, target_count // 3, market)
        tracks.extend(artist_tracks)

        # Strategy 3: Genre search with market specificity
        if len(tracks) < target_count:
            search_tracks = self._get_tracks_from_genre_search(regional_data, target_count - len(tracks), market)
            tracks.extend(search_tracks)

        # Remove duplicates and return
        return self._deduplicate_tracks(tracks)[:target_count]

    def _get_tracks_from_regional_playlists(self, regional_data: Dict, target_count: int) -> List[Dict]:
        """Get tracks from region-specific playlists"""
        tracks = []
        playlist_queries = regional_data.get('playlist_queries', [])

        try:
            for query in playlist_queries[:3]:  # Use top 3 playlist queries
                # Search for playlists
                playlists_response = self.spotify_client._make_spotify_request(
                    'search',
                    params={
                        'q': query,
                        'type': 'playlist',
                        'limit': 5,
                        'market': regional_data.get('market', 'US')
                    }
                )

                if playlists_response and 'playlists' in playlists_response:
                    playlists = playlists_response['playlists']['items']

                    for playlist in playlists[:2]:  # Top 2 playlists per query
                        playlist_id = playlist.get('id')
                        if playlist_id:
                            playlist_tracks = self._get_playlist_tracks(playlist_id, 10)
                            tracks.extend(playlist_tracks)

                            if len(tracks) >= target_count:
                                break

                    if len(tracks) >= target_count:
                        break

        except Exception as e:
            logger.error(f"Error getting tracks from regional playlists: {str(e)}")

        return tracks[:target_count]

    def _get_tracks_from_regional_artists(self, regional_data: Dict, target_count: int, market: str) -> List[Dict]:
        """Get tracks from regional artists with market-specific search"""
        tracks = []
        artists = regional_data.get('artists', [])

        # Randomize artist order for variety
        import random
        shuffled_artists = artists.copy()
        random.shuffle(shuffled_artists)

        tracks_per_artist = max(2, target_count // len(shuffled_artists)) if shuffled_artists else 0

        for artist in shuffled_artists:
            if len(tracks) >= target_count:
                break

            # Use market-specific artist search
            artist_tracks = self.spotify_client.get_artist_top_tracks(artist, limit=tracks_per_artist)

            # Add market and regional metadata
            for track in artist_tracks:
                track['market'] = market
                track['regional'] = True
                track['region_source'] = 'artist'

            tracks.extend(artist_tracks)

        return tracks[:target_count]

    def _get_tracks_from_genre_search(self, regional_data: Dict, target_count: int, market: str) -> List[Dict]:
        """Get tracks using genre search terms with market specificity"""
        tracks = []
        search_terms = regional_data.get('search_terms', [])

        tracks_per_term = max(5, target_count // len(search_terms)) if search_terms else 0

        for search_term in search_terms:
            if len(tracks) >= target_count:
                break

            # Enhanced search with market parameter
            search_tracks = self.spotify_client.get_top_tracks_by_genre(search_term, limit=tracks_per_term)

            # Add regional metadata
            for track in search_tracks:
                track['market'] = market
                track['regional'] = True
                track['region_source'] = 'search'

            tracks.extend(search_tracks)

        return tracks[:target_count]

    def _get_playlist_tracks(self, playlist_id: str, limit: int) -> List[Dict]:
        """Get tracks from a specific playlist"""
        try:
            tracks_response = self.spotify_client._make_spotify_request(
                f'playlists/{playlist_id}/tracks',
                params={'limit': limit, 'market': 'US'}
            )

            if tracks_response and 'items' in tracks_response:
                tracks = []
                for item in tracks_response['items']:
                    track = item.get('track')
                    if track and track.get('id'):
                        formatted_track = self.spotify_client._format_spotify_track(track)
                        formatted_track['regional'] = True
                        formatted_track['region_source'] = 'playlist'
                        tracks.append(formatted_track)

                return tracks

        except Exception as e:
            logger.error(f"Error getting playlist tracks: {str(e)}")

        return []
    
    def _assign_regional_genre(self, track: Dict, regional_genres: List[str]) -> str:
        """Assign appropriate regional genre to a track"""
        # Use the most common regional genre as default
        if regional_genres:
            return regional_genres[0]
        return 'world'
    
    def _deduplicate_tracks(self, tracks: List[Dict]) -> List[Dict]:
        """Remove duplicate tracks based on title and artist"""
        seen = set()
        unique_tracks = []
        
        for track in tracks:
            track_key = f"{track.get('artist', '').lower()}:{track.get('title', '').lower()}"
            if track_key not in seen:
                seen.add(track_key)
                unique_tracks.append(track)
        
        return unique_tracks
    
    def is_regional_content_available(self, country: str) -> bool:
        """Check if regional content is available for a country"""
        country_key = country.lower().strip()
        region = self.COUNTRY_REGIONS.get(country_key)
        return region is not None and region in self.REGIONAL_ARTISTS
    
    def get_supported_regions(self) -> List[str]:
        """Get list of supported regions for regional content"""
        return list(self.REGIONAL_ARTISTS.keys())
