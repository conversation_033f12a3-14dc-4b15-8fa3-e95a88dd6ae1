"""
Main seeding service that orchestrates the entire music pin seeding process.

This service implements the core seeding logic including:
- Area density checking to determine when seeding is needed
- Exploration pattern seed generation following three-tier strategy
- Duplicate seeding prevention for same geographic areas
- Async seeding to avoid blocking user requests
"""

import logging
import math
import random
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from django.contrib.gis.geos import Point
from django.contrib.gis.measure import D
from django.db.models import Count, Q
from django.utils import timezone
from django.core.cache import cache

from pins.models import Pin
from seeding.models import SeedingArea, SeedingMetrics
from .location_service import LocationService
from .content_service import ContentService
from .pin_generation_service import PinGenerationService
from .personalized_seeding_service import PersonalizedSeedingService

logger = logging.getLogger(__name__)


class SeedingService:
    """
    Main orchestration service for music pin seeding system.
    
    Implements the exploration pattern strategy from the design document:
    - Immediate Discovery Zone (0-500m): 5-8 pins within walking distance
    - Nearby Exploration Zone (500m-2km): 5-10 pins for short exploration trips  
    - City Discovery Zone (2km+): 3-5 pins across broader city area
    """
    
    # Seeding thresholds and parameters
    MIN_ORGANIC_PINS_THRESHOLD = 8  # Trigger seeding if fewer than 8 organic pins in 10km
    SEEDING_RADIUS_KM = 10.0  # Check for organic pins within 10km radius
    DUPLICATE_PREVENTION_RADIUS_KM = 5.0  # Prevent duplicate seeding within 5km

    # Exploration zone configuration - significantly increased for city-wide distribution
    IMMEDIATE_ZONE_RADIUS_M = 2000   # Walking/cycling distance (2km)
    NEARBY_ZONE_RADIUS_M = 10000      # Short exploration trips (8km)
    CITY_ZONE_RADIUS_M = 30000       # Broader city exploration (20km)
    EXTENDED_ZONE_RADIUS_M = 40000   # Extended coverage area (30km)
    
    # Pin count targets per zone
    IMMEDIATE_ZONE_PIN_COUNT = (5, 8)  # 5-8 pins
    NEARBY_ZONE_PIN_COUNT = (5, 10)    # 5-10 pins
    CITY_ZONE_PIN_COUNT = (3, 5)       # 3-5 pins
    EXTENDED_ZONE_PIN_COUNT = (8, 12)  # 8-12 pins for extended coverage
    
    def __init__(self):
        self.location_service = LocationService()
        self.content_service = ContentService()
        self.pin_generation_service = PinGenerationService()
        self.personalized_service = PersonalizedSeedingService()
        # Import here to avoid circular imports
        from seeding.services.user_seeding_state_service import UserSeedingStateService
        self.user_state_service = UserSeedingStateService()
    
    def should_seed_area(self, lat: float, lng: float, radius_km: float = None) -> bool:
        """
        Check if an area needs seeding based on organic pin density.
        
        Args:
            lat: Latitude of the center point
            lng: Longitude of the center point
            radius_km: Search radius in kilometers (default: 5.0)
            
        Returns:
            Boolean indicating if seeding is needed
        """
        if radius_km is None:
            radius_km = self.SEEDING_RADIUS_KM
            
        try:
            center_point = Point(lng, lat, srid=4326)
            
            # Check for existing seeding in this area to prevent duplicates
            existing_seeding = SeedingArea.objects.filter(
                center_point__distance_lte=(center_point, D(km=self.DUPLICATE_PREVENTION_RADIUS_KM))
            ).exists()
            
            if existing_seeding:
                logger.info(f"Area already seeded within {self.DUPLICATE_PREVENTION_RADIUS_KM}km of ({lat}, {lng})")
                return False
            
            # Count organic pins (non-seed pins) in the area
            organic_pin_count = Pin.objects.filter(
                Q(location__distance_lte=(center_point, D(km=radius_km))) &
                (Q(expiration_date__isnull=True) | Q(expiration_date__gt=timezone.now())) &
                Q(is_private=False) &
                ~Q(tags__contains=['seed'])
            ).count()
            
            needs_seeding = organic_pin_count < self.MIN_ORGANIC_PINS_THRESHOLD
            
            logger.info(
                f"Area density check for ({lat}, {lng}): "
                f"{organic_pin_count} organic pins in {radius_km}km radius. "
                f"Seeding needed: {needs_seeding}"
            )
            
            return needs_seeding

        except Exception as e:
            logger.error(f"Error checking if area needs seeding: {str(e)}")
            return False

    def check_personalized_seeding_needed(self, user, lat: float, lng: float, search_radius_km: float = 5.0) -> dict:
        """
        Check if a user needs personalized seeding in a given area.

        This method implements the core personalized seeding logic:
        - If user has never been seeded AND enters a pre-seeded area → personalized seeding
        - If user has only personalized seeding AND enters unseeded area → full seeding
        - If user has full seeding → no additional seeding

        Args:
            user: User instance
            lat: Latitude of the area
            lng: Longitude of the area
            search_radius_km: User's search radius in kilometers (for overlap detection)

        Returns:
            Dict with seeding decision and metadata
        """
        try:
            logger.debug(f"[PERSONALIZED_SEEDING] Starting seeding check for user {getattr(user, 'username', 'anonymous')} at ({lat}, {lng})")

            if not user or not user.is_authenticated:
                logger.info(f"[PERSONALIZED_SEEDING] ✗ User not authenticated, skipping personalized seeding")
                return {
                    'should_seed': False,
                    'seeding_type': 'none',
                    'reason': 'User not authenticated'
                }

            # Check user's seeding eligibility for this area
            logger.debug(f"[PERSONALIZED_SEEDING] Checking area seeding eligibility for {user.username} with search radius {search_radius_km}km")
            eligibility = self.user_state_service.check_area_seeding_eligibility(user, lat, lng, search_radius_km)

            logger.info(
                f"[PERSONALIZED_SEEDING] 🎯 Final decision for {user.username} at ({lat}, {lng}): "
                f"Type: {eligibility['seeding_type']}, Should seed: {eligibility['should_seed']}, Reason: {eligibility['reason']}"
            )

            return eligibility

        except Exception as e:
            logger.error(f"[PERSONALIZED_SEEDING] ✗ Error checking personalized seeding for user {getattr(user, 'username', 'unknown')}: {str(e)}")
            return {
                'should_seed': False,
                'seeding_type': 'none',
                'reason': f'Error: {str(e)}'
            }
    
    def generate_exploration_seed_pins(self, lat: float, lng: float, user=None) -> List[Pin]:
        """
        Generate seed pins following the exploration pattern strategy.

        Creates pins across four exploration zones:
        1. Immediate Discovery Zone (0-2km): Quick satisfaction
        2. Nearby Exploration Zone (2km-8km): Short exploration trips
        3. City Discovery Zone (8km-20km): Longer exploration motivation
        4. Extended Coverage Zone (20km-30km): Maximum area coverage

        Args:
            lat: Latitude of the center point
            lng: Longitude of the center point
            user: Optional user for personalization (not used for public seeds)

        Returns:
            List of created Pin objects
        """
        try:
            center_point = Point(lng, lat, srid=4326)

            # Get location zones with POIs
            location_zones = self.location_service.get_seed_locations(lat, lng)

            # Get city profile for regional context
            city_profile = self.content_service._get_city_music_profile(lat, lng)

            # Check if user should get personalized seeding
            eligibility = self.check_personalized_seeding_needed(user, lat, lng)
            if eligibility['should_seed'] and eligibility['seeding_type'] == 'personalized':
                logger.info(f"Using personalized seeding for user {user.username}")

                # Flatten all locations for personalized service
                all_locations = (
                    location_zones.get('immediate', []) +
                    location_zones.get('nearby', []) +
                    location_zones.get('city', []) +
                    location_zones.get('extended', [])
                )

                # Generate personalized seed pins with regional intelligence
                personalized_pin_data = self.personalized_service.generate_personalized_seed_pins(
                    user=user,
                    locations=all_locations,
                    city_profile=city_profile,
                    lat=lat,
                    lng=lng,
                    target_count=28  # Increased to account for extended zone (8+10+5+12 = 35 max)
                )

                # Convert personalized pin data to actual Pin objects
                created_pins = []
                existing_tracks = self._get_existing_tracks_in_area(Point(lng, lat, srid=4326))
                session_used_tracks = set()

                for pin_data in personalized_pin_data:
                    # Create a content structure that the pin generation service expects
                    content_for_pin = {'tracks': [pin_data['content']]}

                    pin = self.pin_generation_service.create_seed_pin_with_enhanced_dedup(
                        location=pin_data['location'],
                        content=content_for_pin,
                        zone_type='personalized',
                        existing_tracks=existing_tracks,
                        session_used_tracks=session_used_tracks
                    )
                    if pin:
                        # Add personalized tags
                        pin.tags = pin_data.get('tags', ['seed'])
                        pin.save()
                        created_pins.append(pin)

            else:
                # Use general seeding approach
                logger.info("Using general seeding approach")

                # Get fresh content from APIs only - 75% popular/trending + 25% personalized
                fresh_tracks = self.content_service.get_fresh_popular_content(lat, lng, target_count=80, user=user)

                if not fresh_tracks:
                    logger.error("Failed to get fresh content from APIs - cannot seed without API content")
                    return []

                # Organize fresh tracks by mood while preserving personalization ratio
                import random
                seed_content = {
                    'happy': [],
                    'chill': [],
                    'energetic': [],
                    'focus': [],
                    'party': [],
                    'romantic': [],
                    'workout': []
                }

                # Separate personalized and popular tracks
                personalized_tracks = [track for track in fresh_tracks if track.get('is_personalized', False)]
                popular_tracks = [track for track in fresh_tracks if not track.get('is_personalized', False)]

                logger.info(f"Content breakdown: {len(personalized_tracks)} personalized, {len(popular_tracks)} popular")

                # Distribute tracks across moods while maintaining personalization ratio
                moods = list(seed_content.keys())

                # First distribute personalized tracks evenly across moods
                for i, track in enumerate(personalized_tracks):
                    mood = moods[i % len(moods)]
                    seed_content[mood].append(track)

                # Then distribute popular tracks across moods
                for track in popular_tracks:
                    mood = random.choice(moods)
                    seed_content[mood].append(track)

                logger.info(f"Using {len(fresh_tracks)} fresh tracks from Last.fm distributed across moods")

                # Generate pins for each exploration zone with deduplication
                created_pins = []
                existing_tracks = self._get_existing_tracks_in_area(center_point)  # Existing pins in area
                session_used_tracks = set()  # Tracks used in this session

                # Immediate Discovery Zone (0-500m)
                immediate_pins = self._generate_zone_pins_with_enhanced_dedup(
                    zone_name='immediate',
                    locations=location_zones.get('immediate', []),
                    content=seed_content,
                    pin_count_range=self.IMMEDIATE_ZONE_PIN_COUNT,
                    center_point=center_point,
                    existing_tracks=existing_tracks,
                    session_used_tracks=session_used_tracks
                )
                created_pins.extend(immediate_pins)

                # Nearby Exploration Zone (500m-2km)
                nearby_pins = self._generate_zone_pins_with_enhanced_dedup(
                    zone_name='nearby',
                    locations=location_zones.get('nearby', []),
                    content=seed_content,
                    pin_count_range=self.NEARBY_ZONE_PIN_COUNT,
                    center_point=center_point,
                    existing_tracks=existing_tracks,
                    session_used_tracks=session_used_tracks
                )
                created_pins.extend(nearby_pins)

                # City Discovery Zone (8km-20km)
                city_pins = self._generate_zone_pins_with_enhanced_dedup(
                    zone_name='city',
                    locations=location_zones.get('city', []),
                    content=seed_content,
                    pin_count_range=self.CITY_ZONE_PIN_COUNT,
                    center_point=center_point,
                    existing_tracks=existing_tracks,
                    session_used_tracks=session_used_tracks
                )
                created_pins.extend(city_pins)

                # Extended Coverage Zone (20km-30km)
                extended_pins = self._generate_zone_pins_with_enhanced_dedup(
                    zone_name='extended',
                    locations=location_zones.get('extended', []),
                    content=seed_content,
                    pin_count_range=self.EXTENDED_ZONE_PIN_COUNT,
                    center_point=center_point,
                    existing_tracks=existing_tracks,
                    session_used_tracks=session_used_tracks
                )
                created_pins.extend(extended_pins)
            
            # Record the seeding area
            seeding_area = self._record_seeding_area(center_point, len(created_pins), lat, lng)

            # Mark user as having received full seeding if user is provided
            if user and user.is_authenticated:
                if seeding_area:
                    area_id = str(seeding_area.id)
                    logger.info(f"Marking user {user.username} as full seeded in area {area_id}")
                else:
                    # If area recording failed, create a fallback area identifier
                    # This ensures user seeding state is still tracked properly
                    area_id = f"fallback_{lat:.4f}_{lng:.4f}_{timezone.now().strftime('%Y%m%d_%H%M%S')}"
                    logger.warning(f"Seeding area recording failed, using fallback area_id: {area_id}")

                self.user_state_service.mark_user_seeding_completed(
                    user=user,
                    seeding_type='full',
                    area_id=area_id
                )

            # Update metrics
            self._update_seeding_metrics(len(created_pins))

            # Log results based on seeding type
            if user and hasattr(user, 'top_genres') and (user.top_genres or user.top_artists):
                logger.info(f"Generated {len(created_pins)} personalized seed pins for user {user.username} at ({lat}, {lng})")
            else:
                logger.info(
                    f"Generated {len(created_pins)} seed pins for area ({lat}, {lng}): "
                    f"{len(immediate_pins)} immediate, {len(nearby_pins)} nearby, {len(city_pins)} city, {len(extended_pins)} extended"
                )

            return created_pins
            
        except Exception as e:
            logger.error(f"Error generating exploration seed pins: {str(e)}")
            return []
    
    def _generate_zone_pins(self, zone_name: str, locations: List[Dict], content: Dict, 
                           pin_count_range: Tuple[int, int], center_point: Point) -> List[Pin]:
        """Generate pins for a specific exploration zone"""
        import random
        
        if not locations:
            logger.warning(f"No locations available for {zone_name} zone")
            return []
        
        # Determine number of pins to create
        min_pins, max_pins = pin_count_range
        available_locations = len(locations)

        # Ensure we don't try to create more pins than available locations
        effective_max = min(max_pins, available_locations)
        effective_min = min(min_pins, available_locations)

        # Ensure min is not greater than max
        if effective_min > effective_max:
            effective_min = effective_max

        # Handle edge case where no locations are available
        if effective_max == 0:
            logger.warning(f"No locations available for {zone_name} zone")
            return []

        target_pin_count = random.randint(effective_min, effective_max)
        
        # Select random locations
        selected_locations = random.sample(locations, target_pin_count)
        
        # Generate pins
        created_pins = []
        for location in selected_locations:
            try:
                pin = self.pin_generation_service.create_seed_pin(
                    location=location,
                    content=content,
                    zone_type=zone_name
                )
                if pin:
                    created_pins.append(pin)
            except Exception as e:
                logger.error(f"Error creating pin for {zone_name} zone: {str(e)}")
                continue
        
        return created_pins

    def _generate_zone_pins_with_dedup(self, zone_name: str, locations: List[Dict], content: Dict,
                                      pin_count_range: Tuple[int, int], center_point: Point,
                                      used_tracks: set) -> List[Pin]:
        """Generate pins for a specific exploration zone with track deduplication"""
        import random

        if not locations:
            logger.warning(f"No locations available for {zone_name} zone")
            return []

        # Determine number of pins to create
        min_pins, max_pins = pin_count_range
        available_locations = len(locations)

        # Ensure we don't try to create more pins than available locations
        effective_max = min(max_pins, available_locations)
        effective_min = min(min_pins, available_locations)

        # Ensure min is not greater than max
        if effective_min > effective_max:
            effective_min = effective_max

        # Handle edge case where no locations are available
        if effective_max == 0:
            logger.warning(f"No locations available for {zone_name} zone")
            return []

        target_pin_count = random.randint(effective_min, effective_max)

        # Select random locations
        selected_locations = random.sample(locations, target_pin_count)

        # Generate pins with track deduplication
        created_pins = []
        for location in selected_locations:
            try:
                pin = self.pin_generation_service.create_seed_pin_with_dedup(
                    location=location,
                    content=content,
                    zone_type=zone_name,
                    used_tracks=used_tracks
                )
                if pin:
                    # Track the song used
                    track_key = f"{pin.track_artist}:{pin.track_title}"
                    used_tracks.add(track_key)
                    created_pins.append(pin)
                    logger.debug(f"Added track to used_tracks: {track_key} (total: {len(used_tracks)})")
            except Exception as e:
                logger.error(f"Error creating pin for {zone_name} zone: {str(e)}")
                continue

        return created_pins

    def _generate_zone_pins_with_enhanced_dedup(self, zone_name: str, locations: List[Dict], content: Dict,
                                              pin_count_range: Tuple[int, int], center_point: Point,
                                              existing_tracks: set, session_used_tracks: set) -> List[Pin]:
        """Generate pins with both database and session-level deduplication"""
        import random

        if not locations:
            logger.warning(f"No locations available for {zone_name} zone")
            return []

        # Determine number of pins to create
        min_pins, max_pins = pin_count_range
        available_locations = len(locations)

        # Ensure we don't try to create more pins than available locations
        effective_max = min(max_pins, available_locations)
        effective_min = min(min_pins, available_locations)

        # Ensure min is not greater than max
        if effective_min > effective_max:
            effective_min = effective_max

        # Handle edge case where no locations are available
        if effective_max == 0:
            logger.warning(f"No locations available for {zone_name} zone")
            return []

        target_pin_count = random.randint(effective_min, effective_max)

        # Select random locations
        selected_locations = random.sample(locations, target_pin_count)

        # Generate pins with enhanced deduplication
        created_pins = []
        for location in selected_locations:
            try:
                pin = self.pin_generation_service.create_seed_pin_with_enhanced_dedup(
                    location=location,
                    content=content,
                    zone_type=zone_name,
                    existing_tracks=existing_tracks,
                    session_used_tracks=session_used_tracks
                )
                if pin:
                    # Track the song used in this session
                    track_key = f"{pin.track_artist}:{pin.track_title}"
                    session_used_tracks.add(track_key)
                    created_pins.append(pin)
                    logger.debug(f"Added track to session_used_tracks: {track_key} (session total: {len(session_used_tracks)})")
            except Exception as e:
                logger.error(f"Error creating pin for {zone_name} zone: {str(e)}")
                continue

        return created_pins

    def _get_existing_tracks_in_area(self, center_point: Point) -> set:
        """Get tracks from existing seed pins in the area to avoid duplicates"""
        from django.contrib.gis.measure import D
        from pins.models import Pin

        try:
            # Get existing seed pins within 5km radius
            existing_pins = Pin.objects.filter(
                location__distance_lte=(center_point, D(km=5)),
                tags__contains=['seed']
            )

            # Extract track keys from existing pins
            used_tracks = set()
            for pin in existing_pins:
                if pin.track_artist and pin.track_title:
                    track_key = f"{pin.track_artist}:{pin.track_title}"
                    used_tracks.add(track_key)

            logger.info(f"Found {len(used_tracks)} existing tracks in area to avoid duplicating")
            return used_tracks

        except Exception as e:
            logger.error(f"Error getting existing tracks in area: {str(e)}")
            return set()

    def _record_seeding_area(self, center_point: Point, seed_count: int, lat: float, lng: float):
        """Record the seeded area to prevent duplicate seeding"""
        try:
            # Get city name from coordinates
            city_name = self.location_service.determine_city_from_coordinates(lat, lng)

            # Get content version, with fallback if none exists
            try:
                content_version = self.content_service.get_current_content_version()
            except Exception as content_error:
                logger.warning(f"Could not get content version: {content_error}. Using fallback.")
                content_version = "unknown"

            seeding_area = SeedingArea.objects.create(
                center_point=center_point,
                radius_km=self.SEEDING_RADIUS_KM,
                seed_count=seed_count,
                city_name=city_name,
                content_version=content_version,
                strategy_used='exploration_zones'
            )
            logger.info(f"Successfully recorded seeding area {seeding_area.id} in {city_name}")
            return seeding_area
        except Exception as e:
            logger.error(f"Error recording seeding area at ({lat}, {lng}): {str(e)}")
            import traceback
            logger.error(f"Seeding area creation traceback: {traceback.format_exc()}")
            return None

    def generate_personalized_seeds_for_area(self, user, lat: float, lng: float, existing_area) -> List:
        """
        Generate 3-4 personalized seed pins within an existing seeded area.

        This method creates personalized pins for users entering a pre-seeded area
        for the first time, placing them within the existing area boundaries.

        Args:
            user: User instance
            lat: Latitude of user location
            lng: Longitude of user location
            existing_area: SeedingArea instance for the pre-seeded area

        Returns:
            List of created Pin instances
        """
        try:
            logger.info(f"[PERSONALIZED_SEEDING] 🎵 Starting personalized seed generation for {user.username} in area {existing_area.id}")
            logger.info(f"[PERSONALIZED_SEEDING] Area details: {existing_area.city_name}, radius: {existing_area.radius_km}km, existing seeds: {existing_area.seed_count}")

            # Generate 3-4 personalized pins
            target_pin_count = random.randint(3, 4)
            logger.info(f"[PERSONALIZED_SEEDING] Target pin count: {target_pin_count}")

            # Get locations within the existing seeded area
            locations = self._get_locations_within_area(existing_area, target_pin_count)

            if not locations:
                logger.warning(f"[PERSONALIZED_SEEDING] ⚠ No locations available for personalized seeding in area {existing_area.id}")
                return []

            logger.info(f"[PERSONALIZED_SEEDING] Generated {len(locations)} locations within area boundaries")

            # Get city profile for regional context
            city_profile = self.content_service._get_city_music_profile(lat, lng)
            logger.debug(f"[PERSONALIZED_SEEDING] City profile: {city_profile}")

            # Generate personalized content
            logger.info(f"[PERSONALIZED_SEEDING] Generating personalized content for {user.username}")
            personalized_pins = self.personalized_service.generate_personalized_seed_pins(
                user=user,
                locations=locations,
                city_profile=city_profile,
                lat=lat,
                lng=lng,
                target_count=target_pin_count
            )

            logger.info(f"[PERSONALIZED_SEEDING] PersonalizedSeedingService returned {len(personalized_pins)} pin data objects")

            # Create the actual pin objects
            created_pins = []
            for i, pin_data in enumerate(personalized_pins):
                try:
                    logger.debug(f"[PERSONALIZED_SEEDING] Creating pin {i+1}/{len(personalized_pins)}: {pin_data.get('content', {}).get('title', 'Unknown')}")
                    pin = self.pin_generation_service.create_pin_from_data(pin_data)
                    if pin:
                        # Add personalized tag
                        if 'personalized' not in pin.tags:
                            pin.tags.append('personalized')
                            pin.save()
                        created_pins.append(pin)
                        logger.debug(f"[PERSONALIZED_SEEDING] ✓ Created pin {pin.id}: {pin.title}")
                    else:
                        logger.warning(f"[PERSONALIZED_SEEDING] ⚠ Pin creation returned None for pin data {i+1}")
                except Exception as e:
                    logger.error(f"[PERSONALIZED_SEEDING] ✗ Error creating personalized pin {i+1}: {str(e)}")
                    continue

            # Mark user as having received personalized seeding
            self.user_state_service.mark_user_seeding_completed(
                user=user,
                seeding_type='personalized',
                area_id=str(existing_area.id)
            )

            logger.info(f"[PERSONALIZED_SEEDING] ✅ Successfully created {len(created_pins)} personalized pins for {user.username} in area {existing_area.id}")
            return created_pins

        except Exception as e:
            logger.error(f"[PERSONALIZED_SEEDING] ✗ Error generating personalized seeds for {user.username}: {str(e)}")
            return []

    def _get_locations_within_area(self, seeding_area, count: int) -> List[Dict]:
        """
        Get random locations within an existing seeded area.

        Args:
            seeding_area: SeedingArea instance
            count: Number of locations needed

        Returns:
            List of location dictionaries
        """
        try:
            center_lat = seeding_area.center_point.y
            center_lng = seeding_area.center_point.x
            radius_m = seeding_area.radius_km * 1000  # Convert to meters

            # Use the location service to get real locations instead of generic names
            from .location_service import LocationService
            location_service = LocationService()

            # Get real locations from the location service
            all_zone_locations = location_service.get_seed_locations(center_lat, center_lng)

            # Combine all zones and select random locations
            all_locations = []
            for zone_locations in all_zone_locations.values():
                all_locations.extend(zone_locations)

            # Select random locations up to the count needed
            if len(all_locations) >= count:
                locations = random.sample(all_locations, count)
            else:
                locations = all_locations

            # If we don't get enough real locations, supplement with generated ones
            if len(locations) < count:
                remaining_count = count - len(locations)

                # Generate additional locations with better names
                for i in range(remaining_count):
                    # Generate random point within radius
                    angle = random.uniform(0, 2 * math.pi)
                    # Use smaller radius to ensure pins are well within the area
                    distance = random.uniform(100, radius_m * 0.8)

                    # Convert to lat/lng offset
                    lat_offset = (distance * math.cos(angle)) / 111000
                    lng_offset = (distance * math.sin(angle)) / (111000 * math.cos(math.radians(center_lat)))

                    # Use more natural location names
                    location_names = [
                        "Local Discovery Spot", "Hidden Gem Location", "Music Discovery Point",
                        "Neighborhood Find", "Local Favorite Spot", "Community Discovery",
                        "Urban Discovery", "Local Music Spot", "Neighborhood Gem"
                    ]

                    location = {
                        'lat': center_lat + lat_offset,
                        'lng': center_lng + lng_offset,
                        'name': random.choice(location_names),
                        'type': 'personalized_discovery',
                        'zone': 'personalized',
                        'source': 'generated'
                    }
                    locations.append(location)

            return locations

        except Exception as e:
            logger.error(f"Error getting locations within area: {str(e)}")
            return []
    
    def _update_seeding_metrics(self, pins_generated: int):
        """Update daily seeding metrics"""
        try:
            today = timezone.now().date()
            metrics, created = SeedingMetrics.objects.get_or_create(
                date=today,
                defaults={
                    'areas_seeded': 0,
                    'pins_generated': 0,
                }
            )
            
            metrics.areas_seeded += 1
            metrics.pins_generated += pins_generated
            metrics.save(update_fields=['areas_seeded', 'pins_generated'])
            
        except Exception as e:
            logger.error(f"Error updating seeding metrics: {str(e)}")
