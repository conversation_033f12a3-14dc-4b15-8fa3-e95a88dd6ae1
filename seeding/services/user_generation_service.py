"""
User generation service for creating diverse, realistic user profiles for seeding.

This service provides:
- Diverse name generation across multiple cultures and demographics
- Realistic username generation with variety
- Avatar URL generation using pravatar.cc
- Comprehensive user profile creation for curator accounts
"""

import random
import hashlib
import uuid
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class UserGenerationService:
    """Service for generating diverse, realistic user profiles"""
    
    # Diverse first names across cultures and demographics
    FIRST_NAMES = {
        'western': {
            'common': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
            'traditional': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
            'modern': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
        },
        'hispanic': {
            'common': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>'],
            'traditional': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
            'modern': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>']
        },
        'as<PERSON>': {
            'common': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
            'traditional': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Chen', 'Li', 'Raj', 'Priya', 'Kenji', 'Sakura', 'Wei', 'Mei'],
            'modern': ['Kai', 'Aria', 'Leo', 'Maya', 'Zen', 'Kira', 'Neo', 'Ava', 'Jin', 'Zara']
        },
        'african': {
            'common': ['Marcus', 'Jasmine', 'Darius', 'Aaliyah', 'Malik', 'Zara', 'Jamal', 'Nia', 'Terrell', 'Kendra'],
            'traditional': ['Kwame', 'Amara', 'Kofi', 'Asha', 'Jabari', 'Kesi', 'Omari', 'Zuri', 'Asante', 'Imani'],
            'modern': ['Zion', 'Aria', 'Phoenix', 'Nova', 'River', 'Luna', 'Sage', 'Ivy', 'Atlas', 'Skye']
        },
        'middle_eastern': {
            'common': ['Omar', 'Layla', 'Hassan', 'Fatima', 'Ali', 'Nour', 'Ahmed', 'Yasmin', 'Yusuf', 'Amira'],
            'traditional': ['Muhammad', 'Aisha', 'Ibrahim', 'Khadija', 'Abdullah', 'Maryam', 'Khalid', 'Zahra', 'Mahmoud', 'Hafsa'],
            'modern': ['Zain', 'Lina', 'Rayan', 'Dina', 'Tariq', 'Maya', 'Samir', 'Rana', 'Faris', 'Naya']
        },
        'european': {
            'common': ['Luca', 'Emma', 'Marco', 'Sophia', 'Felix', 'Olivia', 'Leon', 'Mia', 'Noah', 'Ella'],
            'traditional': ['Giovanni', 'Francesca', 'Pierre', 'Marie', 'Hans', 'Greta', 'Ivan', 'Natasha', 'Lars', 'Astrid'],
            'modern': ['Finn', 'Luna', 'Axel', 'Freya', 'Kai', 'Nora', 'Enzo', 'Zara', 'Theo', 'Iris']
        }
    }
    
    # Diverse last names
    LAST_NAMES = {
        'western': ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez',
                   'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin'],
        'asian': ['Chen', 'Li', 'Wang', 'Zhang', 'Liu', 'Yang', 'Huang', 'Zhao', 'Wu', 'Zhou',
                 'Kim', 'Lee', 'Park', 'Choi', 'Jung', 'Kang', 'Cho', 'Yoon', 'Jang', 'Lim',
                 'Tanaka', 'Suzuki', 'Takahashi', 'Watanabe', 'Ito', 'Yamamoto', 'Nakamura', 'Kobayashi', 'Kato', 'Yoshida'],
        'hispanic': ['Garcia', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Perez', 'Sanchez', 'Ramirez', 'Cruz',
                    'Flores', 'Gomez', 'Diaz', 'Reyes', 'Morales', 'Ortiz', 'Gutierrez', 'Chavez', 'Ramos', 'Castillo'],
        'african': ['Johnson', 'Williams', 'Brown', 'Jackson', 'Davis', 'Wilson', 'Moore', 'Taylor', 'Anderson', 'Thomas',
                   'Okafor', 'Adebayo', 'Oluwaseun', 'Chukwu', 'Nkomo', 'Mthembu', 'Dlamini', 'Kone', 'Traore', 'Diallo'],
        'middle_eastern': ['Al-Ahmad', 'Al-Hassan', 'Al-Ali', 'Al-Omar', 'Al-Mahmoud', 'Al-Khalil', 'Al-Rashid', 'Al-Zahra',
                          'Hosseini', 'Ahmadi', 'Mohammadi', 'Rezaei', 'Moradi', 'Karimi', 'Rahimi', 'Kazemi', 'Sadeghi', 'Hashemi'],
        'european': ['Rossi', 'Russo', 'Ferrari', 'Esposito', 'Bianchi', 'Romano', 'Colombo', 'Ricci', 'Marino', 'Greco',
                    'Dubois', 'Martin', 'Bernard', 'Petit', 'Robert', 'Richard', 'Durand', 'Leroy', 'Moreau', 'Simon',
                    'Mueller', 'Schmidt', 'Schneider', 'Fischer', 'Weber', 'Meyer', 'Wagner', 'Becker', 'Schulz', 'Hoffmann']
    }
    
    # Username patterns and components - more natural and less obvious
    USERNAME_PATTERNS = [
        '{first_name}_{last_initial}',
        '{first_name}_{number}',
        '{first_name}_{adjective}',
        '{adjective}_{first_name}',
        '{first_name}.{last_name}',
        '{first_initial}{last_name}',
        '{first_name}{number}',
        '{first_name}_{year}',
        '{first_name}_{random_word}',
        '{random_word}_{first_name}',
        '{first_name}_{last_initial}{number}',
        '{first_initial}{last_name}{number}',
        '{first_name}.{random_word}',
        '{random_word}.{first_name}',
        '{first_name}_{color}',
        '{color}_{first_name}',
        '{first_name}_{nature}',
        '{nature}_{first_name}'
    ]
    
    USERNAME_ADJECTIVES = [
        'cool', 'real', 'true', 'wild', 'free', 'bright', 'swift', 'bold', 'calm', 'wise',
        'fresh', 'pure', 'deep', 'high', 'fast', 'slow', 'warm', 'cold', 'dark', 'light',
        'young', 'old', 'new', 'rare', 'epic', 'zen', 'chill', 'smooth', 'sharp', 'soft'
    ]

    # More natural random words that don't scream "music curator"
    USERNAME_RANDOM_WORDS = [
        'blue', 'red', 'green', 'silver', 'gold', 'star', 'moon', 'sun', 'sky', 'ocean',
        'river', 'stone', 'cloud', 'wind', 'fire', 'earth', 'rain', 'snow', 'leaf', 'tree',
        'bird', 'wolf', 'fox', 'bear', 'lion', 'tiger', 'eagle', 'hawk', 'dove', 'swan',
        'rose', 'lily', 'sage', 'iris', 'jade', 'ruby', 'pearl', 'amber', 'coral', 'ivy',
        'nova', 'echo', 'zen', 'flux', 'vibe', 'wave', 'flow', 'drift', 'spark', 'glow'
    ]

    USERNAME_COLORS = [
        'blue', 'red', 'green', 'purple', 'orange', 'yellow', 'pink', 'black', 'white', 'gray',
        'silver', 'gold', 'bronze', 'crimson', 'azure', 'violet', 'indigo', 'coral', 'amber', 'jade'
    ]

    USERNAME_NATURE = [
        'river', 'ocean', 'mountain', 'forest', 'desert', 'valley', 'meadow', 'grove', 'creek', 'lake',
        'stone', 'rock', 'cliff', 'peak', 'hill', 'field', 'garden', 'park', 'trail', 'path',
        'star', 'moon', 'sun', 'cloud', 'storm', 'rain', 'snow', 'wind', 'fire', 'earth'
    ]
    
    def generate_diverse_name(self) -> Tuple[str, str]:
        """
        Generate a diverse first and last name combination.
        
        Returns:
            Tuple of (first_name, last_name)
        """
        # Select a random cultural background
        culture = random.choice(list(self.FIRST_NAMES.keys()))
        
        # Select a random name style within that culture
        style = random.choice(list(self.FIRST_NAMES[culture].keys()))
        
        # Get first name
        first_name = random.choice(self.FIRST_NAMES[culture][style])
        
        # Get last name (can be from same or different culture for diversity)
        last_name_culture = random.choices(
            [culture, random.choice(list(self.LAST_NAMES.keys()))],
            weights=[0.8, 0.2]  # 80% same culture, 20% mixed
        )[0]
        
        last_name = random.choice(self.LAST_NAMES[last_name_culture])
        
        return first_name, last_name
    
    def generate_realistic_username(self, first_name: str, last_name: str) -> str:
        """
        Generate a realistic username based on name and common patterns.
        
        Args:
            first_name: User's first name
            last_name: User's last name
            
        Returns:
            Generated username
        """
        # Select a random pattern
        pattern = random.choice(self.USERNAME_PATTERNS)
        
        # Prepare variables for pattern substitution
        variables = {
            'first_name': first_name.lower(),
            'last_name': last_name.lower(),
            'first_initial': first_name[0].lower(),
            'last_initial': last_name[0].lower(),
            'number': str(random.randint(1, 999)),
            'adjective': random.choice(self.USERNAME_ADJECTIVES),
            'random_word': random.choice(self.USERNAME_RANDOM_WORDS),
            'color': random.choice(self.USERNAME_COLORS),
            'nature': random.choice(self.USERNAME_NATURE),
            'year': str(random.randint(1990, 2010))
        }
        
        # Generate username
        username = pattern.format(**variables)
        
        # Add random suffix if username might be too common
        if random.random() < 0.3:  # 30% chance
            username += str(random.randint(1, 99))
        
        return username

    def generate_avatar_url(self, unique_identifier: Optional[str] = None) -> str:
        """
        Generate a unique avatar URL using pravatar.cc.

        Args:
            unique_identifier: Optional unique identifier for consistent avatar generation

        Returns:
            Avatar URL
        """
        if not unique_identifier:
            # Generate a unique identifier based on UUID
            unique_identifier = str(uuid.uuid4())

        # Create a hash of the identifier to ensure consistency
        hash_object = hashlib.md5(unique_identifier.encode())
        hash_hex = hash_object.hexdigest()

        # Use the hash as the unique parameter for i.pravatar.cc
        avatar_url = f"https://i.pravatar.cc/150?u={hash_hex}"

        return avatar_url

    def generate_bio(self, persona_type: str, first_name: str) -> str:
        """
        Generate a realistic bio based on persona type and name.

        Args:
            persona_type: The curator persona type
            first_name: User's first name for personalization

        Returns:
            Generated bio text
        """
        bio_templates = {
            'indie_explorer': [
                f"Hey, I'm {first_name}! Always hunting for the next underground gem 🎧 Indie music enthusiast discovering hidden tracks in unexpected places",
                f"{first_name} here - diving deep into the indie scene to find those perfect hidden gems ✨ Music is my compass",
                f"Music explorer {first_name} 🎵 Passionate about discovering emerging artists and sharing the sounds that move me",
                f"I'm {first_name}, your friendly neighborhood indie scout 🔍 Finding the tracks that deserve more love"
            ],
            'coffee_enthusiast': [
                f"Coffee shop connoisseur {first_name} ☕🎵 Curating the perfect soundtrack for your caffeine fix",
                f"Hi! I'm {first_name} - matching great coffee with even better music ☕ Every brew deserves its perfect playlist",
                f"{first_name} here, blending coffee culture with musical discovery 🎶 Life's too short for bad coffee or boring music",
                f"Barista by day, music curator by heart ☕ I'm {first_name}, and I believe every coffee shop needs the perfect vibe"
            ],
            'venue_hopper': [
                f"Live music lover {first_name} sharing the best tracks from amazing venues 🎤 If there's a stage, I've probably been there",
                f"Concert enthusiast {first_name} 🎸 Bringing you the energy of live music, one pin at a time",
                f"I'm {first_name}, your guide to the best live music experiences 🎵 From intimate venues to massive festivals",
                f"Music venue explorer {first_name} 🎭 Capturing the magic of live performances and sharing the soundtrack"
            ],
            'campus_curator': [
                f"Student life soundtrack curator {first_name} 📚🎶 Study beats, campus vibes, and late-night library sessions",
                f"College student {first_name} here! 🎓 Curating the perfect study playlists and campus discovery soundtracks",
                f"Campus music guide {first_name} 📖 From study sessions to weekend parties, I've got your soundtrack covered",
                f"Student {first_name} sharing the sounds that make campus life better 🎵 Music for every mood and moment"
            ],
            'city_wanderer': [
                f"Urban explorer {first_name} mapping the city through music 🏙️🎵 Every street corner has a soundtrack",
                f"City guide {first_name} here! 🚶‍♀️ Discovering the musical heartbeat of urban life, one neighborhood at a time",
                f"I'm {first_name}, your musical tour guide through the city 🗺️ Finding the perfect tracks for every location",
                f"Urban music explorer {first_name} 🌆 Connecting places with their perfect soundtracks"
            ],
            'music_historian': [
                f"Music historian {first_name} connecting past and present through timeless tracks 📻 Every song tells a story",
                f"I'm {first_name}, preserving musical heritage one pin at a time 🎼 Bridging generations through great music",
                f"Musical storyteller {first_name} 📚 Sharing the rich history behind the songs that shaped our world",
                f"Heritage music curator {first_name} 🎵 Keeping the classics alive while discovering their modern echoes"
            ],
            'genre_specialist': [
                f"Genre specialist {first_name} diving deep into musical styles 🎼 Expert in electronic, ambient, and experimental sounds",
                f"Music analyst {first_name} here! 🔬 Exploring the intricate details of different genres and their evolution",
                f"I'm {first_name}, your guide through the complex world of musical genres 🎵 Every style has its story",
                f"Genre explorer {first_name} 🎧 Mapping the boundaries and connections between different musical worlds"
            ],
            'local_guide': [
                f"Local music guide {first_name} 🗺️🎵 Sharing the sounds that make this city special",
                f"Hometown hero {first_name} here! 🏠 Showcasing the local artists and venues that define our musical identity",
                f"I'm {first_name}, your friendly neighborhood music ambassador 🎶 Celebrating local talent and hidden gems",
                f"Community music curator {first_name} 🤝 Connecting people with the artists and sounds from our backyard"
            ]
        }

        # Get templates for the persona type, fallback to city_wanderer if not found
        templates = bio_templates.get(persona_type, bio_templates['city_wanderer'])

        # Select a random template
        return random.choice(templates)

    def generate_user_profile(self, persona_type: str, preferred_genres: List[str],
                            preferred_locations: List[str], assigned_cities: List[str] = None) -> Dict[str, str]:
        """
        Generate a complete user profile with diverse, realistic information.

        Args:
            persona_type: The curator persona type
            preferred_genres: List of preferred music genres
            preferred_locations: List of preferred location types
            assigned_cities: Optional list of assigned cities

        Returns:
            Dictionary containing user profile information
        """
        # Generate diverse name
        first_name, last_name = self.generate_diverse_name()

        # Generate realistic username
        username = self.generate_realistic_username(first_name, last_name)

        # Generate unique avatar
        unique_id = f"{username}_{persona_type}_{random.randint(1000, 9999)}"
        avatar_url = self.generate_avatar_url(unique_id)

        # Generate personalized bio
        bio = self.generate_bio(persona_type, first_name)

        # Generate email
        email = f"{username}@bopmaps.com"

        return {
            'username': username,
            'first_name': first_name,
            'last_name': last_name,
            'email': email,
            'bio': bio,
            'profile_pic': avatar_url,
            'persona_type': persona_type,
            'preferred_genres': preferred_genres,
            'preferred_locations': preferred_locations,
            'assigned_cities': assigned_cities or []
        }

    def ensure_username_uniqueness(self, username: str, existing_usernames: set) -> str:
        """
        Ensure username uniqueness by adding suffixes if needed.

        Args:
            username: Proposed username
            existing_usernames: Set of existing usernames to check against

        Returns:
            Unique username
        """
        original_username = username
        counter = 1

        while username in existing_usernames:
            username = f"{original_username}{counter}"
            counter += 1

            # Prevent infinite loops
            if counter > 1000:
                username = f"{original_username}_{random.randint(1000, 9999)}"
                break

        return username
