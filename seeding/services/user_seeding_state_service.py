"""
User Seeding State Service for managing personalized seeding logic.

This service handles the tracking and management of user seeding states,
enabling the personalized seeding feature that provides different seeding
experiences based on user history.
"""

import logging
from typing import Op<PERSON>, Tuple, List
from django.contrib.auth import get_user_model
from django.contrib.gis.geos import Point
from django.contrib.gis.measure import D
from django.db.models import Q

from seeding.models import UserSeedingState, SeedingArea

User = get_user_model()
logger = logging.getLogger(__name__)


class UserSeedingStateService:
    """
    Service for managing user seeding states and determining appropriate seeding actions.
    
    This service implements the core logic for the personalized seeding feature:
    - Tracks user seeding history across different geographic areas
    - Determines when users should receive personalized vs full seeding
    - Manages state transitions based on user interactions with seeded areas
    """
    
    def __init__(self):
        pass
    
    def get_user_seeding_state(self, user: User) -> UserSeedingState:
        """
        Get or create the seeding state for a user.

        Args:
            user: User instance

        Returns:
            UserSeedingState instance
        """
        try:
            state, created = UserSeedingState.get_or_create_for_user(user)
            if created:
                logger.info(f"[PERSONALIZED_SEEDING] Created new seeding state for user {user.username} - State: NEVER_SEEDED")
            else:
                logger.debug(f"[PERSONALIZED_SEEDING] Retrieved existing seeding state for user {user.username} - State: {state.current_state}")
            return state
        except Exception as e:
            logger.error(f"[PERSONALIZED_SEEDING] Error getting seeding state for user {user.username}: {str(e)}")
            # Return a default state if database operation fails
            return UserSeedingState(user=user, current_state='NEVER_SEEDED')
    
    def check_area_seeding_eligibility(self, user: User, lat: float, lng: float, search_radius_km: float = 5.0) -> dict:
        """
        Check what type of seeding (if any) a user should receive in a given area.

        Args:
            user: User instance
            lat: Latitude of the area
            lng: Longitude of the area
            search_radius_km: User's search radius in kilometers (for overlap detection)

        Returns:
            Dict with seeding eligibility information:
            {
                'should_seed': bool,
                'seeding_type': str,  # 'personalized', 'full', or 'none'
                'reason': str,
                'existing_area': SeedingArea or None,
                'user_state': str
            }
        """
        try:
            user_state = self.get_user_seeding_state(user)
            center_point = Point(lng, lat, srid=4326)

            # Check if there's an existing seeded area nearby (considering search radius overlap)
            existing_area = self._find_existing_seeded_area(center_point, search_radius_km)
            
            result = {
                'should_seed': False,
                'seeding_type': 'none',
                'reason': '',
                'existing_area': existing_area,
                'user_state': user_state.current_state
            }
            
            if existing_area:
                # Area is already seeded - check if user should get personalized seeding
                area_id = str(existing_area.id)

                logger.info(f"[PERSONALIZED_SEEDING] Found existing seeded area {area_id} for user {user.username} at ({lat}, {lng})")

                if user_state.should_get_personalized_in_area(area_id):
                    result.update({
                        'should_seed': True,
                        'seeding_type': 'personalized',
                        'reason': f'User {user.username} entering pre-seeded area {area_id} for first time'
                    })
                    logger.info(f"[PERSONALIZED_SEEDING] ✓ User {user.username} eligible for personalized seeding in area {area_id}")
                else:
                    result['reason'] = f'User {user.username} already seeded in area {area_id} or not eligible (state: {user_state.current_state})'
                    logger.info(f"[PERSONALIZED_SEEDING] ✗ User {user.username} not eligible for personalized seeding in area {area_id} - {result['reason']}")
            else:
                # No SeedingArea found, but check if other users have been seeded in this location
                # by looking for users with fallback area IDs that match this location
                nearby_seeded_users = self._find_users_seeded_nearby(lat, lng, search_radius_km)

                if nearby_seeded_users and user_state.current_state == 'NEVER_SEEDED':
                    # Other users have been seeded nearby, so this should be personalized seeding
                    result.update({
                        'should_seed': True,
                        'seeding_type': 'personalized',
                        'reason': f'User {user.username} entering area where other users have been seeded (fallback detection)'
                    })
                    logger.info(f"[PERSONALIZED_SEEDING] ✓ User {user.username} eligible for personalized seeding (detected via fallback method)")
                else:
                    # Area is not seeded - check if user should get full seeding
                    logger.info(f"[PERSONALIZED_SEEDING] No existing seeded area found for user {user.username} at ({lat}, {lng})")

                    if user_state.can_receive_full_seeding():
                        result.update({
                            'should_seed': True,
                            'seeding_type': 'full',
                            'reason': f'User {user.username} entering unseeded area (state: {user_state.current_state})'
                        })
                        logger.info(f"[PERSONALIZED_SEEDING] ✓ User {user.username} eligible for full seeding in new area")
                    else:
                        result['reason'] = f'User {user.username} already has full seeding (state: {user_state.current_state})'
                        logger.info(f"[PERSONALIZED_SEEDING] ✗ User {user.username} not eligible for any seeding - {result['reason']}")

            logger.info(
                f"[PERSONALIZED_SEEDING] Final eligibility decision for {user.username} at ({lat}, {lng}): "
                f"Type: {result['seeding_type']}, Should seed: {result['should_seed']}, Reason: {result['reason']}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking seeding eligibility: {str(e)}")
            return {
                'should_seed': False,
                'seeding_type': 'none',
                'reason': f'Error: {str(e)}',
                'existing_area': None,
                'user_state': 'unknown'
            }
    
    def mark_user_seeding_completed(self, user: User, seeding_type: str, area_id: Optional[str] = None):
        """
        Mark that a user has completed seeding of a specific type.

        Args:
            user: User instance
            seeding_type: Type of seeding completed ('personalized' or 'full')
            area_id: Optional area identifier where seeding occurred
        """
        try:
            user_state = self.get_user_seeding_state(user)
            previous_state = user_state.current_state

            if seeding_type == 'personalized':
                user_state.mark_personalized_seeding(area_id)
                logger.info(f"[PERSONALIZED_SEEDING] ✓ Marked personalized seeding complete for {user.username} in area {area_id}")
                logger.info(f"[PERSONALIZED_SEEDING] State transition: {previous_state} → {user_state.current_state}")
            elif seeding_type == 'full':
                user_state.mark_full_seeding(area_id)
                logger.info(f"[PERSONALIZED_SEEDING] ✓ Marked full seeding complete for {user.username} in area {area_id}")
                logger.info(f"[PERSONALIZED_SEEDING] State transition: {previous_state} → {user_state.current_state}")
            else:
                logger.warning(f"[PERSONALIZED_SEEDING] ⚠ Unknown seeding type: {seeding_type} for user {user.username}")

        except Exception as e:
            logger.error(f"[PERSONALIZED_SEEDING] ✗ Error marking seeding completed for {user.username}: {str(e)}")
    
    def get_user_seeding_history(self, user: User) -> dict:
        """
        Get comprehensive seeding history for a user.
        
        Args:
            user: User instance
            
        Returns:
            Dict with user's seeding history
        """
        try:
            user_state = self.get_user_seeding_state(user)
            
            return {
                'current_state': user_state.current_state,
                'first_seeding_at': user_state.first_seeding_at,
                'personalized_seeding_at': user_state.personalized_seeding_at,
                'full_seeding_at': user_state.full_seeding_at,
                'personalized_areas_count': len(user_state.personalized_seeding_areas),
                'full_seeding_areas_count': len(user_state.full_seeding_areas),
                'can_receive_personalized': user_state.can_receive_personalized_seeding(),
                'can_receive_full': user_state.can_receive_full_seeding()
            }
            
        except Exception as e:
            logger.error(f"Error getting seeding history for {user.username}: {str(e)}")
            return {}
    
    def _find_existing_seeded_area(self, center_point: Point, search_radius_km: float = 5.0) -> Optional[SeedingArea]:
        """
        Find if there's an existing seeded area that the user should be considered part of.

        This method uses enhanced boundary detection that considers:
        1. If user is inside the seeded area boundary
        2. If user's search radius overlaps significantly with the seeded area

        Args:
            center_point: Point to check around
            search_radius_km: User's search radius in kilometers

        Returns:
            SeedingArea instance if found, None otherwise
        """
        try:
            # First, find any seeded areas within a reasonable distance
            # We use a larger search radius to find areas that might overlap with user's search
            max_search_distance = search_radius_km + 10  # Look for areas within user's search + 10km

            # Find nearby seeded areas using PostGIS distance query
            from django.db import connection

            # Use raw SQL for distance calculation to avoid Django ORM issues
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT id, center_point, radius_km, city_name,
                           ST_Distance(center_point::geography, ST_SetSRID(ST_MakePoint(%s, %s), 4326)::geography) / 1000 as distance_km
                    FROM seeding_seedingarea
                    WHERE ST_DWithin(center_point::geography, ST_SetSRID(ST_MakePoint(%s, %s), 4326)::geography, %s)
                    ORDER BY distance_km
                """, [center_point.x, center_point.y, center_point.x, center_point.y, max_search_distance * 1000])

                area_data = cursor.fetchall()

            # Convert to SeedingArea objects
            nearby_areas = []
            for row in area_data:
                try:
                    area = SeedingArea.objects.get(id=row[0])
                    area._distance_km = row[4]  # Store calculated distance
                    nearby_areas.append(area)
                except SeedingArea.DoesNotExist:
                    continue

            for area in nearby_areas:
                # Use the pre-calculated distance from the SQL query
                distance_to_area = getattr(area, '_distance_km', 0)

                # Case 1: User is inside the seeded area
                if distance_to_area <= area.radius_km:
                    logger.debug(f"[PERSONALIZED_SEEDING] User inside seeded area {area.id} (distance: {distance_to_area:.1f}km <= radius: {area.radius_km}km)")
                    return area

                # Case 2: User is outside but their search radius overlaps significantly with seeded area
                # Check if circles overlap: distance between centers < sum of radii
                circles_overlap = distance_to_area < (area.radius_km + search_radius_km)

                logger.debug(f"[PERSONALIZED_SEEDING] Area {area.id} overlap check: distance={distance_to_area:.1f}km, area_radius={area.radius_km}km, search_radius={search_radius_km}km")
                logger.debug(f"[PERSONALIZED_SEEDING] Circles overlap: {circles_overlap} (distance < {area.radius_km + search_radius_km:.1f}km)")

                if circles_overlap:
                    # Calculate overlap significance
                    # If user is close enough that their search significantly overlaps with seeded area
                    overlap_distance = (area.radius_km + search_radius_km) - distance_to_area

                    # Calculate what percentage of the user's search radius overlaps with seeded area
                    # This is a simplified calculation
                    overlap_ratio = overlap_distance / search_radius_km
                    overlap_threshold = 0.3  # 30% overlap threshold (more conservative)

                    logger.debug(f"[PERSONALIZED_SEEDING] Overlap calculation: overlap_distance={overlap_distance:.1f}km, overlap_ratio={overlap_ratio:.2f}, threshold={overlap_threshold}")

                    if overlap_ratio >= overlap_threshold:
                        logger.info(f"[PERSONALIZED_SEEDING] User outside seeded area {area.id} but significant overlap detected")
                        logger.info(f"[PERSONALIZED_SEEDING] Distance to area: {distance_to_area:.1f}km, Area radius: {area.radius_km}km, User search: {search_radius_km}km")
                        logger.info(f"[PERSONALIZED_SEEDING] Overlap distance: {overlap_distance:.1f}km, Overlap ratio: {overlap_ratio:.2f} >= threshold: {overlap_threshold}")
                        return area
                    else:
                        logger.debug(f"[PERSONALIZED_SEEDING] Area {area.id} overlap too small: {overlap_ratio:.2f} < {overlap_threshold}")
                else:
                    logger.debug(f"[PERSONALIZED_SEEDING] No overlap between user search and area {area.id}")

            logger.debug(f"[PERSONALIZED_SEEDING] No suitable seeded area found for user location")
            return None

        except Exception as e:
            logger.error(f"[PERSONALIZED_SEEDING] Error finding existing seeded area: {str(e)}")
            return None
    
    def reset_user_seeding_state(self, user: User):
        """
        Reset a user's seeding state (for testing or admin purposes).
        
        Args:
            user: User instance
        """
        try:
            user_state = self.get_user_seeding_state(user)
            user_state.current_state = 'NEVER_SEEDED'
            user_state.first_seeding_at = None
            user_state.personalized_seeding_at = None
            user_state.full_seeding_at = None
            user_state.personalized_seeding_areas = []
            user_state.full_seeding_areas = []
            user_state.save()
            
            logger.info(f"Reset seeding state for user {user.username}")
            
        except Exception as e:
            logger.error(f"Error resetting seeding state for {user.username}: {str(e)}")

    def _find_users_seeded_nearby(self, lat: float, lng: float, radius_km: float = 5.0) -> List[UserSeedingState]:
        """
        Find users who have been seeded near this location (fallback method).

        This method helps detect when an area has been seeded even if the SeedingArea
        record creation failed, by looking for users with fallback area IDs that
        match the location pattern.

        Args:
            lat: Latitude to check around
            lng: Longitude to check around
            radius_km: Search radius in kilometers

        Returns:
            List of UserSeedingState objects for users seeded nearby
        """
        try:
            # Look for users with fallback area IDs that match this location pattern
            # Fallback area IDs have format: fallback_{lat:.4f}_{lng:.4f}_{timestamp}
            lat_pattern = f"{lat:.4f}"
            lng_pattern = f"{lng:.4f}"

            # Find users with full seeding areas containing fallback IDs for this location
            nearby_users = UserSeedingState.objects.filter(
                current_state__in=['PERSONALIZED_ONLY', 'FULL_SEEDED'],
                full_seeding_areas__isnull=False
            )

            matching_users = []
            for user_state in nearby_users:
                for area_id in user_state.full_seeding_areas:
                    if (area_id.startswith('fallback_') and
                        lat_pattern in area_id and lng_pattern in area_id):
                        matching_users.append(user_state)
                        logger.debug(f"[PERSONALIZED_SEEDING] Found user {user_state.user.username} seeded nearby via fallback area {area_id}")
                        break

            return matching_users

        except Exception as e:
            logger.error(f"Error finding users seeded nearby: {str(e)}")
            return []
