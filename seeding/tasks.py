"""
Celery tasks for automated seed lifecycle management and maintenance.

These tasks handle:
- Daily seed cleanup and expiration
- Seed retirement in areas with sufficient organic content
- Seed content database refresh from music APIs
- Seeding metrics collection and analytics
"""

import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.core.management import call_command
from django.db.models import Count, Q
from django.contrib.gis.measure import D

from celery import shared_task

from pins.models import Pin
from seeding.models import SeedingArea, SeedingMetrics, SeedContentDatabase

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def daily_seed_cleanup(self):
    """
    Daily task to clean up expired seed pins and retire seeds in organic areas.

    This task should be scheduled to run once daily, preferably during low-traffic hours.
    Runs basic cleanup (pins and areas) daily, with comprehensive cleanup weekly.
    """
    try:
        logger.info("Starting daily seed cleanup task")

        # Run basic cleanup (pins and areas only)
        call_command('cleanup_seeds')

        logger.info("Daily seed cleanup completed successfully")
        return {"status": "success", "message": "Daily seed cleanup completed"}

    except Exception as e:
        logger.error(f"Error in daily seed cleanup: {str(e)}")

        # Retry the task up to 3 times
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying daily seed cleanup (attempt {self.request.retries + 1})")
            raise self.retry(countdown=300, exc=e)  # Retry after 5 minutes

        return {"status": "error", "message": str(e)}


@shared_task(bind=True, max_retries=2)
def weekly_comprehensive_cleanup(self):
    """
    Weekly task to run comprehensive cleanup of all seeding-related data.

    This task should be scheduled to run once weekly, preferably during low-traffic hours.
    Includes curator cleanup, user state cleanup, old content cleanup, and metrics archival.
    """
    try:
        logger.info("Starting weekly comprehensive cleanup task")

        # Run comprehensive cleanup with all options
        call_command('cleanup_seeds', '--cleanup-all')

        logger.info("Weekly comprehensive cleanup completed successfully")
        return {"status": "success", "message": "Weekly comprehensive cleanup completed"}

    except Exception as e:
        logger.error(f"Error in weekly comprehensive cleanup: {str(e)}")

        # Retry the task up to 2 times
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying weekly comprehensive cleanup (attempt {self.request.retries + 1})")
            raise self.retry(countdown=600, exc=e)  # Retry after 10 minutes

        return {"status": "error", "message": str(e)}


@shared_task(bind=True, max_retries=2)
def update_seeding_metrics(self):
    """
    Task to update daily seeding metrics and analytics.
    
    Collects statistics about seeding performance, user engagement,
    and system health for monitoring and optimization.
    """
    try:
        logger.info("Starting seeding metrics update")
        
        today = timezone.now().date()
        
        # Get or create today's metrics
        metrics, created = SeedingMetrics.objects.get_or_create(
            date=today,
            defaults={
                'areas_seeded': 0,
                'pins_generated': 0,
                'seed_pin_views': 0,
                'seed_pin_collects': 0,
                'seed_pin_likes': 0,
                'avg_user_engagement': 0.0,
                'avg_seeding_response_time': 0.0,
                'api_failures': 0,
                'cache_hit_rate': 0.0,
            }
        )
        
        # Count active seed pins
        active_seed_count = Pin.objects.filter(
            Q(expiration_date__isnull=True) | Q(expiration_date__gt=timezone.now()),
            tags__contains=['seed']
        ).count()
        
        # Count seeded areas
        total_seeded_areas = SeedingArea.objects.count()
        
        # Calculate engagement metrics for seed pins
        from pins.models import PinInteraction
        
        seed_interactions_today = PinInteraction.objects.filter(
            pin__tags__contains=['seed'],
            created_at__date=today
        )
        
        views_count = seed_interactions_today.filter(interaction_type='view').count()
        collects_count = seed_interactions_today.filter(interaction_type='collect').count()
        likes_count = seed_interactions_today.filter(interaction_type='like').count()
        
        # Calculate engagement rate
        total_interactions = views_count + collects_count + likes_count
        engagement_rate = (collects_count + likes_count) / max(views_count, 1) * 100
        
        # Update metrics
        metrics.seed_pin_views = views_count
        metrics.seed_pin_collects = collects_count
        metrics.seed_pin_likes = likes_count
        metrics.avg_user_engagement = engagement_rate
        metrics.save()
        
        logger.info(
            f"Updated seeding metrics: {views_count} views, {collects_count} collects, "
            f"{likes_count} likes, {engagement_rate:.2f}% engagement"
        )
        
        return {
            "status": "success",
            "metrics": {
                "active_seeds": active_seed_count,
                "seeded_areas": total_seeded_areas,
                "views": views_count,
                "collects": collects_count,
                "likes": likes_count,
                "engagement_rate": engagement_rate
            }
        }
        
    except Exception as e:
        logger.error(f"Error updating seeding metrics: {str(e)}")
        
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying metrics update (attempt {self.request.retries + 1})")
            raise self.retry(countdown=600, exc=e)  # Retry after 10 minutes
        
        return {"status": "error", "message": str(e)}


@shared_task(bind=True, max_retries=2)
def refresh_seed_content_database(self):
    """
    Task to refresh the seed content database with new tracks from music APIs.
    
    This task should be scheduled to run weekly to keep the seed content fresh
    and incorporate new popular tracks and trending music.
    """
    try:
        logger.info("Starting seed content database refresh")
        
        # Check if we have an active content database
        current_content = SeedContentDatabase.get_active_content()
        if not current_content:
            logger.warning("No active seed content database found")
            return {"status": "warning", "message": "No active content database"}
        
        # Check if content is older than 7 days
        content_age = timezone.now() - current_content.created_at
        if content_age < timedelta(days=7):
            logger.info(f"Content database is only {content_age.days} days old, skipping refresh")
            return {"status": "skipped", "message": "Content is still fresh"}
        
        # For now, we'll just update the last_api_update timestamp
        # In a full implementation, this would query Spotify, Last.fm, and SoundCharts APIs
        current_content.last_api_update = timezone.now()
        current_content.save(update_fields=['last_api_update'])
        
        logger.info("Seed content database refresh completed")
        
        return {
            "status": "success",
            "message": "Content database refreshed",
            "last_update": current_content.last_api_update.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error refreshing seed content database: {str(e)}")
        
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying content refresh (attempt {self.request.retries + 1})")
            raise self.retry(countdown=1800, exc=e)  # Retry after 30 minutes
        
        return {"status": "error", "message": str(e)}


@shared_task(bind=True, max_retries=2)
def monitor_seeding_health(self):
    """
    Task to monitor the health of the seeding system and alert on issues.
    
    Checks for:
    - High API failure rates
    - Low cache hit rates
    - Slow seeding response times
    - Insufficient seed content
    """
    try:
        logger.info("Starting seeding health monitoring")
        
        # Get recent metrics (last 7 days)
        recent_date = timezone.now().date() - timedelta(days=7)
        recent_metrics = SeedingMetrics.objects.filter(date__gte=recent_date)
        
        if not recent_metrics.exists():
            logger.warning("No recent seeding metrics found")
            return {"status": "warning", "message": "No recent metrics"}
        
        # Calculate averages
        avg_response_time = recent_metrics.aggregate(
            avg_time=Count('avg_seeding_response_time')
        )['avg_time'] or 0
        
        avg_cache_hit_rate = recent_metrics.aggregate(
            avg_cache=Count('cache_hit_rate')
        )['avg_cache'] or 0
        
        total_api_failures = sum(m.api_failures for m in recent_metrics)
        
        # Health checks
        health_issues = []
        
        # Check response time (should be < 500ms)
        if avg_response_time > 500:
            health_issues.append(f"High response time: {avg_response_time:.2f}ms")
        
        # Check cache hit rate (should be > 80%)
        if avg_cache_hit_rate < 80:
            health_issues.append(f"Low cache hit rate: {avg_cache_hit_rate:.2f}%")
        
        # Check API failures (should be < 10 per day)
        if total_api_failures > 70:  # 10 per day * 7 days
            health_issues.append(f"High API failure rate: {total_api_failures} in 7 days")
        
        # Check seed content availability
        active_content = SeedContentDatabase.get_active_content()
        if not active_content or active_content.total_tracks < 50:
            health_issues.append("Insufficient seed content available")
        
        # Check active curator accounts
        from seeding.models import CuratorAccount
        active_curators = CuratorAccount.objects.filter(is_active=True).count()
        if active_curators < 5:
            health_issues.append(f"Low curator count: {active_curators}")
        
        if health_issues:
            logger.warning(f"Seeding health issues detected: {', '.join(health_issues)}")
            # In a production system, this would send alerts to monitoring systems
        else:
            logger.info("Seeding system health check passed")
        
        return {
            "status": "success" if not health_issues else "warning",
            "health_issues": health_issues,
            "metrics": {
                "avg_response_time": avg_response_time,
                "avg_cache_hit_rate": avg_cache_hit_rate,
                "total_api_failures": total_api_failures,
                "active_curators": active_curators,
                "content_tracks": active_content.total_tracks if active_content else 0
            }
        }
        
    except Exception as e:
        logger.error(f"Error in seeding health monitoring: {str(e)}")
        
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying health monitoring (attempt {self.request.retries + 1})")
            raise self.retry(countdown=900, exc=e)  # Retry after 15 minutes
        
        return {"status": "error", "message": str(e)}


@shared_task
def cleanup_old_seeding_areas(days_old=30):
    """
    Task to clean up old seeding area records to prevent database bloat.
    
    Removes seeding area records older than the specified number of days
    that no longer have active seed pins.
    """
    try:
        logger.info(f"Cleaning up seeding areas older than {days_old} days")
        
        cutoff_date = timezone.now() - timedelta(days=days_old)
        
        # Find old seeding areas
        old_areas = SeedingArea.objects.filter(created_at__lt=cutoff_date)
        
        cleaned_count = 0
        for area in old_areas:
            # Check if area still has active seed pins
            active_seeds = Pin.objects.filter(
                Q(location__distance_lte=(area.center_point, D(km=area.radius_km))) &
                Q(tags__contains=['seed']) &
                (Q(expiration_date__isnull=True) | Q(expiration_date__gt=timezone.now()))
            ).exists()
            
            if not active_seeds:
                area.delete()
                cleaned_count += 1
        
        logger.info(f"Cleaned up {cleaned_count} old seeding area records")
        
        return {
            "status": "success",
            "cleaned_areas": cleaned_count
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up old seeding areas: {str(e)}")
        return {"status": "error", "message": str(e)}
