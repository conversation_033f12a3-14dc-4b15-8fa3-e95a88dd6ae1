"""
End-to-end tests for the complete music pin seeding workflow.

These tests verify the entire user journey from making a nearby pins request
to discovering seed pins, including performance requirements and edge cases.
"""

import time
from unittest.mock import patch, MagicMock
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.contrib.gis.geos import Point
from django.utils import timezone
from django.test.client import Client
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from datetime import timedelta

from pins.models import Pin
from seeding.models import SeedContentDatabase, SeedingArea, CuratorAccount
from seeding.services import SeedingService

User = get_user_model()


class EndToEndSeedingWorkflowTestCase(TransactionTestCase):
    """
    End-to-end tests for the complete seeding workflow.
    
    Tests the full user journey from API request to seed pin discovery,
    including performance requirements and real-world scenarios.
    """
    
    def setUp(self):
        self.client = APIClient()
        
        # Create test user
        self.user = User.objects.create_user(
            username="test_user",
            email="<EMAIL>",
            password="testpass123"
        )
        
        # Create curator accounts
        self.curator_user = User.objects.create_user(
            username="test_curator",
            email="<EMAIL>"
        )
        
        self.curator = CuratorAccount.objects.create(
            user=self.curator_user,
            persona_type='indie_explorer',
            preferred_genres=['indie', 'pop'],
            preferred_locations=['cafe', 'park']
        )
        
        # Create seed content database
        self.seed_content = {
            "version": "test_1.0.0",
            "content": {
                "global": [
                    {
                        "track_id": "test:track:1",
                        "title": "Test Track 1",
                        "artist": "Test Artist 1",
                        "genre": "pop",
                        "mood": "happy",
                        "track_url": "https://example.com/track1",
                        "service": "spotify"
                    },
                    {
                        "track_id": "test:track:2",
                        "title": "Test Track 2", 
                        "artist": "Test Artist 2",
                        "genre": "indie",
                        "mood": "chill",
                        "track_url": "https://example.com/track2",
                        "service": "spotify"
                    }
                ],
                "moods": {
                    "happy": [
                        {
                            "track_id": "test:track:1",
                            "title": "Test Track 1",
                            "artist": "Test Artist 1",
                            "genre": "pop",
                            "mood": "happy",
                            "track_url": "https://example.com/track1",
                            "service": "spotify"
                        }
                    ],
                    "chill": [
                        {
                            "track_id": "test:track:2",
                            "title": "Test Track 2",
                            "artist": "Test Artist 2", 
                            "genre": "indie",
                            "mood": "chill",
                            "track_url": "https://example.com/track2",
                            "service": "spotify"
                        }
                    ]
                }
            }
        }
        
        self.content_db = SeedContentDatabase.objects.create(
            version="test_1.0.0",
            content_data=self.seed_content,
            is_active=True,
            total_tracks=2
        )
        
        # Test coordinates (San Francisco)
        self.test_lat = 37.7749
        self.test_lng = -122.4194
        
        # Authenticate client
        self.client.force_authenticate(user=self.user)
    
    def test_complete_user_journey_empty_area(self):
        """Test complete user journey in an area with no existing pins"""
        # Mock location service to return test locations
        mock_locations = {
            'immediate': [
                {'lat': 37.7749, 'lng': -122.4194, 'name': 'Test Cafe', 'type': 'cafe'},
                {'lat': 37.7750, 'lng': -122.4195, 'name': 'Test Park', 'type': 'park'}
            ],
            'nearby': [
                {'lat': 37.7751, 'lng': -122.4196, 'name': 'Test Library', 'type': 'campus'}
            ],
            'city': [
                {'lat': 37.7752, 'lng': -122.4197, 'name': 'Test Landmark', 'type': 'landmark'}
            ]
        }
        
        with patch('seeding.services.location_service.LocationService.get_seed_locations') as mock_get_locations:
            mock_get_locations.return_value = mock_locations
            
            # Make nearby pins request
            url = reverse('pin-nearby')
            response = self.client.get(url, {
                'latitude': self.test_lat,
                'longitude': self.test_lng,
                'radius': 1000
            })
            
            # Should succeed
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            
            # Should return seed pins
            pins_data = response.json()
            self.assertGreater(len(pins_data), 0)
            
            # Verify seed pins were created in database
            seed_pins = Pin.objects.filter(tags__contains=['seed'])
            self.assertGreater(seed_pins.count(), 0)
            
            # Verify pins have proper metadata
            for pin in seed_pins:
                self.assertIn('seed', pin.tags)
                self.assertIsNotNone(pin.track_title)
                self.assertIsNotNone(pin.track_artist)
                self.assertIsNotNone(pin.track_url)
                self.assertEqual(pin.service, 'spotify')
                self.assertIsNotNone(pin.expiration_date)
                self.assertEqual(pin.owner, self.curator.user)
    
    def test_mixed_organic_and_seed_content(self):
        """Test that organic and seed pins are properly mixed in response"""
        # Create some organic pins first
        organic_pin = Pin.objects.create(
            owner=self.user,
            location=Point(self.test_lng, self.test_lat, srid=4326),
            title="Organic Pin",
            track_title="Organic Track",
            track_artist="Organic Artist",
            track_url="https://example.com/organic",
            service="spotify",
            tags=[]  # No seed tag
        )
        
        # Mock location service
        mock_locations = {
            'immediate': [{'lat': 37.7749, 'lng': -122.4194, 'name': 'Test Cafe', 'type': 'cafe'}],
            'nearby': [],
            'city': []
        }
        
        with patch('seeding.services.location_service.LocationService.get_seed_locations') as mock_get_locations:
            mock_get_locations.return_value = mock_locations
            
            # Make nearby pins request
            url = reverse('pin-nearby')
            response = self.client.get(url, {
                'latitude': self.test_lat,
                'longitude': self.test_lng,
                'radius': 1000
            })
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            pins_data = response.json()
            
            # Should contain both organic and seed pins
            organic_pins = [p for p in pins_data if 'seed' not in p.get('tags', [])]
            seed_pins = [p for p in pins_data if 'seed' in p.get('tags', [])]
            
            self.assertGreater(len(organic_pins), 0)
            self.assertGreater(len(seed_pins), 0)
    
    def test_seeding_performance_requirement(self):
        """Test that seeding evaluation completes within 500ms"""
        mock_locations = {
            'immediate': [{'lat': 37.7749, 'lng': -122.4194, 'name': 'Test Cafe', 'type': 'cafe'}],
            'nearby': [{'lat': 37.7750, 'lng': -122.4195, 'name': 'Test Library', 'type': 'campus'}],
            'city': [{'lat': 37.7751, 'lng': -122.4196, 'name': 'Test Landmark', 'type': 'landmark'}]
        }
        
        with patch('seeding.services.location_service.LocationService.get_seed_locations') as mock_get_locations:
            mock_get_locations.return_value = mock_locations
            
            # Measure seeding performance
            start_time = time.time()
            
            url = reverse('pin-nearby')
            response = self.client.get(url, {
                'latitude': self.test_lat,
                'longitude': self.test_lng,
                'radius': 1000
            })
            
            end_time = time.time()
            response_time_ms = (end_time - start_time) * 1000
            
            # Should complete within 500ms (being generous for test environment)
            self.assertLess(response_time_ms, 1000)  # 1 second for test environment
            self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_duplicate_seeding_prevention(self):
        """Test that areas are not seeded multiple times"""
        # Create existing seeding area
        center_point = Point(self.test_lng, self.test_lat, srid=4326)
        SeedingArea.objects.create(
            center_point=center_point,
            radius_km=5.0,
            seed_count=10
        )
        
        # Try to seed the same area
        url = reverse('pin-nearby')
        response = self.client.get(url, {
            'latitude': self.test_lat,
            'longitude': self.test_lng,
            'radius': 1000
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should not create new seed pins
        seed_pins = Pin.objects.filter(tags__contains=['seed'])
        self.assertEqual(seed_pins.count(), 0)
    
    def test_seeding_with_sufficient_organic_pins(self):
        """Test that seeding is skipped when area has sufficient organic pins"""
        center_point = Point(self.test_lng, self.test_lat, srid=4326)
        
        # Create sufficient organic pins (above threshold of 3)
        for i in range(5):
            Pin.objects.create(
                owner=self.user,
                location=center_point,
                title=f"Organic Pin {i}",
                track_title=f"Track {i}",
                track_artist="Artist",
                track_url="https://example.com/track",
                service="spotify",
                tags=[]  # No seed tag
            )
        
        url = reverse('pin-nearby')
        response = self.client.get(url, {
            'latitude': self.test_lat,
            'longitude': self.test_lng,
            'radius': 1000
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should not create seed pins
        seed_pins = Pin.objects.filter(tags__contains=['seed'])
        self.assertEqual(seed_pins.count(), 0)
    
    def test_geographic_edge_cases(self):
        """Test seeding behavior at geographic edge cases"""
        edge_cases = [
            # Near poles
            (89.0, 0.0),
            (-89.0, 0.0),
            # Near date line
            (0.0, 179.0),
            (0.0, -179.0),
            # Null Island
            (0.0, 0.0)
        ]
        
        for lat, lng in edge_cases:
            with self.subTest(lat=lat, lng=lng):
                url = reverse('pin-nearby')
                response = self.client.get(url, {
                    'latitude': lat,
                    'longitude': lng,
                    'radius': 1000
                })
                
                # Should handle gracefully without errors
                self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_seeding_graceful_degradation(self):
        """Test that seeding fails gracefully when services are unavailable"""
        # Mock location service to raise exception
        with patch('seeding.services.location_service.LocationService.get_seed_locations') as mock_get_locations:
            mock_get_locations.side_effect = Exception("Service unavailable")
            
            url = reverse('pin-nearby')
            response = self.client.get(url, {
                'latitude': self.test_lat,
                'longitude': self.test_lng,
                'radius': 1000
            })
            
            # Should still return successful response (graceful degradation)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            
            # Should return empty list or organic pins only
            pins_data = response.json()
            self.assertIsInstance(pins_data, list)
    
    def test_seed_pin_expiration(self):
        """Test that expired seed pins are not returned"""
        # Create expired seed pin
        expired_pin = Pin.objects.create(
            owner=self.curator.user,
            location=Point(self.test_lng, self.test_lat, srid=4326),
            title="Expired Seed Pin",
            track_title="Expired Track",
            track_artist="Expired Artist",
            track_url="https://example.com/expired",
            service="spotify",
            tags=['seed'],
            expiration_date=timezone.now() - timedelta(days=1)
        )
        
        url = reverse('pin-nearby')
        response = self.client.get(url, {
            'latitude': self.test_lat,
            'longitude': self.test_lng,
            'radius': 1000
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        pins_data = response.json()
        
        # Expired pin should not be in response
        pin_ids = [p['id'] for p in pins_data]
        self.assertNotIn(expired_pin.id, pin_ids)
    
    def test_invalid_coordinates(self):
        """Test handling of invalid coordinates"""
        invalid_coords = [
            (91.0, 0.0),    # Invalid latitude
            (-91.0, 0.0),   # Invalid latitude
            (0.0, 181.0),   # Invalid longitude
            (0.0, -181.0),  # Invalid longitude
            ('invalid', 0.0),  # Non-numeric
            (0.0, 'invalid')   # Non-numeric
        ]
        
        for lat, lng in invalid_coords:
            with self.subTest(lat=lat, lng=lng):
                url = reverse('pin-nearby')
                response = self.client.get(url, {
                    'latitude': lat,
                    'longitude': lng,
                    'radius': 1000
                })
                
                # Should return error for invalid coordinates
                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_seeding_area_recording(self):
        """Test that seeding areas are properly recorded"""
        mock_locations = {
            'immediate': [{'lat': 37.7749, 'lng': -122.4194, 'name': 'Test Cafe', 'type': 'cafe'}],
            'nearby': [],
            'city': []
        }
        
        with patch('seeding.services.location_service.LocationService.get_seed_locations') as mock_get_locations:
            mock_get_locations.return_value = mock_locations
            
            # Initial count
            initial_count = SeedingArea.objects.count()
            
            url = reverse('pin-nearby')
            response = self.client.get(url, {
                'latitude': self.test_lat,
                'longitude': self.test_lng,
                'radius': 1000
            })
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            
            # Should create seeding area record
            final_count = SeedingArea.objects.count()
            self.assertEqual(final_count, initial_count + 1)
            
            # Verify seeding area details
            seeding_area = SeedingArea.objects.latest('created_at')
            self.assertAlmostEqual(seeding_area.center_point.y, self.test_lat, places=4)
            self.assertAlmostEqual(seeding_area.center_point.x, self.test_lng, places=4)
            self.assertGreater(seeding_area.seed_count, 0)
