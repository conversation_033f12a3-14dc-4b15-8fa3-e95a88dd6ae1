"""
Unit tests for seeding models.

Tests the core data models used in the music pin seeding system:
- SeedContentDatabase
- SeedingArea  
- SeedingMetrics
- CuratorAccount
"""

from django.test import TestCase
from django.contrib.gis.geos import Point
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from seeding.models import SeedContentDatabase, SeedingArea, SeedingMetrics, CuratorAccount

User = get_user_model()


class SeedContentDatabaseTestCase(TestCase):
    """Test cases for SeedContentDatabase model"""
    
    def setUp(self):
        self.sample_content = {
            "version": "1.0.0",
            "content": {
                "global": [
                    {
                        "track_id": "test:track:1",
                        "title": "Test Track",
                        "artist": "Test Artist",
                        "genre": "pop",
                        "mood": "happy"
                    }
                ]
            }
        }
    
    def test_create_seed_content_database(self):
        """Test creating a seed content database"""
        content_db = SeedContentDatabase.objects.create(
            version="1.0.0",
            content_data=self.sample_content,
            total_tracks=1,
            cities_covered=0
        )
        
        self.assertEqual(content_db.version, "1.0.0")
        self.assertEqual(content_db.total_tracks, 1)
        self.assertTrue(content_db.is_active)
        self.assertIsNotNone(content_db.created_at)
    
    def test_get_active_content(self):
        """Test getting the active content database"""
        # Create inactive content
        SeedContentDatabase.objects.create(
            version="0.9.0",
            content_data=self.sample_content,
            is_active=False
        )
        
        # Create active content
        active_content = SeedContentDatabase.objects.create(
            version="1.0.0",
            content_data=self.sample_content,
            is_active=True
        )
        
        retrieved_content = SeedContentDatabase.get_active_content()
        self.assertEqual(retrieved_content.id, active_content.id)
        self.assertEqual(retrieved_content.version, "1.0.0")
    
    def test_no_active_content(self):
        """Test when no active content exists"""
        SeedContentDatabase.objects.create(
            version="1.0.0",
            content_data=self.sample_content,
            is_active=False
        )
        
        active_content = SeedContentDatabase.get_active_content()
        self.assertIsNone(active_content)


class SeedingAreaTestCase(TestCase):
    """Test cases for SeedingArea model"""
    
    def setUp(self):
        self.test_point = Point(-122.4194, 37.7749, srid=4326)  # San Francisco
    
    def test_create_seeding_area(self):
        """Test creating a seeding area"""
        area = SeedingArea.objects.create(
            center_point=self.test_point,
            radius_km=5.0,
            seed_count=10,
            city_name="San Francisco",
            country_code="US"
        )
        
        self.assertEqual(area.radius_km, 5.0)
        self.assertEqual(area.seed_count, 10)
        self.assertEqual(area.city_name, "San Francisco")
        self.assertEqual(area.country_code, "US")
        self.assertIsNotNone(area.created_at)
        self.assertIsNotNone(area.last_organic_check)
    
    def test_seeding_area_str_representation(self):
        """Test string representation of seeding area"""
        area = SeedingArea.objects.create(
            center_point=self.test_point,
            seed_count=5,
            city_name="San Francisco"
        )
        
        expected_str = "Seeded area in San Francisco (5 pins)"
        self.assertEqual(str(area), expected_str)
    
    def test_seeding_area_without_city(self):
        """Test seeding area without city name"""
        area = SeedingArea.objects.create(
            center_point=self.test_point,
            seed_count=3
        )
        
        expected_str = "Seeded area (3 pins)"
        self.assertEqual(str(area), expected_str)


class SeedingMetricsTestCase(TestCase):
    """Test cases for SeedingMetrics model"""
    
    def test_create_seeding_metrics(self):
        """Test creating seeding metrics"""
        today = timezone.now().date()
        
        metrics = SeedingMetrics.objects.create(
            date=today,
            areas_seeded=5,
            pins_generated=25,
            seed_pin_views=100,
            seed_pin_collects=15,
            avg_user_engagement=15.0
        )
        
        self.assertEqual(metrics.date, today)
        self.assertEqual(metrics.areas_seeded, 5)
        self.assertEqual(metrics.pins_generated, 25)
        self.assertEqual(metrics.seed_pin_views, 100)
        self.assertEqual(metrics.avg_user_engagement, 15.0)
    
    def test_metrics_str_representation(self):
        """Test string representation of metrics"""
        today = timezone.now().date()
        
        metrics = SeedingMetrics.objects.create(date=today)
        expected_str = f"Seeding metrics for {today}"
        self.assertEqual(str(metrics), expected_str)
    
    def test_metrics_unique_date_constraint(self):
        """Test that only one metrics record per date is allowed"""
        today = timezone.now().date()
        
        # Create first metrics record
        SeedingMetrics.objects.create(date=today, areas_seeded=1)
        
        # Try to create another for the same date
        with self.assertRaises(Exception):
            SeedingMetrics.objects.create(date=today, areas_seeded=2)


class CuratorAccountTestCase(TestCase):
    """Test cases for CuratorAccount model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username="test_curator",
            email="<EMAIL>",
            bio="Test curator bio"
        )
    
    def test_create_curator_account(self):
        """Test creating a curator account"""
        curator = CuratorAccount.objects.create(
            user=self.user,
            persona_type='indie_explorer',
            preferred_genres=['indie', 'alternative'],
            preferred_locations=['cafe', 'venue'],
            assigned_cities=['san francisco', 'oakland']
        )
        
        self.assertEqual(curator.user, self.user)
        self.assertEqual(curator.persona_type, 'indie_explorer')
        self.assertEqual(curator.preferred_genres, ['indie', 'alternative'])
        self.assertEqual(curator.preferred_locations, ['cafe', 'venue'])
        self.assertEqual(curator.assigned_cities, ['san francisco', 'oakland'])
        self.assertTrue(curator.is_active)
        self.assertEqual(curator.pins_created, 0)
        self.assertIsNone(curator.last_used)
    
    def test_curator_str_representation(self):
        """Test string representation of curator"""
        curator = CuratorAccount.objects.create(
            user=self.user,
            persona_type='coffee_enthusiast'
        )
        
        expected_str = "test_curator (Coffee Enthusiast)"
        self.assertEqual(str(curator), expected_str)
    
    def test_update_usage(self):
        """Test updating curator usage statistics"""
        curator = CuratorAccount.objects.create(
            user=self.user,
            persona_type='venue_hopper'
        )
        
        # Initial state
        self.assertEqual(curator.pins_created, 0)
        self.assertIsNone(curator.last_used)
        
        # Update usage
        curator.update_usage()
        
        # Check updated state
        self.assertEqual(curator.pins_created, 1)
        self.assertIsNotNone(curator.last_used)
        
        # Update again
        old_last_used = curator.last_used
        curator.update_usage()
        
        self.assertEqual(curator.pins_created, 2)
        self.assertGreater(curator.last_used, old_last_used)
    
    def test_curator_persona_choices(self):
        """Test that persona type choices are valid"""
        valid_personas = [
            'indie_explorer',
            'coffee_enthusiast', 
            'venue_hopper',
            'campus_curator',
            'city_wanderer',
            'music_historian',
            'genre_specialist',
            'local_guide'
        ]
        
        for persona in valid_personas:
            curator = CuratorAccount.objects.create(
                user=User.objects.create_user(
                    username=f"curator_{persona}",
                    email=f"{persona}@test.com"
                ),
                persona_type=persona
            )
            self.assertEqual(curator.persona_type, persona)
    
    def test_curator_with_empty_arrays(self):
        """Test curator with empty preference arrays"""
        curator = CuratorAccount.objects.create(
            user=self.user,
            persona_type='city_wanderer',
            preferred_genres=[],
            preferred_locations=[],
            assigned_cities=[]
        )
        
        self.assertEqual(curator.preferred_genres, [])
        self.assertEqual(curator.preferred_locations, [])
        self.assertEqual(curator.assigned_cities, [])
