"""
Unit tests for seeding services.

Tests the core business logic services:
- SeedingService
- LocationService
- ContentService
- PinGenerationService
"""

from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.contrib.gis.geos import Point
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

from pins.models import Pin
from seeding.models import SeedContentDatabase, SeedingArea, CuratorAccount
from seeding.services import SeedingService, LocationService, ContentService, PinGenerationService

User = get_user_model()


class SeedingServiceTestCase(TestCase):
    """Test cases for SeedingService"""
    
    def setUp(self):
        self.seeding_service = SeedingService()
        self.test_lat = 37.7749
        self.test_lng = -122.4194
        
        # Create test user and curator
        self.test_user = User.objects.create_user(
            username="test_user",
            email="<EMAIL>"
        )
        
        self.curator = CuratorAccount.objects.create(
            user=User.objects.create_user(
                username="test_curator",
                email="<EMAIL>"
            ),
            persona_type='indie_explorer',
            preferred_genres=['indie', 'pop'],
            preferred_locations=['cafe', 'park']
        )
    
    def test_should_seed_area_with_no_pins(self):
        """Test that empty areas should be seeded"""
        should_seed = self.seeding_service.should_seed_area(self.test_lat, self.test_lng)
        self.assertTrue(should_seed)
    
    def test_should_not_seed_area_with_sufficient_pins(self):
        """Test that areas with sufficient organic pins should not be seeded"""
        # Create organic pins in the area
        center_point = Point(self.test_lng, self.test_lat, srid=4326)
        
        for i in range(5):  # Create 5 organic pins (above threshold of 3)
            Pin.objects.create(
                owner=self.test_user,
                location=center_point,
                title=f"Test Pin {i}",
                track_title=f"Track {i}",
                track_artist="Test Artist",
                track_url="https://example.com/track",
                service="spotify",
                tags=[]  # No 'seed' tag = organic pin
            )
        
        should_seed = self.seeding_service.should_seed_area(self.test_lat, self.test_lng)
        self.assertFalse(should_seed)
    
    def test_should_not_seed_already_seeded_area(self):
        """Test that already seeded areas should not be seeded again"""
        # Create a seeding area record
        center_point = Point(self.test_lng, self.test_lat, srid=4326)
        SeedingArea.objects.create(
            center_point=center_point,
            radius_km=5.0,
            seed_count=10
        )
        
        should_seed = self.seeding_service.should_seed_area(self.test_lat, self.test_lng)
        self.assertFalse(should_seed)
    
    def test_should_seed_area_ignores_expired_pins(self):
        """Test that expired pins are ignored when checking if area needs seeding"""
        center_point = Point(self.test_lng, self.test_lat, srid=4326)
        
        # Create expired pins
        expired_date = timezone.now() - timedelta(days=1)
        for i in range(5):
            Pin.objects.create(
                owner=self.test_user,
                location=center_point,
                title=f"Expired Pin {i}",
                track_title=f"Track {i}",
                track_artist="Test Artist",
                track_url="https://example.com/track",
                service="spotify",
                expiration_date=expired_date,
                tags=[]
            )
        
        should_seed = self.seeding_service.should_seed_area(self.test_lat, self.test_lng)
        self.assertTrue(should_seed)  # Should seed because expired pins don't count
    
    def test_should_seed_area_ignores_seed_pins(self):
        """Test that existing seed pins are ignored when checking if area needs seeding"""
        center_point = Point(self.test_lng, self.test_lat, srid=4326)
        
        # Create seed pins
        for i in range(5):
            Pin.objects.create(
                owner=self.test_user,
                location=center_point,
                title=f"Seed Pin {i}",
                track_title=f"Track {i}",
                track_artist="Test Artist",
                track_url="https://example.com/track",
                service="spotify",
                tags=['seed']  # Seed pin
            )
        
        should_seed = self.seeding_service.should_seed_area(self.test_lat, self.test_lng)
        self.assertTrue(should_seed)  # Should seed because seed pins don't count as organic
    
    @patch('seeding.services.seeding_service.LocationService')
    @patch('seeding.services.seeding_service.ContentService')
    @patch('seeding.services.seeding_service.PinGenerationService')
    def test_generate_exploration_seed_pins(self, mock_pin_service, mock_content_service, mock_location_service):
        """Test generating exploration seed pins"""
        # Mock the services
        mock_location_service.return_value.get_seed_locations.return_value = {
            'immediate': [{'lat': 37.7749, 'lng': -122.4194, 'name': 'Test Cafe', 'type': 'cafe'}],
            'nearby': [{'lat': 37.7750, 'lng': -122.4195, 'name': 'Test Park', 'type': 'park'}],
            'city': [{'lat': 37.7751, 'lng': -122.4196, 'name': 'Test Venue', 'type': 'venue'}]
        }
        
        mock_content_service.return_value.get_diverse_seed_content.return_value = {
            'happy': [{'title': 'Test Track', 'artist': 'Test Artist', 'genre': 'pop', 'mood': 'happy'}]
        }
        
        # Mock pin creation
        mock_pin = Pin(
            owner=self.curator.user,
            location=Point(self.test_lng, self.test_lat, srid=4326),
            title="Test Pin",
            track_title="Test Track",
            track_artist="Test Artist",
            track_url="https://example.com/track",
            service="spotify"
        )
        mock_pin_service.return_value.create_seed_pin.return_value = mock_pin
        
        # Generate seed pins
        pins = self.seeding_service.generate_exploration_seed_pins(self.test_lat, self.test_lng)
        
        # Verify pins were created
        self.assertIsInstance(pins, list)
        # Note: Actual count depends on random selection, so we just check it's not empty
        # In a real implementation, we'd mock the random selection too


class LocationServiceTestCase(TestCase):
    """Test cases for LocationService"""
    
    def setUp(self):
        self.location_service = LocationService()
        self.test_lat = 37.7749
        self.test_lng = -122.4194
    
    def test_get_seed_locations_structure(self):
        """Test that get_seed_locations returns proper structure"""
        with patch.object(self.location_service, '_get_zone_locations') as mock_get_zone:
            mock_get_zone.return_value = [
                {'lat': 37.7749, 'lng': -122.4194, 'name': 'Test Location', 'type': 'cafe'}
            ]
            
            locations = self.location_service.get_seed_locations(self.test_lat, self.test_lng)
            
            self.assertIn('immediate', locations)
            self.assertIn('nearby', locations)
            self.assertIn('city', locations)
            self.assertIsInstance(locations['immediate'], list)
            self.assertIsInstance(locations['nearby'], list)
            self.assertIsInstance(locations['city'], list)
    
    def test_categorize_location_type(self):
        """Test location type categorization"""
        # Test cafe categorization
        cafe_types = ['cafe', 'restaurant']
        result = self.location_service._categorize_location_type(cafe_types)
        self.assertEqual(result, 'cafe')
        
        # Test park categorization
        park_types = ['park', 'establishment']
        result = self.location_service._categorize_location_type(park_types)
        self.assertEqual(result, 'park')
        
        # Test unknown types
        unknown_types = ['unknown_type']
        result = self.location_service._categorize_location_type(unknown_types)
        self.assertEqual(result, 'landmark')
    
    def test_calculate_distance(self):
        """Test distance calculation between two points"""
        lat1, lng1 = 37.7749, -122.4194  # San Francisco
        lat2, lng2 = 37.7849, -122.4094  # ~1.5km away
        
        distance = self.location_service._calculate_distance(lat1, lng1, lat2, lng2)
        
        # Should be approximately 1500 meters
        self.assertGreater(distance, 1000)
        self.assertLess(distance, 2000)
    
    def test_deduplicate_locations(self):
        """Test location deduplication"""
        locations = [
            {'lat': 37.7749, 'lng': -122.4194, 'name': 'Location 1'},
            {'lat': 37.7749, 'lng': -122.4194, 'name': 'Location 2'},  # Duplicate
            {'lat': 37.7849, 'lng': -122.4094, 'name': 'Location 3'},  # Different
        ]
        
        unique_locations = self.location_service._deduplicate_locations(locations)
        
        # Should remove the duplicate
        self.assertEqual(len(unique_locations), 2)
        self.assertEqual(unique_locations[0]['name'], 'Location 1')
        self.assertEqual(unique_locations[1]['name'], 'Location 3')


class ContentServiceTestCase(TestCase):
    """Test cases for ContentService"""
    
    def setUp(self):
        self.content_service = ContentService()
        self.test_lat = 37.7749
        self.test_lng = -122.4194
        
        # Create test content database
        self.test_content = {
            "content": {
                "global": [
                    {
                        "track_id": "test:1",
                        "title": "Test Track 1",
                        "artist": "Artist 1",
                        "genre": "pop",
                        "mood": "happy"
                    },
                    {
                        "track_id": "test:2", 
                        "title": "Test Track 2",
                        "artist": "Artist 2",
                        "genre": "rock",
                        "mood": "energetic"
                    }
                ],
                "genres": {
                    "pop": [
                        {
                            "track_id": "test:1",
                            "title": "Test Track 1",
                            "artist": "Artist 1",
                            "genre": "pop",
                            "mood": "happy"
                        }
                    ]
                }
            }
        }
        
        self.content_db = SeedContentDatabase.objects.create(
            version="1.0.0",
            content_data=self.test_content,
            is_active=True,
            total_tracks=2
        )
    
    def test_get_diverse_seed_content_structure(self):
        """Test that diverse seed content returns proper structure"""
        content = self.content_service.get_diverse_seed_content(self.test_lat, self.test_lng)
        
        # Should return content organized by mood
        expected_moods = ['chill', 'energetic', 'happy', 'focus', 'party', 'romantic', 'workout']
        for mood in expected_moods:
            self.assertIn(mood, content)
            self.assertIsInstance(content[mood], list)
    
    def test_get_city_music_profile(self):
        """Test getting city music profile"""
        # Test known city
        with patch.object(self.content_service, '_get_city_music_profile') as mock_get_profile:
            mock_get_profile.return_value = {
                'popular_genres': ['pop', 'rock'],
                'genre_weights': {'pop': 0.5, 'rock': 0.5},
                'cultural_context': 'diverse'
            }
            
            profile = self.content_service._get_city_music_profile(self.test_lat, self.test_lng)
            
            self.assertIn('popular_genres', profile)
            self.assertIn('genre_weights', profile)
            self.assertIn('cultural_context', profile)
    
    def test_enforce_genre_diversity(self):
        """Test genre diversity enforcement"""
        tracks = [
            {'genre': 'pop', 'title': 'Pop Track 1'},
            {'genre': 'pop', 'title': 'Pop Track 2'},
            {'genre': 'pop', 'title': 'Pop Track 3'},
            {'genre': 'rock', 'title': 'Rock Track 1'},
        ]
        
        # With target count of 3, no genre should exceed 60% (1.8 tracks)
        # So max 1 track per genre in this case
        diverse_tracks = self.content_service._enforce_genre_diversity(tracks, 3)
        
        # Count genres in result
        genre_counts = {}
        for track in diverse_tracks:
            genre = track['genre']
            genre_counts[genre] = genre_counts.get(genre, 0) + 1
        
        # No genre should exceed 60% of total
        max_allowed = int(3 * 0.6)  # 1.8 -> 1
        for count in genre_counts.values():
            self.assertLessEqual(count, max_allowed + 1)  # Allow some rounding
    
    def test_get_content_for_location_type(self):
        """Test getting content for specific location types"""
        content = {
            'chill': [{'title': 'Chill Track'}],
            'energetic': [{'title': 'Energetic Track'}],
            'happy': [{'title': 'Happy Track'}]
        }
        
        # Test cafe (should get chill, focus, happy)
        cafe_content = self.content_service.get_content_for_location_type(content, 'cafe')
        self.assertGreater(len(cafe_content), 0)
        
        # Test venue (should get energetic, party, workout)
        venue_content = self.content_service.get_content_for_location_type(content, 'venue')
        self.assertGreater(len(venue_content), 0)
    
    def test_fallback_content(self):
        """Test fallback content when database is unavailable"""
        # Temporarily deactivate content database
        self.content_db.is_active = False
        self.content_db.save()
        
        content = self.content_service.get_diverse_seed_content(self.test_lat, self.test_lng)
        
        # Should still return structured content
        self.assertIn('happy', content)
        self.assertGreater(len(content['happy']), 0)
        
        # Restore content database
        self.content_db.is_active = True
        self.content_db.save()


class PinGenerationServiceTestCase(TestCase):
    """Test cases for PinGenerationService"""
    
    def setUp(self):
        self.pin_service = PinGenerationService()
        
        # Create test curator
        self.curator_user = User.objects.create_user(
            username="test_curator",
            email="<EMAIL>"
        )
        
        self.curator = CuratorAccount.objects.create(
            user=self.curator_user,
            persona_type='indie_explorer',
            preferred_genres=['indie', 'pop'],
            preferred_locations=['cafe', 'park']
        )
        
        self.test_location = {
            'lat': 37.7749,
            'lng': -122.4194,
            'name': 'Test Cafe',
            'type': 'cafe'
        }
        
        self.test_content = {
            'happy': [
                {
                    'title': 'Test Track',
                    'artist': 'Test Artist',
                    'genre': 'pop',
                    'mood': 'happy',
                    'track_url': 'https://example.com/track',
                    'service': 'spotify'
                }
            ]
        }
    
    def test_generate_contextual_description(self):
        """Test contextual description generation"""
        track = {
            'title': 'Test Track',
            'mood': 'happy'
        }
        
        description = self.pin_service._generate_contextual_description(
            track, 'cafe', 'Test Cafe'
        )
        
        self.assertIsInstance(description, str)
        self.assertGreater(len(description), 0)
        # Should contain some context about the location or mood
        self.assertTrue(any(word in description.lower() for word in ['cafe', 'happy', 'test']))
    
    def test_get_curator_for_location_matching(self):
        """Test curator selection based on location and track matching"""
        # Create curator with matching preferences
        matching_curator = CuratorAccount.objects.create(
            user=User.objects.create_user(
                username="matching_curator",
                email="<EMAIL>"
            ),
            persona_type='coffee_enthusiast',
            preferred_genres=['pop'],
            preferred_locations=['cafe']
        )
        
        track = {'genre': 'pop'}
        location = {'type': 'cafe'}
        
        curator = self.pin_service._get_curator_for_location(location, track)
        
        # Should prefer the matching curator
        self.assertIsNotNone(curator)
        self.assertIn('cafe', curator.preferred_locations)
        self.assertIn('pop', curator.preferred_genres)
    
    def test_create_default_curator(self):
        """Test creating default curator when none exist"""
        # Remove existing curators
        CuratorAccount.objects.all().delete()
        User.objects.filter(username="music_explorer").delete()
        
        curator = self.pin_service._create_default_curator()
        
        self.assertIsNotNone(curator)
        self.assertEqual(curator.user.username, "music_explorer")
        self.assertEqual(curator.persona_type, 'city_wanderer')
        self.assertTrue(curator.is_active)


class PinSeedingExtensionsTestCase(TestCase):
    """Test cases for Pin model seeding extensions"""

    def setUp(self):
        self.user = User.objects.create_user(
            username="test_user",
            email="<EMAIL>"
        )

        self.test_location = Point(-122.4194, 37.7749, srid=4326)

    def test_is_seed_property(self):
        """Test is_seed property"""
        # Create organic pin
        organic_pin = Pin.objects.create(
            owner=self.user,
            location=self.test_location,
            title="Organic Pin",
            track_title="Track",
            track_artist="Artist",
            track_url="https://example.com/track",
            service="spotify",
            tags=[]
        )

        # Create seed pin
        seed_pin = Pin.objects.create(
            owner=self.user,
            location=self.test_location,
            title="Seed Pin",
            track_title="Track",
            track_artist="Artist",
            track_url="https://example.com/track",
            service="spotify",
            tags=['seed']
        )

        self.assertFalse(organic_pin.is_seed)
        self.assertTrue(seed_pin.is_seed)

    def test_is_expired_property(self):
        """Test is_expired property"""
        # Create non-expiring pin
        permanent_pin = Pin.objects.create(
            owner=self.user,
            location=self.test_location,
            title="Permanent Pin",
            track_title="Track",
            track_artist="Artist",
            track_url="https://example.com/track",
            service="spotify"
        )

        # Create expired pin
        expired_pin = Pin.objects.create(
            owner=self.user,
            location=self.test_location,
            title="Expired Pin",
            track_title="Track",
            track_artist="Artist",
            track_url="https://example.com/track",
            service="spotify",
            expiration_date=timezone.now() - timedelta(days=1)
        )

        # Create future-expiring pin
        future_pin = Pin.objects.create(
            owner=self.user,
            location=self.test_location,
            title="Future Pin",
            track_title="Track",
            track_artist="Artist",
            track_url="https://example.com/track",
            service="spotify",
            expiration_date=timezone.now() + timedelta(days=1)
        )

        self.assertFalse(permanent_pin.is_expired)
        self.assertTrue(expired_pin.is_expired)
        self.assertFalse(future_pin.is_expired)

    def test_mark_as_seed(self):
        """Test marking pin as seed"""
        pin = Pin.objects.create(
            owner=self.user,
            location=self.test_location,
            title="Test Pin",
            track_title="Track",
            track_artist="Artist",
            track_url="https://example.com/track",
            service="spotify",
            tags=[]
        )

        # Mark as seed
        pin.mark_as_seed('immediate', 'cafe')

        self.assertIn('seed', pin.tags)
        self.assertIn('immediate', pin.tags)
        self.assertIn('cafe', pin.tags)
        self.assertTrue(pin.is_seed)

    def test_get_seed_pins_queryset(self):
        """Test get_seed_pins class method"""
        # Create mix of pins
        organic_pin = Pin.objects.create(
            owner=self.user,
            location=self.test_location,
            title="Organic Pin",
            track_title="Track",
            track_artist="Artist",
            track_url="https://example.com/track",
            service="spotify",
            tags=[]
        )

        seed_pin = Pin.objects.create(
            owner=self.user,
            location=self.test_location,
            title="Seed Pin",
            track_title="Track",
            track_artist="Artist",
            track_url="https://example.com/track",
            service="spotify",
            tags=['seed']
        )

        seed_pins = Pin.get_seed_pins()
        organic_pins = Pin.get_organic_pins()

        self.assertIn(seed_pin, seed_pins)
        self.assertNotIn(organic_pin, seed_pins)
        self.assertIn(organic_pin, organic_pins)
        self.assertNotIn(seed_pin, organic_pins)

    def test_get_expired_seeds_queryset(self):
        """Test get_expired_seeds class method"""
        # Create expired seed pin
        expired_seed = Pin.objects.create(
            owner=self.user,
            location=self.test_location,
            title="Expired Seed",
            track_title="Track",
            track_artist="Artist",
            track_url="https://example.com/track",
            service="spotify",
            tags=['seed'],
            expiration_date=timezone.now() - timedelta(days=1)
        )

        # Create active seed pin
        active_seed = Pin.objects.create(
            owner=self.user,
            location=self.test_location,
            title="Active Seed",
            track_title="Track",
            track_artist="Artist",
            track_url="https://example.com/track",
            service="spotify",
            tags=['seed'],
            expiration_date=timezone.now() + timedelta(days=1)
        )

        expired_seeds = Pin.get_expired_seeds()

        self.assertIn(expired_seed, expired_seeds)
        self.assertNotIn(active_seed, expired_seeds)
