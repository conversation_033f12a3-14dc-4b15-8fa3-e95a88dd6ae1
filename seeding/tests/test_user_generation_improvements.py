"""
Tests for user generation improvements in the seeding system.

This test suite validates:
- Diverse name generation across cultures
- Realistic username generation with variety
- Avatar URL uniqueness using pravatar.cc
- Proper user assignment distribution in pin creation
- Overall user profile diversity
"""

import unittest
from datetime import datetime, timedelta
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.db.models import F
from django.utils import timezone
from unittest.mock import patch, MagicMock

from seeding.services.user_generation_service import UserGenerationService
from seeding.services.profile_picture_service import ProfilePictureService
from seeding.services.pin_generation_service import PinGenerationService
from seeding.models import CuratorAccount
from pins.models import Pin

User = get_user_model()


class UserGenerationServiceTest(TestCase):
    """Test the UserGenerationService for diversity and realism"""
    
    def setUp(self):
        self.service = UserGenerationService()
    
    def test_diverse_name_generation(self):
        """Test that names are generated from diverse cultural backgrounds"""
        generated_names = []
        cultures_found = set()
        
        # Generate 50 names to test diversity
        for _ in range(50):
            first_name, last_name = self.service.generate_diverse_name()
            generated_names.append((first_name, last_name))
            
            # Check if name appears in any culture
            for culture, names_dict in self.service.FIRST_NAMES.items():
                for style, names in names_dict.items():
                    if first_name in names:
                        cultures_found.add(culture)
        
        # Should have names from multiple cultures
        self.assertGreaterEqual(len(cultures_found), 3, "Should generate names from at least 3 different cultures")
        
        # All names should be unique or mostly unique
        unique_names = set(generated_names)
        uniqueness_ratio = len(unique_names) / len(generated_names)
        self.assertGreater(uniqueness_ratio, 0.8, "Should have high name diversity")
    
    def test_realistic_username_generation(self):
        """Test that usernames follow realistic patterns and are diverse"""
        usernames = []
        
        # Generate usernames for different names
        test_names = [
            ("Alex", "Smith"), ("Maria", "Garcia"), ("Chen", "Li"), 
            ("Omar", "Hassan"), ("Emma", "Johnson"), ("Kai", "Tanaka")
        ]
        
        for first_name, last_name in test_names:
            for _ in range(5):  # Generate 5 usernames per name
                username = self.service.generate_realistic_username(first_name, last_name)
                usernames.append(username)
                
                # Username should be lowercase and contain valid characters
                self.assertTrue(username.islower(), f"Username {username} should be lowercase")
                self.assertRegex(username, r'^[a-z0-9._-]+$', f"Username {username} should contain only valid characters")
        
        # Should have high diversity
        unique_usernames = set(usernames)
        uniqueness_ratio = len(unique_usernames) / len(usernames)
        self.assertGreater(uniqueness_ratio, 0.9, "Should have high username diversity")
    
    def test_avatar_url_generation(self):
        """Test that avatar URLs are unique and properly formatted"""
        avatars = []
        
        # Generate avatars with different identifiers
        for i in range(20):
            unique_id = f"test_user_{i}"
            avatar_url = self.service.generate_avatar_url(unique_id)
            avatars.append(avatar_url)
            
            # Should be a valid i.pravatar.cc URL
            self.assertTrue(avatar_url.startswith("https://i.pravatar.cc/150?u="))
            self.assertIn("?u=", avatar_url)
        
        # All avatars should be unique
        unique_avatars = set(avatars)
        self.assertEqual(len(unique_avatars), len(avatars), "All avatar URLs should be unique")
    
    def test_bio_generation_diversity(self):
        """Test that bios are diverse and persona-appropriate"""
        persona_types = ['indie_explorer', 'coffee_enthusiast', 'venue_hopper', 'campus_curator']
        
        for persona_type in persona_types:
            bios = []
            for _ in range(10):
                bio = self.service.generate_bio(persona_type, "TestName")
                bios.append(bio)
                
                # Bio should contain the name
                self.assertIn("TestName", bio)
                
                # Bio should be reasonably long
                self.assertGreater(len(bio), 30, "Bio should be descriptive")
            
            # Should have some diversity in bios for same persona
            unique_bios = set(bios)
            self.assertGreater(len(unique_bios), 1, f"Should have bio diversity for {persona_type}")
    
    def test_complete_user_profile_generation(self):
        """Test complete user profile generation"""
        profile = self.service.generate_user_profile(
            persona_type='indie_explorer',
            preferred_genres=['indie', 'alternative'],
            preferred_locations=['cafe', 'venue']
        )
        
        # Check all required fields are present
        required_fields = ['username', 'first_name', 'last_name', 'email', 'bio', 'profile_pic']
        for field in required_fields:
            self.assertIn(field, profile)
            self.assertIsNotNone(profile[field])
            self.assertNotEqual(profile[field], "")
        
        # Check email format
        self.assertIn("@bopmaps.com", profile['email'])
        
        # Check avatar URL format
        self.assertTrue(profile['profile_pic'].startswith("https://pravatar.cc/150?u="))


class ProfilePictureServiceTest(TestCase):
    """Test the ProfilePictureService pravatar.cc integration"""
    
    def setUp(self):
        self.service = ProfilePictureService()
    
    def test_pravatar_url_generation(self):
        """Test pravatar URL generation with unique identifiers"""
        # Test with different identifiers
        identifiers = ["user1_indie_explorer_123", "user2_coffee_enthusiast_456"]
        urls = []
        
        for identifier in identifiers:
            url = self.service.generate_pravatar_url(identifier)
            urls.append(url)
            
            # Should be valid i.pravatar URL
            self.assertTrue(url.startswith("https://i.pravatar.cc/150?u="))
        
        # URLs should be different for different identifiers
        self.assertNotEqual(urls[0], urls[1])
        
        # Same identifier should produce same URL
        url1 = self.service.generate_pravatar_url("test_id")
        url2 = self.service.generate_pravatar_url("test_id")
        self.assertEqual(url1, url2)


class CuratorSelectionTest(TestCase):
    """Test improved curator selection and rotation"""
    
    def setUp(self):
        # Create test curators with different usage patterns
        self.user_gen_service = UserGenerationService()
        self.curators = []
        
        for i in range(5):
            profile = self.user_gen_service.generate_user_profile(
                persona_type='indie_explorer',
                preferred_genres=['electronic', 'indie'],
                preferred_locations=['cafe', 'venue']
            )
            
            user = User.objects.create_user(
                username=f"test_curator_{i}",
                email=profile['email'],
                first_name=profile['first_name'],
                last_name=profile['last_name'],
                bio=profile['bio'],
                profile_pic=profile['profile_pic']
            )
            
            curator = CuratorAccount.objects.create(
                user=user,
                persona_type='indie_explorer',
                preferred_genres=['electronic', 'indie'],
                preferred_locations=['cafe', 'venue']
            )
            
            # Set different usage patterns
            if i < 2:
                # These curators have never been used (last_used = None)
                pass
            else:
                # These curators have been used
                curator.pins_created = i * 10
                curator.last_used = timezone.now() - timedelta(days=i)
                curator.save()
            
            self.curators.append(curator)
    
    def test_curator_selection_prioritizes_unused(self):
        """Test that unused curators (last_used=None) are selected first"""
        from seeding.services.pin_generation_service import PinGenerationService
        
        service = PinGenerationService()
        
        # Mock content
        content = {'genre': 'electronic', 'title': 'Test Track', 'artist': 'Test Artist'}
        
        # Select curator multiple times
        selected_curators = []
        for _ in range(3):
            curator = service._select_curator_for_content(content)
            selected_curators.append(curator)
            
            # Update usage to simulate pin creation
            if curator:
                curator.update_usage()
        
        # First selections should be the unused curators
        unused_curators = [c for c in self.curators if c.last_used is None]
        
        # At least the first selection should be from unused curators
        self.assertIn(selected_curators[0], unused_curators, 
                     "First selection should prioritize unused curators")


class IntegrationTest(TestCase):
    """Integration test for the complete improved seeding system"""
    
    def test_create_diverse_curators_command(self):
        """Test the create_curators management command with improvements"""
        from django.core.management import call_command
        from io import StringIO
        
        # Capture command output
        out = StringIO()
        
        # Create 5 diverse curators
        call_command('create_curators', '--count=5', '--force', stdout=out)
        
        # Check that curators were created
        curators = CuratorAccount.objects.all()
        self.assertEqual(curators.count(), 5)
        
        # Check diversity in names
        first_names = [c.user.first_name for c in curators]
        last_names = [c.user.last_name for c in curators]
        usernames = [c.user.username for c in curators]
        
        # Should have unique usernames
        self.assertEqual(len(set(usernames)), len(usernames), "All usernames should be unique")
        
        # Should have diverse names (not all the same)
        self.assertGreater(len(set(first_names)), 1, "Should have diverse first names")
        self.assertGreater(len(set(last_names)), 1, "Should have diverse last names")
        
        # All should have profile pictures
        for curator in curators:
            self.assertIsNotNone(curator.user.profile_pic)
            self.assertTrue(curator.user.profile_pic.startswith("https://pravatar.cc/"))
        
        # All should have realistic bios
        for curator in curators:
            self.assertIsNotNone(curator.user.bio)
            self.assertGreater(len(curator.user.bio), 30)
