from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Q
from django.utils import timezone
from .models import SpotifyCacheEntry, SpotifyApiUsage, SpotifyRateLimit, SpotifyUserCache, SpotifyTokenUsage


@admin.register(SpotifyCacheEntry)
class SpotifyCacheEntryAdmin(admin.ModelAdmin):
    """Admin interface for Spotify cache entries"""
    
    list_display = ['cache_key_short', 'endpoint', 'user_specific', 'hit_count', 
                   'created_at', 'expires_at', 'is_expired_display', 'last_accessed']
    list_filter = ['endpoint', 'user_specific', 'created_at', 'expires_at']
    search_fields = ['cache_key', 'endpoint', 'query_hash']
    readonly_fields = ['cache_key', 'endpoint', 'query_hash', 'created_at', 'updated_at', 
                      'last_accessed', 'is_expired']
    ordering = ['-last_accessed']
    
    fieldsets = (
        ('Cache Information', {
            'fields': ('cache_key', 'endpoint', 'query_hash', 'user_specific')
        }),
        ('Usage Statistics', {
            'fields': ('hit_count', 'last_accessed')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'expires_at', 'is_expired')
        }),
        ('Response Data', {
            'fields': ('response_data',),
            'classes': ('collapse',),
        }),
    )
    
    def cache_key_short(self, obj):
        """Display shortened cache key"""
        return obj.cache_key[:50] + '...' if len(obj.cache_key) > 50 else obj.cache_key
    cache_key_short.short_description = 'Cache Key'
    
    def is_expired_display(self, obj):
        """Display expiration status with color"""
        if obj.is_expired:
            return format_html('<span style="color: red;">Expired</span>')
        else:
            return format_html('<span style="color: green;">Active</span>')
    is_expired_display.short_description = 'Status'
    
    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related()
    
    actions = ['delete_expired_entries', 'clear_hit_counts']
    
    def delete_expired_entries(self, request, queryset):
        """Delete expired cache entries"""
        expired_entries = queryset.filter(expires_at__lt=timezone.now())
        count = expired_entries.count()
        expired_entries.delete()
        self.message_user(request, f'Deleted {count} expired cache entries.')
    delete_expired_entries.short_description = 'Delete expired entries'
    
    def clear_hit_counts(self, request, queryset):
        """Reset hit counts to zero"""
        count = queryset.update(hit_count=0)
        self.message_user(request, f'Reset hit counts for {count} cache entries.')
    clear_hit_counts.short_description = 'Clear hit counts'


@admin.register(SpotifyUserCache)
class SpotifyUserCacheAdmin(admin.ModelAdmin):
    """Admin interface for Spotify user cache entries"""
    
    list_display = ['cache_key_short', 'endpoint', 'user_hash_short', 'hit_count', 
                   'created_at', 'expires_at', 'is_expired_display', 'last_accessed']
    list_filter = ['endpoint', 'created_at', 'expires_at']
    search_fields = ['cache_key', 'endpoint', 'user_hash']
    readonly_fields = ['cache_key', 'endpoint', 'user_hash', 'created_at', 'updated_at', 
                      'last_accessed', 'is_expired']
    ordering = ['-last_accessed']
    
    fieldsets = (
        ('Cache Information', {
            'fields': ('cache_key', 'endpoint', 'user_hash')
        }),
        ('Usage Statistics', {
            'fields': ('hit_count', 'last_accessed')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'expires_at', 'is_expired')
        }),
        ('Response Data', {
            'fields': ('response_data',),
            'classes': ('collapse',),
        }),
    )
    
    def cache_key_short(self, obj):
        """Display shortened cache key"""
        return obj.cache_key[:50] + '...' if len(obj.cache_key) > 50 else obj.cache_key
    cache_key_short.short_description = 'Cache Key'
    
    def user_hash_short(self, obj):
        """Display shortened user hash"""
        return obj.user_hash[:10] + '...'
    user_hash_short.short_description = 'User Hash'
    
    def is_expired_display(self, obj):
        """Display expiration status with color"""
        if obj.is_expired:
            return format_html('<span style="color: red;">Expired</span>')
        else:
            return format_html('<span style="color: green;">Active</span>')
    is_expired_display.short_description = 'Status'
    
    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related()
    
    actions = ['delete_expired_entries', 'clear_hit_counts']
    
    def delete_expired_entries(self, request, queryset):
        """Delete expired cache entries"""
        expired_entries = queryset.filter(expires_at__lt=timezone.now())
        count = expired_entries.count()
        expired_entries.delete()
        self.message_user(request, f'Deleted {count} expired user cache entries.')
    delete_expired_entries.short_description = 'Delete expired entries'
    
    def clear_hit_counts(self, request, queryset):
        """Reset hit counts to zero"""
        count = queryset.update(hit_count=0)
        self.message_user(request, f'Reset hit counts for {count} user cache entries.')
    clear_hit_counts.short_description = 'Clear hit counts'


@admin.register(SpotifyApiUsage)
class SpotifyApiUsageAdmin(admin.ModelAdmin):
    """Admin interface for Spotify API usage statistics"""
    
    list_display = ['date', 'endpoint', 'total_requests', 'cache_hits', 'cache_misses', 
                   'cache_hit_rate_display', 'api_calls_made', 'average_response_time', 'unique_users']
    list_filter = ['date', 'endpoint']
    search_fields = ['endpoint']
    readonly_fields = ['date', 'endpoint', 'cache_hit_rate']
    ordering = ['-date', 'endpoint']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('date', 'endpoint')
        }),
        ('Request Statistics', {
            'fields': ('total_requests', 'cache_hits', 'cache_misses', 'cache_hit_rate', 'unique_users')
        }),
        ('API Performance', {
            'fields': ('api_calls_made', 'average_response_time')
        }),
    )
    
    def cache_hit_rate_display(self, obj):
        """Display cache hit rate with color coding"""
        rate = obj.cache_hit_rate
        if rate >= 80:
            color = 'green'
        elif rate >= 60:
            color = 'orange'
        else:
            color = 'red'
        return format_html(f'<span style="color: {color};">{rate:.1f}%</span>')
    cache_hit_rate_display.short_description = 'Cache Hit Rate'
    
    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related()


@admin.register(SpotifyRateLimit)
class SpotifyRateLimitAdmin(admin.ModelAdmin):
    """Admin interface for Spotify rate limiting"""
    
    list_display = ['window_start', 'endpoint', 'user_ip', 'requests_made', 'is_current_window']
    list_filter = ['window_start', 'endpoint']
    search_fields = ['endpoint', 'user_ip']
    readonly_fields = ['window_start', 'endpoint', 'user_ip', 'requests_made']
    ordering = ['-window_start']
    
    def is_current_window(self, obj):
        """Check if this is the current rate limit window"""
        now = timezone.now()
        current_window = now.replace(second=0, microsecond=0)
        return obj.window_start == current_window
    is_current_window.boolean = True
    is_current_window.short_description = 'Current Window'
    
    def get_queryset(self, request):
        """Optimize queryset and limit to recent windows"""
        from datetime import timedelta
        recent_time = timezone.now() - timedelta(hours=24)
        return super().get_queryset(request).filter(
            window_start__gte=recent_time
        ).select_related()
    
    def has_add_permission(self, request):
        """Disable manual addition of rate limit records"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Disable manual editing of rate limit records"""
        return False


@admin.register(SpotifyTokenUsage)
class SpotifyTokenUsageAdmin(admin.ModelAdmin):
    """Admin interface for Spotify token usage statistics"""
    
    list_display = ['token_hash_short', 'first_used', 'last_used', 'request_count', 
                   'successful_requests', 'failed_requests', 'success_rate_display', 'is_active']
    list_filter = ['is_active', 'first_used', 'last_used']
    search_fields = ['token_hash']
    readonly_fields = ['token_hash', 'first_used', 'last_used', 'request_count', 
                      'successful_requests', 'failed_requests', 'success_rate']
    ordering = ['-last_used']
    
    fieldsets = (
        ('Token Information', {
            'fields': ('token_hash', 'is_active')
        }),
        ('Usage Statistics', {
            'fields': ('request_count', 'successful_requests', 'failed_requests', 'success_rate')
        }),
        ('Timestamps', {
            'fields': ('first_used', 'last_used', 'token_expired_at')
        }),
    )
    
    def token_hash_short(self, obj):
        """Display shortened token hash"""
        return obj.token_hash[:10] + '...'
    token_hash_short.short_description = 'Token Hash'
    
    def success_rate_display(self, obj):
        """Display success rate with color coding"""
        rate = obj.success_rate
        if rate >= 95:
            color = 'green'
        elif rate >= 85:
            color = 'orange'
        else:
            color = 'red'
        return format_html(f'<span style="color: {color};">{rate:.1f}%</span>')
    success_rate_display.short_description = 'Success Rate'
    
    def get_queryset(self, request):
        """Optimize queryset and limit to recent tokens"""
        from datetime import timedelta
        recent_time = timezone.now() - timedelta(days=30)
        return super().get_queryset(request).filter(
            last_used__gte=recent_time
        ).select_related()
    
    def has_add_permission(self, request):
        """Disable manual addition of token records"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Disable manual editing of token records"""
        return False
    
    actions = ['mark_tokens_inactive']
    
    def mark_tokens_inactive(self, request, queryset):
        """Mark selected tokens as inactive"""
        count = queryset.update(is_active=False)
        self.message_user(request, f'Marked {count} tokens as inactive.')
    mark_tokens_inactive.short_description = 'Mark tokens as inactive'


# Customizations for the admin site
admin.site.site_header = "BOPMaps Spotify Cache Administration"
admin.site.site_title = "Spotify Cache Admin"
admin.site.index_title = "Welcome to Spotify Cache Administration"
