"""
Efficient cache manager for Spotify API responses
"""
import logging
import hashlib
import threading
from typing import Dict, Any, Optional
from datetime import timedelta
from django.core.cache import cache
from django.utils import timezone
from django.db import transaction
from .models import SpotifyCacheEntry, SpotifyUserCache

logger = logging.getLogger(__name__)


class SpotifyCacheManager:
    """
    Efficient cache manager that prioritizes Redis and minimizes database connections
    """
    
    def __init__(self):
        self.endpoint_configs = {
            '/search': {'ttl': 1800, 'user_specific': False},  # 30 minutes
            '/tracks': {'ttl': 3600, 'user_specific': False},  # 1 hour
            '/artists': {'ttl': 3600, 'user_specific': False},  # 1 hour
            '/albums': {'ttl': 3600, 'user_specific': False},  # 1 hour
            '/playlists': {'ttl': 1800, 'user_specific': False},  # 30 minutes
            '/me': {'ttl': 300, 'user_specific': True},  # 5 minutes
            '/me/top': {'ttl': 3600, 'user_specific': True},  # 1 hour
            '/me/playlists': {'ttl': 600, 'user_specific': True},  # 10 minutes
        }
        self._db_write_queue = []
        self._db_write_lock = threading.Lock()
    
    def get_endpoint_config(self, endpoint: str) -> Dict[str, Any]:
        """Get configuration for endpoint"""
        for pattern, config in self.endpoint_configs.items():
            if pattern in endpoint:
                return config
        return {'ttl': 1800, 'user_specific': False}  # Default: 30 minutes
    
    def generate_cache_key(self, endpoint: str, params: Dict[str, Any], 
                          access_token: Optional[str] = None) -> str:
        """Generate cache key for request"""
        # Create a consistent cache key
        key_parts = [endpoint]
        
        if params:
            sorted_params = sorted(params.items())
            params_str = '&'.join([f"{k}={v}" for k, v in sorted_params])
            key_parts.append(params_str)
        
        config = self.get_endpoint_config(endpoint)
        if config['user_specific'] and access_token:
            # Use hash of token for privacy
            token_hash = hashlib.sha256(access_token.encode()).hexdigest()[:16]
            key_parts.append(token_hash)
        
        cache_key = hashlib.sha1('|'.join(key_parts).encode()).hexdigest()
        return f"spotify:{cache_key}"
    
    def get_from_cache(self, cache_key: str, endpoint: str, 
                      access_token: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get data from cache (Redis first, database fallback)"""
        # Try Redis first (fastest)
        try:
            cached_data = cache.get(cache_key)
            if cached_data:
                logger.debug(f"Cache hit (Redis) for {cache_key}")
                return cached_data
        except Exception as e:
            logger.warning(f"Redis cache error: {e}")
        
        # Try database cache as fallback (only if Redis fails)
        return self._get_from_database_cache(cache_key, endpoint, access_token)
    
    def _get_from_database_cache(self, cache_key: str, endpoint: str, 
                                access_token: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get data from database cache"""
        try:
            config = self.get_endpoint_config(endpoint)
            
            if config['user_specific'] and access_token:
                user_hash = hashlib.sha256(access_token.encode()).hexdigest()[:16]
                cache_entry = SpotifyUserCache.objects.select_related().get(
                    cache_key=cache_key,
                    user_hash=user_hash,
                    expires_at__gt=timezone.now()
                )
            else:
                cache_entry = SpotifyCacheEntry.objects.get(
                    cache_key=cache_key,
                    expires_at__gt=timezone.now()
                )
            
            # Update hit count and restore to Redis
            self._update_cache_hit_async(cache_entry)
            
            # Store back in Redis for faster future access
            try:
                cache.set(cache_key, cache_entry.response_data, config['ttl'])
            except Exception as e:
                logger.warning(f"Failed to restore to Redis: {e}")
            
            logger.debug(f"Cache hit (Database) for {cache_key}")
            return cache_entry.response_data
            
        except (SpotifyCacheEntry.DoesNotExist, SpotifyUserCache.DoesNotExist):
            return None
        except Exception as e:
            logger.error(f"Database cache error: {e}")
            return None
    
    def store_in_cache(self, cache_key: str, endpoint: str, data: Dict[str, Any],
                      access_token: Optional[str] = None):
        """Store data in cache (Redis immediately, database async)"""
        config = self.get_endpoint_config(endpoint)
        
        # Store in Redis immediately (most important for performance)
        try:
            cache.set(cache_key, data, config['ttl'])
            logger.debug(f"Stored in Redis cache: {cache_key}")
        except Exception as e:
            logger.warning(f"Failed to store in Redis: {e}")
        
        # Queue database storage for async processing
        self._queue_database_storage(cache_key, endpoint, data, config, access_token)
    
    def _queue_database_storage(self, cache_key: str, endpoint: str, data: Dict[str, Any],
                               config: Dict[str, Any], access_token: Optional[str] = None):
        """Queue database storage for async processing"""
        with self._db_write_lock:
            self._db_write_queue.append({
                'cache_key': cache_key,
                'endpoint': endpoint,
                'data': data,
                'config': config,
                'access_token': access_token,
                'expires_at': timezone.now() + timedelta(seconds=config['ttl'])
            })
            
            # Process queue if it gets too large
            if len(self._db_write_queue) >= 10:
                self._process_database_queue()
    
    def _process_database_queue(self):
        """Process queued database writes in batch"""
        if not self._db_write_queue:
            return
        
        def process_queue():
            try:
                with transaction.atomic():
                    queue_copy = self._db_write_queue.copy()
                    self._db_write_queue.clear()
                    
                    for item in queue_copy:
                        try:
                            self._store_in_database(item)
                        except Exception as e:
                            logger.error(f"Failed to store cache item: {e}")
                            
            except Exception as e:
                logger.error(f"Failed to process database queue: {e}")
        
        # Process in background thread
        thread = threading.Thread(target=process_queue)
        thread.daemon = True
        thread.start()
    
    def _store_in_database(self, item: Dict[str, Any]):
        """Store single item in database"""
        config = item['config']
        
        if config['user_specific'] and item['access_token']:
            user_hash = hashlib.sha256(item['access_token'].encode()).hexdigest()[:16]
            SpotifyUserCache.objects.update_or_create(
                cache_key=item['cache_key'],
                defaults={
                    'endpoint': item['endpoint'],
                    'user_hash': user_hash,
                    'response_data': item['data'],
                    'expires_at': item['expires_at'],
                    'hit_count': 0,
                }
            )
        else:
            query_hash = hashlib.sha1(str(item['data']).encode()).hexdigest()
            SpotifyCacheEntry.objects.update_or_create(
                cache_key=item['cache_key'],
                defaults={
                    'endpoint': item['endpoint'],
                    'query_hash': query_hash,
                    'response_data': item['data'],
                    'expires_at': item['expires_at'],
                    'user_specific': config['user_specific'],
                    'hit_count': 0,
                }
            )
    
    def _update_cache_hit_async(self, cache_entry):
        """Update cache hit count asynchronously"""
        def update_hit():
            try:
                cache_entry.hit_count += 1
                cache_entry.last_accessed = timezone.now()
                cache_entry.save(update_fields=['hit_count', 'last_accessed'])
            except Exception as e:
                logger.warning(f"Failed to update hit count: {e}")
        
        thread = threading.Thread(target=update_hit)
        thread.daemon = True
        thread.start()
    
    def flush_queue(self):
        """Flush any remaining items in the database queue"""
        if self._db_write_queue:
            self._process_database_queue()


# Global cache manager instance
cache_manager = SpotifyCacheManager()
