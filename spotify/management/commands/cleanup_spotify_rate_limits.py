from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from spotify.models import SpotifyRateLimit
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Clean up old SpotifyRateLimit records (older than 1 hour)'

    def handle(self, *args, **options):
        # Clean up old rate limit records (older than 1 hour)
        cutoff_time = timezone.now() - timedelta(hours=1)
        old_records = SpotifyRateLimit.objects.filter(window_start__lt=cutoff_time)
        old_count = old_records.count()
        
        if old_count > 0:
            old_records.delete()
            self.stdout.write(self.style.SUCCESS(
                f"Cleaned up {old_count} old rate limit records"
            ))
            logger.info(f"Cleaned up {old_count} old SpotifyRateLimit records")
        else:
            self.stdout.write("No old rate limit records to clean up")
            logger.info("No old SpotifyRateLimit records to clean up") 