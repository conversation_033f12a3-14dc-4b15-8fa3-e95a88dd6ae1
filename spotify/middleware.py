"""
Middleware for managing database connections and caching in Spotify service
"""
import logging
from django.db import connections
from django.core.cache import cache
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)


class DatabaseConnectionMiddleware(MiddlewareMixin):
    """
    Middleware to manage database connections efficiently
    """
    
    def process_request(self, request):
        """Process incoming request"""
        # Mark the start of request processing
        request._db_connection_start = True
        return None
    
    def process_response(self, request, response):
        """Process response and clean up connections"""
        try:
            # Close any idle connections to prevent accumulation
            for conn in connections.all():
                if conn.queries_logged > 10:  # If too many queries, close connection
                    conn.close()
        except Exception as e:
            logger.warning(f"Error managing database connections: {e}")
        
        return response
    
    def process_exception(self, request, exception):
        """Handle exceptions and clean up connections"""
        try:
            # Close all connections on exception to prevent leaks
            for conn in connections.all():
                conn.close()
        except Exception as e:
            logger.warning(f"Error closing database connections after exception: {e}")
        
        return None


class SpotifyCacheMiddleware(MiddlewareMixin):
    """
    Middleware to optimize Spotify API caching
    """
    
    def process_request(self, request):
        """Check if request can be served from cache"""
        if request.path.startswith('/api/spotify/'):
            # Add cache headers for Spotify endpoints
            request._spotify_cacheable = True
        return None
    
    def process_response(self, request, response):
        """Add appropriate cache headers"""
        if hasattr(request, '_spotify_cacheable') and response.status_code == 200:
            # Add cache headers for successful Spotify responses
            response['Cache-Control'] = 'public, max-age=300'  # 5 minutes
            response['Vary'] = 'Accept, Authorization'
        
        return response


class ConnectionPoolingMiddleware(MiddlewareMixin):
    """
    Middleware to manage connection pooling more efficiently
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def __call__(self, request):
        """Process request with connection management"""
        try:
            response = self.get_response(request)
            return response
        finally:
            # Ensure connections are properly managed after each request
            self._cleanup_connections()
    
    def _cleanup_connections(self):
        """Clean up database connections"""
        try:
            from django.db import transaction
            
            # Close any open transactions
            for alias in connections:
                conn = connections[alias]
                if conn.in_atomic_block:
                    try:
                        transaction.rollback(using=alias)
                    except Exception:
                        pass
                
                # Close connection if it has been idle or has too many queries
                if hasattr(conn, 'queries_logged') and conn.queries_logged > 20:
                    conn.close()
                elif hasattr(conn, 'connection') and conn.connection:
                    # Check if connection is idle
                    try:
                        conn.connection.ping()
                    except Exception:
                        conn.close()
                        
        except Exception as e:
            logger.warning(f"Error in connection cleanup: {e}")
