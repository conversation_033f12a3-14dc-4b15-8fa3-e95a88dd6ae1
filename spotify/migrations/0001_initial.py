# Generated by Django 4.2.7 on 2025-06-26 02:55

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="SpotifyTokenUsage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("token_hash", models.CharField(db_index=True, max_length=64)),
                ("first_used", models.DateTimeField(auto_now_add=True)),
                ("last_used", models.DateTimeField(auto_now=True)),
                ("request_count", models.PositiveIntegerField(default=0)),
                ("successful_requests", models.PositiveIntegerField(default=0)),
                ("failed_requests", models.PositiveIntegerField(default=0)),
                ("token_expired_at", models.DateTimeField(blank=True, null=True)),
                ("is_active", models.<PERSON>oleanField(default=True)),
            ],
            options={
                "ordering": ["-last_used"],
            },
        ),
        migrations.CreateModel(
            name="SpotifyUserCache",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "cache_key",
                    models.CharField(db_index=True, max_length=255, unique=True),
                ),
                ("endpoint", models.CharField(db_index=True, max_length=200)),
                ("user_hash", models.CharField(db_index=True, max_length=64)),
                ("response_data", models.JSONField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("expires_at", models.DateTimeField(db_index=True)),
                ("hit_count", models.PositiveIntegerField(default=0)),
                ("last_accessed", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["-last_accessed"],
                "indexes": [
                    models.Index(
                        fields=["user_hash", "endpoint", "expires_at"],
                        name="spotify_spo_user_ha_093b0a_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="spotify_spo_created_616196_idx"
                    ),
                    models.Index(
                        fields=["last_accessed"], name="spotify_spo_last_ac_cd8f83_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="SpotifyRateLimit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("window_start", models.DateTimeField(db_index=True)),
                ("requests_made", models.PositiveIntegerField(default=0)),
                ("endpoint", models.CharField(blank=True, max_length=200)),
                ("user_ip", models.GenericIPAddressField(blank=True, null=True)),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["window_start", "endpoint"],
                        name="spotify_spo_window__29c0db_idx",
                    ),
                    models.Index(
                        fields=["window_start", "user_ip"],
                        name="spotify_spo_window__b44fd1_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="SpotifyCacheEntry",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "cache_key",
                    models.CharField(db_index=True, max_length=255, unique=True),
                ),
                ("endpoint", models.CharField(db_index=True, max_length=200)),
                ("query_hash", models.CharField(db_index=True, max_length=64)),
                ("response_data", models.JSONField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("expires_at", models.DateTimeField(db_index=True)),
                ("hit_count", models.PositiveIntegerField(default=0)),
                ("last_accessed", models.DateTimeField(auto_now=True)),
                ("user_specific", models.BooleanField(db_index=True, default=False)),
            ],
            options={
                "ordering": ["-last_accessed"],
                "indexes": [
                    models.Index(
                        fields=["endpoint", "expires_at"],
                        name="spotify_spo_endpoin_3f9f45_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="spotify_spo_created_25bc46_idx"
                    ),
                    models.Index(
                        fields=["last_accessed"], name="spotify_spo_last_ac_ee006b_idx"
                    ),
                    models.Index(
                        fields=["user_specific", "expires_at"],
                        name="spotify_spo_user_sp_98f4e1_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="SpotifyApiUsage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(auto_now_add=True, db_index=True)),
                ("endpoint", models.CharField(db_index=True, max_length=200)),
                ("total_requests", models.PositiveIntegerField(default=0)),
                ("cache_hits", models.PositiveIntegerField(default=0)),
                ("cache_misses", models.PositiveIntegerField(default=0)),
                ("api_calls_made", models.PositiveIntegerField(default=0)),
                ("average_response_time", models.FloatField(default=0.0)),
                ("unique_users", models.PositiveIntegerField(default=0)),
            ],
            options={
                "ordering": ["-date", "endpoint"],
                "unique_together": {("date", "endpoint")},
            },
        ),
    ]
