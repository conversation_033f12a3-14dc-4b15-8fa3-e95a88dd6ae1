from django.db import models
from django.utils import timezone
import hashlib


class SpotifyCacheEntry(models.Model):
    """Cache entry for Spotify API responses"""
    
    cache_key = models.CharField(max_length=255, unique=True, db_index=True)
    endpoint = models.CharField(max_length=200, db_index=True)
    query_hash = models.CharField(max_length=64, db_index=True)
    response_data = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(db_index=True)
    hit_count = models.PositiveIntegerField(default=0)
    last_accessed = models.DateTimeField(auto_now=True)
    user_specific = models.BooleanField(default=False, db_index=True)  # Some endpoints are user-specific
    
    class Meta:
        indexes = [
            models.Index(fields=['endpoint', 'expires_at']),
            models.Index(fields=['created_at']),
            models.Index(fields=['last_accessed']),
            models.Index(fields=['user_specific', 'expires_at']),
        ]
        ordering = ['-last_accessed']
    
    def __str__(self):
        return f"Spotify Cache: {self.endpoint} - {self.cache_key[:50]}"
    
    @property
    def is_expired(self):
        return timezone.now() > self.expires_at
    
    def increment_hit_count(self):
        self.hit_count += 1
        self.last_accessed = timezone.now()
        self.save(update_fields=['hit_count', 'last_accessed'])


class SpotifyApiUsage(models.Model):
    """Track Spotify API usage statistics"""
    
    date = models.DateField(auto_now_add=True, db_index=True)
    endpoint = models.CharField(max_length=200, db_index=True)
    total_requests = models.PositiveIntegerField(default=0)
    cache_hits = models.PositiveIntegerField(default=0)
    cache_misses = models.PositiveIntegerField(default=0)
    api_calls_made = models.PositiveIntegerField(default=0)
    average_response_time = models.FloatField(default=0.0)
    unique_users = models.PositiveIntegerField(default=0)  # Track unique users per day
    
    class Meta:
        unique_together = ('date', 'endpoint')
        ordering = ['-date', 'endpoint']
    
    def __str__(self):
        return f"Spotify Usage: {self.date} - {self.endpoint}"
    
    @property
    def cache_hit_rate(self):
        if self.total_requests > 0:
            return (self.cache_hits / self.total_requests) * 100
        return 0.0


class SpotifyRateLimit(models.Model):
    """Track rate limiting for Spotify API"""
    
    window_start = models.DateTimeField(db_index=True)
    requests_made = models.PositiveIntegerField(default=0)
    endpoint = models.CharField(max_length=200, blank=True)
    user_ip = models.GenericIPAddressField(null=True, blank=True)  # Track per-IP for client rate limiting
    
    class Meta:
        indexes = [
            models.Index(fields=['window_start', 'endpoint']),
            models.Index(fields=['window_start', 'user_ip']),
        ]
        unique_together = ('window_start', 'endpoint', 'user_ip')
    
    def __str__(self):
        return f"Spotify Rate Limit: {self.window_start} - {self.requests_made} requests"


class SpotifyUserCache(models.Model):
    """Cache user-specific Spotify data with user authentication tracking"""
    
    cache_key = models.CharField(max_length=255, unique=True, db_index=True)
    endpoint = models.CharField(max_length=200, db_index=True)
    user_hash = models.CharField(max_length=64, db_index=True)  # Hash of user identifier for privacy
    response_data = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(db_index=True)
    hit_count = models.PositiveIntegerField(default=0)
    last_accessed = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user_hash', 'endpoint', 'expires_at']),
            models.Index(fields=['created_at']),
            models.Index(fields=['last_accessed']),
        ]
        ordering = ['-last_accessed']
    
    def __str__(self):
        return f"Spotify User Cache: {self.endpoint} - {self.user_hash[:10]}..."
    
    @property
    def is_expired(self):
        return timezone.now() > self.expires_at
    
    def increment_hit_count(self):
        self.hit_count += 1
        self.last_accessed = timezone.now()
        self.save(update_fields=['hit_count', 'last_accessed'])


class SpotifyTokenUsage(models.Model):
    """Track token usage and validity for monitoring"""
    
    token_hash = models.CharField(max_length=64, db_index=True)  # Hash of token for privacy
    first_used = models.DateTimeField(auto_now_add=True)
    last_used = models.DateTimeField(auto_now=True)
    request_count = models.PositiveIntegerField(default=0)
    successful_requests = models.PositiveIntegerField(default=0)
    failed_requests = models.PositiveIntegerField(default=0)
    token_expired_at = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['-last_used']
    
    def __str__(self):
        return f"Spotify Token: {self.token_hash[:10]}... ({self.request_count} requests)"
    
    def record_request(self, success=True):
        self.request_count += 1
        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
        self.last_used = timezone.now()
        self.save(update_fields=['request_count', 'successful_requests', 'failed_requests', 'last_used'])
    
    @property
    def success_rate(self):
        if self.request_count > 0:
            return (self.successful_requests / self.request_count) * 100
        return 0.0
