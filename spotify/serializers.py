from rest_framework import serializers
from .models import SpotifyCacheEntry, SpotifyApiUsage, SpotifyUserCache, SpotifyTokenUsage


class SpotifyRequestSerializer(serializers.Serializer):
    """Serializer for Spotify API request parameters"""
    
    # Search parameters
    q = serializers.CharField(max_length=500, required=False, help_text="Search query")
    type = serializers.CharField(max_length=50, required=False, help_text="Search type (track, artist, album, playlist)")
    
    # Pagination parameters
    limit = serializers.IntegerField(min_value=1, max_value=50, default=20, required=False,
                                   help_text="Number of results to return (default: 20, max: 50)")
    offset = serializers.IntegerField(min_value=0, default=0, required=False,
                                    help_text="Offset for pagination")
    
    # Market/region parameters
    market = serializers.CharField(max_length=5, required=False,
                                 help_text="Market/country code (e.g., 'US', 'GB')")
    
    # Time range for top tracks/artists
    time_range = serializers.ChoiceField(
        choices=['short_term', 'medium_term', 'long_term'],
        default='medium_term',
        required=False,
        help_text="Time range for top items"
    )
    
    # IDs for batch requests
    ids = serializers.CharField(max_length=1000, required=False,
                               help_text="Comma-separated list of Spotify IDs")
    
    def validate_type(self, value):
        """Validate search type"""
        if value:
            valid_types = ['track', 'artist', 'album', 'playlist', 'show', 'episode']
            if value not in valid_types:
                raise serializers.ValidationError(
                    f"Invalid search type '{value}'. Valid types: {', '.join(valid_types)}"
                )
        return value
    
    def validate_market(self, value):
        """Validate market code"""
        if value and len(value) != 2:
            raise serializers.ValidationError("Market code must be 2 characters (e.g., 'US', 'GB')")
        return value
    
    def validate_ids(self, value):
        """Validate IDs format"""
        if value:
            ids = [id.strip() for id in value.split(',')]
            if len(ids) > 50:
                raise serializers.ValidationError("Maximum 50 IDs allowed")
            # Basic validation for Spotify ID format
            for id in ids:
                if not id or len(id) != 22:
                    raise serializers.ValidationError(f"Invalid Spotify ID format: {id}")
        return value


class SpotifySearchRequestSerializer(SpotifyRequestSerializer):
    """Specialized serializer for search requests"""
    
    q = serializers.CharField(max_length=500, required=True, help_text="Search query")
    type = serializers.CharField(max_length=50, required=True, help_text="Search type (track, artist, album, playlist)")


class SpotifyUserEndpointSerializer(serializers.Serializer):
    """Serializer for user-specific endpoints"""
    
    limit = serializers.IntegerField(min_value=1, max_value=50, default=20, required=False)
    offset = serializers.IntegerField(min_value=0, default=0, required=False)
    time_range = serializers.ChoiceField(
        choices=['short_term', 'medium_term', 'long_term'],
        default='medium_term',
        required=False
    )


class SpotifyCacheEntrySerializer(serializers.ModelSerializer):
    """Serializer for cache entry information"""
    
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = SpotifyCacheEntry
        fields = [
            'id', 'cache_key', 'endpoint', 'created_at', 'updated_at', 
            'expires_at', 'hit_count', 'last_accessed', 'user_specific', 'is_expired'
        ]
        read_only_fields = ['id', 'cache_key', 'endpoint', 'created_at', 'updated_at', 
                           'expires_at', 'hit_count', 'last_accessed', 'user_specific']


class SpotifyUserCacheSerializer(serializers.ModelSerializer):
    """Serializer for user cache entry information"""
    
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = SpotifyUserCache
        fields = [
            'id', 'cache_key', 'endpoint', 'user_hash', 'created_at', 'updated_at', 
            'expires_at', 'hit_count', 'last_accessed', 'is_expired'
        ]
        read_only_fields = ['id', 'cache_key', 'endpoint', 'user_hash', 'created_at', 
                           'updated_at', 'expires_at', 'hit_count', 'last_accessed']


class SpotifyApiUsageSerializer(serializers.ModelSerializer):
    """Serializer for API usage statistics"""
    
    cache_hit_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = SpotifyApiUsage
        fields = [
            'id', 'date', 'endpoint', 'total_requests', 'cache_hits', 
            'cache_misses', 'api_calls_made', 'average_response_time', 
            'unique_users', 'cache_hit_rate'
        ]
        read_only_fields = ['id', 'date', 'endpoint', 'total_requests', 'cache_hits', 
                           'cache_misses', 'api_calls_made', 'average_response_time', 'unique_users']


class SpotifyTokenUsageSerializer(serializers.ModelSerializer):
    """Serializer for token usage statistics"""
    
    success_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = SpotifyTokenUsage
        fields = [
            'id', 'token_hash', 'first_used', 'last_used', 'request_count',
            'successful_requests', 'failed_requests', 'token_expired_at',
            'is_active', 'success_rate'
        ]
        read_only_fields = ['id', 'token_hash', 'first_used', 'last_used', 'request_count',
                           'successful_requests', 'failed_requests', 'token_expired_at', 'is_active']


class SpotifyCacheStatsSerializer(serializers.Serializer):
    """Serializer for cache statistics"""
    
    today_stats = serializers.DictField(read_only=True)
    total_cache_entries = serializers.IntegerField(read_only=True)
    total_user_cache_entries = serializers.IntegerField(read_only=True)
    expired_entries = serializers.IntegerField(read_only=True)
    expired_user_entries = serializers.IntegerField(read_only=True)
    active_entries = serializers.IntegerField(read_only=True)
    active_user_entries = serializers.IntegerField(read_only=True)
    active_tokens = serializers.IntegerField(read_only=True)


class SpotifyErrorResponseSerializer(serializers.Serializer):
    """Serializer for error responses"""
    
    error = serializers.CharField()
    message = serializers.CharField()
    details = serializers.DictField(required=False)


class SpotifyTrackSerializer(serializers.Serializer):
    """Serializer for Spotify track data (read-only, for documentation)"""
    
    id = serializers.CharField(read_only=True)
    name = serializers.CharField(read_only=True)
    artists = serializers.ListField(read_only=True)
    album = serializers.DictField(read_only=True)
    duration_ms = serializers.IntegerField(read_only=True)
    explicit = serializers.BooleanField(read_only=True)
    external_urls = serializers.DictField(read_only=True)
    popularity = serializers.IntegerField(read_only=True)
    preview_url = serializers.URLField(read_only=True, allow_null=True)
    uri = serializers.CharField(read_only=True)


class SpotifyArtistSerializer(serializers.Serializer):
    """Serializer for Spotify artist data (read-only, for documentation)"""
    
    id = serializers.CharField(read_only=True)
    name = serializers.CharField(read_only=True)
    genres = serializers.ListField(read_only=True)
    images = serializers.ListField(read_only=True)
    popularity = serializers.IntegerField(read_only=True)
    external_urls = serializers.DictField(read_only=True)
    followers = serializers.DictField(read_only=True)
    uri = serializers.CharField(read_only=True)


class SpotifyPlaylistSerializer(serializers.Serializer):
    """Serializer for Spotify playlist data (read-only, for documentation)"""
    
    id = serializers.CharField(read_only=True)
    name = serializers.CharField(read_only=True)
    description = serializers.CharField(read_only=True, allow_null=True)
    images = serializers.ListField(read_only=True)
    owner = serializers.DictField(read_only=True)
    public = serializers.BooleanField(read_only=True)
    snapshot_id = serializers.CharField(read_only=True)
    tracks = serializers.DictField(read_only=True)
    external_urls = serializers.DictField(read_only=True)
    uri = serializers.CharField(read_only=True)


class SpotifySearchResponseSerializer(serializers.Serializer):
    """Serializer for search response structure (read-only, for documentation)"""
    
    tracks = serializers.DictField(read_only=True, required=False)
    artists = serializers.DictField(read_only=True, required=False)
    albums = serializers.DictField(read_only=True, required=False)
    playlists = serializers.DictField(read_only=True, required=False)


class SpotifyPostRequestSerializer(serializers.Serializer):
    """Base serializer for POST requests with Spotify access token"""
    access_token = serializers.CharField(
        required=True,
        help_text="Spotify access token"
    )
    limit = serializers.IntegerField(
        required=False, 
        default=20,
        min_value=1,
        max_value=50,
        help_text="Number of items to return"
    )
    offset = serializers.IntegerField(
        required=False, 
        default=0,
        min_value=0,
        help_text="Offset for pagination"
    )
    market = serializers.CharField(
        required=False,
        max_length=2,
        help_text="Market/country code (e.g., US, GB)"
    )


class SpotifyUserTopItemsPostSerializer(SpotifyPostRequestSerializer):
    """Serializer for POST requests to user top items endpoints"""
    time_range = serializers.ChoiceField(
        choices=['short_term', 'medium_term', 'long_term'],
        default='medium_term',
        required=False,
        help_text="Time range for top items"
    )


class SpotifySearchPostSerializer(SpotifyPostRequestSerializer):
    """Serializer for POST search requests"""
    q = serializers.CharField(
        required=True,
        help_text="Search query"
    )
    type = serializers.ChoiceField(
        choices=['track', 'artist', 'album', 'playlist'],
        required=True,
        help_text="Type of search"
    )


class SpotifyUserProfilePostSerializer(serializers.Serializer):
    """Serializer for POST requests to user profile endpoint"""
    access_token = serializers.CharField(
        required=True,
        help_text="Spotify access token"
    )


class SpotifyPlaylistTracksPostSerializer(serializers.Serializer):
    """Serializer for POST requests to playlist tracks endpoint"""
    access_token = serializers.CharField(
        required=False,
        help_text="Spotify access token (optional for public playlists)"
    )
    limit = serializers.IntegerField(
        required=False, 
        default=50,
        min_value=1,
        max_value=100,
        help_text="Number of tracks to return"
    )
    offset = serializers.IntegerField(
        required=False, 
        default=0,
        min_value=0,
        help_text="Offset for pagination"
    )
    market = serializers.CharField(
        required=False,
        max_length=2,
        help_text="Market/country code (e.g., US, GB)"
    ) 