#!/usr/bin/env python3
"""
Test script for Spotify POST endpoints
"""
import requests
import json
import sys


def test_post_endpoints(base_url="http://localhost:8000", access_token=None):
    """Test all Spotify POST endpoints"""
    
    if not access_token:
        print("Warning: No access token provided. Using a dummy token.")
        print("To test with a real token, run: python test_post_endpoints.py YOUR_SPOTIFY_TOKEN")
        access_token = "dummy_token_for_testing"
    
    endpoints = [
        {
            "name": "Search Tracks (without token)",
            "url": f"{base_url}/api/spotify/search/",
            "data": {
                "q": "Beatles",
                "type": "track",
                "limit": 5
            }
        },
        {
            "name": "Search Tracks (with token)",
            "url": f"{base_url}/api/spotify/search/",
            "data": {
                "access_token": access_token,
                "q": "Beatles",
                "type": "track",
                "limit": 5
            }
        },
        {
            "name": "User Top Tracks",
            "url": f"{base_url}/api/spotify/me/top/tracks/",
            "data": {
                "access_token": access_token,
                "time_range": "short_term",
                "limit": 5
            }
        },
        {
            "name": "User Top Artists",
            "url": f"{base_url}/api/spotify/me/top/artists/",
            "data": {
                "access_token": access_token,
                "time_range": "medium_term",
                "limit": 5
            }
        },
        {
            "name": "User Saved Tracks",
            "url": f"{base_url}/api/spotify/me/tracks/",
            "data": {
                "access_token": access_token,
                "limit": 10,
                "offset": 0
            }
        }
    ]
    
    for endpoint in endpoints:
        print(f"\n{'='*60}")
        print(f"Testing: {endpoint['name']}")
        print(f"URL: {endpoint['url']}")
        print(f"Data: {json.dumps(endpoint['data'], indent=2)}")
        print("-" * 60)
        
        try:
            response = requests.post(
                endpoint['url'],
                json=endpoint['data'],
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"Status Code: {response.status_code}")
            
            # Pretty print the response
            try:
                response_data = response.json()
                if response.status_code == 200:
                    # For successful responses, just show a summary
                    if 'tracks' in response_data and 'items' in response_data['tracks']:
                        print(f"Success! Found {len(response_data['tracks']['items'])} tracks")
                    elif 'items' in response_data:
                        print(f"Success! Found {len(response_data['items'])} items")
                    else:
                        print("Success!")
                else:
                    # For errors, show the full response
                    print(f"Response: {json.dumps(response_data, indent=2)}")
            except json.JSONDecodeError:
                print(f"Response: {response.text[:200]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"Error: {e}")
    
    print(f"\n{'='*60}")
    print("Testing complete!")


if __name__ == "__main__":
    # Allow passing access token as command line argument
    access_token = sys.argv[1] if len(sys.argv) > 1 else None
    
    # You can also set the base URL if testing against a different server
    base_url = sys.argv[2] if len(sys.argv) > 2 else "http://localhost:8000"
    
    test_post_endpoints(base_url, access_token) 