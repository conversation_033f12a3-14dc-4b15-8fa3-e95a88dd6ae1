from django.test import TestCase
import json
import time
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta
from django.urls import reverse
from django.utils import timezone
from django.core.cache import cache
from rest_framework.test import APITestCase
from rest_framework import status

from .models import SpotifyCacheEntry, SpotifyApiUsage, SpotifyRateLimit, SpotifyUserCache, SpotifyTokenUsage
from .services import SpotifyService
from .serializers import SpotifyRequestSerializer, SpotifySearchRequestSerializer


class SpotifyServiceTestCase(TestCase):
    """Test cases for SpotifyService"""
    
    def setUp(self):
        self.service = SpotifyService()
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def test_generate_cache_key(self):
        """Test cache key generation"""
        endpoint = '/search'
        params = {'q': 'test', 'type': 'track', 'limit': '20'}
        
        key1 = self.service._generate_cache_key(endpoint, params)
        key2 = self.service._generate_cache_key(endpoint, params)
        
        self.assertEqual(key1, key2)
        self.assertTrue(key1.startswith('spotify:'))
        
        # Different parameters should generate different keys
        params2 = {'q': 'test2', 'type': 'track', 'limit': '20'}
        key3 = self.service._generate_cache_key(endpoint, params2)
        self.assertNotEqual(key1, key3)
    
    def test_endpoint_config(self):
        """Test endpoint configuration"""
        config = self.service._get_endpoint_config('/me/tracks')
        self.assertTrue(config['user_specific'])
        self.assertTrue(config['auth_required'])
        
        config = self.service._get_endpoint_config('/search')
        self.assertFalse(config['user_specific'])
        self.assertFalse(config['auth_required'])
    
    @patch('spotify.services.SpotifyRateLimit.objects.get_or_create')
    def test_rate_limiting(self, mock_get_or_create):
        """Test rate limiting functionality"""
        mock_rate_limit = MagicMock()
        mock_rate_limit.requests_made = 100
        mock_get_or_create.return_value = (mock_rate_limit, False)
        
        result = self.service._check_rate_limit('/search')
        self.assertTrue(result)
        
        mock_rate_limit.requests_made = 700
        result = self.service._check_rate_limit('/search')
        self.assertFalse(result)


class SpotifyModelTestCase(TestCase):
    """Test cases for Spotify models"""
    
    def test_spotify_cache_entry_creation(self):
        """Test SpotifyCacheEntry model creation"""
        entry = SpotifyCacheEntry.objects.create(
            cache_key='test_key',
            endpoint='/search',
            query_hash='test_hash',
            response_data={'test': 'data'},
            expires_at=timezone.now() + timedelta(hours=1),
            user_specific=False
        )
        
        self.assertEqual(str(entry), 'Spotify Cache: /search - test_key')
        self.assertFalse(entry.is_expired)
        self.assertEqual(entry.hit_count, 0)
    
    def test_spotify_api_usage_creation(self):
        """Test SpotifyApiUsage model creation"""
        usage = SpotifyApiUsage.objects.create(
            endpoint='/search',
            total_requests=100,
            cache_hits=80,
            cache_misses=20,
            api_calls_made=20
        )
        
        self.assertEqual(usage.cache_hit_rate, 80.0)


class SpotifySerializerTestCase(TestCase):
    """Test cases for Spotify serializers"""
    
    def test_spotify_request_serializer_valid_data(self):
        """Test SpotifyRequestSerializer with valid data"""
        data = {
            'limit': 20,
            'offset': 0,
            'market': 'US',
            'time_range': 'medium_term'
        }
        
        serializer = SpotifyRequestSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['limit'], 20)
    
    def test_spotify_search_request_serializer(self):
        """Test SpotifySearchRequestSerializer"""
        data = {
            'q': 'coldplay',
            'type': 'track',
            'limit': 10
        }
        
        serializer = SpotifySearchRequestSerializer(data=data)
        self.assertTrue(serializer.is_valid())


class SpotifyClientCredentialsTestCase(TestCase):
    """Test cases for Spotify client credentials flow"""
    
    def setUp(self):
        self.service = SpotifyService()
        cache.clear()
        
        # Mock client credentials
        self.service.client_id = 'test_client_id'
        self.service.client_secret = 'test_client_secret'
    
    def tearDown(self):
        cache.clear()
    
    @patch('spotify.services.requests.post')
    def test_get_client_credentials_token_success(self, mock_post):
        """Test successful client credentials token request"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'access_token': 'test_client_token',
            'token_type': 'Bearer',
            'expires_in': 3600
        }
        mock_post.return_value = mock_response
        
        token = self.service._get_client_credentials_token()
        
        self.assertEqual(token, 'test_client_token')
        self.assertTrue(mock_post.called)
        
        # Check that the token is cached
        cached_token = cache.get('spotify_client_token')
        self.assertEqual(cached_token, 'test_client_token')
    
    @patch('spotify.services.requests.post')
    def test_get_client_credentials_token_failure(self, mock_post):
        """Test failed client credentials token request"""
        # Mock failed response
        mock_response = MagicMock()
        mock_response.status_code = 401
        mock_response.text = 'Invalid client credentials'
        mock_post.return_value = mock_response
        
        token = self.service._get_client_credentials_token()
        
        self.assertIsNone(token)
    
    def test_get_client_credentials_token_no_config(self):
        """Test client credentials token request without configuration"""
        self.service.client_id = ''
        self.service.client_secret = ''
        
        token = self.service._get_client_credentials_token()
        
        self.assertIsNone(token)
    
    @patch('spotify.services.SpotifyService._get_client_credentials_token')
    @patch('spotify.services.requests.get')
    def test_make_api_request_with_client_credentials(self, mock_get, mock_get_token):
        """Test API request using client credentials"""
        # Mock client credentials token
        mock_get_token.return_value = 'client_token'
        
        # Mock successful API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'tracks': {'items': []}}
        mock_get.return_value = mock_response
        
        # Test public endpoint without user token
        result = self.service._make_api_request('/search', params={'q': 'test'})
        
        self.assertIsNotNone(result)
        self.assertEqual(result, {'tracks': {'items': []}})
        
        # Verify the request was made with client credentials
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        self.assertIn('Authorization', call_args[1]['headers'])
        self.assertEqual(call_args[1]['headers']['Authorization'], 'Bearer client_token')
    
    @patch('spotify.services.SpotifyService._get_client_credentials_token')
    @patch('spotify.services.requests.get')
    def test_make_api_request_user_token_fallback(self, mock_get, mock_get_token):
        """Test API request with user token fallback to client credentials"""
        # Mock client credentials token
        mock_get_token.return_value = 'client_token'
        
        # Mock first request (user token) fails, second succeeds
        mock_response_fail = MagicMock()
        mock_response_fail.status_code = 401
        
        mock_response_success = MagicMock()
        mock_response_success.status_code = 200
        mock_response_success.json.return_value = {'tracks': {'items': []}}
        
        mock_get.side_effect = [mock_response_fail, mock_response_success]
        
        # Test public endpoint with expired user token
        with patch.object(self.service, '_make_api_request_with_fallback', return_value={'tracks': {'items': []}}) as mock_fallback:
            result = self.service._make_api_request('/search', access_token='expired_token', params={'q': 'test'})
            
            self.assertIsNotNone(result)
            mock_fallback.assert_called_once()


class SpotifyAPITestCase(APITestCase):
    """Test cases for Spotify API endpoints"""
    
    def setUp(self):
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    @patch('spotify.services.SpotifyService.search_tracks')
    def test_search_endpoint_success(self, mock_search):
        """Test successful search endpoint"""
        mock_search.return_value = {
            'tracks': {
                'items': [
                    {'id': '123', 'name': 'Test Track', 'artists': [{'name': 'Test Artist'}]}
                ],
                'total': 1
            }
        }
        
        url = reverse('spotify:search')
        data = {'q': 'test', 'type': 'track'}
        
        response = self.client.get(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('tracks', response.data)
        mock_search.assert_called_once()
    
    def test_search_endpoint_invalid_params(self):
        """Test search endpoint with invalid parameters"""
        url = reverse('spotify:search')
        data = {'q': '', 'type': 'invalid_type'}
        
        response = self.client.get(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    @patch('spotify.services.SpotifyService.search_tracks')
    def test_search_endpoint_uses_client_credentials(self, mock_search):
        """Test search endpoint uses client credentials when no user token provided"""
        mock_search.return_value = {
            'tracks': {
                'items': [{'id': '123', 'name': 'Test Track'}],
                'total': 1
            }
        }
        
        url = reverse('spotify:search')
        data = {'q': 'test', 'type': 'track'}
        
        response = self.client.get(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('tracks', response.data)
        
        # Verify search was called with no access token (will use client credentials)
        mock_search.assert_called_once()
        call_args = mock_search.call_args
        self.assertIsNone(call_args[1]['access_token'])
    
    @patch('spotify.services.SpotifyService.get_cache_stats')
    def test_cache_stats_endpoint(self, mock_get_stats):
        """Test cache stats admin endpoint"""
        mock_get_stats.return_value = {
            'today_stats': {'total_requests': 100},
            'total_cache_entries': 50,
            'active_entries': 45
        }
        
        url = reverse('spotify:cache-stats')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('today_stats', response.data)


class SpotifyIntegrationTestCase(TestCase):
    """Integration tests for Spotify service with Apple Music users"""
    
    def setUp(self):
        self.service = SpotifyService()
        cache.clear()
        
        # Mock client credentials
        self.service.client_id = 'test_client_id'
        self.service.client_secret = 'test_client_secret'
    
    def tearDown(self):
        cache.clear()
    
    @patch('spotify.services.requests.post')
    @patch('spotify.services.requests.get')
    def test_apple_music_user_searches_spotify(self, mock_get, mock_post):
        """Test scenario: Apple Music user searches Spotify content without Spotify account"""
        # Mock client credentials token request
        mock_post_response = MagicMock()
        mock_post_response.status_code = 200
        mock_post_response.json.return_value = {
            'access_token': 'client_token_12345',
            'token_type': 'Bearer',
            'expires_in': 3600
        }
        mock_post.return_value = mock_post_response
        
        # Mock search API response
        mock_get_response = MagicMock()
        mock_get_response.status_code = 200
        mock_get_response.json.return_value = {
            'tracks': {
                'items': [
                    {
                        'id': '4iV5W9uYEdYUVa79Axb7Rh',
                        'name': 'Shape of You',
                        'artists': [{'name': 'Ed Sheeran', 'id': '6eUKZXaKkcviH0Ku9w2n3V'}],
                        'album': {'name': '÷ (Deluxe)', 'id': '3T4tUhGYeRNVUGevb0wThu'},
                        'popularity': 85,
                        'preview_url': 'https://p.scdn.co/mp3-preview/c6e04...'
                    }
                ],
                'total': 1000,
                'limit': 20,
                'offset': 0
            }
        }
        mock_get.return_value = mock_get_response
        
        # User searches without providing a Spotify token
        result = self.service.search_tracks(
            query='Ed Sheeran Shape of You',
            access_token=None,  # No user token (Apple Music user)
            limit=20
        )
        
        # Should successfully return results using client credentials
        self.assertIsNotNone(result)
        self.assertIn('tracks', result)
        self.assertEqual(len(result['tracks']['items']), 1)
        self.assertEqual(result['tracks']['items'][0]['name'], 'Shape of You')
        
        # Verify client credentials token was requested
        mock_post.assert_called_once()
        self.assertIn('grant_type', mock_post.call_args[1]['data'])
        self.assertEqual(mock_post.call_args[1]['data']['grant_type'], 'client_credentials')
        
        # Verify search API was called with client credentials
        mock_get.assert_called_once()
        auth_header = mock_get.call_args[1]['headers']['Authorization']
        self.assertEqual(auth_header, 'Bearer client_token_12345')
