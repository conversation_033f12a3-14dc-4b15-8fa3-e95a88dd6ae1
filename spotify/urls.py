from django.urls import path
from .views import (
    SpotifySearchView,
    SpotifyUserTracksView,
    SpotifyUserRecentlyPlayedView,
    SpotifyUserTopTracksView,
    SpotifyUserTopArtistsView,
    SpotifyTrackDetailsView,
    SpotifyArtistDetailsView,
    SpotifyMultipleArtistsView,
    SpotifyArtistAlbumsView,
    SpotifyAlbumDetailsView,
    SpotifyMultipleAlbumsView,
    SpotifyPlaylistTracksView,
    SpotifyCacheStatsView,
    SpotifyCacheManagementView,
    SpotifyCacheEntriesView,
    SpotifyUsageStatsView,
    SpotifyTokenStatsView,
)

app_name = 'spotify'

urlpatterns = [
    # Main Spotify API endpoints
    path('search/', SpotifySearchView.as_view(), name='search'),
    path('me/tracks/', SpotifyUserTracksView.as_view(), name='user-tracks'),
    path('me/player/recently-played/', SpotifyUserRecentlyPlayedView.as_view(), name='user-recently-played'),
    path('me/top/tracks/', SpotifyUserTopTracksView.as_view(), name='user-top-tracks'),
    path('me/top/artists/', SpotifyUserTopArtistsView.as_view(), name='user-top-artists'),
    path('tracks/<str:track_id>/', SpotifyTrackDetailsView.as_view(), name='track-details'),
    path('artists/<str:artist_id>/', SpotifyArtistDetailsView.as_view(), name='artist-details'),
    path('artists/<str:artist_id>/albums/', SpotifyArtistAlbumsView.as_view(), name='artist-albums'),
    path('artists/', SpotifyMultipleArtistsView.as_view(), name='multiple-artists'),
    path('albums/<str:album_id>/', SpotifyAlbumDetailsView.as_view(), name='album-details'),
    path('albums/', SpotifyMultipleAlbumsView.as_view(), name='multiple-albums'),
    path('playlists/<str:playlist_id>/tracks/', SpotifyPlaylistTracksView.as_view(), name='playlist-tracks'),
    
    # Admin/monitoring endpoints
    path('admin/stats/', SpotifyCacheStatsView.as_view(), name='cache-stats'),
    path('admin/cleanup/', SpotifyCacheManagementView.as_view(), name='cache-cleanup'),
    path('admin/entries/', SpotifyCacheEntriesView.as_view(), name='cache-entries'),
    path('admin/usage/', SpotifyUsageStatsView.as_view(), name='usage-stats'),
    path('admin/tokens/', SpotifyTokenStatsView.as_view(), name='token-stats'),
] 