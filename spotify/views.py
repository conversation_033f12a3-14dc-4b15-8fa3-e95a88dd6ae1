from django.shortcuts import render
import logging
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.throttling import AnonRateThrottle, UserRateThrottle
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes
from django.utils import timezone
from django.core.cache import cache as django_cache
import hashlib
from urllib.parse import urlencode

from .services import SpotifyService, SpotifyRateLimitExceeded
from .serializers import (
    SpotifyRequestSerializer,
    SpotifySearchRequestSerializer,
    SpotifyUserEndpointSerializer,
    SpotifyCacheEntrySerializer,
    SpotifyUserCacheSerializer,
    SpotifyApiUsageSerializer,
    SpotifyTokenUsageSerializer,
    SpotifyCacheStatsSerializer,
    SpotifyErrorResponseSerializer,
    SpotifySearchResponseSerializer,
    SpotifyPostRequestSerializer,
    SpotifyUserTopItemsPostSerializer,
    SpotifySearchPostSerializer,
    SpotifyUserProfilePostSerializer,
    SpotifyPlaylistTracksPostSerializer,
)
from .models import SpotifyCacheEntry, SpotifyUserCache, SpotifyApiUsage, SpotifyTokenUsage

logger = logging.getLogger(__name__)


class SpotifyCacheAwareThrottle(AnonRateThrottle):
    """Custom rate throttle that checks cache before applying rate limiting"""
    scope = 'spotify'
    rate = '100/min'  # 100 requests per minute per IP
    
    def allow_request(self, request, view):
        """Check if request should be allowed - skip rate limiting for cached requests"""
        # For Spotify, we need to check the endpoint and params
        if request.method in ['GET', 'POST']:
            # Extract endpoint from path
            path_parts = request.path.strip('/').split('/')
            if len(path_parts) >= 3:  # api/spotify/endpoint
                endpoint = '/'.join(path_parts[2:])  # Everything after api/spotify/
                
                # Get params based on method
                if request.method == 'GET':
                    params = dict(request.query_params)
                else:  # POST
                    params = request.data if hasattr(request, 'data') else {}
                
                # For search endpoint
                if 'search' in endpoint and params:
                    sorted_params = sorted(params.items())
                    query_string = urlencode(sorted_params)
                    query_hash = hashlib.sha1(query_string.encode()).hexdigest()
                    cache_key = f"spotify:search:{query_hash}"
                    
                    # Check if data exists in cache
                    if django_cache.get(cache_key) is not None:
                        return True
                    
                    # Check database cache
                    try:
                        SpotifyCacheEntry.objects.get(
                            cache_key=cache_key,
                            expires_at__gt=timezone.now()
                        )
                        return True
                    except SpotifyCacheEntry.DoesNotExist:
                        pass
        
        # Not cached, apply normal rate limiting
        return super().allow_request(request, view)


class SpotifyRateThrottle(AnonRateThrottle):
    """Custom rate throttle for Spotify API"""
    scope = 'spotify'
    rate = '100/min'  # 100 requests per minute per IP


def extract_bearer_token(request):
    """Extract Bearer token from request headers"""
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')
    if auth_header.startswith('Bearer '):
        return auth_header[7:]
    return None


def get_client_ip(request):
    """Get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def handle_spotify_service_call(service_method, *args, **kwargs):
    """Helper function to handle Spotify service calls with consistent error handling"""
    try:
        return service_method(*args, **kwargs)
    except SpotifyRateLimitExceeded as e:
        logger.warning(f"Rate limit exceeded in Spotify service call: {e.message}")
        raise e
    except Exception as e:
        logger.error(f"Error in Spotify service call: {e}")
        return None


def handle_spotify_view_response(view_func):
    """Decorator to handle Spotify view responses with consistent error handling"""
    def wrapper(*args, **kwargs):
        try:
            return view_func(*args, **kwargs)
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error in Spotify view: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    return wrapper


class SpotifySearchView(APIView):
    """Search endpoint for Spotify content"""
    
    permission_classes = [AllowAny]
    throttle_classes = [SpotifyCacheAwareThrottle]
    
    @extend_schema(
        summary="Search Spotify Content",
        description="Search for tracks, artists, albums, or playlists on Spotify with caching.",
        parameters=[
            OpenApiParameter(
                name='q',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=True,
                description='Search query'
            ),
            OpenApiParameter(
                name='type',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=True,
                description='Search type (track, artist, album, playlist)',
                enum=['track', 'artist', 'album', 'playlist']
            ),
            OpenApiParameter(
                name='limit',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Number of results to return (default: 20, max: 50)'
            ),
            OpenApiParameter(
                name='offset',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Offset for pagination'
            ),
            OpenApiParameter(
                name='market',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Market/country code (e.g., US, GB)'
            ),
            OpenApiParameter(
                name='Authorization',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.HEADER,
                required=False,
                description='Bearer token for authenticated requests'
            ),
        ],
        responses={
            200: SpotifySearchResponseSerializer,
            400: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
            502: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def get(self, request):
        """Handle search requests"""
        
        # Validate request parameters
        serializer = SpotifySearchRequestSerializer(data=request.query_params)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        access_token = extract_bearer_token(request)
        user_ip = get_client_ip(request)
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.search,
                query=validated_data['q'],
                search_type=validated_data['type'],
                access_token=access_token,
                limit=validated_data.get('limit', 20),
                offset=validated_data.get('offset', 0),
                market=validated_data.get('market'),
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'service_unavailable',
                        'message': 'Spotify service temporarily unavailable'
                    },
                    status=status.HTTP_502_BAD_GATEWAY
                )
            
            # Check if Spotify returned an error
            if 'error' in data:
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error in Spotify search view: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @extend_schema(
        summary="Search Spotify Content (POST)",
        description="Search for tracks, artists, albums, or playlists on Spotify with caching. Pass search parameters and optional access token in request body.",
        request=SpotifySearchPostSerializer,
        responses={
            200: SpotifySearchResponseSerializer,
            400: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
            502: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def post(self, request):
        """Handle search requests via POST"""
        
        serializer = SpotifySearchPostSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        access_token = validated_data.get('access_token')
        user_ip = get_client_ip(request)
        
        try:
            service = SpotifyService()
            # Now supporting all search types
            data = handle_spotify_service_call(
                service.search,
                query=validated_data['q'],
                search_type=validated_data['type'],
                access_token=access_token,
                limit=validated_data.get('limit', 20),
                offset=validated_data.get('offset', 0),
                market=validated_data.get('market'),
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'service_unavailable',
                        'message': 'Spotify service temporarily unavailable'
                    },
                    status=status.HTTP_502_BAD_GATEWAY
                )
            
            # Check if Spotify returned an error
            if 'error' in data:
                error_info = data.get('error', {})
                if isinstance(error_info, dict) and error_info.get('status') == 401:
                    return Response(
                        {
                            'error': 'unauthorized',
                            'message': error_info.get('message', 'Invalid or expired access token')
                        },
                        status=status.HTTP_401_UNAUTHORIZED
                    )
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error in Spotify search view: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyUserTracksView(APIView):
    """User's saved tracks endpoint"""
    
    permission_classes = [AllowAny]
    throttle_classes = [SpotifyCacheAwareThrottle]
    
    @extend_schema(
        summary="Get User's Saved Tracks",
        description="Get the current user's saved tracks (liked songs) with caching.",
        parameters=[
            OpenApiParameter(
                name='limit',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Number of tracks to return (default: 20, max: 50)'
            ),
            OpenApiParameter(
                name='offset',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Offset for pagination'
            ),
            OpenApiParameter(
                name='market',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Market/country code'
            ),
            OpenApiParameter(
                name='Authorization',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.HEADER,
                required=True,
                description='Bearer token for user authentication'
            ),
        ],
        responses={
            200: OpenApiTypes.OBJECT,
            401: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
            502: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def get(self, request):
        """Get user's saved tracks"""
        
        access_token = extract_bearer_token(request)
        if not access_token:
            return Response(
                {
                    'error': 'invalid_token',
                    'message': 'Access token required'
                },
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        # Validate parameters
        serializer = SpotifyRequestSerializer(data=request.query_params)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        user_ip = get_client_ip(request)
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_user_saved_tracks,
                access_token=access_token,
                limit=validated_data.get('limit', 20),
                offset=validated_data.get('offset', 0),
                market=validated_data.get('market'),
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'unauthorized',
                        'message': 'Invalid or expired access token'
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting user saved tracks: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @extend_schema(
        summary="Get User's Saved Tracks (POST)",
        description="Get the current user's saved tracks (liked songs) with caching. Pass Spotify access token in request body.",
        request=SpotifyPostRequestSerializer,
        responses={
            200: OpenApiTypes.OBJECT,
            401: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
            502: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def post(self, request):
        """Get user's saved tracks via POST request"""
        
        serializer = SpotifyPostRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        access_token = validated_data['access_token']
        user_ip = get_client_ip(request)
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_user_saved_tracks,
                access_token=access_token,
                limit=validated_data.get('limit', 20),
                offset=validated_data.get('offset', 0),
                market=validated_data.get('market'),
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'unauthorized',
                        'message': 'Invalid or expired access token'
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            # Check if Spotify returned an error
            if 'error' in data:
                error_info = data['error']
                if isinstance(error_info, dict) and error_info.get('status') == 401:
                    return Response(
                        {
                            'error': 'unauthorized',
                            'message': error_info.get('message', 'Invalid or expired access token')
                        },
                        status=status.HTTP_401_UNAUTHORIZED
                    )
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting user saved tracks: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyUserRecentlyPlayedView(APIView):
    """User's recently played tracks endpoint"""
    
    permission_classes = [AllowAny]
    throttle_classes = [SpotifyCacheAwareThrottle]
    
    @extend_schema(
        summary="Get User's Recently Played Tracks",
        description="Get the current user's recently played tracks with caching.",
        parameters=[
            OpenApiParameter(
                name='limit',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Number of tracks to return (default: 20, max: 50)'
            ),
            OpenApiParameter(
                name='Authorization',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.HEADER,
                required=True,
                description='Bearer token for user authentication'
            ),
        ],
        responses={
            200: OpenApiTypes.OBJECT,
            401: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
            502: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def get(self, request):
        """Get user's recently played tracks"""
        
        access_token = extract_bearer_token(request)
        if not access_token:
            return Response(
                {
                    'error': 'invalid_token',
                    'message': 'Access token required'
                },
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        limit = request.query_params.get('limit', 20)
        try:
            limit = int(limit)
            if limit < 1 or limit > 50:
                limit = 20
        except (ValueError, TypeError):
            limit = 20
        
        user_ip = get_client_ip(request)
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_user_recently_played,
                access_token=access_token,
                limit=limit,
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'unauthorized',
                        'message': 'Invalid or expired access token'
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting user recently played: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @extend_schema(
        summary="Get User's Recently Played Tracks (POST)",
        description="Get the current user's recently played tracks with caching. Pass Spotify access token in request body.",
        request=SpotifyPostRequestSerializer,
        responses={
            200: OpenApiTypes.OBJECT,
            401: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
            502: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def post(self, request):
        """Get user's recently played tracks via POST request"""
        
        serializer = SpotifyPostRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        access_token = validated_data['access_token']
        limit = validated_data.get('limit', 20)
        user_ip = get_client_ip(request)
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_user_recently_played,
                access_token=access_token,
                limit=limit,
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'unauthorized',
                        'message': 'Invalid or expired access token'
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            # Check if Spotify returned an error
            if 'error' in data:
                error_info = data['error']
                if isinstance(error_info, dict) and error_info.get('status') == 401:
                    return Response(
                        {
                            'error': 'unauthorized',
                            'message': error_info.get('message', 'Invalid or expired access token')
                        },
                        status=status.HTTP_401_UNAUTHORIZED
                    )
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting user recently played: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyUserTopTracksView(APIView):
    """User's top tracks endpoint"""
    
    permission_classes = [AllowAny]
    throttle_classes = [SpotifyCacheAwareThrottle]
    
    @extend_schema(
        summary="Get User's Top Tracks",
        description="Get the current user's top tracks with caching.",
        parameters=[
            OpenApiParameter(
                name='time_range',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Time range for top tracks',
                enum=['short_term', 'medium_term', 'long_term']
            ),
            OpenApiParameter(
                name='limit',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Number of tracks to return'
            ),
            OpenApiParameter(
                name='Authorization',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.HEADER,
                required=True,
                description='Bearer token for user authentication'
            ),
        ],
        responses={
            200: OpenApiTypes.OBJECT,
            401: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def get(self, request):
        """Get user's top tracks"""
        
        access_token = extract_bearer_token(request)
        if not access_token:
            return Response(
                {
                    'error': 'invalid_token',
                    'message': 'Access token required'
                },
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        serializer = SpotifyUserEndpointSerializer(data=request.query_params)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        user_ip = get_client_ip(request)
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_user_top_tracks,
                access_token=access_token,
                time_range=validated_data.get('time_range', 'medium_term'),
                limit=validated_data.get('limit', 20),
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'unauthorized',
                        'message': 'Invalid or expired access token'
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting user top tracks: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @extend_schema(
        summary="Get User's Top Tracks (POST)",
        description="Get the current user's top tracks with caching. Pass Spotify access token in request body.",
        request=SpotifyUserTopItemsPostSerializer,
        responses={
            200: OpenApiTypes.OBJECT,
            401: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def post(self, request):
        """Get user's top tracks via POST request"""
        
        serializer = SpotifyUserTopItemsPostSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        access_token = validated_data['access_token']
        user_ip = get_client_ip(request)
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_user_top_tracks,
                access_token=access_token,
                time_range=validated_data.get('time_range', 'medium_term'),
                limit=validated_data.get('limit', 20),
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'unauthorized',
                        'message': 'Invalid or expired access token'
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            # Check if Spotify returned an error
            if 'error' in data:
                error_info = data['error']
                if isinstance(error_info, dict) and error_info.get('status') == 401:
                    return Response(
                        {
                            'error': 'unauthorized',
                            'message': error_info.get('message', 'Invalid or expired access token')
                        },
                        status=status.HTTP_401_UNAUTHORIZED
                    )
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting user top tracks: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyUserTopArtistsView(APIView):
    """User's top artists endpoint"""
    
    permission_classes = [AllowAny]
    throttle_classes = [SpotifyCacheAwareThrottle]
    
    @extend_schema(
        summary="Get User's Top Artists",
        description="Get the current user's top artists with caching.",
        parameters=[
            OpenApiParameter(
                name='time_range',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Time range for top artists',
                enum=['short_term', 'medium_term', 'long_term']
            ),
            OpenApiParameter(
                name='limit',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Number of artists to return'
            ),
            OpenApiParameter(
                name='Authorization',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.HEADER,
                required=True,
                description='Bearer token for user authentication'
            ),
        ],
        responses={
            200: OpenApiTypes.OBJECT,
            401: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def get(self, request):
        """Get user's top artists"""
        
        access_token = extract_bearer_token(request)
        if not access_token:
            return Response(
                {
                    'error': 'invalid_token',
                    'message': 'Access token required'
                },
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        serializer = SpotifyUserEndpointSerializer(data=request.query_params)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        user_ip = get_client_ip(request)
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_user_top_artists,
                access_token=access_token,
                time_range=validated_data.get('time_range', 'medium_term'),
                limit=validated_data.get('limit', 20),
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'unauthorized',
                        'message': 'Invalid or expired access token'
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting user top artists: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @extend_schema(
        summary="Get User's Top Artists (POST)",
        description="Get the current user's top artists with caching. Pass Spotify access token in request body.",
        request=SpotifyUserTopItemsPostSerializer,
        responses={
            200: OpenApiTypes.OBJECT,
            401: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def post(self, request):
        """Get user's top artists via POST request"""
        
        serializer = SpotifyUserTopItemsPostSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        access_token = validated_data['access_token']
        user_ip = get_client_ip(request)
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_user_top_artists,
                access_token=access_token,
                time_range=validated_data.get('time_range', 'medium_term'),
                limit=validated_data.get('limit', 20),
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'unauthorized',
                        'message': 'Invalid or expired access token'
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            # Check if Spotify returned an error
            if 'error' in data:
                error_info = data['error']
                if isinstance(error_info, dict) and error_info.get('status') == 401:
                    return Response(
                        {
                            'error': 'unauthorized',
                            'message': error_info.get('message', 'Invalid or expired access token')
                        },
                        status=status.HTTP_401_UNAUTHORIZED
                    )
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting user top artists: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyTrackDetailsView(APIView):
    """Track details endpoint"""
    
    permission_classes = [AllowAny]
    throttle_classes = [SpotifyCacheAwareThrottle]
    
    @extend_schema(
        summary="Get Track Details",
        description="Get detailed information about a specific track with caching.",
        parameters=[
            OpenApiParameter(
                name='market',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Market/country code'
            ),
            OpenApiParameter(
                name='Authorization',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.HEADER,
                required=False,
                description='Bearer token (optional for public tracks)'
            ),
        ],
        responses={
            200: OpenApiTypes.OBJECT,
            404: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def get(self, request, track_id):
        """Get track details"""
        
        if not track_id or len(track_id) != 22:
            return Response(
                {
                    'error': 'invalid_id',
                    'message': 'Invalid Spotify track ID'
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        access_token = extract_bearer_token(request)
        user_ip = get_client_ip(request)
        market = request.query_params.get('market')
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_track_details,
                track_id=track_id,
                access_token=access_token,
                market=market,
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'not_found',
                        'message': 'Track not found'
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting track details: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyArtistDetailsView(APIView):
    """Single artist details endpoint"""
    
    permission_classes = [AllowAny]
    throttle_classes = [SpotifyCacheAwareThrottle]
    
    @extend_schema(
        summary="Get Artist Details",
        description="Get detailed information about a specific artist with caching.",
        parameters=[
            OpenApiParameter(
                name='Authorization',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.HEADER,
                required=False,
                description='Bearer token (optional for public artist data)'
            ),
        ],
        responses={
            200: OpenApiTypes.OBJECT,
            404: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def get(self, request, artist_id):
        """Get artist details"""
        
        if not artist_id or len(artist_id) != 22:
            return Response(
                {
                    'error': 'invalid_id',
                    'message': 'Invalid Spotify artist ID'
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        access_token = extract_bearer_token(request)
        user_ip = get_client_ip(request)
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_artist_details,
                artist_id=artist_id,
                access_token=access_token,
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'not_found',
                        'message': 'Artist not found'
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting artist details: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyMultipleArtistsView(APIView):
    """Multiple artists endpoint"""
    
    permission_classes = [AllowAny]
    throttle_classes = [SpotifyCacheAwareThrottle]
    
    @extend_schema(
        summary="Get Multiple Artists",
        description="Get detailed information about multiple artists with caching.",
        parameters=[
            OpenApiParameter(
                name='ids',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=True,
                description='Comma-separated list of Spotify artist IDs (max 50)'
            ),
            OpenApiParameter(
                name='Authorization',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.HEADER,
                required=False,
                description='Bearer token (optional for public artist data)'
            ),
        ],
        responses={
            200: OpenApiTypes.OBJECT,
            400: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def get(self, request):
        """Get multiple artists"""
        
        ids_param = request.query_params.get('ids')
        if not ids_param:
            return Response(
                {
                    'error': 'missing_ids',
                    'message': 'Artist IDs parameter is required'
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Parse and validate IDs
        artist_ids = [id.strip() for id in ids_param.split(',')]
        
        if len(artist_ids) > 50:
            return Response(
                {
                    'error': 'too_many_ids',
                    'message': 'Maximum 50 artist IDs allowed'
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate ID format
        for artist_id in artist_ids:
            if not artist_id or len(artist_id) != 22:
                return Response(
                    {
                        'error': 'invalid_id',
                        'message': f'Invalid Spotify artist ID: {artist_id}'
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        access_token = extract_bearer_token(request)
        user_ip = get_client_ip(request)
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_multiple_artists,
                artist_ids=artist_ids,
                access_token=access_token,
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'service_unavailable',
                        'message': 'Spotify service temporarily unavailable'
                    },
                    status=status.HTTP_502_BAD_GATEWAY
                )
            
            # Check if Spotify returned an error
            if 'error' in data:
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting multiple artists: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyArtistAlbumsView(APIView):
    """Artist albums endpoint"""
    
    permission_classes = [AllowAny]
    throttle_classes = [SpotifyCacheAwareThrottle]
    
    @extend_schema(
        summary="Get Artist Albums",
        description="Get albums by a specific artist with caching.",
        parameters=[
            OpenApiParameter(
                name='include_groups',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Album types to include (album,single,appears_on,compilation)',
                default='album,single,appears_on'
            ),
            OpenApiParameter(
                name='limit',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Number of albums to return (default: 20, max: 50)'
            ),
            OpenApiParameter(
                name='offset',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Offset for pagination'
            ),
            OpenApiParameter(
                name='market',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Market/country code'
            ),
            OpenApiParameter(
                name='Authorization',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.HEADER,
                required=False,
                description='Bearer token (optional for public artist data)'
            ),
        ],
        responses={
            200: OpenApiTypes.OBJECT,
            400: SpotifyErrorResponseSerializer,
            404: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def get(self, request, artist_id):
        """Get artist's albums"""
        
        if not artist_id or len(artist_id) != 22:
            return Response(
                {
                    'error': 'invalid_id',
                    'message': 'Invalid Spotify artist ID'
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        access_token = extract_bearer_token(request)
        user_ip = get_client_ip(request)
        
        # Get parameters
        include_groups = request.query_params.get('include_groups', 'album,single,appears_on')
        limit = request.query_params.get('limit', 20)
        offset = request.query_params.get('offset', 0)
        market = request.query_params.get('market')
        
        try:
            limit = int(limit)
            if limit < 1 or limit > 50:
                limit = 20
        except (ValueError, TypeError):
            limit = 20
            
        try:
            offset = int(offset)
            if offset < 0:
                offset = 0
        except (ValueError, TypeError):
            offset = 0
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_artist_albums,
                artist_id=artist_id,
                access_token=access_token,
                include_groups=include_groups,
                limit=limit,
                offset=offset,
                market=market,
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'not_found',
                        'message': 'Artist not found'
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Check if Spotify returned an error
            if 'error' in data:
                error_info = data['error']
                if isinstance(error_info, dict):
                    if error_info.get('status') == 404:
                        return Response(
                            {
                                'error': 'not_found',
                                'message': error_info.get('message', 'Artist not found')
                            },
                            status=status.HTTP_404_NOT_FOUND
                        )
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting artist albums: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyAlbumDetailsView(APIView):
    """Album details endpoint"""
    
    permission_classes = [AllowAny]
    throttle_classes = [SpotifyCacheAwareThrottle]
    
    @extend_schema(
        summary="Get Album Details",
        description="Get detailed information about a specific album with caching.",
        parameters=[
            OpenApiParameter(
                name='market',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Market/country code'
            ),
            OpenApiParameter(
                name='Authorization',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.HEADER,
                required=False,
                description='Bearer token (optional for public album data)'
            ),
        ],
        responses={
            200: OpenApiTypes.OBJECT,
            400: SpotifyErrorResponseSerializer,
            404: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def get(self, request, album_id):
        """Get album details"""
        
        if not album_id or len(album_id) != 22:
            return Response(
                {
                    'error': 'invalid_id',
                    'message': 'Invalid Spotify album ID'
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        access_token = extract_bearer_token(request)
        user_ip = get_client_ip(request)
        market = request.query_params.get('market')
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_album_details,
                album_id=album_id,
                access_token=access_token,
                market=market,
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'not_found',
                        'message': 'Album not found'
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting album details: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyMultipleAlbumsView(APIView):
    """Multiple albums endpoint"""
    
    permission_classes = [AllowAny]
    throttle_classes = [SpotifyCacheAwareThrottle]
    
    @extend_schema(
        summary="Get Multiple Albums",
        description="Get detailed information about multiple albums with caching.",
        parameters=[
            OpenApiParameter(
                name='ids',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=True,
                description='Comma-separated list of Spotify album IDs (max 20)'
            ),
            OpenApiParameter(
                name='market',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Market/country code'
            ),
            OpenApiParameter(
                name='Authorization',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.HEADER,
                required=False,
                description='Bearer token (optional for public album data)'
            ),
        ],
        responses={
            200: OpenApiTypes.OBJECT,
            400: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def get(self, request):
        """Get multiple albums"""
        
        ids_param = request.query_params.get('ids')
        if not ids_param:
            return Response(
                {
                    'error': 'missing_ids',
                    'message': 'Album IDs parameter is required'
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Parse and validate IDs
        album_ids = [id.strip() for id in ids_param.split(',')]
        
        if len(album_ids) > 20:
            return Response(
                {
                    'error': 'too_many_ids',
                    'message': 'Maximum 20 album IDs allowed'
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate ID format
        for album_id in album_ids:
            if not album_id or len(album_id) != 22:
                return Response(
                    {
                        'error': 'invalid_id',
                        'message': f'Invalid Spotify album ID: {album_id}'
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        access_token = extract_bearer_token(request)
        user_ip = get_client_ip(request)
        market = request.query_params.get('market')
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_multiple_albums,
                album_ids=album_ids,
                access_token=access_token,
                market=market,
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'service_unavailable',
                        'message': 'Spotify service temporarily unavailable'
                    },
                    status=status.HTTP_502_BAD_GATEWAY
                )
            
            # Check if Spotify returned an error
            if 'error' in data:
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting multiple albums: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyPlaylistTracksView(APIView):
    """Playlist tracks endpoint"""
    
    permission_classes = [AllowAny]
    throttle_classes = [SpotifyCacheAwareThrottle]
    
    @extend_schema(
        summary="Get Playlist Tracks",
        description="Get tracks from a specific playlist with caching.",
        parameters=[
            OpenApiParameter(
                name='limit',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Number of tracks to return (default: 50, max: 100)'
            ),
            OpenApiParameter(
                name='offset',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Offset for pagination'
            ),
            OpenApiParameter(
                name='market',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Market/country code'
            ),
            OpenApiParameter(
                name='Authorization',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.HEADER,
                required=False,
                description='Bearer token (optional for public playlists)'
            ),
        ],
        responses={
            200: OpenApiTypes.OBJECT,
            404: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def get(self, request, playlist_id):
        """Get tracks from a playlist"""
        
        access_token = extract_bearer_token(request)
        
        limit = request.query_params.get('limit', 50)
        offset = request.query_params.get('offset', 0)
        market = request.query_params.get('market')
        
        try:
            limit = int(limit)
            if limit < 1 or limit > 100:
                limit = 50
        except (ValueError, TypeError):
            limit = 50
            
        try:
            offset = int(offset)
            if offset < 0:
                offset = 0
        except (ValueError, TypeError):
            offset = 0
        
        user_ip = get_client_ip(request)
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_playlist_tracks,
                playlist_id=playlist_id,
                access_token=access_token,
                limit=limit,
                offset=offset,
                market=market,
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'not_found',
                        'message': 'Playlist not found or is not accessible'
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Check if Spotify returned an error
            if 'error' in data:
                error_info = data['error']
                if isinstance(error_info, dict):
                    if error_info.get('status') == 404:
                        return Response(
                            {
                                'error': 'not_found',
                                'message': error_info.get('message', 'Playlist not found')
                            },
                            status=status.HTTP_404_NOT_FOUND
                        )
                    elif error_info.get('status') == 401:
                        return Response(
                            {
                                'error': 'unauthorized',
                                'message': error_info.get('message', 'Access token required for private playlist')
                            },
                            status=status.HTTP_401_UNAUTHORIZED
                        )
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting playlist tracks: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @extend_schema(
        summary="Get Playlist Tracks (POST)",
        description="Get tracks from a playlist with caching. Pass Spotify access token in request body.",
        request=SpotifyPlaylistTracksPostSerializer,
        responses={
            200: OpenApiTypes.OBJECT,
            404: SpotifyErrorResponseSerializer,
            401: SpotifyErrorResponseSerializer,
            429: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache API']
    )
    def post(self, request, playlist_id):
        """Get playlist tracks via POST request"""
        
        serializer = SpotifyPlaylistTracksPostSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        access_token = validated_data.get('access_token')
        limit = validated_data.get('limit', 50)
        offset = validated_data.get('offset', 0)
        market = validated_data.get('market')
        user_ip = get_client_ip(request)
        
        try:
            service = SpotifyService()
            data = handle_spotify_service_call(
                service.get_playlist_tracks,
                playlist_id=playlist_id,
                access_token=access_token,
                limit=limit,
                offset=offset,
                market=market,
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'not_found',
                        'message': 'Playlist not found or is not accessible'
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Check if Spotify returned an error
            if 'error' in data:
                error_info = data['error']
                if isinstance(error_info, dict):
                    if error_info.get('status') == 404:
                        return Response(
                            {
                                'error': 'not_found',
                                'message': error_info.get('message', 'Playlist not found')
                            },
                            status=status.HTTP_404_NOT_FOUND
                        )
                    elif error_info.get('status') == 401:
                        return Response(
                            {
                                'error': 'unauthorized',
                                'message': error_info.get('message', 'Access token required for private playlist')
                            },
                            status=status.HTTP_401_UNAUTHORIZED
                        )
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting playlist tracks: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Admin Views

class SpotifyCacheStatsView(APIView):
    """View for cache statistics"""
    
    permission_classes = [AllowAny]  # Should be restricted to admin users in production
    
    @extend_schema(
        summary="Get Spotify Cache Statistics",
        description="Get statistics about the Spotify cache performance",
        responses={
            200: SpotifyCacheStatsSerializer,
        },
        tags=['Spotify Cache Admin']
    )
    def get(self, request):
        """Get cache statistics"""
        try:
            service = SpotifyService()
            stats = service.get_cache_stats()
            
            serializer = SpotifyCacheStatsSerializer(stats)
            return Response(serializer.data, status=status.HTTP_200_OK)
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Error retrieving cache statistics'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyCacheManagementView(APIView):
    """View for cache management operations"""
    
    permission_classes = [AllowAny]  # Should be restricted to admin users in production
    
    @extend_schema(
        summary="Clean Expired Spotify Cache",
        description="Remove expired cache entries from the database",
        responses={
            200: OpenApiTypes.OBJECT,
            500: SpotifyErrorResponseSerializer,
        },
        tags=['Spotify Cache Admin']
    )
    def delete(self, request):
        """Clean up expired cache entries"""
        try:
            service = SpotifyService()
            deleted_count = service.cleanup_expired_cache()
            
            return Response(
                {
                    'message': f'Successfully cleaned up {deleted_count} expired cache entries',
                    'deleted_count': deleted_count
                },
                status=status.HTTP_200_OK
            )
            
        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error cleaning cache: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Error cleaning expired cache entries'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyCacheEntriesView(APIView):
    """View for browsing cache entries"""
    
    permission_classes = [AllowAny]  # Should be restricted to admin users in production
    
    @extend_schema(
        summary="List Spotify Cache Entries",
        description="Get a list of cache entries with pagination",
        parameters=[
            OpenApiParameter(
                name='endpoint',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Filter by Spotify endpoint'
            ),
            OpenApiParameter(
                name='expired',
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Filter by expiration status'
            ),
            OpenApiParameter(
                name='user_specific',
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Filter by user-specific flag'
            ),
        ],
        responses={
            200: SpotifyCacheEntrySerializer(many=True),
        },
        tags=['Spotify Cache Admin']
    )
    def get(self, request):
        """Get cache entries with optional filtering"""
        try:
            queryset = SpotifyCacheEntry.objects.all()
            
            # Filter by endpoint if provided
            endpoint = request.query_params.get('endpoint')
            if endpoint:
                queryset = queryset.filter(endpoint__icontains=endpoint)
            
            # Filter by expiration status if provided
            expired = request.query_params.get('expired')
            if expired is not None:
                if expired.lower() in ['true', '1']:
                    queryset = queryset.filter(expires_at__lt=timezone.now())
                elif expired.lower() in ['false', '0']:
                    queryset = queryset.filter(expires_at__gte=timezone.now())
            
            # Filter by user_specific flag if provided
            user_specific = request.query_params.get('user_specific')
            if user_specific is not None:
                if user_specific.lower() in ['true', '1']:
                    queryset = queryset.filter(user_specific=True)
                elif user_specific.lower() in ['false', '0']:
                    queryset = queryset.filter(user_specific=False)
            
            # Order by last accessed and limit
            queryset = queryset.order_by('-last_accessed')[:100]
            
            serializer = SpotifyCacheEntrySerializer(queryset, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error getting cache entries: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Error retrieving cache entries'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyUsageStatsView(APIView):
    """View for API usage statistics"""
    
    permission_classes = [AllowAny]  # Should be restricted to admin users in production
    
    @extend_schema(
        summary="Get Spotify Usage Statistics",
        description="Get API usage statistics by date and endpoint",
        parameters=[
            OpenApiParameter(
                name='days',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Number of days to include (default: 7)'
            ),
        ],
        responses={
            200: SpotifyApiUsageSerializer(many=True),
        },
        tags=['Spotify Cache Admin']
    )
    def get(self, request):
        """Get usage statistics"""
        try:
            days = request.query_params.get('days', 7)
            try:
                days = int(days)
                if days < 1 or days > 90:
                    days = 7
            except (ValueError, TypeError):
                days = 7
            
            from datetime import date, timedelta
            start_date = date.today() - timedelta(days=days-1)
            
            queryset = SpotifyApiUsage.objects.filter(
                date__gte=start_date
            ).order_by('-date', 'endpoint')
            
            serializer = SpotifyApiUsageSerializer(queryset, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error getting usage stats: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Error retrieving usage statistics'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SpotifyTokenStatsView(APIView):
    """View for token usage statistics"""
    
    permission_classes = [AllowAny]  # Should be restricted to admin users in production
    
    @extend_schema(
        summary="Get Spotify Token Statistics",
        description="Get token usage statistics for monitoring",
        responses={
            200: SpotifyTokenUsageSerializer(many=True),
        },
        tags=['Spotify Cache Admin']
    )
    def get(self, request):
        """Get token statistics"""
        try:
            # Get recent token usage (last 7 days)
            from datetime import timedelta
            recent_date = timezone.now() - timedelta(days=7)
            
            queryset = SpotifyTokenUsage.objects.filter(
                last_used__gte=recent_date
            ).order_by('-last_used')[:50]  # Limit to 50 most recent
            
            serializer = SpotifyTokenUsageSerializer(queryset, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error getting token stats: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Error retrieving token statistics'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
