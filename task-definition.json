{"taskDefinitionArn": "arn:aws:ecs:us-east-1:296028253727:task-definition/bopmaps-prod-app:5", "containerDefinitions": [{"name": "app", "image": "296028253727.dkr.ecr.us-east-1.amazonaws.com/bopmaps-prod:latest", "cpu": 0, "portMappings": [{"containerPort": 8000, "hostPort": 8000, "protocol": "tcp"}], "essential": true, "environment": [{"name": "ONESIGNAL_API_KEY", "value": ""}, {"name": "SOUNDCLOUD_CLIENT_SECRET", "value": ""}, {"name": "SPOTIFY_REDIRECT_URI", "value": "http://localhost:8888/callback"}, {"name": "ENVIRONMENT", "value": "prod"}, {"name": "COLLECT_STATIC", "value": "true"}, {"name": "B2_REGION", "value": "us-east-005"}, {"name": "USE_S3_FOR_MEDIA", "value": "True"}, {"name": "JWT_ACCESS_TOKEN_LIFETIME", "value": "36500"}, {"name": "EMAIL_HOST", "value": ""}, {"name": "EMAIL_BACKEND", "value": "django.core.mail.backends.console.EmailBackend"}, {"name": "SES_CONFIGURATION_SET", "value": "bopmaps-emails"}, {"name": "ONESIGNAL_API_URL", "value": "https://onesignal.com/api/v1/notifications"}, {"name": "SOUNDCLOUD_CLIENT_ID", "value": ""}, {"name": "B2_ENDPOINT_URL", "value": "https://s3.us-east-005.backblazeb2.com"}, {"name": "CSRF_TRUSTED_ORIGINS", "value": "http://localhost:3000"}, {"name": "DEFAULT_FROM_EMAIL", "value": "<EMAIL>"}, {"name": "MEDIA_BUCKET_NAME", "value": "bopmaps-prod-media-67ba6151"}, {"name": "SPOTIFY_CLIENT_ID", "value": "fb25ee73d874499a9c424cd5a2466e13"}, {"name": "APPLE_MUSIC_TEAM_ID", "value": ""}, {"name": "B2_BUCKET_NAME", "value": "bopmaps-prod-static-67ba6151"}, {"name": "B2_APPLICATION_KEY", "value": "K0058c2wlRv7QFP/2On8cXsQ6XRV9fw"}, {"name": "B2_TILES_BUCKET_NAME", "value": "bopmaps-prod-tiles-67ba6151"}, {"name": "B2_APPLICATION_KEY_ID", "value": "005b50dabb51c630000000001"}, {"name": "CORS_ALLOWED_ORIGINS", "value": "http://localhost:3000"}, {"name": "EMAIL_PORT", "value": "587"}, {"name": "SPOTIFY_MOBILE_REDIRECT_URI", "value": "bopmaps://callback"}, {"name": "SECURE_SSL_REDIRECT", "value": "true"}, {"name": "AWS_REGION", "value": "us-east-1"}, {"name": "ALLOWED_REDIRECT_URIS", "value": "bopmaps://callback"}, {"name": "DEBUG", "value": "False"}, {"name": "EMAIL_USE_TLS", "value": "true"}, {"name": "APPLE_MUSIC_PRIVATE_KEY", "value": ""}, {"name": "ONESIGNAL_APP_ID", "value": ""}, {"name": "SPOTIFY_CLIENT_SECRET", "value": "33aaa655d24846c9b6f61ef24ecf9710"}, {"name": "EMAIL_HOST_PASSWORD", "value": ""}, {"name": "EMAIL_HOST_USER", "value": ""}, {"name": "LASTFM_API_KEY", "value": "fd7597d7a76640941f4db64e38649529"}, {"name": "APPLE_MUSIC_KEY_ID", "value": ""}, {"name": "SERVER_EMAIL", "value": "<EMAIL>"}, {"name": "REDIS_URL", "value": "redis://:<EMAIL>:6379/0"}, {"name": "JWT_REFRESH_TOKEN_LIFETIME", "value": "36500"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:296028253727:secret:bopmaps-prod-django-secret-b2exfo-sE34Ve"}, {"name": "DATABASE_URL", "valueFrom": "arn:aws:secretsmanager:us-east-1:296028253727:secret:bopmaps-prod-database-url-lw24tu-hG2Ill"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/bopmaps-prod/app", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8000/health/ || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}, "systemControls": []}], "family": "bopmaps-prod-app", "taskRoleArn": "arn:aws:iam::296028253727:role/bopmaps-prod-ecs-task", "executionRoleArn": "arn:aws:iam::296028253727:role/bopmaps-prod-ecs-task-execution", "networkMode": "awsvpc", "revision": 5, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "ecs.capability.secrets.asm.environment-variables"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.container-health-check"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "registeredAt": "2025-07-06T18:41:25.171000-05:00", "registeredBy": "arn:aws:iam::296028253727:user/Admin"}