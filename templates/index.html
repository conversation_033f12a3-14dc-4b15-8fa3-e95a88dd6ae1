<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BOPMaps - Musical Geocaching App</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            margin-top: 40px;
        }
        h2 {
            color: #3498db;
            margin-top: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .api-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .btn {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            margin: 10px 5px;
            border-radius: 5px;
            text-decoration: none;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 0.9em;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BOPMaps</h1>
        <p>A Musical Geocaching App for Sharing and Discovering Music in the Real World</p>
    </div>

    <div class="api-section">
        <h2>API Exploration</h2>
        <p>This is the backend server for BOPMaps. You can explore the API documentation using the links below:</p>
        
        <a href="/api/schema/swagger-ui/" class="btn">Swagger UI</a>
        <a href="/api/schema/redoc/" class="btn">ReDoc</a>
        <a href="/api/schema/" class="btn">Raw Schema</a>
    </div>

    <div>
        <h2>Key Features</h2>
        <ul>
            <li><strong>Music Integration:</strong> Connect with Spotify and other music services</li>
            <li><strong>Geocaching:</strong> Drop and discover music pins on the map</li>
            <li><strong>Social Features:</strong> Connect with friends and share your music discoveries</li>
            <li><strong>Gamification:</strong> Collect rewards and compete on leaderboards</li>
        </ul>
    </div>

    <div>
        <h2>API Endpoints</h2>
        <ul>
            <li><a href="/api/users/">/api/users/</a> - User management</li>
            <li><a href="/api/pins/">/api/pins/</a> - Music pins</li>
            <li><a href="/api/music/">/api/music/</a> - Music service integration</li>
            <li><a href="/api/friends/">/api/friends/</a> - Social connections</li>
            <li><a href="/api/gamification/">/api/gamification/</a> - Achievements and rewards</li>
            <li><a href="/api/geo/">/api/geo/</a> - Geolocation services</li>
        </ul>
    </div>

    <div class="footer">
        <p>BOPMaps Demo Server - &copy; 2025</p>
    </div>
</body>
</html> 