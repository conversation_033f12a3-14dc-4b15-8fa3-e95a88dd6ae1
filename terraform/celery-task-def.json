[{"name": "celery-worker", "image": "296028253727.dkr.ecr.us-east-1.amazonaws.com/bopmaps-prod:latest", "command": ["celery", "-A", "bopmaps", "worker", "-l", "INFO"], "essential": true, "environment": [{"name": "ENVIRONMENT", "value": "prod"}, {"name": "DEBUG", "value": "False"}, {"name": "B2_APPLICATION_KEY_ID", "value": "005b50dabb51c630000000001"}, {"name": "B2_APPLICATION_KEY", "value": "K0058c2wlRv7QFP/2On8cXsQ6XRV9fw"}, {"name": "B2_BUCKET_NAME", "value": "bopmaps-prod-static-67ba6151"}, {"name": "B2_ENDPOINT_URL", "value": "https://s3.us-east-005.backblazeb2.com"}, {"name": "B2_REGION", "value": "us-east-005"}, {"name": "MEDIA_BUCKET_NAME", "value": "bopmaps-prod-media-67ba6151"}, {"name": "B2_TILES_BUCKET_NAME", "value": "bopmaps-prod-tiles-67ba6151"}, {"name": "USE_S3_FOR_MEDIA", "value": "True"}, {"name": "REDIS_URL", "value": "redis://:<EMAIL>:6379/0"}, {"name": "SPOTIFY_CLIENT_ID", "value": "fb25ee73d874499a9c424cd5a2466e13"}, {"name": "SPOTIFY_CLIENT_SECRET", "value": "33aaa655d24846c9b6f61ef24ecf9710"}, {"name": "SPOTIFY_REDIRECT_URI", "value": "http://localhost:8888/callback"}, {"name": "SPOTIFY_MOBILE_REDIRECT_URI", "value": "bopmaps://callback"}, {"name": "LASTFM_API_KEY", "value": "fd7597d7a76640941f4db64e38649529"}, {"name": "SOUNDCLOUD_CLIENT_ID", "value": ""}, {"name": "SOUNDCLOUD_CLIENT_SECRET", "value": ""}, {"name": "APPLE_MUSIC_KEY_ID", "value": ""}, {"name": "APPLE_MUSIC_PRIVATE_KEY", "value": ""}, {"name": "APPLE_MUSIC_TEAM_ID", "value": ""}, {"name": "ONESIGNAL_APP_ID", "value": ""}, {"name": "ONESIGNAL_API_KEY", "value": ""}, {"name": "ONESIGNAL_API_URL", "value": "https://onesignal.com/api/v1/notifications"}, {"name": "CORS_ALLOWED_ORIGINS", "value": "http://localhost:3000"}, {"name": "CSRF_TRUSTED_ORIGINS", "value": "http://localhost:3000"}, {"name": "ALLOWED_REDIRECT_URIS", "value": "bopmaps://callback"}, {"name": "JWT_ACCESS_TOKEN_LIFETIME", "value": "36500"}, {"name": "JWT_REFRESH_TOKEN_LIFETIME", "value": "36500"}, {"name": "EMAIL_BACKEND", "value": "django.core.mail.backends.console.EmailBackend"}, {"name": "EMAIL_HOST", "value": ""}, {"name": "EMAIL_PORT", "value": "587"}, {"name": "EMAIL_HOST_USER", "value": ""}, {"name": "EMAIL_HOST_PASSWORD", "value": ""}, {"name": "EMAIL_USE_TLS", "value": "true"}, {"name": "DEFAULT_FROM_EMAIL", "value": "<EMAIL>"}, {"name": "SERVER_EMAIL", "value": "<EMAIL>"}, {"name": "SECURE_SSL_REDIRECT", "value": "true"}, {"name": "SES_CONFIGURATION_SET", "value": "bopmaps-emails"}, {"name": "AWS_REGION", "value": "us-east-1"}], "secrets": [{"name": "SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:296028253727:secret:bopmaps-prod-django-secret-ZncVBP"}, {"name": "DATABASE_URL", "valueFrom": "arn:aws:secretsmanager:us-east-1:296028253727:secret:bopmaps-prod-database-url-9aQM7F"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/bopmaps-prod/celery", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}]