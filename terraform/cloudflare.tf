# Cloudflare DNS Records with Backblaze B2 Integration
# Note: App and tiles DNS records already exist in Cloudflare
# If you need to manage them via Terraform, import them first:
# terraform import cloudflare_record.app <record_id>
# terraform import cloudflare_record.tiles <record_id>

resource "cloudflare_record" "app" {
  zone_id = var.cloudflare_zone_id
  name    = var.domain_name
  content = aws_lb.main.dns_name
  type    = "CNAME"
  ttl     = 1  # Required for proxied records
  proxied = true

  comment = "Main application domain"
}

# resource "cloudflare_record" "tiles" {
#   zone_id = var.cloudflare_zone_id
#   name    = var.tiles_domain_name
#   content = aws_lb.main.dns_name
#   type    = "CNAME"
#   ttl     = 1  # Required for proxied records
#   proxied = true
#
#   comment = "Tile server domain - cached by Cloudflare"
# }

# Backblaze B2 via Cloudflare CDN
resource "cloudflare_record" "static" {
  zone_id = var.cloudflare_zone_id
  name    = "static"
  content = replace(local.b2_static_url, "https://", "")
  type    = "CNAME"
  ttl     = 1  # Required for proxied records
  proxied = true

  comment = "Static files CDN via Backblaze B2"
}

resource "cloudflare_record" "media" {
  zone_id = var.cloudflare_zone_id
  name    = "media"
  content = replace(local.b2_media_url, "https://", "")
  type    = "CNAME"
  ttl     = 1  # Required for proxied records
  proxied = true

  comment = "Media files CDN via Backblaze B2"
}

resource "cloudflare_record" "b2tiles" {
  zone_id = var.cloudflare_zone_id
  name    = "b2tiles"
  content = replace(local.b2_tiles_url, "https://", "")
  type    = "CNAME"
  ttl     = 1  # Required for proxied records
  proxied = true

  comment = "Map tiles CDN via Backblaze B2"
}

# Cloudflare Cache Rules for B2 Buckets
# Note: Page rules require additional Cloudflare API permissions
# Create these manually in Cloudflare dashboard or update API token permissions

# resource "cloudflare_page_rule" "b2_static_cache" {
#   zone_id = var.cloudflare_zone_id
#   target  = "https://static.${var.domain_name}/*"
#   priority = 1
#
#   actions {
#     cache_level = "cache_everything"
#     edge_cache_ttl = 7200  # 2 hours
#     browser_cache_ttl = 86400  # 24 hours
#   }
# }
#
# resource "cloudflare_page_rule" "b2_media_cache" {
#   zone_id = var.cloudflare_zone_id
#   target  = "https://media.${var.domain_name}/*"
#   priority = 2
#
#   actions {
#     cache_level = "cache_everything"
#     edge_cache_ttl = 3600  # 1 hour
#     browser_cache_ttl = 43200  # 12 hours
#   }
# }
#
# resource "cloudflare_page_rule" "b2_tiles_cache" {
#   zone_id = var.cloudflare_zone_id
#   target  = "https://b2tiles.${var.domain_name}/*"
#   priority = 3
#
#   actions {
#     cache_level = "cache_everything"
#     edge_cache_ttl = 86400  # 24 hours
#     browser_cache_ttl = 604800  # 7 days
#   }
# }

# Note: Zone settings can be configured manually in Cloudflare dashboard
# Common optimizations to enable manually:
# - Always Use HTTPS: ON
# - Automatic HTTPS Rewrites: ON  
# - SSL/TLS Mode: Full
# - Minimum TLS Version: 1.2
# - Brotli Compression: ON
# - Auto Minify (CSS, JS, HTML): ON

# Output Cloudflare information
output "cloudflare_dns_records" {
  description = "Cloudflare DNS records created"
  value = {
    app     = cloudflare_record.app.hostname
    # tiles   = cloudflare_record.tiles.hostname  # Managed outside Terraform
    static  = cloudflare_record.static.hostname
    media   = cloudflare_record.media.hostname
    b2tiles = cloudflare_record.b2tiles.hostname
  }
}

# Note: Map tiles will be automatically cached by Cloudflare's caching rules
# We've set up specific page rules for optimal caching of B2 content 