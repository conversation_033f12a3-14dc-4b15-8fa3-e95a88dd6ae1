// Cloudflare Worker for tile optimization and caching
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url)
  const cacheKey = new Request(url.toString(), request)
  
  // Check if this is a tile request
  const tileMatch = url.pathname.match(/^\/tiles\/(\d+)\/(\d+)\/(\d+)\.(png|jpg|webp|pbf)$/)
  
  if (!tileMatch) {
    // Not a tile request, pass through
    return fetch(request)
  }
  
  const [, z, x, y, format] = tileMatch
  const zoom = parseInt(z)
  const tileX = parseInt(x)
  const tileY = parseInt(y)
  
  // Validate tile coordinates
  if (zoom < 0 || zoom > 20 || 
      tileX < 0 || tileX >= Math.pow(2, zoom) ||
      tileY < 0 || tileY >= Math.pow(2, zoom)) {
    return new Response('Invalid tile coordinates', { status: 400 })
  }
  
  // Check cache first
  const cache = caches.default
  let response = await cache.match(cacheKey)
  
  if (response) {
    // Add cache hit header
    const newResponse = new Response(response.body, response)
    newResponse.headers.set('X-Cache', 'HIT')
    newResponse.headers.set('X-Cache-Worker', 'Cloudflare')
    return newResponse
  }
  
  // Cache miss - fetch from origin
  try {
    response = await fetch(request)
    
    if (response.ok) {
      // Clone response for caching
      const responseToCache = response.clone()
      
      // Set appropriate cache headers based on zoom level
      const cacheHeaders = new Headers(response.headers)
      
      if (zoom <= 10) {
        // Low zoom tiles - cache for 30 days
        cacheHeaders.set('Cache-Control', 'public, max-age=2592000, immutable')
      } else if (zoom <= 15) {
        // Medium zoom tiles - cache for 7 days
        cacheHeaders.set('Cache-Control', 'public, max-age=604800, immutable')
      } else {
        // High zoom tiles - cache for 1 day
        cacheHeaders.set('Cache-Control', 'public, max-age=86400, immutable')
      }
      
      cacheHeaders.set('X-Cache', 'MISS')
      cacheHeaders.set('X-Cache-Worker', 'Cloudflare')
      
      // Create new response with updated headers
      const newResponse = new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: cacheHeaders
      })
      
      // Store in cache
      event.waitUntil(cache.put(cacheKey, responseToCache))
      
      return newResponse
    } else {
      // Origin error - return error response
      return response
    }
  } catch (error) {
    // Network error - return cached error page or fallback
    return new Response('Service temporarily unavailable', { 
      status: 503,
      headers: {
        'Content-Type': 'text/plain',
        'Retry-After': '60'
      }
    })
  }
}

// Optional: Add tile format conversion
async function convertTileFormat(originalResponse, targetFormat) {
  // This would require additional processing
  // For now, just return the original response
  return originalResponse
}

// Optional: Add tile compression
async function compressTile(response) {
  // This would require additional processing
  // For now, just return the original response
  return response
} 