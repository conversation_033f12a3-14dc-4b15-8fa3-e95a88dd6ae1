function handler(event) {
    var request = event.request;
    var uri = request.uri;
    
    // Normalize multiple slashes to single slash
    if (uri.includes('//')) {
        uri = uri.replace(/\/+/g, '/');
        request.uri = uri;
    }
    
    // Remove trailing slash except for root
    if (uri.length > 1 && uri.endsWith('/')) {
        request.uri = uri.slice(0, -1);
        return {
            statusCode: 301,
            statusDescription: 'Moved Permanently',
            headers: {
                location: { value: request.uri }
            }
        };
    }
    
    // Add .html extension for clean URLs if file doesn't have extension
    if (!uri.includes('.') && !uri.endsWith('/')) {
        request.uri = uri + '.html';
    }
    
    // Default to index.html for directory requests
    if (uri.endsWith('/')) {
        request.uri = uri + 'index.html';
    }
    
    return request;
} 