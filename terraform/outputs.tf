# Network Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = aws_subnet.public[*].id
}

output "private_subnet_ids" {
  description = "IDs of the private subnets"
  value       = aws_subnet.private[*].id
}

output "database_subnet_ids" {
  description = "IDs of the database subnets"
  value       = aws_subnet.database[*].id
}

# Load Balancer Outputs
output "load_balancer_url" {
  description = "URL of the load balancer"
  value       = "https://${aws_lb.main.dns_name}"
}

output "load_balancer_zone_id" {
  description = "Zone ID of the load balancer"
  value       = aws_lb.main.zone_id
}

# Database Outputs
output "database_endpoint" {
  description = "Database endpoint"
  value       = aws_db_instance.main.endpoint
  sensitive   = true
}

output "database_port" {
  description = "Database port"
  value       = aws_db_instance.main.port
}

output "database_name" {
  description = "Database name"
  value       = aws_db_instance.main.db_name
}

output "database_username" {
  description = "Database username"
  value       = aws_db_instance.main.username
  sensitive   = true
}

# Redis Outputs
output "redis_endpoint" {
  description = "Redis endpoint"
  value       = aws_elasticache_replication_group.redis.primary_endpoint_address
  sensitive   = true
}

output "redis_port" {
  description = "Redis port"
  value       = aws_elasticache_replication_group.redis.port
}

# ECS Outputs
output "ecs_cluster_name" {
  description = "Name of the ECS cluster"
  value       = aws_ecs_cluster.main.name
}

output "ecs_cluster_arn" {
  description = "ARN of the ECS cluster"
  value       = aws_ecs_cluster.main.arn
}

output "ecs_security_group_id" {
  description = "ID of the ECS security group"
  value       = aws_security_group.ecs.id
}

# ECR Outputs
output "ecr_repository_url" {
  description = "URL of the ECR repository for the application"
  value       = aws_ecr_repository.app.repository_url
}

# Note: Tileserver ECR output removed - using B2 upload instead

# B2 Storage Outputs
output "b2_static_bucket_name" {
  description = "Name of the B2 static files bucket"
  value       = b2_bucket.static.bucket_name
}

output "b2_media_bucket_name" {
  description = "Name of the B2 media files bucket"
  value       = b2_bucket.media.bucket_name
}

output "b2_tiles_bucket_name" {
  description = "Name of the B2 tiles bucket"
  value       = b2_bucket.tiles.bucket_name
}

output "b2_endpoint_url" {
  description = "B2 S3-compatible endpoint URL"
  value       = local.b2_s3_endpoint
}

# CloudFront Outputs
output "static_domain" {
  description = "Cloudflare domain for static files"
  value       = "static.${var.domain_name}"
}

output "media_domain" {
  description = "Cloudflare domain for media files"
  value       = "media.${var.domain_name}"
}

output "b2tiles_domain" {
  description = "Cloudflare domain for B2 tiles"
  value       = "b2tiles.${var.domain_name}"
}

# Note: EFS outputs removed - using B2 upload instead

# Secrets Manager Outputs
output "django_secret_arn" {
  description = "ARN of the Django secret in Secrets Manager"
  value       = aws_secretsmanager_secret.django_secret.arn
}

output "database_secret_arn" {
  description = "ARN of the database password in Secrets Manager"
  value       = aws_secretsmanager_secret.db_password.arn
}

output "redis_secret_arn" {
  description = "ARN of the Redis auth token in Secrets Manager"
  value       = aws_secretsmanager_secret.redis_auth.arn
}

# Domain Outputs
output "application_url" {
  description = "URL of the application"
  value       = "https://${var.domain_name}"
}

output "tiles_url" {
  description = "URL of the tile server"
  value       = "https://${var.tiles_domain_name}"
}

output "static_url" {
  description = "URL of the static files CDN"
  value       = "https://static.${var.domain_name}"
}

output "media_url" {
  description = "URL of the media files CDN"
  value       = "https://media.${var.domain_name}"
}

# Monitoring Outputs
output "cloudwatch_dashboard_url" {
  description = "URL of the CloudWatch dashboard"
  value       = "https://${var.aws_region}.console.aws.amazon.com/cloudwatch/home?region=${var.aws_region}#dashboards:name=${aws_cloudwatch_dashboard.main.dashboard_name}"
}

output "sns_alerts_topic_arn" {
  description = "ARN of the SNS topic for alerts"
  value       = aws_sns_topic.alerts.arn
}

# Cost Optimization Information
output "estimated_monthly_cost" {
  description = "Estimated monthly cost breakdown (USD)"
  value = {
    rds_instance      = "~$50-100 (db.t4g.medium)"
    ecs_fargate       = "~$20-40 (2 app + 2 celery instances, no tileserver)"
    alb               = "~$16 (fixed cost)"
    nat_gateways      = "~$90 (2 NAT gateways)"
    elasticache       = "~$12 (cache.t4g.micro)"
    b2_storage        = "~$5-10 (Backblaze B2, cheaper than S3)"
    cloudfront        = "~$5-15 (depending on traffic)"
    data_transfer     = "~$10-50 (depending on traffic)"
    total_estimate    = "~$200-330/month"
    note             = "Costs reduced by removing EFS and tileserver. Use Reserved Instances and Savings Plans for ~30% savings."
  }
}

# Deployment Commands
output "deployment_commands" {
  description = "Commands to deploy the application"
  value = {
    docker_login    = "aws ecr get-login-password --region ${var.aws_region} | docker login --username AWS --password-stdin ${aws_ecr_repository.app.repository_url}"
    build_and_push  = "docker build -t ${aws_ecr_repository.app.repository_url}:latest . && docker push ${aws_ecr_repository.app.repository_url}:latest"
    update_service  = "aws ecs update-service --cluster ${aws_ecs_cluster.main.name} --service ${aws_ecs_service.app.name} --force-new-deployment"
  }
}

# Environment Variables for Application
output "application_environment_variables" {
  description = "Environment variables to configure in the application"
  value = {
    DATABASE_URL              = "Retrieved from AWS Secrets Manager"
    REDIS_URL                = "Retrieved from AWS Secrets Manager"
    SECRET_KEY               = "Retrieved from AWS Secrets Manager"
    B2_APPLICATION_KEY_ID    = var.b2_application_key_id
    B2_APPLICATION_KEY       = var.b2_application_key
    B2_BUCKET_NAME          = b2_bucket.static.bucket_name
    B2_ENDPOINT_URL         = local.b2_s3_endpoint
    B2_REGION               = var.b2_region
    MEDIA_BUCKET_NAME       = b2_bucket.media.bucket_name
    USE_S3_FOR_MEDIA        = "True"
    ENVIRONMENT             = var.environment
    DEBUG                   = "False"
    ALLOWED_HOSTS           = var.domain_name
    CORS_ALLOWED_ORIGINS    = "https://${var.domain_name}"
  }
  sensitive = true
} 