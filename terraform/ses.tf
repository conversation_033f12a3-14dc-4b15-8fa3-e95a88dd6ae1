# AWS SES Configuration for BOPMaps
# This file sets up Simple Email Service for sending verification emails

# Email <NAME_EMAIL>
resource "aws_ses_email_identity" "admin_email" {
  email = "<EMAIL>"
}

# Domain identity for bopmaps.com
resource "aws_ses_domain_identity" "bopmaps_domain" {
  domain = "bopmaps.com"
}

# DKIM for domain authentication
resource "aws_ses_domain_dkim" "bopmaps_dkim" {
  domain = aws_ses_domain_identity.bopmaps_domain.domain
}

# Configuration set for tracking email metrics
resource "aws_ses_configuration_set" "bopmaps_emails" {
  name = "bopmaps-emails"

  delivery_options {
    tls_policy = "Require"
  }

  reputation_metrics_enabled = true
  sending_enabled            = true
}

# Event destination for bounce and complaint tracking
resource "aws_ses_event_destination" "cloudwatch" {
  name                   = "cloudwatch-destination"
  configuration_set_name = aws_ses_configuration_set.bopmaps_emails.name
  enabled                = true
  matching_types         = ["send", "reject", "bounce", "complaint", "delivery"]

  cloudwatch_destination {
    default_value  = "default"
    dimension_name = "MessageTag"
    value_source   = "messageTag"
  }
}

# IAM policy for SES permissions
resource "aws_iam_policy" "ses_send_policy" {
  name        = "bopmaps-ses-send-policy"
  description = "Policy for sending emails via SES"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ses:SendEmail",
          "ses:SendRawEmail",
          "ses:SendTemplatedEmail",
          "ses:SendBulkTemplatedEmail"
        ]
        Resource = "*"
        Condition = {
          StringEquals = {
            "ses:FromAddress" = [
              "<EMAIL>",
              "<EMAIL>",
              "<EMAIL>"
            ]
          }
        }
      }
    ]
  })
  
  tags = local.common_tags
}

# Attach SES policy to ECS task role
resource "aws_iam_role_policy_attachment" "ecs_task_ses_policy" {
  role       = aws_iam_role.ecs_task.name
  policy_arn = aws_iam_policy.ses_send_policy.arn
}

# Email templates
resource "aws_ses_template" "user_verification" {
  name    = "user-verification"
  subject = "🎵 Verify Your Email - BOP Maps"

  html = file("${path.module}/email_templates/user_verification.html")
  text = file("${path.module}/email_templates/user_verification.txt")
}

resource "aws_ses_template" "school_verification" {
  name    = "school-verification"
  subject = "🎓 Verify Your School Email - BOP Maps"

  html = file("${path.module}/email_templates/school_verification.html")
  text = file("${path.module}/email_templates/school_verification.txt")
}

resource "aws_ses_template" "school_verification_success" {
  name    = "school-verification-success"
  subject = "🎉 Welcome to BOP Maps - School Verification Complete!"

  html = file("${path.module}/email_templates/school_verification_success.html")
  text = file("${path.module}/email_templates/school_verification_success.txt")
}

# CloudWatch log group for SES logs
resource "aws_cloudwatch_log_group" "ses_logs" {
  name              = "/aws/ses/bopmaps"
  retention_in_days = 14
  
  tags = local.common_tags
}

# Outputs for use in application
output "ses_domain_identity_arn" {
  description = "ARN of the SES domain identity"
  value       = aws_ses_domain_identity.bopmaps_domain.arn
}

output "ses_configuration_set_name" {
  description = "Name of the SES configuration set"
  value       = aws_ses_configuration_set.bopmaps_emails.name
}

output "ses_admin_email" {
  description = "Verified admin email address"
  value       = aws_ses_email_identity.admin_email.email
}

output "ses_dkim_tokens" {
  description = "DKIM tokens for DNS configuration"
  value       = aws_ses_domain_dkim.bopmaps_dkim.dkim_tokens
} 