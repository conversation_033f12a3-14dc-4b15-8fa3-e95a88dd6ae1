# Backblaze B2 Storage Configuration

provider "b2" {
  application_key_id = var.b2_application_key_id
  application_key    = var.b2_application_key
}

# Random suffix for bucket names to ensure uniqueness
resource "random_id" "b2_bucket_suffix" {
  byte_length = 4
}

# B2 Buckets
resource "b2_bucket" "static" {
  bucket_name = "${var.b2_bucket_name_prefix}-${var.environment}-static-${random_id.b2_bucket_suffix.hex}"
  bucket_type = "allPublic"
  
  lifecycle_rules {
    file_name_prefix = ""
    days_from_hiding_to_deleting = 1
    days_from_uploading_to_hiding = 0
  }

  cors_rules {
    cors_rule_name    = "downloadFromAnyOrigin"
    allowed_origins   = ["*"]
    allowed_headers   = ["*"]
    allowed_operations = ["b2_download_file_by_name", "b2_download_file_by_id"]
    expose_headers    = ["x-bz-content-sha1", "x-bz-file-id", "x-bz-file-name"]
    max_age_seconds   = 3600
  }
}

resource "b2_bucket" "media" {
  bucket_name = "${var.b2_bucket_name_prefix}-${var.environment}-media-${random_id.b2_bucket_suffix.hex}"
  bucket_type = "allPublic"
  
  lifecycle_rules {
    file_name_prefix = ""
    days_from_hiding_to_deleting = 30
    days_from_uploading_to_hiding = 0
  }

  cors_rules {
    cors_rule_name    = "downloadFromApp"
    allowed_origins   = ["https://${var.domain_name}"]
    allowed_headers   = ["*"]
    allowed_operations = ["b2_download_file_by_name", "b2_download_file_by_id", "b2_upload_file", "b2_upload_part"]
    expose_headers    = ["x-bz-content-sha1", "x-bz-file-id", "x-bz-file-name"]
    max_age_seconds   = 3600
  }
}

resource "b2_bucket" "tiles" {
  bucket_name = "${var.b2_bucket_name_prefix}-${var.environment}-tiles-${random_id.b2_bucket_suffix.hex}"
  bucket_type = "allPublic"
  
  lifecycle_rules {
    file_name_prefix = ""
    days_from_hiding_to_deleting = 90
    days_from_uploading_to_hiding = 0
  }

  cors_rules {
    cors_rule_name    = "tilesAccess"
    allowed_origins   = ["*"]
    allowed_headers   = ["*"]
    allowed_operations = ["b2_download_file_by_name", "b2_download_file_by_id"]
    expose_headers    = ["x-bz-content-sha1", "x-bz-file-id", "x-bz-file-name"]
    max_age_seconds   = 86400
  }
}

# B2 Bucket URLs (for Cloudflare integration)
locals {
  b2_static_url = "https://f${format("%03d", tonumber(split("-", var.b2_region)[2]))}.backblazeb2.com/file/${b2_bucket.static.bucket_name}"
  b2_media_url  = "https://f${format("%03d", tonumber(split("-", var.b2_region)[2]))}.backblazeb2.com/file/${b2_bucket.media.bucket_name}"
  b2_tiles_url  = "https://f${format("%03d", tonumber(split("-", var.b2_region)[2]))}.backblazeb2.com/file/${b2_bucket.tiles.bucket_name}"
  
  # S3-compatible API endpoints for Django
  b2_s3_endpoint = "https://s3.${var.b2_region}.backblazeb2.com"
}

# Output B2 bucket information
output "b2_static_bucket" {
  description = "B2 static bucket name"
  value       = b2_bucket.static.bucket_name
}

output "b2_media_bucket" {
  description = "B2 media bucket name"
  value       = b2_bucket.media.bucket_name
}

output "b2_tiles_bucket" {
  description = "B2 tiles bucket name"
  value       = b2_bucket.tiles.bucket_name
}

output "b2_static_url" {
  description = "B2 static files URL"
  value       = local.b2_static_url
}

output "b2_media_url" {
  description = "B2 media files URL"
  value       = local.b2_media_url
}

output "b2_tiles_url" {
  description = "B2 tiles URL"
  value       = local.b2_tiles_url
}

output "b2_s3_endpoint" {
  description = "B2 S3-compatible endpoint"
  value       = local.b2_s3_endpoint
} 