{"version": 4, "terraform_version": "1.5.7", "serial": 1412, "lineage": "9cc25646-d8f0-aa73-84b8-90693e371c32", "outputs": {"application_environment_variables": {"value": {"ALLOWED_HOSTS": "api.bopmaps.com", "B2_APPLICATION_KEY": "K0058c2wlRv7QFP/2On8cXsQ6XRV9fw", "B2_APPLICATION_KEY_ID": "005b50dabb51c630000000001", "B2_BUCKET_NAME": "bopmaps-prod-static-67ba6151", "B2_ENDPOINT_URL": "https://s3.us-east-005.backblazeb2.com", "B2_REGION": "us-east-005", "CORS_ALLOWED_ORIGINS": "https://api.bopmaps.com", "DATABASE_URL": "Retrieved from AWS Secrets Manager", "DEBUG": "False", "ENVIRONMENT": "prod", "MEDIA_BUCKET_NAME": "bopmaps-prod-media-67ba6151", "REDIS_URL": "Retrieved from AWS Secrets Manager", "SECRET_KEY": "Retrieved from AWS Secrets Manager", "USE_S3_FOR_MEDIA": "True"}, "type": ["object", {"ALLOWED_HOSTS": "string", "B2_APPLICATION_KEY": "string", "B2_APPLICATION_KEY_ID": "string", "B2_BUCKET_NAME": "string", "B2_ENDPOINT_URL": "string", "B2_REGION": "string", "CORS_ALLOWED_ORIGINS": "string", "DATABASE_URL": "string", "DEBUG": "string", "ENVIRONMENT": "string", "MEDIA_BUCKET_NAME": "string", "REDIS_URL": "string", "SECRET_KEY": "string", "USE_S3_FOR_MEDIA": "string"}], "sensitive": true}, "application_url": {"value": "https://api.bopmaps.com", "type": "string"}, "b2_endpoint_url": {"value": "https://s3.us-east-005.backblazeb2.com", "type": "string"}, "b2_media_bucket": {"value": "bopmaps-prod-media-67ba6151", "type": "string"}, "b2_media_bucket_name": {"value": "bopmaps-prod-media-67ba6151", "type": "string"}, "b2_media_url": {"value": "https://f005.backblazeb2.com/file/bopmaps-prod-media-67ba6151", "type": "string"}, "b2_s3_endpoint": {"value": "https://s3.us-east-005.backblazeb2.com", "type": "string"}, "b2_static_bucket": {"value": "bopmaps-prod-static-67ba6151", "type": "string"}, "b2_static_bucket_name": {"value": "bopmaps-prod-static-67ba6151", "type": "string"}, "b2_static_url": {"value": "https://f005.backblazeb2.com/file/bopmaps-prod-static-67ba6151", "type": "string"}, "b2_tiles_bucket": {"value": "bopmaps-prod-tiles-67ba6151", "type": "string"}, "b2_tiles_bucket_name": {"value": "bopmaps-prod-tiles-67ba6151", "type": "string"}, "b2_tiles_url": {"value": "https://f005.backblazeb2.com/file/bopmaps-prod-tiles-67ba6151", "type": "string"}, "b2tiles_domain": {"value": "b2tiles.api.bopmaps.com", "type": "string"}, "certificate_arn": {"value": "arn:aws:acm:us-east-1:************:certificate/d4e4f752-a782-4418-a861-bd3d9e54c9d2", "type": "string"}, "cloudflare_dns_records": {"value": {"app": "api.bopmaps.com", "b2tiles": "b2tiles.bopmaps.com", "media": "media.bopmaps.com", "static": "static.bopmaps.com"}, "type": ["object", {"app": "string", "b2tiles": "string", "media": "string", "static": "string"}]}, "cloudwatch_dashboard_url": {"value": "https://us-east-1.console.aws.amazon.com/cloudwatch/home?region=us-east-1#dashboards:name=bopmaps-prod-dashboard", "type": "string"}, "database_endpoint": {"value": "bopmaps-prod-postgres.c2vcs4guq0ku.us-east-1.rds.amazonaws.com:5432", "type": "string", "sensitive": true}, "database_name": {"value": "bopmaps", "type": "string"}, "database_port": {"value": 5432, "type": "number"}, "database_secret_arn": {"value": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-db-password-rFvGs1", "type": "string"}, "database_subnet_ids": {"value": ["subnet-09848662178d915de", "subnet-020ac6c5d382b814a"], "type": ["tuple", ["string", "string"]]}, "database_username": {"value": "bopmaps_user", "type": "string", "sensitive": true}, "deployment_commands": {"value": {"build_and_push": "docker build -t ************.dkr.ecr.us-east-1.amazonaws.com/bopmaps-prod:latest . && docker push ************.dkr.ecr.us-east-1.amazonaws.com/bopmaps-prod:latest", "docker_login": "aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com/bopmaps-prod", "update_service": "aws ecs update-service --cluster bopmaps-prod --service bopmaps-prod-app --force-new-deployment"}, "type": ["object", {"build_and_push": "string", "docker_login": "string", "update_service": "string"}]}, "django_secret_arn": {"value": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-django-secret-pW6vE9", "type": "string"}, "ecr_repository_url": {"value": "************.dkr.ecr.us-east-1.amazonaws.com/bopmaps-prod", "type": "string"}, "ecs_cluster_arn": {"value": "arn:aws:ecs:us-east-1:************:cluster/bopmaps-prod", "type": "string"}, "ecs_cluster_name": {"value": "bopmaps-prod", "type": "string"}, "estimated_monthly_cost": {"value": {"alb": "~$16 (fixed cost)", "b2_storage": "~$5-10 (Backblaze B2, cheaper than S3)", "cloudfront": "~$5-15 (depending on traffic)", "data_transfer": "~$10-50 (depending on traffic)", "ecs_fargate": "~$20-40 (2 app + 2 celery instances, no tileserver)", "elasticache": "~$12 (cache.t4g.micro)", "nat_gateways": "~$90 (2 NAT gateways)", "note": "Costs reduced by removing EFS and tileserver. Use Reserved Instances and Savings Plans for ~30% savings.", "rds_instance": "~$50-100 (db.t4g.medium)", "total_estimate": "~$200-330/month"}, "type": ["object", {"alb": "string", "b2_storage": "string", "cloudfront": "string", "data_transfer": "string", "ecs_fargate": "string", "elasticache": "string", "nat_gateways": "string", "note": "string", "rds_instance": "string", "total_estimate": "string"}]}, "load_balancer_url": {"value": "https://bopmaps-prod-alb-*********.us-east-1.elb.amazonaws.com", "type": "string"}, "load_balancer_zone_id": {"value": "Z35SXDOTRQ7X7K", "type": "string"}, "media_domain": {"value": "media.api.bopmaps.com", "type": "string"}, "media_url": {"value": "https://media.api.bopmaps.com", "type": "string"}, "private_subnet_ids": {"value": ["subnet-0284f054c45fb6f70", "subnet-0889380393e07eddb"], "type": ["tuple", ["string", "string"]]}, "public_subnet_ids": {"value": ["subnet-0093c6ce8bf0d0ee0", "subnet-0779972d6df51b24b"], "type": ["tuple", ["string", "string"]]}, "redis_endpoint": {"value": "master.bopmaps-prod-redis.0r0qne.use1.cache.amazonaws.com", "type": "string", "sensitive": true}, "redis_port": {"value": 6379, "type": "number"}, "redis_secret_arn": {"value": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-redis-auth-AU3Jd8", "type": "string"}, "ses_admin_email": {"value": "<EMAIL>", "type": "string"}, "ses_configuration_set_name": {"value": "bopmaps-emails", "type": "string"}, "ses_dkim_tokens": {"value": ["zakefia5gsrjl7cbjnprydlmr2fslawm", "i7ef7kcwim5jpvur5g4ijlza7lkxqweo", "2zryh3kgiaqwyjph2c7mccyeayqtjyl4"], "type": ["list", "string"]}, "ses_domain_identity_arn": {"value": "arn:aws:ses:us-east-1:************:identity/bopmaps.com", "type": "string"}, "sns_alerts_topic_arn": {"value": "arn:aws:sns:us-east-1:************:bopmaps-prod-alerts", "type": "string"}, "static_domain": {"value": "static.api.bopmaps.com", "type": "string"}, "static_url": {"value": "https://static.api.bopmaps.com", "type": "string"}, "tiles_url": {"value": "https://tiles.bopmaps.com", "type": "string"}, "vpc_id": {"value": "vpc-0f829382da4d81ab8", "type": "string"}}, "resources": [{"mode": "data", "type": "aws_availability_zones", "name": "available", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"all_availability_zones": null, "exclude_names": null, "exclude_zone_ids": null, "filter": null, "group_names": ["us-east-1-zg-1"], "id": "us-east-1", "names": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-1e", "us-east-1f"], "state": "available", "timeouts": null, "zone_ids": ["use1-az2", "use1-az4", "use1-az6", "use1-az1", "use1-az3", "use1-az5"]}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/SuperAdmin", "id": "************", "user_id": "AIDA4SZHNYEGC7IYLP52I"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "ecr_endpoint_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"ecr:GetDownloadUrlForLayer\",\n        \"ecr:GetAuthorizationToken\",\n        \"ecr:BatchGetImage\",\n        \"ecr:BatchCheckLayerAvailability\"\n      ],\n      \"Resource\": \"*\",\n      \"Principal\": {\n        \"AWS\": \"*\"\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"ecr:GetDownloadUrlForLayer\",\"ecr:GetAuthorizationToken\",\"ecr:BatchGetImage\",\"ecr:BatchCheckLayerAvailability\"],\"Resource\":\"*\",\"Principal\":{\"AWS\":\"*\"}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["ecr:BatchCheckLayerAvailability", "ecr:BatchGetImage", "ecr:GetAuthorizationToken", "ecr:GetDownloadUrlForLayer"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["*"], "type": "AWS"}], "resources": ["*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "media_bucket_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Sid\": \"AllowPublicRead\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"s3:GetObject\",\n      \"Resource\": \"arn:aws:s3:::bopmaps-prod-media-58e4c360/*\",\n      \"Principal\": \"*\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Sid\":\"AllowPublicRead\",\"Effect\":\"Allow\",\"Action\":\"s3:GetObject\",\"Resource\":\"arn:aws:s3:::bopmaps-prod-media-58e4c360/*\",\"Principal\":\"*\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["s3:GetObject"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["*"], "type": "*"}], "resources": ["arn:aws:s3:::bopmaps-prod-media-58e4c360/*"], "sid": "AllowPublicRead"}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "static_bucket_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "594136567", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Sid\": \"AllowPublicRead\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"s3:GetObject\",\n      \"Resource\": \"arn:aws:s3:::bopmaps-prod-static-58e4c360/*\",\n      \"Principal\": \"*\"\n    },\n    {\n      \"Sid\": \"AllowALBLogging\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"s3:PutObject\",\n      \"Resource\": \"arn:aws:s3:::bopmaps-prod-static-58e4c360/alb-logs/AWSLogs/************/*\",\n      \"Principal\": {\n        \"AWS\": \"arn:aws:iam::127311923021:root\"\n      },\n      \"Condition\": {\n        \"StringEquals\": {\n          \"s3:x-amz-acl\": \"bucket-owner-full-control\"\n        }\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Sid\":\"AllowPublicRead\",\"Effect\":\"Allow\",\"Action\":\"s3:GetObject\",\"Resource\":\"arn:aws:s3:::bopmaps-prod-static-58e4c360/*\",\"Principal\":\"*\"},{\"Sid\":\"AllowALBLogging\",\"Effect\":\"Allow\",\"Action\":\"s3:PutObject\",\"Resource\":\"arn:aws:s3:::bopmaps-prod-static-58e4c360/alb-logs/AWSLogs/************/*\",\"Principal\":{\"AWS\":\"arn:aws:iam::127311923021:root\"},\"Condition\":{\"StringEquals\":{\"s3:x-amz-acl\":\"bucket-owner-full-control\"}}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["s3:GetObject"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["*"], "type": "*"}], "resources": ["arn:aws:s3:::bopmaps-prod-static-58e4c360/*"], "sid": "AllowPublicRead"}, {"actions": ["s3:PutObject"], "condition": [{"test": "StringEquals", "values": ["bucket-owner-full-control"], "variable": "s3:x-amz-acl"}], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["arn:aws:iam::127311923021:root"], "type": "AWS"}], "resources": ["arn:aws:s3:::bopmaps-prod-static-58e4c360/alb-logs/AWSLogs/************/*"], "sid": "AllowALBLogging"}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_acm_certificate", "name": "cert", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:acm:us-east-1:************:certificate/d4e4f752-a782-4418-a861-bd3d9e54c9d2", "certificate_authority_arn": "", "certificate_body": null, "certificate_chain": null, "domain_name": "api.bopmaps.com", "domain_validation_options": [{"domain_name": "api.bopmaps.com", "resource_record_name": "_4a847e5cbfb09fab3487e2ebbdd21aef.api.bopmaps.com.", "resource_record_type": "CNAME", "resource_record_value": "_cdc00e4e8932b0ec2d3e6f6c99d88484.xlfgrmvvlj.acm-validations.aws."}, {"domain_name": "media.api.bopmaps.com", "resource_record_name": "_68201d01637d09e2412ca56ed2dcfa6d.media.api.bopmaps.com.", "resource_record_type": "CNAME", "resource_record_value": "_e535b7878ab1711dd13e5581844906ed.xlfgrmvvlj.acm-validations.aws."}, {"domain_name": "static.api.bopmaps.com", "resource_record_name": "_14903d84e385543801e698b1f6b3a3a8.static.api.bopmaps.com.", "resource_record_type": "CNAME", "resource_record_value": "_cc7e2e5483050cd5eff3f4a1cec20708.xlfgrmvvlj.acm-validations.aws."}, {"domain_name": "tiles.bopmaps.com", "resource_record_name": "_c2b6b1189029e6e67f362248aee56d56.tiles.bopmaps.com.", "resource_record_type": "CNAME", "resource_record_value": "_2593d4c5693cb9b3896043d57d6666ab.xlfgrmvvlj.acm-validations.aws."}], "early_renewal_duration": "", "id": "arn:aws:acm:us-east-1:************:certificate/d4e4f752-a782-4418-a861-bd3d9e54c9d2", "key_algorithm": "RSA_2048", "not_after": "2026-07-31T23:59:59Z", "not_before": "2025-07-02T00:00:00Z", "options": [{"certificate_transparency_logging_preference": "ENABLED"}], "pending_renewal": false, "private_key": null, "renewal_eligibility": "ELIGIBLE", "renewal_summary": [], "status": "ISSUED", "subject_alternative_names": ["api.bopmaps.com", "media.api.bopmaps.com", "static.api.bopmaps.com", "tiles.bopmaps.com"], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "type": "AMAZON_ISSUED", "validation_emails": [], "validation_method": "DNS", "validation_option": []}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_acm_certificate_validation", "name": "cert", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"certificate_arn": "arn:aws:acm:us-east-1:************:certificate/d4e4f752-a782-4418-a861-bd3d9e54c9d2", "id": "0001-01-01 00:00:00 +0000 UTC", "timeouts": {"create": "5m"}, "validation_record_fqdns": ["_14903d84e385543801e698b1f6b3a3a8.static.api.bopmaps.com", "_4a847e5cbfb09fab3487e2ebbdd21aef.api.bopmaps.com", "_68201d01637d09e2412ca56ed2dcfa6d.media.api.bopmaps.com", "_c2b6b1189029e6e67f362248aee56d56.tiles.bopmaps.com"]}, "sensitive_attributes": [], "private": "****************************************************************************************", "dependencies": ["aws_acm_certificate.cert", "cloudflare_record.cert_validation"]}]}, {"mode": "managed", "type": "aws_appautoscaling_policy", "name": "app_cpu", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"alarm_arns": ["arn:aws:cloudwatch:us-east-1:************:alarm:TargetTracking-service/bopmaps-prod/bopmaps-prod-app-AlarmHigh-8a083797-410f-43ef-bf10-f4203786e467", "arn:aws:cloudwatch:us-east-1:************:alarm:TargetTracking-service/bopmaps-prod/bopmaps-prod-app-AlarmLow-53d87fa8-47e7-46af-8858-2141910be765"], "arn": "arn:aws:autoscaling:us-east-1:************:scalingPolicy:c283bf40-0f53-41a7-a4f7-7d8ac99b2976:resource/ecs/service/bopmaps-prod/bopmaps-prod-app:policyName/bopmaps-prod-app-cpu-scaling", "id": "bopmaps-prod-app-cpu-scaling", "name": "bopmaps-prod-app-cpu-scaling", "policy_type": "TargetTrackingScaling", "resource_id": "service/bopmaps-prod/bopmaps-prod-app", "scalable_dimension": "ecs:service:DesiredCount", "service_namespace": "ecs", "step_scaling_policy_configuration": [], "target_tracking_scaling_policy_configuration": [{"customized_metric_specification": [], "disable_scale_in": false, "predefined_metric_specification": [{"predefined_metric_type": "ECSServiceAverageCPUUtilization", "resource_label": ""}], "scale_in_cooldown": 300, "scale_out_cooldown": 300, "target_value": 70}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_acm_certificate.cert", "aws_acm_certificate_validation.cert", "aws_appautoscaling_target.app", "aws_cloudwatch_log_group.app", "aws_cloudwatch_log_group.ecs", "aws_ecr_repository.app", "aws_ecs_cluster.main", "aws_ecs_service.app", "aws_ecs_task_definition.app", "aws_elasticache_parameter_group.redis", "aws_elasticache_replication_group.redis", "aws_elasticache_subnet_group.main", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_lb.main", "aws_lb_listener.app_https", "aws_lb_target_group.app", "aws_s3_bucket.static", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.redis", "aws_service_discovery_private_dns_namespace.main", "aws_service_discovery_service.app", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "cloudflare_record.cert_validation", "data.aws_availability_zones.available", "random_id.b2_bucket_suffix", "random_id.bucket_suffix", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_appautoscaling_policy", "name": "app_memory", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"alarm_arns": ["arn:aws:cloudwatch:us-east-1:************:alarm:TargetTracking-service/bopmaps-prod/bopmaps-prod-app-AlarmHigh-895ad863-5525-4276-a166-cd3738b348e8", "arn:aws:cloudwatch:us-east-1:************:alarm:TargetTracking-service/bopmaps-prod/bopmaps-prod-app-AlarmLow-5a44028f-fd4c-4725-87b9-0b0a56efbc37"], "arn": "arn:aws:autoscaling:us-east-1:************:scalingPolicy:c283bf40-0f53-41a7-a4f7-7d8ac99b2976:resource/ecs/service/bopmaps-prod/bopmaps-prod-app:policyName/bopmaps-prod-app-memory-scaling", "id": "bopmaps-prod-app-memory-scaling", "name": "bopmaps-prod-app-memory-scaling", "policy_type": "TargetTrackingScaling", "resource_id": "service/bopmaps-prod/bopmaps-prod-app", "scalable_dimension": "ecs:service:DesiredCount", "service_namespace": "ecs", "step_scaling_policy_configuration": [], "target_tracking_scaling_policy_configuration": [{"customized_metric_specification": [], "disable_scale_in": false, "predefined_metric_specification": [{"predefined_metric_type": "ECSServiceAverageMemoryUtilization", "resource_label": ""}], "scale_in_cooldown": 300, "scale_out_cooldown": 300, "target_value": 80}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_acm_certificate.cert", "aws_acm_certificate_validation.cert", "aws_appautoscaling_target.app", "aws_cloudwatch_log_group.app", "aws_cloudwatch_log_group.ecs", "aws_ecr_repository.app", "aws_ecs_cluster.main", "aws_ecs_service.app", "aws_ecs_task_definition.app", "aws_elasticache_parameter_group.redis", "aws_elasticache_replication_group.redis", "aws_elasticache_subnet_group.main", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_lb.main", "aws_lb_listener.app_https", "aws_lb_target_group.app", "aws_s3_bucket.static", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.redis", "aws_service_discovery_private_dns_namespace.main", "aws_service_discovery_service.app", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "cloudflare_record.cert_validation", "data.aws_availability_zones.available", "random_id.b2_bucket_suffix", "random_id.bucket_suffix", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_appautoscaling_policy", "name": "app_request_count", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"alarm_arns": ["arn:aws:cloudwatch:us-east-1:************:alarm:TargetTracking-service/bopmaps-prod/bopmaps-prod-app-AlarmHigh-bf1a0802-a50a-4a12-bacb-82ef5039b610", "arn:aws:cloudwatch:us-east-1:************:alarm:TargetTracking-service/bopmaps-prod/bopmaps-prod-app-AlarmLow-8ba31770-be9c-415f-bee4-97d5ab81415b"], "arn": "arn:aws:autoscaling:us-east-1:************:scalingPolicy:c283bf40-0f53-41a7-a4f7-7d8ac99b2976:resource/ecs/service/bopmaps-prod/bopmaps-prod-app:policyName/bopmaps-prod-app-request-scaling", "id": "bopmaps-prod-app-request-scaling", "name": "bopmaps-prod-app-request-scaling", "policy_type": "TargetTrackingScaling", "resource_id": "service/bopmaps-prod/bopmaps-prod-app", "scalable_dimension": "ecs:service:DesiredCount", "service_namespace": "ecs", "step_scaling_policy_configuration": [], "target_tracking_scaling_policy_configuration": [{"customized_metric_specification": [], "disable_scale_in": false, "predefined_metric_specification": [{"predefined_metric_type": "ALBRequestCountPerTarget", "resource_label": "app/bopmaps-prod-alb/bf637793e7b7602b/targetgroup/bopmaps-prod-app/23c232f58b2846bb"}], "scale_in_cooldown": 300, "scale_out_cooldown": 300, "target_value": 1000}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_acm_certificate.cert", "aws_acm_certificate_validation.cert", "aws_appautoscaling_target.app", "aws_cloudwatch_log_group.app", "aws_cloudwatch_log_group.ecs", "aws_ecr_repository.app", "aws_ecs_cluster.main", "aws_ecs_service.app", "aws_ecs_task_definition.app", "aws_elasticache_parameter_group.redis", "aws_elasticache_replication_group.redis", "aws_elasticache_subnet_group.main", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_lb.main", "aws_lb_listener.app_https", "aws_lb_target_group.app", "aws_s3_bucket.static", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.redis", "aws_service_discovery_private_dns_namespace.main", "aws_service_discovery_service.app", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "cloudflare_record.cert_validation", "data.aws_availability_zones.available", "random_id.b2_bucket_suffix", "random_id.bucket_suffix", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_appautoscaling_policy", "name": "celery_cpu", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"alarm_arns": ["arn:aws:cloudwatch:us-east-1:************:alarm:TargetTracking-service/bopmaps-prod/bopmaps-prod-celery-AlarmHigh-9e219ad8-9f7f-4103-9d6a-f201f5d2064b", "arn:aws:cloudwatch:us-east-1:************:alarm:TargetTracking-service/bopmaps-prod/bopmaps-prod-celery-AlarmLow-d8a6d01a-a5a0-459f-b5c1-955fb44a9db0"], "arn": "arn:aws:autoscaling:us-east-1:************:scalingPolicy:57b1f173-385c-47ea-b1ba-e62086c52707:resource/ecs/service/bopmaps-prod/bopmaps-prod-celery:policyName/bopmaps-prod-celery-cpu-scaling", "id": "bopmaps-prod-celery-cpu-scaling", "name": "bopmaps-prod-celery-cpu-scaling", "policy_type": "TargetTrackingScaling", "resource_id": "service/bopmaps-prod/bopmaps-prod-celery", "scalable_dimension": "ecs:service:DesiredCount", "service_namespace": "ecs", "step_scaling_policy_configuration": [], "target_tracking_scaling_policy_configuration": [{"customized_metric_specification": [], "disable_scale_in": false, "predefined_metric_specification": [{"predefined_metric_type": "ECSServiceAverageCPUUtilization", "resource_label": ""}], "scale_in_cooldown": 600, "scale_out_cooldown": 300, "target_value": 70}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_appautoscaling_target.celery", "aws_cloudwatch_log_group.celery", "aws_cloudwatch_log_group.ecs", "aws_ecr_repository.app", "aws_ecs_cluster.main", "aws_ecs_service.celery", "aws_ecs_task_definition.celery", "aws_elasticache_parameter_group.redis", "aws_elasticache_replication_group.redis", "aws_elasticache_subnet_group.main", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.redis", "aws_subnet.private", "aws_vpc.main", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "data.aws_availability_zones.available", "random_id.b2_bucket_suffix", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_appautoscaling_policy", "name": "celery_memory", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"alarm_arns": ["arn:aws:cloudwatch:us-east-1:************:alarm:TargetTracking-service/bopmaps-prod/bopmaps-prod-celery-AlarmHigh-ca94ccb5-4cb0-4415-b598-6e94df270d17", "arn:aws:cloudwatch:us-east-1:************:alarm:TargetTracking-service/bopmaps-prod/bopmaps-prod-celery-AlarmLow-bd22a23c-0c91-457b-8695-c70d917e5284"], "arn": "arn:aws:autoscaling:us-east-1:************:scalingPolicy:57b1f173-385c-47ea-b1ba-e62086c52707:resource/ecs/service/bopmaps-prod/bopmaps-prod-celery:policyName/bopmaps-prod-celery-memory-scaling", "id": "bopmaps-prod-celery-memory-scaling", "name": "bopmaps-prod-celery-memory-scaling", "policy_type": "TargetTrackingScaling", "resource_id": "service/bopmaps-prod/bopmaps-prod-celery", "scalable_dimension": "ecs:service:DesiredCount", "service_namespace": "ecs", "step_scaling_policy_configuration": [], "target_tracking_scaling_policy_configuration": [{"customized_metric_specification": [], "disable_scale_in": false, "predefined_metric_specification": [{"predefined_metric_type": "ECSServiceAverageMemoryUtilization", "resource_label": ""}], "scale_in_cooldown": 600, "scale_out_cooldown": 300, "target_value": 80}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_appautoscaling_target.celery", "aws_cloudwatch_log_group.celery", "aws_cloudwatch_log_group.ecs", "aws_ecr_repository.app", "aws_ecs_cluster.main", "aws_ecs_service.celery", "aws_ecs_task_definition.celery", "aws_elasticache_parameter_group.redis", "aws_elasticache_replication_group.redis", "aws_elasticache_subnet_group.main", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.redis", "aws_subnet.private", "aws_vpc.main", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "data.aws_availability_zones.available", "random_id.b2_bucket_suffix", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_appautoscaling_policy", "name": "celery_queue", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"alarm_arns": ["arn:aws:cloudwatch:us-east-1:************:alarm:TargetTracking-service/bopmaps-prod/bopmaps-prod-celery-AlarmHigh-f242d126-cc1b-4b27-86fd-27f6a3d23dc2", "arn:aws:cloudwatch:us-east-1:************:alarm:TargetTracking-service/bopmaps-prod/bopmaps-prod-celery-AlarmLow-8c0b6001-a07b-4248-b55c-d1fce7d7b4de"], "arn": "arn:aws:autoscaling:us-east-1:************:scalingPolicy:57b1f173-385c-47ea-b1ba-e62086c52707:resource/ecs/service/bopmaps-prod/bopmaps-prod-celery:policyName/bopmaps-prod-celery-queue-scaling", "id": "bopmaps-prod-celery-queue-scaling", "name": "bopmaps-prod-celery-queue-scaling", "policy_type": "TargetTrackingScaling", "resource_id": "service/bopmaps-prod/bopmaps-prod-celery", "scalable_dimension": "ecs:service:DesiredCount", "service_namespace": "ecs", "step_scaling_policy_configuration": [], "target_tracking_scaling_policy_configuration": [{"customized_metric_specification": [{"dimensions": [], "metric_name": "CeleryActiveTaskCount", "metrics": [], "namespace": "BOPMaps/Celery", "statistic": "Average", "unit": ""}], "disable_scale_in": false, "predefined_metric_specification": [], "scale_in_cooldown": 600, "scale_out_cooldown": 180, "target_value": 5}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_appautoscaling_target.celery", "aws_cloudwatch_log_group.celery", "aws_cloudwatch_log_group.ecs", "aws_ecr_repository.app", "aws_ecs_cluster.main", "aws_ecs_service.celery", "aws_ecs_task_definition.celery", "aws_elasticache_parameter_group.redis", "aws_elasticache_replication_group.redis", "aws_elasticache_subnet_group.main", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.redis", "aws_subnet.private", "aws_vpc.main", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "data.aws_availability_zones.available", "random_id.b2_bucket_suffix", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_appautoscaling_target", "name": "app", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:application-autoscaling:us-east-1:************:scalable-target/0ec5c283bf400f5341a7a4f77d8ac99b2976", "id": "service/bopmaps-prod/bopmaps-prod-app", "max_capacity": 20, "min_capacity": 2, "resource_id": "service/bopmaps-prod/bopmaps-prod-app", "role_arn": "arn:aws:iam::************:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "scalable_dimension": "ecs:service:DesiredCount", "service_namespace": "ecs", "suspended_state": [{"dynamic_scaling_in_suspended": false, "dynamic_scaling_out_suspended": false, "scheduled_scaling_suspended": false}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_cloudwatch_log_group.app", "aws_ecr_repository.app", "aws_ecs_cluster.main", "aws_ecs_service.app", "aws_ecs_task_definition.app", "aws_elasticache_replication_group.redis", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_lb_listener.app_https", "aws_lb_target_group.app", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_security_group.ecs", "aws_service_discovery_service.app", "aws_subnet.private", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_appautoscaling_target", "name": "celery", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:application-autoscaling:us-east-1:************:scalable-target/0ec557b1f173385c47eab1bae62086c52707", "id": "service/bopmaps-prod/bopmaps-prod-celery", "max_capacity": 6, "min_capacity": 2, "resource_id": "service/bopmaps-prod/bopmaps-prod-celery", "role_arn": "arn:aws:iam::************:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "scalable_dimension": "ecs:service:DesiredCount", "service_namespace": "ecs", "suspended_state": [{"dynamic_scaling_in_suspended": false, "dynamic_scaling_out_suspended": false, "scheduled_scaling_suspended": false}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_cloudwatch_log_group.celery", "aws_cloudwatch_log_group.ecs", "aws_ecr_repository.app", "aws_ecs_cluster.main", "aws_ecs_service.celery", "aws_ecs_task_definition.celery", "aws_elasticache_parameter_group.redis", "aws_elasticache_replication_group.redis", "aws_elasticache_subnet_group.main", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.redis", "aws_subnet.private", "aws_vpc.main", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "data.aws_availability_zones.available", "random_id.b2_bucket_suffix", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_cloudwatch_dashboard", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"dashboard_arn": "arn:aws:cloudwatch::************:dashboard/bopmaps-prod-dashboard", "dashboard_body": "{\"widgets\":[{\"height\":6,\"properties\":{\"metrics\":[[\"AWS/ECS\",\"CPUUtilization\",\"ServiceName\",\"bopmaps-prod-app\",\"ClusterName\",\"bopmaps-prod\"],[\".\",\"MemoryUtilization\",\".\",\".\",\".\",\".\"]],\"period\":300,\"region\":\"us-east-1\",\"stat\":\"Average\",\"title\":\"ECS Service Metrics\"},\"type\":\"metric\",\"width\":12,\"x\":0,\"y\":0},{\"height\":6,\"properties\":{\"metrics\":[[\"AWS/ApplicationELB\",\"RequestCount\",\"LoadBalancer\",\"app/bopmaps-prod-alb/bf637793e7b7602b\"],[\".\",\"TargetResponseTime\",\".\",\".\"]],\"period\":300,\"region\":\"us-east-1\",\"stat\":\"Average\",\"title\":\"Load Balancer Metrics\"},\"type\":\"metric\",\"width\":12,\"x\":0,\"y\":6},{\"height\":6,\"properties\":{\"metrics\":[[\"AWS/RDS\",\"CPUUtilization\",\"DBInstanceIdentifier\",\"db-ZOHRVMZSEFRWAZZVHMY6A44LA4\"],[\".\",\"DatabaseConnections\",\".\",\".\"]],\"period\":300,\"region\":\"us-east-1\",\"stat\":\"Average\",\"title\":\"RDS Metrics\"},\"type\":\"metric\",\"width\":12,\"x\":0,\"y\":12}]}", "dashboard_name": "bopmaps-prod-dashboard", "id": "bopmaps-prod-dashboard"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_acm_certificate.cert", "aws_acm_certificate_validation.cert", "aws_cloudwatch_log_group.app", "aws_cloudwatch_log_group.ecs", "aws_db_instance.main", "aws_db_parameter_group.postgres", "aws_db_subnet_group.main", "aws_ecr_repository.app", "aws_ecs_cluster.main", "aws_ecs_service.app", "aws_ecs_task_definition.app", "aws_elasticache_parameter_group.redis", "aws_elasticache_replication_group.redis", "aws_elasticache_subnet_group.main", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_iam_role.rds_monitoring", "aws_lb.main", "aws_lb_listener.app_https", "aws_lb_target_group.app", "aws_s3_bucket.static", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.rds", "aws_security_group.redis", "aws_service_discovery_private_dns_namespace.main", "aws_service_discovery_service.app", "aws_subnet.database", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "cloudflare_record.cert_validation", "data.aws_availability_zones.available", "random_id.b2_bucket_suffix", "random_id.bucket_suffix", "random_password.db_password", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "app", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/ecs/bopmaps-prod/app", "id": "/ecs/bopmaps-prod/app", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/ecs/bopmaps-prod/app", "name_prefix": "", "retention_in_days": 14, "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "celery", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/ecs/bopmaps-prod/celery", "id": "/ecs/bopmaps-prod/celery", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/ecs/bopmaps-prod/celery", "name_prefix": "", "retention_in_days": 7, "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "ecs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/ecs/bopmaps-prod", "id": "/ecs/bopmaps-prod", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/ecs/bopmaps-prod", "name_prefix": "", "retention_in_days": 30, "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "migration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/ecs/bopmaps-prod/migration", "id": "/ecs/bopmaps-prod/migration", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/ecs/bopmaps-prod/migration", "name_prefix": "", "retention_in_days": 7, "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "planet_processor", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/ecs/bopmaps-prod/planet-processor", "id": "/ecs/bopmaps-prod/planet-processor", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/ecs/bopmaps-prod/planet-processor", "name_prefix": "", "retention_in_days": 30, "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "ses_logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/aws/ses/bopmaps", "id": "/aws/ses/bopmaps", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/ses/bopmaps", "name_prefix": "", "retention_in_days": 14, "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_cloudwatch_log_metric_filter", "name": "celery_queue_length", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "bopmaps-prod-celery-queue-length", "log_group_name": "/ecs/bopmaps-prod/celery", "metric_transformation": [{"default_value": "", "dimensions": {}, "name": "CeleryActiveTaskCount", "namespace": "BOPMaps/Celery", "unit": "None", "value": "$count"}], "name": "bopmaps-prod-celery-queue-length", "pattern": "[timestamp, level, module, process, thread, message=\"Active tasks:\", count]"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_cloudwatch_log_group.celery"]}]}, {"mode": "managed", "type": "aws_cloudwatch_metric_alarm", "name": "alb_high_response_time", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"actions_enabled": true, "alarm_actions": ["arn:aws:sns:us-east-1:************:bopmaps-prod-alerts"], "alarm_description": "This metric monitors ALB response time", "alarm_name": "bopmaps-prod-alb-high-response-time", "arn": "arn:aws:cloudwatch:us-east-1:************:alarm:bopmaps-prod-alb-high-response-time", "comparison_operator": "GreaterThanThreshold", "datapoints_to_alarm": 0, "dimensions": {"LoadBalancer": "app/bopmaps-prod-alb/bf637793e7b7602b"}, "evaluate_low_sample_count_percentiles": "", "evaluation_periods": 2, "extended_statistic": "", "id": "bopmaps-prod-alb-high-response-time", "insufficient_data_actions": [], "metric_name": "TargetResponseTime", "metric_query": [], "namespace": "AWS/ApplicationELB", "ok_actions": [], "period": 60, "statistic": "Average", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "threshold": 5, "threshold_metric_id": "", "treat_missing_data": "missing", "unit": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_lb.main", "aws_s3_bucket.static", "aws_security_group.alb", "aws_sns_topic.alerts", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_cloudwatch_metric_alarm", "name": "app_high_cpu", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"actions_enabled": true, "alarm_actions": ["arn:aws:sns:us-east-1:************:bopmaps-prod-alerts"], "alarm_description": "This metric monitors ecs cpu utilization", "alarm_name": "bopmaps-prod-app-high-cpu", "arn": "arn:aws:cloudwatch:us-east-1:************:alarm:bopmaps-prod-app-high-cpu", "comparison_operator": "GreaterThanThreshold", "datapoints_to_alarm": 0, "dimensions": {"ClusterName": "bopmaps-prod", "ServiceName": "bopmaps-prod-app"}, "evaluate_low_sample_count_percentiles": "", "evaluation_periods": 2, "extended_statistic": "", "id": "bopmaps-prod-app-high-cpu", "insufficient_data_actions": [], "metric_name": "CPUUtilization", "metric_query": [], "namespace": "AWS/ECS", "ok_actions": [], "period": 120, "statistic": "Average", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "threshold": 80, "threshold_metric_id": "", "treat_missing_data": "missing", "unit": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_acm_certificate.cert", "aws_acm_certificate_validation.cert", "aws_cloudwatch_log_group.app", "aws_cloudwatch_log_group.ecs", "aws_ecr_repository.app", "aws_ecs_cluster.main", "aws_ecs_service.app", "aws_ecs_task_definition.app", "aws_elasticache_parameter_group.redis", "aws_elasticache_replication_group.redis", "aws_elasticache_subnet_group.main", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_lb.main", "aws_lb_listener.app_https", "aws_lb_target_group.app", "aws_s3_bucket.static", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.redis", "aws_service_discovery_private_dns_namespace.main", "aws_service_discovery_service.app", "aws_sns_topic.alerts", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "cloudflare_record.cert_validation", "data.aws_availability_zones.available", "random_id.b2_bucket_suffix", "random_id.bucket_suffix", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_cloudwatch_metric_alarm", "name": "app_high_memory", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"actions_enabled": true, "alarm_actions": ["arn:aws:sns:us-east-1:************:bopmaps-prod-alerts"], "alarm_description": "This metric monitors ecs memory utilization", "alarm_name": "bopmaps-prod-app-high-memory", "arn": "arn:aws:cloudwatch:us-east-1:************:alarm:bopmaps-prod-app-high-memory", "comparison_operator": "GreaterThanThreshold", "datapoints_to_alarm": 0, "dimensions": {"ClusterName": "bopmaps-prod", "ServiceName": "bopmaps-prod-app"}, "evaluate_low_sample_count_percentiles": "", "evaluation_periods": 2, "extended_statistic": "", "id": "bopmaps-prod-app-high-memory", "insufficient_data_actions": [], "metric_name": "MemoryUtilization", "metric_query": [], "namespace": "AWS/ECS", "ok_actions": [], "period": 120, "statistic": "Average", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "threshold": 85, "threshold_metric_id": "", "treat_missing_data": "missing", "unit": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_acm_certificate.cert", "aws_acm_certificate_validation.cert", "aws_cloudwatch_log_group.app", "aws_cloudwatch_log_group.ecs", "aws_ecr_repository.app", "aws_ecs_cluster.main", "aws_ecs_service.app", "aws_ecs_task_definition.app", "aws_elasticache_parameter_group.redis", "aws_elasticache_replication_group.redis", "aws_elasticache_subnet_group.main", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_lb.main", "aws_lb_listener.app_https", "aws_lb_target_group.app", "aws_s3_bucket.static", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.redis", "aws_service_discovery_private_dns_namespace.main", "aws_service_discovery_service.app", "aws_sns_topic.alerts", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "cloudflare_record.cert_validation", "data.aws_availability_zones.available", "random_id.b2_bucket_suffix", "random_id.bucket_suffix", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_db_instance", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"address": "bopmaps-prod-postgres.c2vcs4guq0ku.us-east-1.rds.amazonaws.com", "allocated_storage": 100, "allow_major_version_upgrade": null, "apply_immediately": false, "arn": "arn:aws:rds:us-east-1:************:db:bopmaps-prod-postgres", "auto_minor_version_upgrade": true, "availability_zone": "us-east-1a", "backup_retention_period": 7, "backup_target": "region", "backup_window": "03:00-04:00", "blue_green_update": [], "ca_cert_identifier": "rds-ca-rsa2048-g1", "character_set_name": "", "copy_tags_to_snapshot": false, "custom_iam_instance_profile": "", "customer_owned_ip_enabled": false, "database_insights_mode": "standard", "db_name": "bopmaps", "db_subnet_group_name": "bopmaps-prod-db-subnet-group", "dedicated_log_volume": false, "delete_automated_backups": false, "deletion_protection": false, "domain": "", "domain_auth_secret_arn": "", "domain_dns_ips": [], "domain_fqdn": "", "domain_iam_role_name": "", "domain_ou": "", "enabled_cloudwatch_logs_exports": [], "endpoint": "bopmaps-prod-postgres.c2vcs4guq0ku.us-east-1.rds.amazonaws.com:5432", "engine": "postgres", "engine_lifecycle_support": "open-source-rds-extended-support", "engine_version": "14.13", "engine_version_actual": "14.13", "final_snapshot_identifier": null, "hosted_zone_id": "Z2R2ITUGPM61AM", "iam_database_authentication_enabled": false, "id": "db-ZOHRVMZSEFRWAZZVHMY6A44LA4", "identifier": "bopmaps-prod-postgres", "identifier_prefix": "", "instance_class": "db.t4g.medium", "iops": 3000, "kms_key_id": "arn:aws:kms:us-east-1:************:key/36f3864b-085a-4663-bd03-8f4fae1fc50a", "latest_restorable_time": "2025-07-05T07:34:31Z", "license_model": "postgresql-license", "listener_endpoint": [], "maintenance_window": "sun:04:00-sun:05:00", "manage_master_user_password": null, "master_user_secret": [], "master_user_secret_kms_key_id": null, "max_allocated_storage": 1000, "monitoring_interval": 60, "monitoring_role_arn": "arn:aws:iam::************:role/bopmaps-prod-rds-monitoring", "multi_az": true, "nchar_character_set_name": "", "network_type": "IPV4", "option_group_name": "default:postgres-14", "parameter_group_name": "bopmaps-prod-postgres14", "password": "bZ+_$WGovC+h*VE<bv+I3x6*P%RF4lm$", "password_wo": null, "password_wo_version": null, "performance_insights_enabled": true, "performance_insights_kms_key_id": "arn:aws:kms:us-east-1:************:key/36f3864b-085a-4663-bd03-8f4fae1fc50a", "performance_insights_retention_period": 7, "port": 5432, "publicly_accessible": false, "replica_mode": "", "replicas": [], "replicate_source_db": "", "resource_id": "db-ZOHRVMZSEFRWAZZVHMY6A44LA4", "restore_to_point_in_time": [], "s3_import": [], "skip_final_snapshot": true, "snapshot_identifier": null, "status": "available", "storage_encrypted": true, "storage_throughput": 125, "storage_type": "gp3", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-postgres", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-postgres", "Project": "BOPMaps"}, "timeouts": null, "timezone": "", "upgrade_storage_config": null, "username": "bopmaps_user", "vpc_security_group_ids": ["sg-0704d25c1c67db7bb"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "password"}]], "private": "********************************************************************************************************************************************************************************", "dependencies": ["aws_db_parameter_group.postgres", "aws_db_subnet_group.main", "aws_iam_role.rds_monitoring", "aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.rds", "aws_subnet.database", "aws_vpc.main", "data.aws_availability_zones.available", "random_password.db_password"]}]}, {"mode": "managed", "type": "aws_db_instance", "name": "read_replica", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 2, "attributes": {"address": "bopmaps-prod-postgres-replica.c2vcs4guq0ku.us-east-1.rds.amazonaws.com", "allocated_storage": 100, "allow_major_version_upgrade": null, "apply_immediately": false, "arn": "arn:aws:rds:us-east-1:************:db:bopmaps-prod-postgres-replica", "auto_minor_version_upgrade": true, "availability_zone": "us-east-1a", "backup_retention_period": 0, "backup_target": "region", "backup_window": "03:00-04:00", "blue_green_update": [], "ca_cert_identifier": "rds-ca-rsa2048-g1", "character_set_name": "", "copy_tags_to_snapshot": false, "custom_iam_instance_profile": "", "customer_owned_ip_enabled": false, "database_insights_mode": "standard", "db_name": "bopmaps", "db_subnet_group_name": "bopmaps-prod-db-subnet-group", "dedicated_log_volume": false, "delete_automated_backups": true, "deletion_protection": false, "domain": "", "domain_auth_secret_arn": "", "domain_dns_ips": null, "domain_fqdn": "", "domain_iam_role_name": "", "domain_ou": "", "enabled_cloudwatch_logs_exports": null, "endpoint": "bopmaps-prod-postgres-replica.c2vcs4guq0ku.us-east-1.rds.amazonaws.com:5432", "engine": "postgres", "engine_lifecycle_support": "open-source-rds-extended-support", "engine_version": "14.13", "engine_version_actual": "14.13", "final_snapshot_identifier": null, "hosted_zone_id": "Z2R2ITUGPM61AM", "iam_database_authentication_enabled": false, "id": "db-4WHPJ6QDVMT6BGRXPLAW5W7PEU", "identifier": "bopmaps-prod-postgres-replica", "identifier_prefix": "", "instance_class": "db.t4g.medium", "iops": 3000, "kms_key_id": "arn:aws:kms:us-east-1:************:key/36f3864b-085a-4663-bd03-8f4fae1fc50a", "latest_restorable_time": "", "license_model": "postgresql-license", "listener_endpoint": [], "maintenance_window": "sun:04:00-sun:05:00", "manage_master_user_password": null, "master_user_secret": [], "master_user_secret_kms_key_id": null, "max_allocated_storage": 1000, "monitoring_interval": 60, "monitoring_role_arn": "arn:aws:iam::************:role/bopmaps-prod-rds-monitoring", "multi_az": false, "nchar_character_set_name": "", "network_type": "IPV4", "option_group_name": "default:postgres-14", "parameter_group_name": "bopmaps-prod-postgres14", "password": null, "password_wo": null, "password_wo_version": null, "performance_insights_enabled": true, "performance_insights_kms_key_id": "arn:aws:kms:us-east-1:************:key/36f3864b-085a-4663-bd03-8f4fae1fc50a", "performance_insights_retention_period": 7, "port": 5432, "publicly_accessible": false, "replica_mode": "", "replicas": [], "replicate_source_db": "bopmaps-prod-postgres", "resource_id": "db-4WHPJ6QDVMT6BGRXPLAW5W7PEU", "restore_to_point_in_time": [], "s3_import": [], "skip_final_snapshot": true, "snapshot_identifier": null, "status": "available", "storage_encrypted": true, "storage_throughput": 125, "storage_type": "gp3", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-postgres-replica", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-postgres-replica", "Project": "BOPMaps"}, "timeouts": null, "timezone": "", "upgrade_storage_config": null, "username": "bopmaps_user", "vpc_security_group_ids": ["sg-0704d25c1c67db7bb"]}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["aws_db_instance.main", "aws_iam_role.rds_monitoring", "aws_security_group.rds"]}]}, {"mode": "managed", "type": "aws_db_parameter_group", "name": "postgres", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:rds:us-east-1:************:pg:bopmaps-prod-postgres14", "description": "Managed by Terraform", "family": "postgres14", "id": "bopmaps-prod-postgres14", "name": "bopmaps-prod-postgres14", "name_prefix": "", "parameter": [{"apply_method": "immediate", "name": "log_min_duration_statement", "value": "1000"}, {"apply_method": "immediate", "name": "log_statement", "value": "all"}, {"apply_method": "immediate", "name": "shared_preload_libraries", "value": "pg_stat_statements"}], "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-postgres-params", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-postgres-params", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_db_subnet_group", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:rds:us-east-1:************:subgrp:bopmaps-prod-db-subnet-group", "description": "Managed by Terraform", "id": "bopmaps-prod-db-subnet-group", "name": "bopmaps-prod-db-subnet-group", "name_prefix": "", "subnet_ids": ["subnet-020ac6c5d382b814a", "subnet-09848662178d915de"], "supported_network_types": ["IPV4"], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-db-subnet-group", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-db-subnet-group", "Project": "BOPMaps"}, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_subnet.database", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_ecr_lifecycle_policy", "name": "app", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "bopmaps-prod", "policy": "{\"rules\":[{\"action\":{\"type\":\"expire\"},\"description\":\"Keep last 10 production images\",\"rulePriority\":1,\"selection\":{\"countNumber\":10,\"countType\":\"imageCountMoreThan\",\"tagPrefixList\":[\"prod\"],\"tagStatus\":\"tagged\"}},{\"action\":{\"type\":\"expire\"},\"description\":\"Keep last 5 staging images\",\"rulePriority\":2,\"selection\":{\"countNumber\":5,\"countType\":\"imageCountMoreThan\",\"tagPrefixList\":[\"staging\"],\"tagStatus\":\"tagged\"}},{\"action\":{\"type\":\"expire\"},\"description\":\"Delete untagged images older than 1 day\",\"rulePriority\":3,\"selection\":{\"countNumber\":1,\"countType\":\"sinceImagePushed\",\"countUnit\":\"days\",\"tagStatus\":\"untagged\"}}]}", "registry_id": "************", "repository": "bopmaps-prod"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_ecr_repository.app"]}]}, {"mode": "managed", "type": "aws_ecr_repository", "name": "app", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ecr:us-east-1:************:repository/bopmaps-prod", "encryption_configuration": [{"encryption_type": "AES256", "kms_key": ""}], "force_delete": true, "id": "bopmaps-prod", "image_scanning_configuration": [{"scan_on_push": true}], "image_tag_mutability": "MUTABLE", "name": "bopmaps-prod", "registry_id": "************", "repository_url": "************.dkr.ecr.us-east-1.amazonaws.com/bopmaps-prod", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxMjAwMDAwMDAwMDAwfX0="}]}, {"mode": "managed", "type": "aws_ecs_cluster", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:cluster/bopmaps-prod", "configuration": [{"execute_command_configuration": [{"kms_key_id": "", "log_configuration": [{"cloud_watch_encryption_enabled": false, "cloud_watch_log_group_name": "/ecs/bopmaps-prod", "s3_bucket_encryption_enabled": false, "s3_bucket_name": "", "s3_key_prefix": ""}], "logging": "OVERRIDE"}], "managed_storage_configuration": []}], "id": "arn:aws:ecs:us-east-1:************:cluster/bopmaps-prod", "name": "bopmaps-prod", "service_connect_defaults": [], "setting": [{"name": "containerInsights", "value": "enabled"}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_cloudwatch_log_group.ecs"]}]}, {"mode": "managed", "type": "aws_ecs_cluster_capacity_providers", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"capacity_providers": ["FARGATE", "FARGATE_SPOT"], "cluster_name": "bopmaps-prod", "default_capacity_provider_strategy": [{"base": 1, "capacity_provider": "FARGATE", "weight": 100}], "id": "bopmaps-prod"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_cloudwatch_log_group.ecs", "aws_ecs_cluster.main"]}]}, {"mode": "managed", "type": "aws_ecs_service", "name": "app", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"alarms": [], "availability_zone_rebalancing": "DISABLED", "capacity_provider_strategy": [{"base": 0, "capacity_provider": "FARGATE", "weight": 100}], "cluster": "arn:aws:ecs:us-east-1:************:cluster/bopmaps-prod", "deployment_circuit_breaker": [{"enable": false, "rollback": false}], "deployment_controller": [{"type": "ECS"}], "deployment_maximum_percent": 200, "deployment_minimum_healthy_percent": 100, "desired_count": 2, "enable_ecs_managed_tags": false, "enable_execute_command": false, "force_delete": null, "force_new_deployment": null, "health_check_grace_period_seconds": 0, "iam_role": "/aws-service-role/ecs.amazonaws.com/AWSServiceRoleForECS", "id": "arn:aws:ecs:us-east-1:************:service/bopmaps-prod/bopmaps-prod-app", "launch_type": "", "load_balancer": [{"container_name": "app", "container_port": 8000, "elb_name": "", "target_group_arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/bopmaps-prod-app/23c232f58b2846bb"}], "name": "bopmaps-prod-app", "network_configuration": [{"assign_public_ip": false, "security_groups": ["sg-0801eef16980ee578"], "subnets": ["subnet-0284f054c45fb6f70", "subnet-0889380393e07eddb"]}], "ordered_placement_strategy": [], "placement_constraints": [], "platform_version": "LATEST", "propagate_tags": "NONE", "scheduling_strategy": "REPLICA", "service_connect_configuration": [], "service_registries": [{"container_name": "", "container_port": 0, "port": 0, "registry_arn": "arn:aws:servicediscovery:us-east-1:************:service/srv-yqdevtfu23g47mhp"}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "task_definition": "arn:aws:ecs:us-east-1:************:task-definition/bopmaps-prod-app:32", "timeouts": null, "triggers": {}, "volume_configuration": [], "vpc_lattice_configurations": [], "wait_for_steady_state": false}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_cloudwatch_log_group.app", "aws_ecr_repository.app", "aws_ecs_cluster.main", "aws_ecs_task_definition.app", "aws_elasticache_replication_group.redis", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_lb_listener.app_https", "aws_lb_target_group.app", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_security_group.ecs", "aws_service_discovery_service.app", "aws_subnet.private", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_ecs_service", "name": "celery", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"alarms": [], "availability_zone_rebalancing": "DISABLED", "capacity_provider_strategy": [{"base": 0, "capacity_provider": "FARGATE", "weight": 100}], "cluster": "arn:aws:ecs:us-east-1:************:cluster/bopmaps-prod", "deployment_circuit_breaker": [{"enable": false, "rollback": false}], "deployment_controller": [{"type": "ECS"}], "deployment_maximum_percent": 200, "deployment_minimum_healthy_percent": 100, "desired_count": 2, "enable_ecs_managed_tags": false, "enable_execute_command": false, "force_delete": null, "force_new_deployment": null, "health_check_grace_period_seconds": 0, "iam_role": "/aws-service-role/ecs.amazonaws.com/AWSServiceRoleForECS", "id": "arn:aws:ecs:us-east-1:************:service/bopmaps-prod/bopmaps-prod-celery", "launch_type": "", "load_balancer": [], "name": "bopmaps-prod-celery", "network_configuration": [{"assign_public_ip": false, "security_groups": ["sg-0801eef16980ee578"], "subnets": ["subnet-0284f054c45fb6f70", "subnet-0889380393e07eddb"]}], "ordered_placement_strategy": [], "placement_constraints": [], "platform_version": "LATEST", "propagate_tags": "NONE", "scheduling_strategy": "REPLICA", "service_connect_configuration": [], "service_registries": [], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "task_definition": "arn:aws:ecs:us-east-1:************:task-definition/bopmaps-prod-celery:8", "timeouts": null, "triggers": {}, "volume_configuration": [], "vpc_lattice_configurations": [], "wait_for_steady_state": false}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_cloudwatch_log_group.celery", "aws_ecr_repository.app", "aws_ecs_cluster.main", "aws_ecs_task_definition.celery", "aws_elasticache_replication_group.redis", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_security_group.ecs", "aws_subnet.private", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_ecs_task_definition", "name": "app", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:task-definition/bopmaps-prod-app:32", "arn_without_revision": "arn:aws:ecs:us-east-1:************:task-definition/bopmaps-prod-app", "container_definitions": "[{\"environment\":[{\"name\":\"ALLOWED_REDIRECT_URIS\",\"value\":\"bopmaps://callback\"},{\"name\":\"APPLE_MUSIC_KEY_ID\",\"value\":\"\"},{\"name\":\"APPLE_MUSIC_PRIVATE_KEY\",\"value\":\"\"},{\"name\":\"APPLE_MUSIC_TEAM_ID\",\"value\":\"\"},{\"name\":\"B2_APPLICATION_KEY\",\"value\":\"K0058c2wlRv7QFP/2On8cXsQ6XRV9fw\"},{\"name\":\"B2_APPLICATION_KEY_ID\",\"value\":\"005b50dabb51c630000000001\"},{\"name\":\"B2_BUCKET_NAME\",\"value\":\"bopmaps-prod-static-67ba6151\"},{\"name\":\"B2_ENDPOINT_URL\",\"value\":\"https://s3.us-east-005.backblazeb2.com\"},{\"name\":\"B2_REGION\",\"value\":\"us-east-005\"},{\"name\":\"B2_TILES_BUCKET_NAME\",\"value\":\"bopmaps-prod-tiles-67ba6151\"},{\"name\":\"COLLECT_STATIC\",\"value\":\"true\"},{\"name\":\"CORS_ALLOWED_ORIGINS\",\"value\":\"http://localhost:3000\"},{\"name\":\"CSRF_TRUSTED_ORIGINS\",\"value\":\"http://localhost:3000\"},{\"name\":\"DEBUG\",\"value\":\"False\"},{\"name\":\"DEFAULT_FROM_EMAIL\",\"value\":\"<EMAIL>\"},{\"name\":\"EMAIL_BACKEND\",\"value\":\"django.core.mail.backends.console.EmailBackend\"},{\"name\":\"EMAIL_HOST\",\"value\":\"\"},{\"name\":\"EMAIL_HOST_PASSWORD\",\"value\":\"\"},{\"name\":\"EMAIL_HOST_USER\",\"value\":\"\"},{\"name\":\"EMAIL_PORT\",\"value\":\"587\"},{\"name\":\"EMAIL_USE_TLS\",\"value\":\"true\"},{\"name\":\"ENVIRONMENT\",\"value\":\"prod\"},{\"name\":\"JWT_ACCESS_TOKEN_LIFETIME\",\"value\":\"1\"},{\"name\":\"JWT_REFRESH_TOKEN_LIFETIME\",\"value\":\"7\"},{\"name\":\"LASTFM_API_KEY\",\"value\":\"fd7597d7a76640941f4db64e38649529\"},{\"name\":\"MEDIA_BUCKET_NAME\",\"value\":\"bopmaps-prod-media-67ba6151\"},{\"name\":\"ONESIGNAL_API_KEY\",\"value\":\"\"},{\"name\":\"ONESIGNAL_API_URL\",\"value\":\"https://onesignal.com/api/v1/notifications\"},{\"name\":\"ONESIGNAL_APP_ID\",\"value\":\"\"},{\"name\":\"REDIS_URL\",\"value\":\"redis://:<EMAIL>:6379/0\"},{\"name\":\"SECURE_SSL_REDIRECT\",\"value\":\"true\"},{\"name\":\"SERVER_EMAIL\",\"value\":\"<EMAIL>\"},{\"name\":\"SOUNDCLOUD_CLIENT_ID\",\"value\":\"\"},{\"name\":\"SOUNDCLOUD_CLIENT_SECRET\",\"value\":\"\"},{\"name\":\"SPOTIFY_CLIENT_ID\",\"value\":\"fb25ee73d874499a9c424cd5a2466e13\"},{\"name\":\"SPOTIFY_CLIENT_SECRET\",\"value\":\"33aaa655d24846c9b6f61ef24ecf9710\"},{\"name\":\"SPOTIFY_MOBILE_REDIRECT_URI\",\"value\":\"bopmaps://callback\"},{\"name\":\"SPOTIFY_REDIRECT_URI\",\"value\":\"http://localhost:8888/callback\"},{\"name\":\"USE_S3_FOR_MEDIA\",\"value\":\"True\"}],\"essential\":true,\"healthCheck\":{\"command\":[\"CMD-SHELL\",\"curl -f http://localhost:8000/health/ || exit 1\"],\"interval\":30,\"retries\":3,\"startPeriod\":60,\"timeout\":5},\"image\":\"************.dkr.ecr.us-east-1.amazonaws.com/bopmaps-prod:latest\",\"logConfiguration\":{\"logDriver\":\"awslogs\",\"options\":{\"awslogs-region\":\"us-east-1\",\"awslogs-stream-prefix\":\"ecs\",\"awslogs-group\":\"/ecs/bopmaps-prod/app\"}},\"mountPoints\":[],\"name\":\"app\",\"portMappings\":[{\"containerPort\":8000,\"hostPort\":8000,\"protocol\":\"tcp\"}],\"secrets\":[{\"name\":\"DATABASE_URL\",\"valueFrom\":\"arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-database-url-GJS5jg\"},{\"name\":\"SECRET_KEY\",\"valueFrom\":\"arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-django-secret-pW6vE9\"}],\"systemControls\":[],\"volumesFrom\":[]}]", "cpu": "1024", "enable_fault_injection": false, "ephemeral_storage": [], "execution_role_arn": "arn:aws:iam::************:role/bopmaps-prod-ecs-task-execution", "family": "bopmaps-prod-app", "id": "bopmaps-prod-app", "inference_accelerator": [], "ipc_mode": "", "memory": "2048", "network_mode": "awsvpc", "pid_mode": "", "placement_constraints": [], "proxy_configuration": [], "requires_compatibilities": ["FARGATE"], "revision": 32, "runtime_platform": [], "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "task_role_arn": "arn:aws:iam::************:role/bopmaps-prod-ecs-task", "track_latest": false, "volume": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "container_definitions"}]], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_cloudwatch_log_group.app", "aws_ecr_repository.app", "aws_elasticache_replication_group.redis", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_ecs_task_definition", "name": "celery", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:task-definition/bopmaps-prod-celery:8", "arn_without_revision": "arn:aws:ecs:us-east-1:************:task-definition/bopmaps-prod-celery", "container_definitions": "[{\"command\":[\"celery\",\"-A\",\"bopmaps\",\"worker\",\"-l\",\"INFO\"],\"environment\":[{\"name\":\"ALLOWED_REDIRECT_URIS\",\"value\":\"bopmaps://callback\"},{\"name\":\"APPLE_MUSIC_KEY_ID\",\"value\":\"\"},{\"name\":\"APPLE_MUSIC_PRIVATE_KEY\",\"value\":\"\"},{\"name\":\"APPLE_MUSIC_TEAM_ID\",\"value\":\"\"},{\"name\":\"B2_APPLICATION_KEY\",\"value\":\"K0058c2wlRv7QFP/2On8cXsQ6XRV9fw\"},{\"name\":\"B2_APPLICATION_KEY_ID\",\"value\":\"005b50dabb51c630000000001\"},{\"name\":\"B2_BUCKET_NAME\",\"value\":\"bopmaps-prod-static-67ba6151\"},{\"name\":\"B2_ENDPOINT_URL\",\"value\":\"https://s3.us-east-005.backblazeb2.com\"},{\"name\":\"B2_REGION\",\"value\":\"us-east-005\"},{\"name\":\"B2_TILES_BUCKET_NAME\",\"value\":\"bopmaps-prod-tiles-67ba6151\"},{\"name\":\"CORS_ALLOWED_ORIGINS\",\"value\":\"http://localhost:3000\"},{\"name\":\"CSRF_TRUSTED_ORIGINS\",\"value\":\"http://localhost:3000\"},{\"name\":\"DEBUG\",\"value\":\"False\"},{\"name\":\"DEFAULT_FROM_EMAIL\",\"value\":\"<EMAIL>\"},{\"name\":\"EMAIL_BACKEND\",\"value\":\"django.core.mail.backends.console.EmailBackend\"},{\"name\":\"EMAIL_HOST\",\"value\":\"\"},{\"name\":\"EMAIL_HOST_PASSWORD\",\"value\":\"\"},{\"name\":\"EMAIL_HOST_USER\",\"value\":\"\"},{\"name\":\"EMAIL_PORT\",\"value\":\"587\"},{\"name\":\"EMAIL_USE_TLS\",\"value\":\"true\"},{\"name\":\"ENVIRONMENT\",\"value\":\"prod\"},{\"name\":\"JWT_ACCESS_TOKEN_LIFETIME\",\"value\":\"1\"},{\"name\":\"JWT_REFRESH_TOKEN_LIFETIME\",\"value\":\"7\"},{\"name\":\"LASTFM_API_KEY\",\"value\":\"fd7597d7a76640941f4db64e38649529\"},{\"name\":\"MEDIA_BUCKET_NAME\",\"value\":\"bopmaps-prod-media-67ba6151\"},{\"name\":\"ONESIGNAL_API_KEY\",\"value\":\"\"},{\"name\":\"ONESIGNAL_API_URL\",\"value\":\"https://onesignal.com/api/v1/notifications\"},{\"name\":\"ONESIGNAL_APP_ID\",\"value\":\"\"},{\"name\":\"REDIS_URL\",\"value\":\"redis://:<EMAIL>:6379/0\"},{\"name\":\"SECURE_SSL_REDIRECT\",\"value\":\"true\"},{\"name\":\"SERVER_EMAIL\",\"value\":\"<EMAIL>\"},{\"name\":\"SOUNDCLOUD_CLIENT_ID\",\"value\":\"\"},{\"name\":\"SOUNDCLOUD_CLIENT_SECRET\",\"value\":\"\"},{\"name\":\"SPOTIFY_CLIENT_ID\",\"value\":\"fb25ee73d874499a9c424cd5a2466e13\"},{\"name\":\"SPOTIFY_CLIENT_SECRET\",\"value\":\"33aaa655d24846c9b6f61ef24ecf9710\"},{\"name\":\"SPOTIFY_MOBILE_REDIRECT_URI\",\"value\":\"bopmaps://callback\"},{\"name\":\"SPOTIFY_REDIRECT_URI\",\"value\":\"http://localhost:8888/callback\"},{\"name\":\"USE_S3_FOR_MEDIA\",\"value\":\"True\"}],\"essential\":true,\"image\":\"************.dkr.ecr.us-east-1.amazonaws.com/bopmaps-prod:latest\",\"logConfiguration\":{\"logDriver\":\"awslogs\",\"options\":{\"awslogs-group\":\"/ecs/bopmaps-prod/celery\",\"awslogs-region\":\"us-east-1\",\"awslogs-stream-prefix\":\"ecs\"}},\"mountPoints\":[],\"name\":\"celery-worker\",\"portMappings\":[],\"secrets\":[{\"name\":\"DATABASE_URL\",\"valueFrom\":\"arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-database-url-GJS5jg\"},{\"name\":\"SECRET_KEY\",\"valueFrom\":\"arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-django-secret-pW6vE9\"}],\"systemControls\":[],\"volumesFrom\":[]}]", "cpu": "512", "enable_fault_injection": false, "ephemeral_storage": [], "execution_role_arn": "arn:aws:iam::************:role/bopmaps-prod-ecs-task-execution", "family": "bopmaps-prod-celery", "id": "bopmaps-prod-celery", "inference_accelerator": [], "ipc_mode": "", "memory": "1024", "network_mode": "awsvpc", "pid_mode": "", "placement_constraints": [], "proxy_configuration": [], "requires_compatibilities": ["FARGATE"], "revision": 8, "runtime_platform": [], "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "task_role_arn": "arn:aws:iam::************:role/bopmaps-prod-ecs-task", "track_latest": false, "volume": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "container_definitions"}]], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_cloudwatch_log_group.celery", "aws_ecr_repository.app", "aws_elasticache_replication_group.redis", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_ecs_task_definition", "name": "migration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:task-definition/bopmaps-prod-migration:3", "arn_without_revision": "arn:aws:ecs:us-east-1:************:task-definition/bopmaps-prod-migration", "container_definitions": "[{\"command\":[\"sh\",\"-c\",\"python manage.py migrate --noinput && python manage.py create_default_skin && echo 'Migrations completed successfully'\"],\"environment\":[{\"name\":\"B2_APPLICATION_KEY\",\"value\":\"K0058c2wlRv7QFP/2On8cXsQ6XRV9fw\"},{\"name\":\"B2_APPLICATION_KEY_ID\",\"value\":\"005b50dabb51c630000000001\"},{\"name\":\"B2_BUCKET_NAME\",\"value\":\"bopmaps-prod-static-67ba6151\"},{\"name\":\"B2_ENDPOINT_URL\",\"value\":\"https://s3.us-east-005.backblazeb2.com\"},{\"name\":\"B2_REGION\",\"value\":\"us-east-005\"},{\"name\":\"DEBUG\",\"value\":\"False\"},{\"name\":\"ENVIRONMENT\",\"value\":\"prod\"},{\"name\":\"MEDIA_BUCKET_NAME\",\"value\":\"bopmaps-prod-media-67ba6151\"},{\"name\":\"REDIS_URL\",\"value\":\"redis://:<EMAIL>:6379/0\"},{\"name\":\"USE_S3_FOR_MEDIA\",\"value\":\"True\"}],\"essential\":true,\"image\":\"************.dkr.ecr.us-east-1.amazonaws.com/bopmaps-prod:latest\",\"logConfiguration\":{\"logDriver\":\"awslogs\",\"options\":{\"awslogs-group\":\"/ecs/bopmaps-prod/migration\",\"awslogs-region\":\"us-east-1\",\"awslogs-stream-prefix\":\"ecs\"}},\"mountPoints\":[],\"name\":\"migration\",\"portMappings\":[],\"secrets\":[{\"name\":\"DATABASE_URL\",\"valueFrom\":\"arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-database-url-GJS5jg\"},{\"name\":\"SECRET_KEY\",\"valueFrom\":\"arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-django-secret-pW6vE9\"}],\"systemControls\":[],\"volumesFrom\":[]}]", "cpu": "512", "enable_fault_injection": false, "ephemeral_storage": [], "execution_role_arn": "arn:aws:iam::************:role/bopmaps-prod-ecs-task-execution", "family": "bopmaps-prod-migration", "id": "bopmaps-prod-migration", "inference_accelerator": [], "ipc_mode": "", "memory": "1024", "network_mode": "awsvpc", "pid_mode": "", "placement_constraints": [], "proxy_configuration": [], "requires_compatibilities": ["FARGATE"], "revision": 3, "runtime_platform": [], "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "task_role_arn": "arn:aws:iam::************:role/bopmaps-prod-ecs-task", "track_latest": false, "volume": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "container_definitions"}]], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_cloudwatch_log_group.migration", "aws_ecr_repository.app", "aws_elasticache_replication_group.redis", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "b2_bucket.media", "b2_bucket.static", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_ecs_task_definition", "name": "planet_processor", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:task-definition/bopmaps-prod-planet-processor:5", "arn_without_revision": "arn:aws:ecs:us-east-1:************:task-definition/bopmaps-prod-planet-processor", "container_definitions": "[{\"command\":[\"python\",\"manage.py\",\"process_planet_tiles\",\"--area=planet\",\"--zoom-levels=0-14\"],\"environment\":[{\"name\":\"B2_APPLICATION_KEY\",\"value\":\"K0058c2wlRv7QFP/2On8cXsQ6XRV9fw\"},{\"name\":\"B2_APPLICATION_KEY_ID\",\"value\":\"005b50dabb51c630000000001\"},{\"name\":\"B2_BUCKET_NAME\",\"value\":\"bopmaps-prod-static-67ba6151\"},{\"name\":\"B2_ENDPOINT_URL\",\"value\":\"https://s3.us-east-005.backblazeb2.com\"},{\"name\":\"B2_REGION\",\"value\":\"us-east-005\"},{\"name\":\"B2_TILES_BUCKET_NAME\",\"value\":\"bopmaps-prod-tiles-67ba6151\"},{\"name\":\"DEBUG\",\"value\":\"False\"},{\"name\":\"ENVIRONMENT\",\"value\":\"prod\"},{\"name\":\"MEDIA_BUCKET_NAME\",\"value\":\"bopmaps-prod-media-67ba6151\"},{\"name\":\"REDIS_URL\",\"value\":\"redis://:<EMAIL>:6379/0\"},{\"name\":\"USE_S3_FOR_MEDIA\",\"value\":\"True\"}],\"essential\":true,\"image\":\"************.dkr.ecr.us-east-1.amazonaws.com/bopmaps-prod:latest\",\"logConfiguration\":{\"logDriver\":\"awslogs\",\"options\":{\"awslogs-group\":\"/ecs/bopmaps-prod/planet-processor\",\"awslogs-region\":\"us-east-1\",\"awslogs-stream-prefix\":\"ecs\"}},\"mountPoints\":[],\"name\":\"planet-processor\",\"portMappings\":[],\"secrets\":[{\"name\":\"DATABASE_URL\",\"valueFrom\":\"arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-database-url-GJS5jg\"},{\"name\":\"SECRET_KEY\",\"valueFrom\":\"arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-django-secret-pW6vE9\"}],\"systemControls\":[],\"volumesFrom\":[]}]", "cpu": "4096", "enable_fault_injection": false, "ephemeral_storage": [], "execution_role_arn": "arn:aws:iam::************:role/bopmaps-prod-ecs-task-execution", "family": "bopmaps-prod-planet-processor", "id": "bopmaps-prod-planet-processor", "inference_accelerator": [], "ipc_mode": "", "memory": "16384", "network_mode": "awsvpc", "pid_mode": "", "placement_constraints": [], "proxy_configuration": [], "requires_compatibilities": ["FARGATE"], "revision": 5, "runtime_platform": [], "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "task_role_arn": "arn:aws:iam::************:role/bopmaps-prod-ecs-task", "track_latest": false, "volume": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "container_definitions"}]], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_cloudwatch_log_group.planet_processor", "aws_ecr_repository.app", "aws_elasticache_replication_group.redis", "aws_iam_role.ecs_task", "aws_iam_role.ecs_task_execution", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "b2_bucket.media", "b2_bucket.static", "b2_bucket.tiles", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_eip", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-00751bca493b9b62a", "arn": "arn:aws:ec2:us-east-1:************:elastic-ip/eipalloc-00751bca493b9b62a", "associate_with_private_ip": null, "association_id": "eipassoc-01dd78c320aba78df", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-00751bca493b9b62a", "instance": "", "ipam_pool_id": null, "network_border_group": "us-east-1", "network_interface": "eni-07115c4b3138b3bf5", "private_dns": "ip-10-0-1-176.ec2.internal", "private_ip": "**********", "ptr_record": "", "public_dns": "ec2-54-196-206-230.compute-1.amazonaws.com", "public_ip": "**************", "public_ipv4_pool": "amazon", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-eip-1", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-eip-1", "Project": "BOPMaps"}, "timeouts": null, "vpc": true}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************", "dependencies": ["aws_internet_gateway.main", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-0f30fb541b79d5479", "arn": "arn:aws:ec2:us-east-1:************:elastic-ip/eipalloc-0f30fb541b79d5479", "associate_with_private_ip": null, "association_id": "eipassoc-0763efa99e254a117", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-0f30fb541b79d5479", "instance": "", "ipam_pool_id": null, "network_border_group": "us-east-1", "network_interface": "eni-02461d933b33a7d6e", "private_dns": "ip-10-0-2-45.ec2.internal", "private_ip": "*********", "ptr_record": "", "public_dns": "ec2-54-173-184-32.compute-1.amazonaws.com", "public_ip": "*************", "public_ipv4_pool": "amazon", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-eip-2", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-eip-2", "Project": "BOPMaps"}, "timeouts": null, "vpc": true}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************", "dependencies": ["aws_internet_gateway.main", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_elasticache_parameter_group", "name": "redis", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:elasticache:us-east-1:************:parametergroup:bopmaps-prod-redis7", "description": "Managed by Terraform", "family": "redis7", "id": "bopmaps-prod-redis7", "name": "bopmaps-prod-redis7", "parameter": [{"name": "maxmemory-policy", "value": "allkeys-lru"}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-redis-params", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-redis-params", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_elasticache_replication_group", "name": "redis", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"apply_immediately": null, "arn": "arn:aws:elasticache:us-east-1:************:replicationgroup:bopmaps-prod-redis", "at_rest_encryption_enabled": "true", "auth_token": "M7KndG10g9FMUShOBAMcFnxYAPHYlcvR", "auth_token_update_strategy": "ROTATE", "auto_minor_version_upgrade": "true", "automatic_failover_enabled": false, "cluster_enabled": false, "cluster_mode": "disabled", "configuration_endpoint_address": null, "data_tiering_enabled": false, "description": "Redis cluster for bopmaps prod", "engine": "redis", "engine_version": "7.1", "engine_version_actual": "7.1.0", "final_snapshot_identifier": null, "global_replication_group_id": null, "id": "bopmaps-prod-redis", "ip_discovery": "ipv4", "kms_key_id": "", "log_delivery_configuration": [], "maintenance_window": "sun:05:00-sun:07:00", "member_clusters": ["bopmaps-prod-redis-001"], "multi_az_enabled": false, "network_type": "ipv4", "node_type": "cache.t4g.micro", "notification_topic_arn": null, "num_cache_clusters": 1, "num_node_groups": 1, "parameter_group_name": "bopmaps-prod-redis7", "port": 6379, "preferred_cache_cluster_azs": null, "primary_endpoint_address": "master.bopmaps-prod-redis.0r0qne.use1.cache.amazonaws.com", "reader_endpoint_address": "replica.bopmaps-prod-redis.0r0qne.use1.cache.amazonaws.com", "replicas_per_node_group": 0, "replication_group_id": "bopmaps-prod-redis", "security_group_ids": ["sg-0c24db6831dd2491e"], "security_group_names": [], "snapshot_arns": null, "snapshot_name": null, "snapshot_retention_limit": 3, "snapshot_window": "03:00-05:00", "subnet_group_name": "bopmaps-prod-cache-subnet", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-redis", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-redis", "Project": "BOPMaps"}, "timeouts": null, "transit_encryption_enabled": true, "transit_encryption_mode": "required", "user_group_ids": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "auth_token"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozNjAwMDAwMDAwMDAwLCJkZWxldGUiOjI3MDAwMDAwMDAwMDAsInVwZGF0ZSI6MjQwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMiJ9", "dependencies": ["aws_elasticache_parameter_group.redis", "aws_elasticache_subnet_group.main", "aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.redis", "aws_subnet.private", "aws_vpc.main", "data.aws_availability_zones.available", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_elasticache_subnet_group", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:elasticache:us-east-1:************:subnetgroup:bopmaps-prod-cache-subnet", "description": "Managed by Terraform", "id": "bopmaps-prod-cache-subnet", "name": "bopmaps-prod-cache-subnet", "subnet_ids": ["subnet-0284f054c45fb6f70", "subnet-0889380393e07eddb"], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-cache-subnet-group", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-cache-subnet-group", "Project": "BOPMaps"}, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_subnet.private", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "b2_upload", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/bopmaps-prod-b2-upload", "attachment_count": 1, "description": "Policy for B2 uploads and planet data processing", "id": "arn:aws:iam::************:policy/bopmaps-prod-b2-upload", "name": "bopmaps-prod-b2-upload", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"s3:GetObject\",\"s3:PutObject\",\"s3:DeleteObject\",\"s3:ListBucket\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::bopmaps-prod-tiles-67ba6151\",\"arn:aws:s3:::bopmaps-prod-tiles-67ba6151/*\",\"arn:aws:s3:::bopmaps-prod-static-67ba6151\",\"arn:aws:s3:::bopmaps-prod-static-67ba6151/*\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPA4SZHNYEGKICVLAXK2", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["b2_bucket.static", "b2_bucket.tiles", "random_id.b2_bucket_suffix"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "cloudwatch_logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/bopmaps-prod-cloudwatch-logs", "attachment_count": 1, "description": "Policy for CloudWatch Logs", "id": "arn:aws:iam::************:policy/bopmaps-prod-cloudwatch-logs", "name": "bopmaps-prod-cloudwatch-logs", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\",\"logs:DescribeLogStreams\",\"logs:DescribeLogGroups\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPA4SZHNYEGM5TFP5SID", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "secrets_manager_access", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/bopmaps-prod-secrets-manager-access", "attachment_count": 1, "description": "Policy for accessing Secrets Manager", "id": "arn:aws:iam::************:policy/bopmaps-prod-secrets-manager-access", "name": "bopmaps-prod-secrets-manager-access", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"secretsmanager:GetSecretValue\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-django-secret-pW6vE9\",\"arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-database-url-GJS5jg\",\"arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-redis-auth-AU3Jd8\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPA4SZHNYEGCN6X2K5TV", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_secretsmanager_secret.redis_auth"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "ses_send_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/bopmaps-ses-send-policy", "attachment_count": 1, "description": "Policy for sending emails via SES", "id": "arn:aws:iam::************:policy/bopmaps-ses-send-policy", "name": "bopmaps-ses-send-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"ses:SendEmail\",\"ses:SendRawEmail\",\"ses:SendTemplatedEmail\",\"ses:SendBulkTemplatedEmail\"],\"Condition\":{\"StringEquals\":{\"ses:FromAddress\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"]}},\"Effect\":\"Allow\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPA4SZHNYEGHI34FEKAK", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "ecs_autoscaling", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/bopmaps-prod-ecs-autoscaling", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"application-autoscaling.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-02T16:22:20Z", "description": "", "force_detach_policies": false, "id": "bopmaps-prod-ecs-autoscaling", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "bopmaps-prod-ecs-autoscaling", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "unique_id": "AROA4SZHNYEGKBMY3RL2A"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "ecs_task", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/bopmaps-prod-ecs-task", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ecs-tasks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-02T16:22:18Z", "description": "", "force_detach_policies": false, "id": "bopmaps-prod-ecs-task", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::************:policy/bopmaps-prod-b2-upload", "arn:aws:iam::************:policy/bopmaps-prod-cloudwatch-logs", "arn:aws:iam::************:policy/bopmaps-ses-send-policy"], "max_session_duration": 3600, "name": "bopmaps-prod-ecs-task", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "unique_id": "AROA4SZHNYEGFKPN34JQY"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "ecs_task_execution", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/bopmaps-prod-ecs-task-execution", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ecs-tasks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-02T16:22:16Z", "description": "", "force_detach_policies": false, "id": "bopmaps-prod-ecs-task-execution", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::************:policy/bopmaps-prod-secrets-manager-access", "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"], "max_session_duration": 3600, "name": "bopmaps-prod-ecs-task-execution", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "unique_id": "AROA4SZHNYEGLFNKUL6QA"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "rds_monitoring", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/bopmaps-prod-rds-monitoring", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"monitoring.rds.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-02T16:22:16Z", "description": "", "force_detach_policies": false, "id": "bopmaps-prod-rds-monitoring", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"], "max_session_duration": 3600, "name": "bopmaps-prod-rds-monitoring", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "unique_id": "AROA4SZHNYEGFHRBG7OFA"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ecs_task_b2", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "bopmaps-prod-ecs-task-2025070216225953450000000f", "policy_arn": "arn:aws:iam::************:policy/bopmaps-prod-b2-upload", "role": "bopmaps-prod-ecs-task"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_policy.b2_upload", "aws_iam_role.ecs_task", "b2_bucket.static", "b2_bucket.tiles", "random_id.b2_bucket_suffix"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ecs_task_execution", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "bopmaps-prod-ecs-task-execution-20250702162222608700000006", "policy_arn": "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy", "role": "bopmaps-prod-ecs-task-execution"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.ecs_task_execution"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ecs_task_execution_secrets", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "bopmaps-prod-ecs-task-execution-20250702165450359100000007", "policy_arn": "arn:aws:iam::************:policy/bopmaps-prod-secrets-manager-access", "role": "bopmaps-prod-ecs-task-execution"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_policy.secrets_manager_access", "aws_iam_role.ecs_task_execution", "aws_secretsmanager_secret.database_url", "aws_secretsmanager_secret.django_secret", "aws_secretsmanager_secret.redis_auth"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ecs_task_logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "bopmaps-prod-ecs-task-20250702162236834900000008", "policy_arn": "arn:aws:iam::************:policy/bopmaps-prod-cloudwatch-logs", "role": "bopmaps-prod-ecs-task"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_policy.cloudwatch_logs", "aws_iam_role.ecs_task"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ecs_task_ses_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "bopmaps-prod-ecs-task-20250705084924782900000001", "policy_arn": "arn:aws:iam::************:policy/bopmaps-ses-send-policy", "role": "bopmaps-prod-ecs-task"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_policy.ses_send_policy", "aws_iam_role.ecs_task"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "rds_monitoring", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "bopmaps-prod-rds-monitoring-20250702162222916800000007", "policy_arn": "arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole", "role": "bopmaps-prod-rds-monitoring"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.rds_monitoring"]}]}, {"mode": "managed", "type": "aws_internet_gateway", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:internet-gateway/igw-03ead6cfec87699d6", "id": "igw-03ead6cfec87699d6", "owner_id": "************", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-igw", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-igw", "Project": "BOPMaps"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_lb", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_logs": [{"bucket": "bopmaps-prod-static-58e4c360", "enabled": true, "prefix": "alb-logs"}], "arn": "arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/bopmaps-prod-alb/bf637793e7b7602b", "arn_suffix": "app/bopmaps-prod-alb/bf637793e7b7602b", "client_keep_alive": 3600, "connection_logs": [{"bucket": "", "enabled": false, "prefix": ""}], "customer_owned_ipv4_pool": "", "desync_mitigation_mode": "defensive", "dns_name": "bopmaps-prod-alb-*********.us-east-1.elb.amazonaws.com", "dns_record_client_routing_policy": null, "drop_invalid_header_fields": false, "enable_cross_zone_load_balancing": true, "enable_deletion_protection": false, "enable_http2": true, "enable_tls_version_and_cipher_suite_headers": false, "enable_waf_fail_open": false, "enable_xff_client_port": false, "enable_zonal_shift": false, "enforce_security_group_inbound_rules_on_private_link_traffic": "", "id": "arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/bopmaps-prod-alb/bf637793e7b7602b", "idle_timeout": 60, "internal": false, "ip_address_type": "ipv4", "ipam_pools": [], "load_balancer_type": "application", "minimum_load_balancer_capacity": [], "name": "bopmaps-prod-alb", "name_prefix": "", "preserve_host_header": false, "security_groups": ["sg-0780be76668d27dff"], "subnet_mapping": [{"allocation_id": "", "ipv6_address": "", "outpost_id": "", "private_ipv4_address": "", "subnet_id": "subnet-0093c6ce8bf0d0ee0"}, {"allocation_id": "", "ipv6_address": "", "outpost_id": "", "private_ipv4_address": "", "subnet_id": "subnet-0779972d6df51b24b"}], "subnets": ["subnet-0093c6ce8bf0d0ee0", "subnet-0779972d6df51b24b"], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8", "xff_header_processing_mode": "append", "zone_id": "Z35SXDOTRQ7X7K"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_s3_bucket.static", "aws_security_group.alb", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_lb_listener", "name": "app", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"alpn_policy": null, "arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/bopmaps-prod-alb/bf637793e7b7602b/2a944983eb3e52f6", "certificate_arn": null, "default_action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [], "forward": [], "order": 1, "redirect": [{"host": "#{host}", "path": "/#{path}", "port": "443", "protocol": "HTTPS", "query": "#{query}", "status_code": "HTTP_301"}], "target_group_arn": "", "type": "redirect"}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/bopmaps-prod-alb/bf637793e7b7602b/2a944983eb3e52f6", "load_balancer_arn": "arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/bopmaps-prod-alb/bf637793e7b7602b", "mutual_authentication": [], "port": 80, "protocol": "HTTP", "routing_http_request_x_amzn_mtls_clientcert_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_issuer_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_leaf_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_serial_number_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_subject_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_validity_header_name": null, "routing_http_request_x_amzn_tls_cipher_suite_header_name": null, "routing_http_request_x_amzn_tls_version_header_name": null, "routing_http_response_access_control_allow_credentials_header_value": "", "routing_http_response_access_control_allow_headers_header_value": "", "routing_http_response_access_control_allow_methods_header_value": "", "routing_http_response_access_control_allow_origin_header_value": "", "routing_http_response_access_control_expose_headers_header_value": "", "routing_http_response_access_control_max_age_header_value": "", "routing_http_response_content_security_policy_header_value": "", "routing_http_response_server_enabled": true, "routing_http_response_strict_transport_security_header_value": "", "routing_http_response_x_content_type_options_header_value": "", "routing_http_response_x_frame_options_header_value": "", "ssl_policy": "", "tags": {}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tcp_idle_timeout_seconds": null, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************", "dependencies": ["aws_lb.main", "aws_s3_bucket.static", "aws_security_group.alb", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_lb_listener", "name": "app_https", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"alpn_policy": null, "arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/bopmaps-prod-alb/bf637793e7b7602b/5684833b067b52e7", "certificate_arn": "arn:aws:acm:us-east-1:************:certificate/d4e4f752-a782-4418-a861-bd3d9e54c9d2", "default_action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [], "forward": [], "order": 1, "redirect": [], "target_group_arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/bopmaps-prod-app/23c232f58b2846bb", "type": "forward"}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/bopmaps-prod-alb/bf637793e7b7602b/5684833b067b52e7", "load_balancer_arn": "arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/bopmaps-prod-alb/bf637793e7b7602b", "mutual_authentication": [{"advertise_trust_store_ca_names": "", "ignore_client_certificate_expiry": false, "mode": "off", "trust_store_arn": ""}], "port": 443, "protocol": "HTTPS", "routing_http_request_x_amzn_mtls_clientcert_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_issuer_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_leaf_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_serial_number_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_subject_header_name": "", "routing_http_request_x_amzn_mtls_clientcert_validity_header_name": "", "routing_http_request_x_amzn_tls_cipher_suite_header_name": "", "routing_http_request_x_amzn_tls_version_header_name": "", "routing_http_response_access_control_allow_credentials_header_value": "", "routing_http_response_access_control_allow_headers_header_value": "", "routing_http_response_access_control_allow_methods_header_value": "", "routing_http_response_access_control_allow_origin_header_value": "", "routing_http_response_access_control_expose_headers_header_value": "", "routing_http_response_access_control_max_age_header_value": "", "routing_http_response_content_security_policy_header_value": "", "routing_http_response_server_enabled": true, "routing_http_response_strict_transport_security_header_value": "", "routing_http_response_x_content_type_options_header_value": "", "routing_http_response_x_frame_options_header_value": "", "ssl_policy": "ELBSecurityPolicy-TLS-1-2-2017-01", "tags": {}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tcp_idle_timeout_seconds": null, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************", "dependencies": ["aws_acm_certificate.cert", "aws_acm_certificate_validation.cert", "aws_lb.main", "aws_lb_target_group.app", "aws_s3_bucket.static", "aws_security_group.alb", "aws_subnet.public", "aws_vpc.main", "cloudflare_record.cert_validation", "data.aws_availability_zones.available", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_lb_target_group", "name": "app", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/bopmaps-prod-app/23c232f58b2846bb", "arn_suffix": "targetgroup/bopmaps-prod-app/23c232f58b2846bb", "connection_termination": null, "deregistration_delay": "300", "health_check": [{"enabled": true, "healthy_threshold": 2, "interval": 30, "matcher": "200,301", "path": "/health/", "port": "traffic-port", "protocol": "HTTP", "timeout": 10, "unhealthy_threshold": 3}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/bopmaps-prod-app/23c232f58b2846bb", "ip_address_type": "ipv4", "lambda_multi_value_headers_enabled": false, "load_balancer_arns": ["arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/bopmaps-prod-alb/bf637793e7b7602b"], "load_balancing_algorithm_type": "round_robin", "load_balancing_anomaly_mitigation": "off", "load_balancing_cross_zone_enabled": "use_load_balancer_configuration", "name": "bopmaps-prod-app", "name_prefix": "", "port": 8000, "preserve_client_ip": null, "protocol": "HTTP", "protocol_version": "HTTP1", "proxy_protocol_v2": false, "slow_start": 0, "stickiness": [{"cookie_duration": 86400, "cookie_name": "", "enabled": false, "type": "lb_cookie"}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "target_failover": [{"on_deregistration": null, "on_unhealthy": null}], "target_group_health": [{"dns_failover": [{"minimum_healthy_targets_count": "1", "minimum_healthy_targets_percentage": "off"}], "unhealthy_state_routing": [{"minimum_healthy_targets_count": 1, "minimum_healthy_targets_percentage": "off"}]}], "target_health_state": [{"enable_unhealthy_connection_termination": null, "unhealthy_draining_interval": null}], "target_type": "ip", "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_nat_gateway", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"allocation_id": "eipalloc-00751bca493b9b62a", "association_id": "eipassoc-01dd78c320aba78df", "connectivity_type": "public", "id": "nat-0f3e50b595ffacad0", "network_interface_id": "eni-07115c4b3138b3bf5", "private_ip": "**********", "public_ip": "**************", "secondary_allocation_ids": [], "secondary_private_ip_address_count": 0, "secondary_private_ip_addresses": [], "subnet_id": "subnet-0093c6ce8bf0d0ee0", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-nat-1", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-nat-1", "Project": "BOPMaps"}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTgwMDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 0, "attributes": {"allocation_id": "eipalloc-0f30fb541b79d5479", "association_id": "eipassoc-0763efa99e254a117", "connectivity_type": "public", "id": "nat-0fd71974aee31e02c", "network_interface_id": "eni-02461d933b33a7d6e", "private_ip": "*********", "public_ip": "*************", "secondary_allocation_ids": [], "secondary_private_ip_address_count": 0, "secondary_private_ip_addresses": [], "subnet_id": "subnet-0779972d6df51b24b", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-nat-2", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-nat-2", "Project": "BOPMaps"}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTgwMDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "database", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-061a43aac38d3173d", "id": "rtb-061a43aac38d3173d", "owner_id": "************", "propagating_vgws": [], "route": [], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-database-rt", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-database-rt", "Project": "BOPMaps"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-01894a5814df71a40", "id": "rtb-01894a5814df71a40", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-0f3e50b595ffacad0", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-private-rt-1", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-private-rt-1", "Project": "BOPMaps"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-0903c62098a0902b5", "id": "rtb-0903c62098a0902b5", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-0fd71974aee31e02c", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-private-rt-2", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-private-rt-2", "Project": "BOPMaps"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-0409a28e5755f4288", "id": "rtb-0409a28e5755f4288", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-03ead6cfec87699d6", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-public-rt", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-public-rt", "Project": "BOPMaps"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "database", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-033eb13ce35b1229a", "route_table_id": "rtb-061a43aac38d3173d", "subnet_id": "subnet-09848662178d915de", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_route_table.database", "aws_subnet.database", "aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0bb768ca9e16223cf", "route_table_id": "rtb-061a43aac38d3173d", "subnet_id": "subnet-020ac6c5d382b814a", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_route_table.database", "aws_subnet.database", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0c89f420a1c0a6548", "route_table_id": "rtb-01894a5814df71a40", "subnet_id": "subnet-0284f054c45fb6f70", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_route_table.private", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-001c440f11821b66e", "route_table_id": "rtb-0903c62098a0902b5", "subnet_id": "subnet-0889380393e07eddb", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_route_table.private", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-08e56a32f5b241d21", "route_table_id": "rtb-0409a28e5755f4288", "subnet_id": "subnet-0093c6ce8bf0d0ee0", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main", "aws_route_table.public", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0f99825239abb15b6", "route_table_id": "rtb-0409a28e5755f4288", "subnet_id": "subnet-0779972d6df51b24b", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main", "aws_route_table.public", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "media", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::bopmaps-prod-media-58e4c360", "bucket": "bopmaps-prod-media-58e4c360", "bucket_domain_name": "bopmaps-prod-media-58e4c360.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "bopmaps-prod-media-58e4c360.s3.us-east-1.amazonaws.com", "cors_rule": [{"allowed_headers": ["*"], "allowed_methods": ["POST", "GET", "HEAD", "DELETE", "PUT"], "allowed_origins": ["https://api.bopmaps.com"], "expose_headers": ["ETag"], "max_age_seconds": 3000}], "force_destroy": true, "grant": [{"id": "ce3f7a356da476fcbce28ee81a7ac15823bce6386ad533d4ec437f064c166c5a", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3AQBSTGFYJSTF", "id": "bopmaps-prod-media-58e4c360", "lifecycle_rule": [{"abort_incomplete_multipart_upload_days": 0, "enabled": true, "expiration": [], "id": "media_lifecycle", "noncurrent_version_expiration": [{"days": 365}], "noncurrent_version_transition": [{"days": 30, "storage_class": "STANDARD_IA"}, {"days": 60, "storage_class": "GLACIER"}], "prefix": "", "tags": {}, "transition": []}], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"arn:aws:s3:::bopmaps-prod-media-58e4c360/*\",\"Sid\":\"AllowPublicRead\"}],\"Version\":\"2012-10-17\"}", "region": "us-east-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-media", "Project": "BOPMaps", "Purpose": "Media Files"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-media", "Project": "BOPMaps", "Purpose": "Media Files"}, "timeouts": null, "versioning": [{"enabled": true, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "static", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::bopmaps-prod-static-58e4c360", "bucket": "bopmaps-prod-static-58e4c360", "bucket_domain_name": "bopmaps-prod-static-58e4c360.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "bopmaps-prod-static-58e4c360.s3.us-east-1.amazonaws.com", "cors_rule": [{"allowed_headers": ["*"], "allowed_methods": ["GET", "HEAD"], "allowed_origins": ["*"], "expose_headers": ["ETag"], "max_age_seconds": 3000}], "force_destroy": true, "grant": [{"id": "ce3f7a356da476fcbce28ee81a7ac15823bce6386ad533d4ec437f064c166c5a", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3AQBSTGFYJSTF", "id": "bopmaps-prod-static-58e4c360", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"arn:aws:s3:::bopmaps-prod-static-58e4c360/*\",\"Sid\":\"AllowPublicRead\"},{\"Action\":\"s3:PutObject\",\"Condition\":{\"StringEquals\":{\"s3:x-amz-acl\":\"bucket-owner-full-control\"}},\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::127311923021:root\"},\"Resource\":\"arn:aws:s3:::bopmaps-prod-static-58e4c360/alb-logs/AWSLogs/************/*\",\"Sid\":\"AllowALBLogging\"}],\"Version\":\"2012-10-17\"}", "region": "us-east-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-static", "Project": "BOPMaps", "Purpose": "Static Files"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-static", "Project": "BOPMaps", "Purpose": "Static Files"}, "timeouts": null, "versioning": [{"enabled": true, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "tiles", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::bopmaps-prod-tiles-58e4c360", "bucket": "bopmaps-prod-tiles-58e4c360", "bucket_domain_name": "bopmaps-prod-tiles-58e4c360.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "bopmaps-prod-tiles-58e4c360.s3.us-east-1.amazonaws.com", "cors_rule": [{"allowed_headers": ["*"], "allowed_methods": ["GET", "HEAD"], "allowed_origins": ["*"], "expose_headers": ["ETag"], "max_age_seconds": 86400}], "force_destroy": true, "grant": [{"id": "ce3f7a356da476fcbce28ee81a7ac15823bce6386ad533d4ec437f064c166c5a", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3AQBSTGFYJSTF", "id": "bopmaps-prod-tiles-58e4c360", "lifecycle_rule": [{"abort_incomplete_multipart_upload_days": 0, "enabled": true, "expiration": [{"date": "", "days": 90, "expired_object_delete_marker": false}], "id": "tiles_lifecycle", "noncurrent_version_expiration": [], "noncurrent_version_transition": [], "prefix": "", "tags": {}, "transition": [{"date": "", "days": 30, "storage_class": "STANDARD_IA"}]}], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "us-east-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-tiles", "Project": "BOPMaps", "Purpose": "Map Tiles"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-tiles", "Project": "BOPMaps", "Purpose": "Map Tiles"}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_cors_configuration", "name": "media", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "bopmaps-prod-media-58e4c360", "cors_rule": [{"allowed_headers": ["*"], "allowed_methods": ["DELETE", "GET", "HEAD", "POST", "PUT"], "allowed_origins": ["https://api.bopmaps.com"], "expose_headers": ["ETag"], "id": "", "max_age_seconds": 3000}], "expected_bucket_owner": "", "id": "bopmaps-prod-media-58e4c360"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.media", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_cors_configuration", "name": "static", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "bopmaps-prod-static-58e4c360", "cors_rule": [{"allowed_headers": ["*"], "allowed_methods": ["GET", "HEAD"], "allowed_origins": ["*"], "expose_headers": ["ETag"], "id": "", "max_age_seconds": 3000}], "expected_bucket_owner": "", "id": "bopmaps-prod-static-58e4c360"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.static", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_cors_configuration", "name": "tiles", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "bopmaps-prod-tiles-58e4c360", "cors_rule": [{"allowed_headers": ["*"], "allowed_methods": ["GET", "HEAD"], "allowed_origins": ["*"], "expose_headers": ["ETag"], "id": "", "max_age_seconds": 86400}], "expected_bucket_owner": "", "id": "bopmaps-prod-tiles-58e4c360"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.tiles", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_lifecycle_configuration", "name": "media", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"bucket": "bopmaps-prod-media-58e4c360", "expected_bucket_owner": "", "id": "bopmaps-prod-media-58e4c360", "rule": [{"abort_incomplete_multipart_upload": [], "expiration": [], "filter": [{"and": [], "object_size_greater_than": null, "object_size_less_than": null, "prefix": "", "tag": []}], "id": "media_lifecycle", "noncurrent_version_expiration": [{"newer_noncurrent_versions": null, "noncurrent_days": 365}], "noncurrent_version_transition": [{"newer_noncurrent_versions": null, "noncurrent_days": 30, "storage_class": "STANDARD_IA"}, {"newer_noncurrent_versions": null, "noncurrent_days": 60, "storage_class": "GLACIER"}], "prefix": "", "status": "Enabled", "transition": []}], "timeouts": null, "transition_default_minimum_object_size": "all_storage_classes_128K"}, "sensitive_attributes": [], "dependencies": ["aws_s3_bucket.media", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_lifecycle_configuration", "name": "tiles", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"bucket": "bopmaps-prod-tiles-58e4c360", "expected_bucket_owner": "", "id": "bopmaps-prod-tiles-58e4c360", "rule": [{"abort_incomplete_multipart_upload": [], "expiration": [{"date": null, "days": 90, "expired_object_delete_marker": false}], "filter": [{"and": [], "object_size_greater_than": null, "object_size_less_than": null, "prefix": "", "tag": []}], "id": "tiles_lifecycle", "noncurrent_version_expiration": [], "noncurrent_version_transition": [], "prefix": "", "status": "Enabled", "transition": [{"date": null, "days": 30, "storage_class": "STANDARD_IA"}]}], "timeouts": null, "transition_default_minimum_object_size": "all_storage_classes_128K"}, "sensitive_attributes": [], "dependencies": ["aws_s3_bucket.tiles", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_policy", "name": "media", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "bopmaps-prod-media-58e4c360", "id": "bopmaps-prod-media-58e4c360", "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"arn:aws:s3:::bopmaps-prod-media-58e4c360/*\",\"Sid\":\"AllowPublicRead\"}],\"Version\":\"2012-10-17\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.media", "data.aws_iam_policy_document.media_bucket_policy", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_policy", "name": "static", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "bopmaps-prod-static-58e4c360", "id": "bopmaps-prod-static-58e4c360", "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"arn:aws:s3:::bopmaps-prod-static-58e4c360/*\",\"Sid\":\"AllowPublicRead\"},{\"Action\":\"s3:PutObject\",\"Condition\":{\"StringEquals\":{\"s3:x-amz-acl\":\"bucket-owner-full-control\"}},\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::127311923021:root\"},\"Resource\":\"arn:aws:s3:::bopmaps-prod-static-58e4c360/alb-logs/AWSLogs/************/*\",\"Sid\":\"AllowALBLogging\"}],\"Version\":\"2012-10-17\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.static", "data.aws_caller_identity.current", "data.aws_iam_policy_document.static_bucket_policy", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "media", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": false, "bucket": "bopmaps-prod-media-58e4c360", "id": "bopmaps-prod-media-58e4c360", "ignore_public_acls": true, "restrict_public_buckets": false}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.media", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "static", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": false, "bucket": "bopmaps-prod-static-58e4c360", "id": "bopmaps-prod-static-58e4c360", "ignore_public_acls": true, "restrict_public_buckets": false}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.static", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "tiles", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": true, "bucket": "bopmaps-prod-tiles-58e4c360", "id": "bopmaps-prod-tiles-58e4c360", "ignore_public_acls": true, "restrict_public_buckets": true}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.tiles", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_server_side_encryption_configuration", "name": "media", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "bopmaps-prod-media-58e4c360", "expected_bucket_owner": "", "id": "bopmaps-prod-media-58e4c360", "rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.media", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_server_side_encryption_configuration", "name": "static", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "bopmaps-prod-static-58e4c360", "expected_bucket_owner": "", "id": "bopmaps-prod-static-58e4c360", "rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.static", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_server_side_encryption_configuration", "name": "tiles", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "bopmaps-prod-tiles-58e4c360", "expected_bucket_owner": "", "id": "bopmaps-prod-tiles-58e4c360", "rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.tiles", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_versioning", "name": "media", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "bopmaps-prod-media-58e4c360", "expected_bucket_owner": "", "id": "bopmaps-prod-media-58e4c360", "mfa": null, "versioning_configuration": [{"mfa_delete": "", "status": "Enabled"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.media", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_versioning", "name": "static", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "bopmaps-prod-static-58e4c360", "expected_bucket_owner": "", "id": "bopmaps-prod-static-58e4c360", "mfa": null, "versioning_configuration": [{"mfa_delete": "", "status": "Enabled"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.static", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_versioning", "name": "tiles", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "bopmaps-prod-tiles-58e4c360", "expected_bucket_owner": "", "id": "bopmaps-prod-tiles-58e4c360", "mfa": null, "versioning_configuration": [{"mfa_delete": "", "status": "Suspended"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.tiles", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_secretsmanager_secret", "name": "database_url", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-database-url-GJS5jg", "description": "Database URL for bopmaps prod", "force_overwrite_replica_secret": false, "id": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-database-url-GJS5jg", "kms_key_id": "", "name": "bopmaps-prod-database-url", "name_prefix": "", "policy": "", "recovery_window_in_days": 7, "replica": [], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_secretsmanager_secret", "name": "db_password", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-db-password-rFvGs1", "description": "Database password for bopmaps prod", "force_overwrite_replica_secret": false, "id": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-db-password-rFvGs1", "kms_key_id": "", "name": "bopmaps-prod-db-password", "name_prefix": "", "policy": "", "recovery_window_in_days": 7, "replica": [], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_secretsmanager_secret", "name": "django_secret", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-django-secret-pW6vE9", "description": "Django secret key for bopmaps prod", "force_overwrite_replica_secret": false, "id": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-django-secret-pW6vE9", "kms_key_id": "", "name": "bopmaps-prod-django-secret", "name_prefix": "", "policy": "", "recovery_window_in_days": 7, "replica": [], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_secretsmanager_secret", "name": "redis_auth", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-redis-auth-AU3Jd8", "description": "Redis auth token for bopmaps prod", "force_overwrite_replica_secret": false, "id": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-redis-auth-AU3Jd8", "kms_key_id": "", "name": "bopmaps-prod-redis-auth", "name_prefix": "", "policy": "", "recovery_window_in_days": 7, "replica": [], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_secretsmanager_secret_version", "name": "database_url", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-database-url-GJS5jg", "has_secret_string_wo": null, "id": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-database-url-GJS5jg|terraform-20250702165449927400000004", "secret_binary": "", "secret_id": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-database-url-GJS5jg", "secret_string": "postgis://bopmaps_user:<EMAIL>:5432/bopmaps", "secret_string_wo": null, "secret_string_wo_version": null, "version_id": "terraform-20250702165449927400000004", "version_stages": ["AWSCURRENT"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_string"}]], "private": "bnVsbA==", "dependencies": ["aws_db_instance.main", "aws_db_parameter_group.postgres", "aws_db_subnet_group.main", "aws_iam_role.rds_monitoring", "aws_secretsmanager_secret.database_url", "aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.rds", "aws_subnet.database", "aws_vpc.main", "data.aws_availability_zones.available", "random_password.db_password"]}]}, {"mode": "managed", "type": "aws_secretsmanager_secret_version", "name": "db_password", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-db-password-rFvGs1", "has_secret_string_wo": null, "id": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-db-password-rFvGs1|terraform-20250702165449919700000001", "secret_binary": "", "secret_id": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-db-password-rFvGs1", "secret_string": "bZ+_$WGovC+h*VE<bv+I3x6*P%RF4lm$", "secret_string_wo": null, "secret_string_wo_version": null, "version_id": "terraform-20250702165449919700000001", "version_stages": ["AWSCURRENT"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_string"}]], "private": "bnVsbA==", "dependencies": ["aws_secretsmanager_secret.db_password", "random_password.db_password"]}]}, {"mode": "managed", "type": "aws_secretsmanager_secret_version", "name": "django_secret", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-django-secret-pW6vE9", "has_secret_string_wo": null, "id": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-django-secret-pW6vE9|terraform-20250702165449924000000003", "secret_binary": "", "secret_id": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-django-secret-pW6vE9", "secret_string": "EW08uobhX5SIOp52aJwtwX6I1D4Fl9YGcKK094OMuisIvpd-zmIlLw7vWvzgePLkLIY", "secret_string_wo": null, "secret_string_wo_version": null, "version_id": "terraform-20250702165449924000000003", "version_stages": ["AWSCURRENT"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_string"}]], "private": "bnVsbA==", "dependencies": ["aws_secretsmanager_secret.django_secret"]}]}, {"mode": "managed", "type": "aws_secretsmanager_secret_version", "name": "redis_auth", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-redis-auth-AU3Jd8", "has_secret_string_wo": null, "id": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-redis-auth-AU3Jd8|terraform-20250702165449923800000002", "secret_binary": "", "secret_id": "arn:aws:secretsmanager:us-east-1:************:secret:bopmaps-prod-redis-auth-AU3Jd8", "secret_string": "M7KndG10g9FMUShOBAMcFnxYAPHYlcvR", "secret_string_wo": null, "secret_string_wo_version": null, "version_id": "terraform-20250702165449923800000002", "version_stages": ["AWSCURRENT"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_string"}]], "private": "bnVsbA==", "dependencies": ["aws_secretsmanager_secret.redis_auth", "random_password.redis_auth"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "alb", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-0780be76668d27dff", "description": "Security group for Application Load Balancer", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0780be76668d27dff", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "HTTP", "from_port": 80, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 80}, {"cidr_blocks": ["0.0.0.0/0"], "description": "HTTPS", "from_port": 443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 443}], "name": "bopmaps-prod-alb-20250702162237516800000009", "name_prefix": "bopmaps-prod-alb-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-alb-sg", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-alb-sg", "Project": "BOPMaps"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_security_group", "name": "ecs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-0801eef16980ee578", "description": "Security group for ECS tasks", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0801eef16980ee578", "ingress": [{"cidr_blocks": [], "description": "HTTP from ALB", "from_port": 8000, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0780be76668d27dff"], "self": false, "to_port": 8000}], "name": "bopmaps-prod-ecs-2025070216225619140000000d", "name_prefix": "bopmaps-prod-ecs-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-ecs-sg", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-ecs-sg", "Project": "BOPMaps"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_security_group.alb", "aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_security_group", "name": "rds", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-0704d25c1c67db7bb", "description": "Security group for RDS PostgreSQL", "egress": [], "id": "sg-0704d25c1c67db7bb", "ingress": [{"cidr_blocks": [], "description": "PostgreSQL from ECS", "from_port": 5432, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0801eef16980ee578"], "self": false, "to_port": 5432}], "name": "bopmaps-prod-rds-20250702162305440700000010", "name_prefix": "bopmaps-prod-rds-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-rds-sg", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-rds-sg", "Project": "BOPMaps"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_security_group.alb", "aws_security_group.ecs", "aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_security_group", "name": "redis", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-0c24db6831dd2491e", "description": "Security group for ElastiCache Redis", "egress": [], "id": "sg-0c24db6831dd2491e", "ingress": [{"cidr_blocks": [], "description": "<PERSON><PERSON> from ECS", "from_port": 6379, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0801eef16980ee578"], "self": false, "to_port": 6379}], "name": "bopmaps-prod-redis-20250702162308643500000011", "name_prefix": "bopmaps-prod-redis-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-redis-sg", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-redis-sg", "Project": "BOPMaps"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_security_group.alb", "aws_security_group.ecs", "aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_security_group", "name": "vpc_endpoints", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-09e1f694072e316e7", "description": "Security group for VPC endpoints", "egress": [], "id": "sg-09e1f694072e316e7", "ingress": [{"cidr_blocks": [], "description": "HTTPS from ECS", "from_port": 443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0801eef16980ee578"], "self": false, "to_port": 443}], "name": "bopmaps-prod-vpce-20250702162311914200000012", "name_prefix": "bopmaps-prod-vpce-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-vpce-sg", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-vpce-sg", "Project": "BOPMaps"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_security_group.alb", "aws_security_group.ecs", "aws_vpc.main"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_service_discovery_private_dns_namespace", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:servicediscovery:us-east-1:************:namespace/ns-vqom45z4dpggu3zf", "description": "", "hosted_zone": "Z0999457FMKLY70JKM4A", "id": "ns-vqom45z4dpggu3zf", "name": "bopmaps-prod.local", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "vpc": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_service_discovery_service", "name": "app", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:servicediscovery:us-east-1:************:service/srv-yqdevtfu23g47mhp", "description": "", "dns_config": [{"dns_records": [{"ttl": 10, "type": "A"}], "namespace_id": "ns-vqom45z4dpggu3zf", "routing_policy": "MULTIVALUE"}], "force_destroy": false, "health_check_config": [], "health_check_custom_config": [], "id": "srv-yqdevtfu23g47mhp", "name": "app", "namespace_id": "ns-vqom45z4dpggu3zf", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "type": "DNS_HTTP"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_service_discovery_private_dns_namespace.main", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_ses_configuration_set", "name": "bopmaps_emails", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ses:us-east-1:************:configuration-set/bopmaps-emails", "delivery_options": [{"tls_policy": "Require"}], "id": "bopmaps-emails", "last_fresh_start": "0001-01-01T00:00:00Z", "name": "bopmaps-emails", "reputation_metrics_enabled": true, "sending_enabled": true, "tracking_options": []}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_ses_domain_dkim", "name": "bopmaps_dkim", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"dkim_tokens": ["zakefia5gsrjl7cbjnprydlmr2fslawm", "i7ef7kcwim5jpvur5g4ijlza7lkxqweo", "2zryh3kgiaqwyjph2c7mccyeayqtjyl4"], "domain": "bopmaps.com", "id": "bopmaps.com"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_ses_domain_identity.bopmaps_domain"]}]}, {"mode": "managed", "type": "aws_ses_domain_identity", "name": "bopmaps_domain", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ses:us-east-1:************:identity/bopmaps.com", "domain": "bopmaps.com", "id": "bopmaps.com", "verification_token": "oM85WZ94SuF7jujXEtxZ2JJ3HLvzM0zpm1Q5XIUiCTY="}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_ses_email_identity", "name": "admin_email", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ses:us-east-1:************:identity/<EMAIL>", "email": "<EMAIL>", "id": "<EMAIL>"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_ses_email_identity", "name": "noreply_email", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ses:us-east-1:************:identity/<EMAIL>", "email": "<EMAIL>", "id": "<EMAIL>"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_ses_event_destination", "name": "cloudwatch", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ses:us-east-1:************:configuration-set/bopmaps-emails:event-destination/cloudwatch-destination", "cloudwatch_destination": [{"default_value": "default", "dimension_name": "MessageTag", "value_source": "messageTag"}], "configuration_set_name": "bopmaps-emails", "enabled": true, "id": "cloudwatch-destination", "kinesis_destination": [], "matching_types": ["bounce", "complaint", "delivery", "reject", "send"], "name": "cloudwatch-destination", "sns_destination": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_ses_configuration_set.bopmaps_emails"]}]}, {"mode": "managed", "type": "aws_ses_template", "name": "school_verification", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ses:us-east-1:************:template/school-verification", "html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Verify Your School Email - BOP Maps</title>\n    <style>\n        body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;\n            margin: 0;\n            padding: 0;\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            min-height: 100vh;\n        }\n        .container {\n            max-width: 600px;\n            margin: 0 auto;\n            padding: 20px;\n        }\n        .email-card {\n            background: rgba(255, 255, 255, 0.95);\n            backdrop-filter: blur(10px);\n            border-radius: 20px;\n            padding: 40px;\n            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n            border: 1px solid rgba(255, 255, 255, 0.2);\n        }\n        .header {\n            text-align: center;\n            margin-bottom: 30px;\n        }\n        .logo {\n            font-size: 32px;\n            font-weight: bold;\n            background: linear-gradient(45deg, #667eea, #764ba2);\n            -webkit-background-clip: text;\n            -webkit-text-fill-color: transparent;\n            background-clip: text;\n            margin-bottom: 10px;\n        }\n        .title {\n            font-size: 24px;\n            font-weight: 600;\n            color: #2c3e50;\n            margin-bottom: 10px;\n        }\n        .subtitle {\n            font-size: 16px;\n            color: #7f8c8d;\n            margin-bottom: 30px;\n        }\n        .school-badge {\n            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);\n            color: white;\n            padding: 8px 20px;\n            border-radius: 20px;\n            font-size: 14px;\n            font-weight: 600;\n            display: inline-block;\n            margin-bottom: 20px;\n        }\n        .code-container {\n            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n            border-radius: 15px;\n            padding: 30px;\n            text-align: center;\n            margin: 30px 0;\n            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);\n        }\n        .verification-code {\n            font-size: 42px;\n            font-weight: bold;\n            color: white;\n            letter-spacing: 8px;\n            font-family: 'Courier New', monospace;\n            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\n        }\n        .code-label {\n            color: rgba(255, 255, 255, 0.8);\n            font-size: 14px;\n            margin-bottom: 10px;\n            text-transform: uppercase;\n            letter-spacing: 2px;\n        }\n        .info-box {\n            background: rgba(255, 193, 7, 0.1);\n            border: 1px solid rgba(255, 193, 7, 0.3);\n            border-radius: 10px;\n            padding: 20px;\n            margin: 20px 0;\n        }\n        .info-box .icon {\n            font-size: 20px;\n            margin-right: 10px;\n        }\n        .info-text {\n            color: #856404;\n            font-size: 14px;\n            line-height: 1.5;\n        }\n        .features {\n            background: rgba(156, 39, 176, 0.1);\n            border-radius: 10px;\n            padding: 20px;\n            margin: 20px 0;\n        }\n        .features-title {\n            color: #7b1fa2;\n            font-weight: 600;\n            margin-bottom: 15px;\n        }\n        .feature-item {\n            display: flex;\n            align-items: center;\n            margin: 10px 0;\n            color: #7b1fa2;\n        }\n        .feature-icon {\n            font-size: 18px;\n            margin-right: 12px;\n        }\n\n        .footer {\n            text-align: center;\n            margin-top: 40px;\n            padding-top: 20px;\n            border-top: 1px solid rgba(0, 0, 0, 0.1);\n        }\n        .footer-text {\n            color: #95a5a6;\n            font-size: 14px;\n            line-height: 1.5;\n        }\n        .footer-links {\n            margin-top: 15px;\n        }\n        .footer-link {\n            color: #3498db;\n            text-decoration: none;\n            margin: 0 10px;\n        }\n        @media (max-width: 600px) {\n            .container { padding: 10px; }\n            .email-card { padding: 20px; }\n            .verification-code { font-size: 32px; letter-spacing: 4px; }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"email-card\">\n            <div class=\"header\">\n                <div class=\"logo\">\\uD83C\\uDF93 BOP Maps</div>\n                <div class=\"title\">Verify Your School Email</div>\n                <div class=\"subtitle\">Join your campus music community!</div>\n                <div class=\"school-badge\">\\uD83C\\uDFEB {{school_name}}</div>\n            </div>\n\n            <p style=\"color: #2c3e50; font-size: 16px; line-height: 1.6;\">\n                Hi <strong>{{username}}</strong>,\n            </p>\n            \n            <p style=\"color: #2c3e50; font-size: 16px; line-height: 1.6;\">\n                Welcome to BOP Maps! To complete your verification for <strong>{{school_name}}</strong>, please enter the verification code below:\n            </p>\n            \n            <div class=\"code-container\">\n                <div class=\"code-label\">Your Verification Code</div>\n                <div class=\"verification-code\">{{code}}</div>\n            </div>\n            \n            <div class=\"info-box\">\n                <div class=\"info-text\">\n                    <span class=\"icon\">⏰</span>\n                    <strong>This code expires in 30 minutes.</strong><br>\n                    If you didn't request this verification, please ignore this email.\n                </div>\n            </div>\n            \n\n            \n            <div class=\"features\">\n                <div class=\"features-title\">\\uD83C\\uDF93 Campus Features Unlocked:</div>\n                <div class=\"feature-item\">\n                    <span class=\"feature-icon\">\\uD83C\\uDFC6</span>\n                    <span>Campus leaderboards - compete with classmates</span>\n                </div>\n                <div class=\"feature-item\">\n                    <span class=\"feature-icon\">\\uD83C\\uDFB5</span>\n                    <span>School challenges - join weekly music contests</span>\n                </div>\n                <div class=\"feature-item\">\n                    <span class=\"feature-icon\">\\uD83D\\uDC65</span>\n                    <span>Student network - connect with fellow students</span>\n                </div>\n                <div class=\"feature-item\">\n                    <span class=\"feature-icon\">✅</span>\n                    <span>Verified student badge - show your school pride</span>\n                </div>\n                <div class=\"feature-item\">\n                    <span class=\"feature-icon\">\\uD83D\\uDCCD</span>\n                    <span>Campus pins - discover music around your school</span>\n                </div>\n            </div>\n            \n            <div class=\"footer\">\n                <div class=\"footer-text\">\n                    <p>Questions? We're here to help!</p>\n                    <div class=\"footer-links\">\n                        <a href=\"mailto:<EMAIL>\" class=\"footer-link\">Support</a>\n                        <a href=\"https://bopmaps.com/privacy\" class=\"footer-link\">Privacy</a>\n                        <a href=\"https://bopmaps.com/terms\" class=\"footer-link\">Terms</a>\n                    </div>\n                </div>\n                <p style=\"color: #bdc3c7; font-size: 12px; margin-top: 20px;\">\n                    © 2024 BOP Maps. All rights reserved.<br>\n                    Discover Music Together \\uD83C\\uDFB6\n                </p>\n            </div>\n        </div>\n    </div>\n</body>\n</html> ", "id": "school-verification", "name": "school-verification", "subject": "\\uD83C\\uDF93 Verify Your School Email - BOP Maps", "text": "\\uD83C\\uDF93 BOP Maps - Verify Your School Email\n\nHi {{username}},\n\nWelcome to BOP Maps! To complete your verification for {{school_name}}, please enter the verification code below:\n\nVERIFICATION CODE: {{code}}\n\n⏰ This code expires in 30 minutes.\nIf you didn't request this verification, please ignore this email.\n\n\\uD83D\\uDCCB Next Steps:\n1. Open BOP Maps app\n2. Go to School Verification\n3. Enter the 6-digit code above\n4. Enjoy exclusive campus features!\n\n\\uD83C\\uDF93 Campus Features Unlocked:\n• Campus leaderboards - compete with classmates\n• School challenges - join weekly music contests\n• Student network - connect with fellow students\n• Verified student badge - show your school pride\n• Campus pins - discover music around your school\n\nQuestions? We're here to help!\nContact <NAME_EMAIL>\n\n© 2024 BOP Maps. All rights reserved.\nDiscover Music Together \\uD83C\\uDFB6\n\n---\nPrivacy Policy: https://bopmaps.com/privacy\nTerms of Service: https://bopmaps.com/terms "}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_ses_template", "name": "school_verification_success", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ses:us-east-1:************:template/school-verification-success", "html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>School Verification Complete - BOP Maps</title>\n    <style>\n        body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;\n            margin: 0;\n            padding: 0;\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            min-height: 100vh;\n        }\n        .container {\n            max-width: 600px;\n            margin: 0 auto;\n            padding: 20px;\n        }\n        .email-card {\n            background: rgba(255, 255, 255, 0.95);\n            backdrop-filter: blur(10px);\n            border-radius: 20px;\n            padding: 40px;\n            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n            border: 1px solid rgba(255, 255, 255, 0.2);\n        }\n        .header {\n            text-align: center;\n            margin-bottom: 30px;\n        }\n        .logo {\n            font-size: 32px;\n            font-weight: bold;\n            background: linear-gradient(45deg, #667eea, #764ba2);\n            -webkit-background-clip: text;\n            -webkit-text-fill-color: transparent;\n            background-clip: text;\n            margin-bottom: 10px;\n        }\n        .title {\n            font-size: 24px;\n            font-weight: 600;\n            color: #2c3e50;\n            margin-bottom: 10px;\n        }\n        .subtitle {\n            font-size: 16px;\n            color: #7f8c8d;\n            margin-bottom: 30px;\n        }\n        .success-badge {\n            background: linear-gradient(45deg, #4CAF50 0%, #45a049 100%);\n            color: white;\n            padding: 15px 30px;\n            border-radius: 50px;\n            font-size: 16px;\n            font-weight: 600;\n            display: inline-block;\n            margin-bottom: 20px;\n            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);\n        }\n        .school-badge {\n            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);\n            color: white;\n            padding: 8px 20px;\n            border-radius: 20px;\n            font-size: 14px;\n            font-weight: 600;\n            display: inline-block;\n            margin-bottom: 20px;\n        }\n        .celebration-box {\n            background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);\n            border-radius: 15px;\n            padding: 30px;\n            text-align: center;\n            margin: 30px 0;\n            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);\n        }\n        .celebration-text {\n            font-size: 24px;\n            font-weight: bold;\n            color: white;\n            margin-bottom: 10px;\n        }\n        .celebration-subtext {\n            color: rgba(255, 255, 255, 0.9);\n            font-size: 16px;\n        }\n        .features {\n            background: rgba(76, 175, 80, 0.1);\n            border-radius: 10px;\n            padding: 20px;\n            margin: 20px 0;\n        }\n        .features-title {\n            color: #2e7d32;\n            font-weight: 600;\n            margin-bottom: 15px;\n            font-size: 18px;\n        }\n        .feature-item {\n            display: flex;\n            align-items: center;\n            margin: 15px 0;\n            color: #2e7d32;\n            padding: 10px;\n            background: rgba(255, 255, 255, 0.5);\n            border-radius: 8px;\n        }\n        .feature-icon {\n            font-size: 24px;\n            margin-right: 15px;\n        }\n        .feature-content {\n            flex: 1;\n        }\n        .feature-title {\n            font-weight: 600;\n            margin-bottom: 5px;\n        }\n        .feature-desc {\n            font-size: 14px;\n            color: #4a5568;\n        }\n\n        .footer {\n            text-align: center;\n            margin-top: 40px;\n            padding-top: 20px;\n            border-top: 1px solid rgba(0, 0, 0, 0.1);\n        }\n        .footer-text {\n            color: #95a5a6;\n            font-size: 14px;\n            line-height: 1.5;\n        }\n        .footer-links {\n            margin-top: 15px;\n        }\n        .footer-link {\n            color: #3498db;\n            text-decoration: none;\n            margin: 0 10px;\n        }\n        @media (max-width: 600px) {\n            .container { padding: 10px; }\n            .email-card { padding: 20px; }\n            .celebration-text { font-size: 20px; }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"email-card\">\n            <div class=\"header\">\n                <div class=\"logo\">\\uD83C\\uDF93 BOP Maps</div>\n                <div class=\"title\">Verification Complete!</div>\n                <div class=\"subtitle\">Welcome to your campus music community</div>\n                <div class=\"success-badge\">✅ Verified Student</div>\n                <div class=\"school-badge\">\\uD83C\\uDFEB {{school_name}}</div>\n            </div>\n\n            <div class=\"celebration-box\">\n                <div class=\"celebration-text\">\\uD83C\\uDF89 Congratulations!</div>\n                <div class=\"celebration-subtext\">You're now a verified {{school_name}} student on BOP Maps!</div>\n            </div>\n\n            <p style=\"color: #2c3e50; font-size: 16px; line-height: 1.6;\">\n                Hi <strong>{{username}}</strong>,\n            </p>\n            \n            <p style=\"color: #2c3e50; font-size: 16px; line-height: 1.6;\">\n                Your school email has been successfully verified! You now have access to exclusive features for <strong>{{school_name}}</strong> students.\n            </p>\n            \n            <div class=\"features\">\n                <div class=\"features-title\">\\uD83C\\uDF93 Your Campus Features Are Now Active:</div>\n                <div class=\"feature-item\">\n                    <span class=\"feature-icon\">\\uD83C\\uDFC6</span>\n                    <div class=\"feature-content\">\n                        <div class=\"feature-title\">Campus Leaderboards</div>\n                        <div class=\"feature-desc\">Compete with fellow students and climb the rankings</div>\n                    </div>\n                </div>\n                <div class=\"feature-item\">\n                    <span class=\"feature-icon\">\\uD83C\\uDFB5</span>\n                    <div class=\"feature-content\">\n                        <div class=\"feature-title\">School Challenges</div>\n                        <div class=\"feature-desc\">Join weekly music challenges exclusive to your campus</div>\n                    </div>\n                </div>\n                <div class=\"feature-item\">\n                    <span class=\"feature-icon\">\\uD83D\\uDC65</span>\n                    <div class=\"feature-content\">\n                        <div class=\"feature-title\">Student Network</div>\n                        <div class=\"feature-desc\">Connect and discover music with your classmates</div>\n                    </div>\n                </div>\n                <div class=\"feature-item\">\n                    <span class=\"feature-icon\">✅</span>\n                    <div class=\"feature-content\">\n                        <div class=\"feature-title\">Verified Badge</div>\n                        <div class=\"feature-desc\">Show your school pride with your verified student status</div>\n                    </div>\n                </div>\n                <div class=\"feature-item\">\n                    <span class=\"feature-icon\">\\uD83D\\uDCCD</span>\n                    <div class=\"feature-content\">\n                        <div class=\"feature-title\">Campus Pins</div>\n                        <div class=\"feature-desc\">Discover and share music around your campus</div>\n                    </div>\n                </div>\n            </div>\n            \n\n            \n            <div class=\"footer\">\n                <div class=\"footer-text\">\n                    <p>Questions? We're here to help!</p>\n                    <div class=\"footer-links\">\n                        <a href=\"mailto:<EMAIL>\" class=\"footer-link\">Support</a>\n                        <a href=\"https://bopmaps.com/privacy\" class=\"footer-link\">Privacy</a>\n                        <a href=\"https://bopmaps.com/terms\" class=\"footer-link\">Terms</a>\n                    </div>\n                </div>\n                <p style=\"color: #bdc3c7; font-size: 12px; margin-top: 20px;\">\n                    © 2024 BOP Maps. All rights reserved.<br>\n                    Discover Music Together \\uD83C\\uDFB6\n                </p>\n            </div>\n        </div>\n    </div>\n</body>\n</html> ", "id": "school-verification-success", "name": "school-verification-success", "subject": "\\uD83C\\uDF89 Welcome to BOP Maps - School Verification Complete!", "text": "\\uD83C\\uDF93 BOP Maps - School Verification Complete!\n\n\\uD83C\\uDF89 Congratulations!\n\nHi {{username}},\n\nYour school email has been successfully verified! You're now a verified {{school_name}} student on BOP Maps!\n\n✅ Verified Student\n\\uD83C\\uDFEB {{school_name}}\n\n\\uD83C\\uDF93 Your Campus Features Are Now Active:\n\n\\uD83C\\uDFC6 Campus Leaderboards\n   Compete with fellow students and climb the rankings\n\n\\uD83C\\uDFB5 School Challenges\n   Join weekly music challenges exclusive to your campus\n\n\\uD83D\\uDC65 Student Network\n   Connect and discover music with your classmates\n\n✅ Verified Badge\n   Show your school pride with your verified student status\n\n\\uD83D\\uDCCD Campus Pins\n   Discover and share music around your campus\n\n\n\nQuestions? We're here to help!\nContact <NAME_EMAIL>\n\n© 2024 BOP Maps. All rights reserved.\nDiscover Music Together \\uD83C\\uDFB6\n\n---\nPrivacy Policy: https://bopmaps.com/privacy\nTerms of Service: https://bopmaps.com/terms "}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_ses_template", "name": "user_verification", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ses:us-east-1:************:template/user-verification", "html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Verify Your Email - BOP Maps</title>\n    <style>\n        body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;\n            margin: 0;\n            padding: 0;\n            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);\n            min-height: 100vh;\n        }\n        .container {\n            max-width: 600px;\n            margin: 0 auto;\n            padding: 20px;\n        }\n        .email-card {\n            background: rgba(255, 255, 255, 0.95);\n            backdrop-filter: blur(10px);\n            border-radius: 20px;\n            padding: 40px;\n            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n            border: 1px solid rgba(255, 255, 255, 0.2);\n        }\n        .header {\n            text-align: center;\n            margin-bottom: 30px;\n        }\n        .logo {\n            font-size: 32px;\n            font-weight: bold;\n            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);\n            -webkit-background-clip: text;\n            -webkit-text-fill-color: transparent;\n            background-clip: text;\n            margin-bottom: 10px;\n        }\n        .title {\n            font-size: 24px;\n            font-weight: 600;\n            color: #2c3e50;\n            margin-bottom: 10px;\n        }\n        .subtitle {\n            font-size: 16px;\n            color: #7f8c8d;\n            margin-bottom: 30px;\n        }\n        .code-container {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            border-radius: 15px;\n            padding: 30px;\n            text-align: center;\n            margin: 30px 0;\n            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);\n        }\n        .verification-code {\n            font-size: 42px;\n            font-weight: bold;\n            color: white;\n            letter-spacing: 8px;\n            font-family: 'Courier New', monospace;\n            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\n        }\n        .code-label {\n            color: rgba(255, 255, 255, 0.8);\n            font-size: 14px;\n            margin-bottom: 10px;\n            text-transform: uppercase;\n            letter-spacing: 2px;\n        }\n        .info-box {\n            background: rgba(255, 193, 7, 0.1);\n            border: 1px solid rgba(255, 193, 7, 0.3);\n            border-radius: 10px;\n            padding: 20px;\n            margin: 20px 0;\n        }\n        .info-box .icon {\n            font-size: 20px;\n            margin-right: 10px;\n        }\n        .info-text {\n            color: #856404;\n            font-size: 14px;\n            line-height: 1.5;\n        }\n        .features {\n            background: rgba(76, 175, 80, 0.1);\n            border-radius: 10px;\n            padding: 20px;\n            margin: 20px 0;\n        }\n        .features-title {\n            color: #2e7d32;\n            font-weight: 600;\n            margin-bottom: 15px;\n        }\n        .feature-item {\n            display: flex;\n            align-items: center;\n            margin: 10px 0;\n            color: #2e7d32;\n        }\n        .feature-icon {\n            font-size: 18px;\n            margin-right: 12px;\n        }\n        .footer {\n            text-align: center;\n            margin-top: 40px;\n            padding-top: 20px;\n            border-top: 1px solid rgba(0, 0, 0, 0.1);\n        }\n        .footer-text {\n            color: #95a5a6;\n            font-size: 14px;\n            line-height: 1.5;\n        }\n        .footer-links {\n            margin-top: 15px;\n        }\n        .footer-link {\n            color: #3498db;\n            text-decoration: none;\n            margin: 0 10px;\n        }\n        .button {\n            display: inline-block;\n            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);\n            color: white;\n            padding: 12px 30px;\n            border-radius: 25px;\n            text-decoration: none;\n            font-weight: 600;\n            margin: 20px 0;\n            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);\n        }\n        @media (max-width: 600px) {\n            .container { padding: 10px; }\n            .email-card { padding: 20px; }\n            .verification-code { font-size: 32px; letter-spacing: 4px; }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"email-card\">\n            <div class=\"header\">\n                <div class=\"logo\">\\uD83C\\uDFB5 BOP Maps</div>\n                <div class=\"title\">Verify Your Email</div>\n                <div class=\"subtitle\">Welcome to the music discovery community!</div>\n            </div>\n\n            <p style=\"color: #2c3e50; font-size: 16px; line-height: 1.6;\">\n                Hi <strong>{{username}}</strong>,\n            </p>\n            \n            <p style=\"color: #2c3e50; font-size: 16px; line-height: 1.6;\">\n                Welcome to BOP Maps! To complete your account setup, please verify your email address by entering the code below:\n            </p>\n            \n            <div class=\"code-container\">\n                <div class=\"code-label\">Your Verification Code</div>\n                <div class=\"verification-code\">{{code}}</div>\n            </div>\n            \n            <div class=\"info-box\">\n                <div class=\"info-text\">\n                    <span class=\"icon\">⏰</span>\n                    <strong>This code expires in 10 minutes.</strong><br>\n                    If you didn't create an account, please ignore this email.\n                </div>\n            </div>\n            \n            <div class=\"features\">\n                <div class=\"features-title\">\\uD83C\\uDFB6 What's waiting for you:</div>\n                <div class=\"feature-item\">\n                    <span class=\"feature-icon\">\\uD83C\\uDFB5</span>\n                    <span>Drop music pins and share your favorite tracks</span>\n                </div>\n                <div class=\"feature-item\">\n                    <span class=\"feature-icon\">\\uD83D\\uDDFA️</span>\n                    <span>Explore music from around the world</span>\n                </div>\n                <div class=\"feature-item\">\n                    <span class=\"feature-icon\">\\uD83D\\uDC65</span>\n                    <span>Connect with fellow music lovers</span>\n                </div>\n                <div class=\"feature-item\">\n                    <span class=\"feature-icon\">\\uD83C\\uDFC6</span>\n                    <span>Compete in music challenges</span>\n                </div>\n            </div>\n            \n            <div class=\"footer\">\n                <div class=\"footer-text\">\n                    <p>Questions? We're here to help!</p>\n                    <div class=\"footer-links\">\n                        <a href=\"mailto:<EMAIL>\" class=\"footer-link\">Support</a>\n                        <a href=\"https://bopmaps.com/privacy\" class=\"footer-link\">Privacy</a>\n                        <a href=\"https://bopmaps.com/terms\" class=\"footer-link\">Terms</a>\n                    </div>\n                </div>\n                <p style=\"color: #bdc3c7; font-size: 12px; margin-top: 20px;\">\n                    © 2024 BOP Maps. All rights reserved.<br>\n                    Discover Music Together \\uD83C\\uDFB6\n                </p>\n            </div>\n        </div>\n    </div>\n</body>\n</html> ", "id": "user-verification", "name": "user-verification", "subject": "\\uD83C\\uDFB5 Verify Your Email - BOP Maps", "text": "\\uD83C\\uDFB5 BOP Maps - Verify Your Email\n\nHi {{username}},\n\nWelcome to BOP Maps! To complete your account setup, please verify your email address by entering the code below:\n\nVERIFICATION CODE: {{code}}\n\n⏰ This code expires in 10 minutes.\nIf you didn't create an account, please ignore this email.\n\n\\uD83C\\uDFB6 What's waiting for you:\n• Drop music pins and share your favorite tracks\n• Explore music from around the world\n• Connect with fellow music lovers\n• Compete in music challenges\n\nQuestions? We're here to help!\nContact <NAME_EMAIL>\n\n© 2024 BOP Maps. All rights reserved.\nDiscover Music Together \\uD83C\\uDFB6\n\n---\nPrivacy Policy: https://bopmaps.com/privacy\nTerms of Service: https://bopmaps.com/terms "}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_sns_topic", "name": "alerts", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"application_failure_feedback_role_arn": "", "application_success_feedback_role_arn": "", "application_success_feedback_sample_rate": 0, "archive_policy": "", "arn": "arn:aws:sns:us-east-1:************:bopmaps-prod-alerts", "beginning_archive_time": "", "content_based_deduplication": false, "delivery_policy": "", "display_name": "", "fifo_throughput_scope": "", "fifo_topic": false, "firehose_failure_feedback_role_arn": "", "firehose_success_feedback_role_arn": "", "firehose_success_feedback_sample_rate": 0, "http_failure_feedback_role_arn": "", "http_success_feedback_role_arn": "", "http_success_feedback_sample_rate": 0, "id": "arn:aws:sns:us-east-1:************:bopmaps-prod-alerts", "kms_master_key_id": "", "lambda_failure_feedback_role_arn": "", "lambda_success_feedback_role_arn": "", "lambda_success_feedback_sample_rate": 0, "name": "bopmaps-prod-alerts", "name_prefix": "", "owner": "************", "policy": "{\"Id\":\"__default_policy_ID\",\"Statement\":[{\"Action\":[\"SNS:GetTopicAttributes\",\"SNS:SetTopicAttributes\",\"SNS:AddPermission\",\"SNS:RemovePermission\",\"SNS:DeleteTopic\",\"SNS:Subscribe\",\"SNS:ListSubscriptionsByTopic\",\"SNS:Publish\"],\"Condition\":{\"StringEquals\":{\"AWS:SourceOwner\":\"************\"}},\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"*\"},\"Resource\":\"arn:aws:sns:us-east-1:************:bopmaps-prod-alerts\",\"Sid\":\"__default_statement_ID\"}],\"Version\":\"2008-10-17\"}", "signature_version": 0, "sqs_failure_feedback_role_arn": "", "sqs_success_feedback_role_arn": "", "sqs_success_feedback_sample_rate": 0, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "BOPMaps"}, "tracing_config": ""}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_subnet", "name": "database", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-09848662178d915de", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az2", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-09848662178d915de", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-database-1", "Project": "BOPMaps", "Type": "database"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-database-1", "Project": "BOPMaps", "Type": "database"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-020ac6c5d382b814a", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az4", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-020ac6c5d382b814a", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-database-2", "Project": "BOPMaps", "Type": "database"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-database-2", "Project": "BOPMaps", "Type": "database"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-0284f054c45fb6f70", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az2", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0284f054c45fb6f70", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-private-1", "Project": "BOPMaps", "Type": "private"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-private-1", "Project": "BOPMaps", "Type": "private"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-0889380393e07eddb", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az4", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0889380393e07eddb", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-private-2", "Project": "BOPMaps", "Type": "private"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-private-2", "Project": "BOPMaps", "Type": "private"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-0093c6ce8bf0d0ee0", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az2", "cidr_block": "********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0093c6ce8bf0d0ee0", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-public-1", "Project": "BOPMaps", "Type": "public"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-public-1", "Project": "BOPMaps", "Type": "public"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-0779972d6df51b24b", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az4", "cidr_block": "********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0779972d6df51b24b", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-public-2", "Project": "BOPMaps", "Type": "public"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-public-2", "Project": "BOPMaps", "Type": "public"}, "timeouts": null, "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_vpc", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:vpc/vpc-0f829382da4d81ab8", "assign_generated_ipv6_cidr_block": false, "cidr_block": "10.0.0.0/16", "default_network_acl_id": "acl-0b3ad582c7b86c376", "default_route_table_id": "rtb-06f2cd8f956627318", "default_security_group_id": "sg-0e632990cc27fb99c", "dhcp_options_id": "dopt-086ba8a2cd68cc086", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-0f829382da4d81ab8", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-06f2cd8f956627318", "owner_id": "************", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-vpc", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-vpc", "Project": "BOPMaps"}}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_vpc_endpoint", "name": "ecr_api", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:vpc-endpoint/vpce-068df205779c276ab", "auto_accept": null, "cidr_blocks": [], "dns_entry": [{"dns_name": "vpce-068df205779c276ab-0px7rdtr.api.ecr.us-east-1.vpce.amazonaws.com", "hosted_zone_id": "Z7HUB22UULQXV"}, {"dns_name": "vpce-068df205779c276ab-0px7rdtr-us-east-1b.api.ecr.us-east-1.vpce.amazonaws.com", "hosted_zone_id": "Z7HUB22UULQXV"}, {"dns_name": "vpce-068df205779c276ab-0px7rdtr-us-east-1a.api.ecr.us-east-1.vpce.amazonaws.com", "hosted_zone_id": "Z7HUB22UULQXV"}], "dns_options": [{"dns_record_ip_type": "ipv4", "private_dns_only_for_inbound_resolver_endpoint": false}], "id": "vpce-068df205779c276ab", "ip_address_type": "ipv4", "network_interface_ids": ["eni-0173729b0e436162f", "eni-09ec659b74a8f39a5"], "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "prefix_list_id": null, "private_dns_enabled": false, "requester_managed": false, "resource_configuration_arn": "", "route_table_ids": [], "security_group_ids": ["sg-09e1f694072e316e7"], "service_name": "com.amazonaws.us-east-1.ecr.api", "service_network_arn": "", "service_region": "us-east-1", "state": "available", "subnet_configuration": [{"ipv4": "***********", "ipv6": "", "subnet_id": "subnet-0284f054c45fb6f70"}, {"ipv4": "***********", "ipv6": "", "subnet_id": "subnet-0889380393e07eddb"}], "subnet_ids": ["subnet-0284f054c45fb6f70", "subnet-0889380393e07eddb"], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-ecr-api-endpoint", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-ecr-api-endpoint", "Project": "BOPMaps"}, "timeouts": null, "vpc_endpoint_type": "Interface", "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.vpc_endpoints", "aws_subnet.private", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_vpc_endpoint", "name": "ecr_dkr", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:vpc-endpoint/vpce-098c102f4557300ce", "auto_accept": null, "cidr_blocks": [], "dns_entry": [{"dns_name": "vpce-098c102f4557300ce-ya1g4ww6.dkr.ecr.us-east-1.vpce.amazonaws.com", "hosted_zone_id": "Z7HUB22UULQXV"}, {"dns_name": "vpce-098c102f4557300ce-ya1g4ww6-us-east-1b.dkr.ecr.us-east-1.vpce.amazonaws.com", "hosted_zone_id": "Z7HUB22UULQXV"}, {"dns_name": "vpce-098c102f4557300ce-ya1g4ww6-us-east-1a.dkr.ecr.us-east-1.vpce.amazonaws.com", "hosted_zone_id": "Z7HUB22UULQXV"}], "dns_options": [{"dns_record_ip_type": "ipv4", "private_dns_only_for_inbound_resolver_endpoint": false}], "id": "vpce-098c102f4557300ce", "ip_address_type": "ipv4", "network_interface_ids": ["eni-037b268f6706a95ae", "eni-060024744af95031b"], "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":[\"ecr:GetDownloadUrlForLayer\",\"ecr:GetAuthorizationToken\",\"ecr:BatchGetImage\",\"ecr:BatchCheckLayerAvailability\"],\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"*\"},\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "prefix_list_id": null, "private_dns_enabled": false, "requester_managed": false, "resource_configuration_arn": "", "route_table_ids": [], "security_group_ids": ["sg-09e1f694072e316e7"], "service_name": "com.amazonaws.us-east-1.ecr.dkr", "service_network_arn": "", "service_region": "us-east-1", "state": "available", "subnet_configuration": [{"ipv4": "**********", "ipv6": "", "subnet_id": "subnet-0284f054c45fb6f70"}, {"ipv4": "**********", "ipv6": "", "subnet_id": "subnet-0889380393e07eddb"}], "subnet_ids": ["subnet-0284f054c45fb6f70", "subnet-0889380393e07eddb"], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-ecr-dkr-endpoint", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-ecr-dkr-endpoint", "Project": "BOPMaps"}, "timeouts": null, "vpc_endpoint_type": "Interface", "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.vpc_endpoints", "aws_subnet.private", "aws_vpc.main", "data.aws_availability_zones.available", "data.aws_iam_policy_document.ecr_endpoint_policy"]}]}, {"mode": "managed", "type": "aws_vpc_endpoint", "name": "logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:vpc-endpoint/vpce-0562d469097756a5c", "auto_accept": null, "cidr_blocks": [], "dns_entry": [{"dns_name": "vpce-0562d469097756a5c-o7laaf0z.logs.us-east-1.vpce.amazonaws.com", "hosted_zone_id": "Z7HUB22UULQXV"}, {"dns_name": "vpce-0562d469097756a5c-o7laaf0z-us-east-1b.logs.us-east-1.vpce.amazonaws.com", "hosted_zone_id": "Z7HUB22UULQXV"}, {"dns_name": "vpce-0562d469097756a5c-o7laaf0z-us-east-1a.logs.us-east-1.vpce.amazonaws.com", "hosted_zone_id": "Z7HUB22UULQXV"}], "dns_options": [{"dns_record_ip_type": "ipv4", "private_dns_only_for_inbound_resolver_endpoint": false}], "id": "vpce-0562d469097756a5c", "ip_address_type": "ipv4", "network_interface_ids": ["eni-09a73ed455866ba67", "eni-0ac4a3470afc50b65"], "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}]}", "prefix_list_id": null, "private_dns_enabled": false, "requester_managed": false, "resource_configuration_arn": "", "route_table_ids": [], "security_group_ids": ["sg-09e1f694072e316e7"], "service_name": "com.amazonaws.us-east-1.logs", "service_network_arn": "", "service_region": "us-east-1", "state": "available", "subnet_configuration": [{"ipv4": "*********", "ipv6": "", "subnet_id": "subnet-0284f054c45fb6f70"}, {"ipv4": "***********", "ipv6": "", "subnet_id": "subnet-0889380393e07eddb"}], "subnet_ids": ["subnet-0284f054c45fb6f70", "subnet-0889380393e07eddb"], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-logs-endpoint", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-logs-endpoint", "Project": "BOPMaps"}, "timeouts": null, "vpc_endpoint_type": "Interface", "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_security_group.alb", "aws_security_group.ecs", "aws_security_group.vpc_endpoints", "aws_subnet.private", "aws_vpc.main", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_vpc_endpoint", "name": "s3", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:vpc-endpoint/vpce-078891e729efa8353", "auto_accept": null, "cidr_blocks": ["***********/20", "**********/16", "*********/19", "**********/15", "**********/16", "*******/19", "***********/21", "***********/18"], "dns_entry": [], "dns_options": [], "id": "vpce-078891e729efa8353", "ip_address_type": "", "network_interface_ids": [], "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2008-10-17\"}", "prefix_list_id": "pl-63a5400a", "private_dns_enabled": false, "requester_managed": false, "resource_configuration_arn": "", "route_table_ids": ["rtb-01894a5814df71a40", "rtb-0903c62098a0902b5"], "security_group_ids": [], "service_name": "com.amazonaws.us-east-1.s3", "service_network_arn": "", "service_region": "us-east-1", "state": "available", "subnet_configuration": [], "subnet_ids": [], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-s3-endpoint", "Project": "BOPMaps"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "bopmaps-prod-s3-endpoint", "Project": "BOPMaps"}, "timeouts": null, "vpc_endpoint_type": "Gateway", "vpc_id": "vpc-0f829382da4d81ab8"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_vpc_endpoint_route_table_association", "name": "s3_private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "a-vpce-078891e729efa83532635881190", "route_table_id": "rtb-01894a5814df71a40", "vpc_endpoint_id": "vpce-078891e729efa8353"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_route_table.private", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main", "aws_vpc_endpoint.s3", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 0, "attributes": {"id": "a-vpce-078891e729efa83531679962306", "route_table_id": "rtb-0903c62098a0902b5", "vpc_endpoint_id": "vpce-078891e729efa8353"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_route_table.private", "aws_subnet.private", "aws_subnet.public", "aws_vpc.main", "aws_vpc_endpoint.s3", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "b2_bucket", "name": "media", "provider": "provider[\"registry.terraform.io/backblaze/b2\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "b50dabb51c63", "bucket_id": "9b85800dfa2bcbb5917c0613", "bucket_info": {}, "bucket_name": "bopmaps-prod-media-67ba6151", "bucket_type": "allPublic", "cors_rules": [{"allowed_headers": ["*"], "allowed_operations": ["b2_download_file_by_name", "b2_download_file_by_id", "b2_upload_file", "b2_upload_part"], "allowed_origins": ["https://api.bopmaps.com"], "cors_rule_name": "downloadFromApp", "expose_headers": ["x-bz-content-sha1", "x-bz-file-id", "x-bz-file-name"], "max_age_seconds": 3600}], "default_server_side_encryption": [{"algorithm": "", "mode": "none"}], "file_lock_configuration": [{"default_retention": [], "is_file_lock_enabled": false}], "id": "9b85800dfa2bcbb5917c0613", "lifecycle_rules": [{"days_from_hiding_to_deleting": 30, "days_from_uploading_to_hiding": 0, "file_name_prefix": ""}], "options": ["s3"], "revision": 2}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["random_id.b2_bucket_suffix"]}]}, {"mode": "managed", "type": "b2_bucket", "name": "static", "provider": "provider[\"registry.terraform.io/backblaze/b2\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "b50dabb51c63", "bucket_id": "eb2570edfa2bcbb5917c0613", "bucket_info": {}, "bucket_name": "bopmaps-prod-static-67ba6151", "bucket_type": "allPublic", "cors_rules": [{"allowed_headers": ["*"], "allowed_operations": ["b2_download_file_by_name", "b2_download_file_by_id"], "allowed_origins": ["*"], "cors_rule_name": "downloadFromAnyOrigin", "expose_headers": ["x-bz-content-sha1", "x-bz-file-id", "x-bz-file-name"], "max_age_seconds": 3600}], "default_server_side_encryption": [{"algorithm": "", "mode": "none"}], "file_lock_configuration": [{"default_retention": [], "is_file_lock_enabled": false}], "id": "eb2570edfa2bcbb5917c0613", "lifecycle_rules": [{"days_from_hiding_to_deleting": 1, "days_from_uploading_to_hiding": 0, "file_name_prefix": ""}], "options": ["s3"], "revision": 2}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["random_id.b2_bucket_suffix"]}]}, {"mode": "managed", "type": "b2_bucket", "name": "tiles", "provider": "provider[\"registry.terraform.io/backblaze/b2\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "b50dabb51c63", "bucket_id": "bb1570fdfa2bcbb5917c0613", "bucket_info": {}, "bucket_name": "bopmaps-prod-tiles-67ba6151", "bucket_type": "allPublic", "cors_rules": [{"allowed_headers": ["*"], "allowed_operations": ["b2_download_file_by_name", "b2_download_file_by_id"], "allowed_origins": ["*"], "cors_rule_name": "tilesAccess", "expose_headers": ["x-bz-content-sha1", "x-bz-file-id", "x-bz-file-name"], "max_age_seconds": 86400}], "default_server_side_encryption": [{"algorithm": "", "mode": "none"}], "file_lock_configuration": [{"default_retention": [], "is_file_lock_enabled": false}], "id": "bb1570fdfa2bcbb5917c0613", "lifecycle_rules": [{"days_from_hiding_to_deleting": 90, "days_from_uploading_to_hiding": 0, "file_name_prefix": ""}], "options": ["s3"], "revision": 2}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["random_id.b2_bucket_suffix"]}]}, {"mode": "managed", "type": "cloudflare_record", "name": "app", "provider": "provider[\"registry.terraform.io/cloudflare/cloudflare\"]", "instances": [{"schema_version": 3, "attributes": {"allow_overwrite": false, "comment": "Main application domain", "content": "bopmaps-prod-alb-*********.us-east-1.elb.amazonaws.com", "created_on": "2025-07-02T16:26:06.510793Z", "data": [], "hostname": "api.bopmaps.com", "id": "63d3259343b378191ffb9cf9f1fdb4a8", "metadata": {}, "modified_on": "2025-07-02T16:26:06.510793Z", "name": "api.bopmaps.com", "priority": null, "proxiable": true, "proxied": true, "tags": [], "timeouts": null, "ttl": 1, "type": "CNAME", "value": null, "zone_id": "2187d49648625de67fbde39a41bd7019"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMyJ9", "dependencies": ["aws_lb.main", "aws_s3_bucket.static", "aws_security_group.alb", "aws_subnet.public", "aws_vpc.main", "data.aws_availability_zones.available", "random_id.bucket_suffix"]}]}, {"mode": "managed", "type": "cloudflare_record", "name": "b2tiles", "provider": "provider[\"registry.terraform.io/cloudflare/cloudflare\"]", "instances": [{"schema_version": 3, "attributes": {"allow_overwrite": false, "comment": "Map tiles CDN via Backblaze B2", "content": "f005.backblazeb2.com/file/bopmaps-prod-tiles-67ba6151", "created_on": "2025-07-02T16:23:02.392354Z", "data": [], "hostname": "b2tiles.bopmaps.com", "id": "b55cb41461e7e036056822116212c3f5", "metadata": {}, "modified_on": "2025-07-05T07:39:24.152Z", "name": "b2tiles", "priority": null, "proxiable": true, "proxied": true, "tags": [], "timeouts": null, "ttl": 1, "type": "CNAME", "value": null, "zone_id": "2187d49648625de67fbde39a41bd7019"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMyJ9", "dependencies": ["b2_bucket.tiles"]}]}, {"mode": "managed", "type": "cloudflare_record", "name": "cert_validation", "provider": "provider[\"registry.terraform.io/cloudflare/cloudflare\"]", "instances": [{"index_key": "api.bopmaps.com", "schema_version": 3, "attributes": {"allow_overwrite": false, "comment": "SSL certificate validation for api.bopmaps.com", "content": "_cdc00e4e8932b0ec2d3e6f6c99d88484.xlfgrmvvlj.acm-validations.aws", "created_on": "2025-07-02T16:22:55.469933Z", "data": [], "hostname": "_4a847e5cbfb09fab3487e2ebbdd21aef.api.bopmaps.com", "id": "706d43d310061410158c88715354b8d9", "metadata": {}, "modified_on": "2025-07-02T16:22:55.469933Z", "name": "_4a847e5cbfb09fab3487e2ebbdd21aef.api.bopmaps.com.", "priority": null, "proxiable": false, "proxied": false, "tags": [], "timeouts": null, "ttl": 60, "type": "CNAME", "value": null, "zone_id": "2187d49648625de67fbde39a41bd7019"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMyJ9", "dependencies": ["aws_acm_certificate.cert"]}, {"index_key": "media.api.bopmaps.com", "schema_version": 3, "attributes": {"allow_overwrite": false, "comment": "SSL certificate validation for media.api.bopmaps.com", "content": "_e535b7878ab1711dd13e5581844906ed.xlfgrmvvlj.acm-validations.aws", "created_on": "2025-07-02T16:22:53.832656Z", "data": [], "hostname": "_68201d01637d09e2412ca56ed2dcfa6d.media.api.bopmaps.com", "id": "f91eb11d036d15fad7c5b6158f1d2301", "metadata": {}, "modified_on": "2025-07-02T16:22:53.832656Z", "name": "_68201d01637d09e2412ca56ed2dcfa6d.media.api.bopmaps.com.", "priority": null, "proxiable": false, "proxied": false, "tags": [], "timeouts": null, "ttl": 60, "type": "CNAME", "value": null, "zone_id": "2187d49648625de67fbde39a41bd7019"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMyJ9", "dependencies": ["aws_acm_certificate.cert"]}, {"index_key": "static.api.bopmaps.com", "schema_version": 3, "attributes": {"allow_overwrite": false, "comment": "SSL certificate validation for static.api.bopmaps.com", "content": "_cc7e2e5483050cd5eff3f4a1cec20708.xlfgrmvvlj.acm-validations.aws", "created_on": "2025-07-02T16:22:53.497351Z", "data": [], "hostname": "_14903d84e385543801e698b1f6b3a3a8.static.api.bopmaps.com", "id": "52b660095c3edafd42c8681773a8df18", "metadata": {}, "modified_on": "2025-07-02T16:22:53.497351Z", "name": "_14903d84e385543801e698b1f6b3a3a8.static.api.bopmaps.com.", "priority": null, "proxiable": false, "proxied": false, "tags": [], "timeouts": null, "ttl": 60, "type": "CNAME", "value": null, "zone_id": "2187d49648625de67fbde39a41bd7019"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMyJ9", "dependencies": ["aws_acm_certificate.cert"]}, {"index_key": "tiles.bopmaps.com", "schema_version": 3, "attributes": {"allow_overwrite": false, "comment": "SSL certificate validation for tiles.bopmaps.com", "content": "_2593d4c5693cb9b3896043d57d6666ab.xlfgrmvvlj.acm-validations.aws", "created_on": "2025-07-02T16:22:55.06943Z", "data": [], "hostname": "_c2b6b1189029e6e67f362248aee56d56.tiles.bopmaps.com", "id": "9f8eb0e8a0304c235ba3771360e598c3", "metadata": {}, "modified_on": "2025-07-02T16:22:55.06943Z", "name": "_c2b6b1189029e6e67f362248aee56d56.tiles.bopmaps.com.", "priority": null, "proxiable": false, "proxied": false, "tags": [], "timeouts": null, "ttl": 60, "type": "CNAME", "value": null, "zone_id": "2187d49648625de67fbde39a41bd7019"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMyJ9", "dependencies": ["aws_acm_certificate.cert"]}]}, {"mode": "managed", "type": "cloudflare_record", "name": "media", "provider": "provider[\"registry.terraform.io/cloudflare/cloudflare\"]", "instances": [{"schema_version": 3, "attributes": {"allow_overwrite": false, "comment": "Media files CDN via Backblaze B2", "content": "f005.backblazeb2.com/file/bopmaps-prod-media-67ba6151", "created_on": "2025-07-02T16:23:00.535922Z", "data": [], "hostname": "media.bopmaps.com", "id": "2b6ca5b7c7f3b0c0a1911b5c9004b65f", "metadata": {}, "modified_on": "2025-07-05T07:39:24.2749Z", "name": "media", "priority": null, "proxiable": true, "proxied": true, "tags": [], "timeouts": null, "ttl": 1, "type": "CNAME", "value": null, "zone_id": "2187d49648625de67fbde39a41bd7019"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMyJ9", "dependencies": ["b2_bucket.media"]}]}, {"mode": "managed", "type": "cloudflare_record", "name": "static", "provider": "provider[\"registry.terraform.io/cloudflare/cloudflare\"]", "instances": [{"schema_version": 3, "attributes": {"allow_overwrite": false, "comment": "Static files CDN via Backblaze B2", "content": "f005.backblazeb2.com/file/bopmaps-prod-static-67ba6151", "created_on": "2025-07-02T16:23:01.71254Z", "data": [], "hostname": "static.bopmaps.com", "id": "57668f0cf941634bb2bfbce5b2c089bd", "metadata": {}, "modified_on": "2025-07-05T07:39:24.526126Z", "name": "static", "priority": null, "proxiable": true, "proxied": true, "tags": [], "timeouts": null, "ttl": 1, "type": "CNAME", "value": null, "zone_id": "2187d49648625de67fbde39a41bd7019"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMyJ9", "dependencies": ["b2_bucket.static"]}]}, {"mode": "managed", "type": "random_id", "name": "b2_bucket_suffix", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 0, "attributes": {"b64_std": "Z7phUQ==", "b64_url": "Z7phUQ", "byte_length": 4, "dec": "**********", "hex": "67ba6151", "id": "Z7phUQ", "keepers": null, "prefix": null}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "random_id", "name": "bucket_suffix", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 0, "attributes": {"b64_std": "WOTDYA==", "b64_url": "WOTDYA", "byte_length": 4, "dec": "**********", "hex": "58e4c360", "id": "WOTDYA", "keepers": null, "prefix": null}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "random_password", "name": "db_password", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 3, "attributes": {"bcrypt_hash": "$2a$10$3tDB6gIg6clYPTaggWHCaeqetp6ukmr.RTCn/UrxmRBmM/YvLjbR.", "id": "none", "keepers": null, "length": 32, "lower": true, "min_lower": 0, "min_numeric": 0, "min_special": 0, "min_upper": 0, "number": true, "numeric": true, "override_special": "!#$%&*()-_=+[]{}<>:?", "result": "bZ+_$WGovC+h*VE<bv+I3x6*P%RF4lm$", "special": true, "upper": true}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "random_password", "name": "redis_auth", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 3, "attributes": {"bcrypt_hash": "$2a$10$HemL6Z5MIjjo8kpaVDCs6.VIvBhyBJ1G0lF5HXG0cV9gxHnw0yEX2", "id": "none", "keepers": null, "length": 32, "lower": true, "min_lower": 0, "min_numeric": 0, "min_special": 0, "min_upper": 0, "number": true, "numeric": true, "override_special": null, "result": "M7KndG10g9FMUShOBAMcFnxYAPHYlcvR", "special": false, "upper": true}, "sensitive_attributes": []}]}], "check_results": null}