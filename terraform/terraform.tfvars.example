# Example Terraform variables file
# Copy this to terraform.tfvars and fill in your actual values

# General Configuration
aws_region      = "us-east-1"
environment     = "prod"
project_name    = "bopmaps"

# Domain Configuration
domain_name       = "api.bopmaps.com"
tiles_domain_name = "tiles.bopmaps.com"

# Cloudflare Configuration
cloudflare_api_token  = "your-cloudflare-api-token"
cloudflare_zone_id    = "your-cloudflare-zone-id"
cloudflare_account_id = "your-cloudflare-account-id"

# Backblaze B2 Configuration
b2_application_key_id = "your-b2-key-id"
b2_application_key    = "your-b2-application-key"
b2_bucket_name_prefix = "bopmaps"
b2_region            = "us-west-004"

# Application Configuration
django_secret_key = "your-super-secret-django-key-here"
app_image_tag     = "latest"

# Database Configuration
db_password = "your-secure-database-password"

# Third-party API Configuration
# Spotify API (Required for music features)
spotify_client_id     = "your-spotify-client-id"
spotify_client_secret = "your-spotify-client-secret"
spotify_redirect_uri  = "http://localhost:8888/callback"
spotify_mobile_redirect_uri = "bopmaps://callback"
allowed_redirect_uris = "bopmaps://callback"

# Last.fm API (Optional for music scrobbling)
lastfm_api_key = "your-lastfm-api-key"

# Apple Music API (Optional)
apple_music_key_id     = ""
apple_music_team_id    = ""
apple_music_private_key = ""

# SoundCloud API (Optional)
soundcloud_client_id     = ""
soundcloud_client_secret = ""

# OneSignal Push Notifications (Optional)
onesignal_app_id  = ""
onesignal_api_key = ""
onesignal_api_url = "https://onesignal.com/api/v1/notifications"

# JWT Configuration
jwt_access_token_lifetime  = 1    # hours
jwt_refresh_token_lifetime = 7    # days

# CORS Configuration
cors_allowed_origins = "http://localhost:3000,https://bopmaps.com"
csrf_trusted_origins = "http://localhost:3000,https://bopmaps.com"

# Email Configuration (Optional - for production email sending)
email_backend      = "django.core.mail.backends.console.EmailBackend"
email_host         = ""
email_port         = 587
email_host_user    = ""
email_host_password = ""
email_use_tls      = true
default_from_email = "<EMAIL>"
server_email       = "<EMAIL>"

# Security Configuration
secure_ssl_redirect = true

# ECS Configuration (adjust based on your needs)
app_cpu           = 1024
app_memory        = 2048
app_count         = 2
app_min_capacity  = 2
app_max_capacity  = 20
app_target_cpu    = 70

celery_cpu    = 512
celery_memory = 1024
celery_count  = 2



# Database Configuration
db_instance_class        = "db.t4g.medium"
db_allocated_storage     = 100
db_max_allocated_storage = 1000

# Redis Configuration
redis_node_type        = "cache.t4g.micro"
redis_num_cache_nodes  = 1

# Cost Optimization
use_spot_instances         = false  # Set to true for staging environments
enable_deletion_protection = true   # Set to false for testing



# Monitoring
enable_detailed_monitoring = true 