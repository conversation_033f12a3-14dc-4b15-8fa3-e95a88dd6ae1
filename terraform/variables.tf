# General Variables
variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "prod"
}

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "bopmaps"
}

variable "domain_name" {
  description = "Domain name for the application"
  type        = string
  default     = "api.bopmaps.com"
}

variable "tiles_domain_name" {
  description = "Domain name for tile server"
  type        = string
  default     = "tiles.bopmaps.com"
}

# Cloudflare Variables
variable "cloudflare_api_token" {
  description = "Cloudflare API token"
  type        = string
  sensitive   = true
}

variable "cloudflare_zone_id" {
  description = "Cloudflare zone ID"
  type        = string
}

variable "cloudflare_account_id" {
  description = "Cloudflare account ID"
  type        = string
}

# Network Variables
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_cidrs" {
  description = "CIDR blocks for public subnets"
  type        = list(string)
  default     = ["********/24", "********/24"]
}

variable "private_subnet_cidrs" {
  description = "CIDR blocks for private subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24"]
}

variable "database_subnet_cidrs" {
  description = "CIDR blocks for database subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24"]
}

# Application Variables
variable "django_secret_key" {
  description = "Django secret key"
  type        = string
  sensitive   = true
}

variable "app_image_tag" {
  description = "Docker image tag for the application"
  type        = string
  default     = "latest"
}

variable "app_port" {
  description = "Port for the Django application"
  type        = number
  default     = 8000
}

# ECS Configuration
variable "app_cpu" {
  description = "CPU units for Django app (1024 = 1 vCPU)"
  type        = number
  default     = 1024
}

variable "app_memory" {
  description = "Memory in MB for Django app"
  type        = number
  default     = 2048
}

variable "app_count" {
  description = "Number of Django app instances"
  type        = number
  default     = 2
}

variable "celery_cpu" {
  description = "CPU units for Celery worker"
  type        = number
  default     = 512
}

variable "celery_memory" {
  description = "Memory in MB for Celery worker"
  type        = number
  default     = 1024
}

variable "celery_count" {
  description = "Number of Celery worker instances"
  type        = number
  default     = 2
}

# Note: Tileserver variables removed - using B2 upload instead

# Database Configuration
variable "db_instance_class" {
  description = "RDS instance class"
  type        = string
  default     = "db.t4g.medium"
}

variable "db_allocated_storage" {
  description = "RDS allocated storage in GB"
  type        = number
  default     = 100
}

variable "db_max_allocated_storage" {
  description = "RDS max allocated storage in GB"
  type        = number
  default     = 1000
}

variable "db_name" {
  description = "Database name"
  type        = string
  default     = "bopmaps"
}

variable "db_username" {
  description = "Database username"
  type        = string
  default     = "bopmaps_user"
}

variable "db_password" {
  description = "Database password"
  type        = string
  sensitive   = true
}

# Redis Configuration
variable "redis_node_type" {
  description = "ElastiCache Redis node type"
  type        = string
  default     = "cache.t4g.micro"
}

variable "redis_num_cache_nodes" {
  description = "Number of Redis cache nodes"
  type        = number
  default     = 1
}

# Auto Scaling Configuration
variable "app_min_capacity" {
  description = "Minimum number of app instances"
  type        = number
  default     = 2
}

variable "app_max_capacity" {
  description = "Maximum number of app instances"
  type        = number
  default     = 20
}

variable "app_target_cpu" {
  description = "Target CPU utilization for auto scaling"
  type        = number
  default     = 70
}

# Storage Configuration
# Note: EFS variables removed - using B2 upload instead

# Monitoring
variable "enable_detailed_monitoring" {
  description = "Enable detailed CloudWatch monitoring"
  type        = bool
  default     = true
}

# Cost Optimization
variable "use_spot_instances" {
  description = "Use spot instances for ECS tasks"
  type        = bool
  default     = false
}

variable "enable_deletion_protection" {
  description = "Enable deletion protection for RDS"
  type        = bool
  default     = true
}

# Backblaze B2 Variables
variable "b2_application_key_id" {
  description = "Backblaze B2 Application Key ID"
  type        = string
  sensitive   = true
}

variable "b2_application_key" {
  description = "Backblaze B2 Application Key"
  type        = string
  sensitive   = true
}

variable "b2_bucket_name_prefix" {
  description = "Prefix for Backblaze B2 bucket names"
  type        = string
  default     = "bopmaps"
}

variable "b2_region" {
  description = "Backblaze B2 region"
  type        = string
  default     = "us-west-004"
}

# Third-party API Variables
variable "spotify_client_id" {
  description = "Spotify API Client ID"
  type        = string
  sensitive   = true
  default     = ""
}

variable "spotify_client_secret" {
  description = "Spotify API Client Secret"
  type        = string
  sensitive   = true
  default     = ""
}

variable "spotify_redirect_uri" {
  description = "Spotify OAuth Redirect URI"
  type        = string
  default     = "http://localhost:8888/callback"
}

variable "spotify_mobile_redirect_uri" {
  description = "Spotify Mobile OAuth Redirect URI"
  type        = string
  default     = "bopmaps://callback"
}

variable "allowed_redirect_uris" {
  description = "Comma-separated list of allowed redirect URIs"
  type        = string
  default     = "bopmaps://callback"
}

variable "lastfm_api_key" {
  description = "Last.fm API Key"
  type        = string
  sensitive   = true
  default     = ""
}

variable "apple_music_key_id" {
  description = "Apple Music API Key ID"
  type        = string
  sensitive   = true
  default     = ""
}

variable "apple_music_team_id" {
  description = "Apple Music Team ID"
  type        = string
  sensitive   = true
  default     = ""
}

variable "apple_music_private_key" {
  description = "Apple Music Private Key"
  type        = string
  sensitive   = true
  default     = ""
}

variable "soundcloud_client_id" {
  description = "SoundCloud Client ID"
  type        = string
  sensitive   = true
  default     = ""
}

variable "soundcloud_client_secret" {
  description = "SoundCloud Client Secret"
  type        = string
  sensitive   = true
  default     = ""
}

variable "onesignal_app_id" {
  description = "OneSignal App ID"
  type        = string
  sensitive   = true
  default     = ""
}

variable "onesignal_api_key" {
  description = "OneSignal API Key"
  type        = string
  sensitive   = true
  default     = ""
}

variable "onesignal_api_url" {
  description = "OneSignal API URL"
  type        = string
  default     = "https://onesignal.com/api/v1/notifications"
}

# JWT Configuration
variable "jwt_access_token_lifetime" {
  description = "JWT Access Token Lifetime in days (set to 100 years to never expire)"
  type        = number
  default     = 36500
}

variable "jwt_refresh_token_lifetime" {
  description = "JWT Refresh Token Lifetime in days (set to 100 years to never expire)"
  type        = number
  default     = 36500
}

# CORS Configuration
variable "cors_allowed_origins" {
  description = "CORS Allowed Origins"
  type        = string
  default     = "http://localhost:3000"
}

variable "csrf_trusted_origins" {
  description = "CSRF Trusted Origins"
  type        = string
  default     = "http://localhost:3000"
}

# Email Configuration
variable "email_backend" {
  description = "Django Email Backend"
  type        = string
  default     = "django.core.mail.backends.console.EmailBackend"
}

variable "email_host" {
  description = "Email SMTP Host"
  type        = string
  default     = ""
}

variable "email_port" {
  description = "Email SMTP Port"
  type        = number
  default     = 587
}

variable "email_host_user" {
  description = "Email SMTP Username"
  type        = string
  sensitive   = true
  default     = ""
}

variable "email_host_password" {
  description = "Email SMTP Password"
  type        = string
  sensitive   = true
  default     = ""
}

variable "email_use_tls" {
  description = "Email Use TLS"
  type        = bool
  default     = true
}

variable "default_from_email" {
  description = "Default From Email Address"
  type        = string
  default     = "<EMAIL>"
}

variable "server_email" {
  description = "Server Email Address"
  type        = string
  default     = "<EMAIL>"
}

# SES Configuration
variable "ses_configuration_set_name" {
  description = "SES Configuration Set Name"
  type        = string
  default     = "bopmaps-emails"
}

variable "ses_from_email" {
  description = "Default SES From Email Address"
  type        = string
  default     = "<EMAIL>"
}

variable "ses_admin_email" {
  description = "Admin email to verify in SES"
  type        = string
  default     = "<EMAIL>"
}

variable "ses_domain" {
  description = "Domain to verify in SES"
  type        = string
  default     = "bopmaps.com"
}

# Security Configuration
variable "secure_ssl_redirect" {
  description = "Force SSL redirect in production"
  type        = bool
  default     = true
} 