#!/usr/bin/env python
"""
Test script to verify the gamification tracking system works correctly
"""

import os
import sys
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bopmaps.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.gis.geos import Point
from django.utils import timezone
from gamification.models import Achievement, UserAchievement
from gamification.services.progress_tracker import ProgressTracker
from gamification.services.xp_calculator import XPCalculator
from pins.models import Pin
from votes.models import Vote
from comments.models import Comment

User = get_user_model()

def test_tracking_system():
    """Test the complete tracking system"""
    print("🧪 TESTING GAMIFICATION TRACKING SYSTEM")
    print("=" * 50)
    
    # Get an existing user (first one in the database)
    user = User.objects.first()
    if not user:
        print("❌ No users found in database. Please create a user first.")
        return
    
    print(f"📋 Using existing user: {user.username}")
    
    # Test 1: Check initial state
    print("\n1️⃣ INITIAL STATE")
    print("-" * 20)
    initial_xp = XPCalculator.get_user_total_xp(user)
    level_info = XPCalculator.get_user_level_info(user)
    print(f"Initial XP: {initial_xp}")
    print(f"Current Level: {level_info['current_level']['name']} (Level {level_info['current_level']['level']})")
    
    # Test 2: Create a pin and check tracking
    print("\n2️⃣ PIN CREATION TRACKING")
    print("-" * 25)
    
    pin_data = {
        'latitude': 40.7128,
        'longitude': -74.0060,
        'city': 'New York',
        'state': 'New York',
        'country': 'United States',
        'continent': 'North America',
        'artist_name': 'Test Artist',
        'genre': 'Pop',
        'album_name': 'Test Album',
        'created_at': timezone.now(),
        'is_public': True
    }
    
    print(f"Simulating pin creation with data: {pin_data}")
    
    # Get achievements before
    achievements_before = UserAchievement.objects.filter(
        user=user,
        completed_at__isnull=False
    ).count()
    
    # Update progress
    ProgressTracker.update_pin_progress(user, pin_data)
    
    # Check achievements after
    achievements_after = UserAchievement.objects.filter(
        user=user,
        completed_at__isnull=False
    ).count()
    
    new_xp = XPCalculator.get_user_total_xp(user)
    new_level_info = XPCalculator.get_user_level_info(user)
    
    print(f"Achievements before: {achievements_before}")
    print(f"Achievements after: {achievements_after}")
    print(f"New achievements: {achievements_after - achievements_before}")
    print(f"XP gained: {new_xp - initial_xp}")
    print(f"New Level: {new_level_info['current_level']['name']} (Level {new_level_info['current_level']['level']})")
    
    # Test 3: Check specific achievement progress
    print("\n3️⃣ ACHIEVEMENT PROGRESS DETAILS")
    print("-" * 32)
    
    # Get a few user achievements to check progress
    user_achievements = UserAchievement.objects.filter(user=user).select_related('achievement')[:5]
    
    for ua in user_achievements:
        progress_info = ua.progress or {}
        status = "✅ COMPLETED" if ua.completed_at else "🔄 IN PROGRESS"
        print(f"{status} {ua.achievement.name}")
        print(f"   Progress: {progress_info}")
        print(f"   XP Reward: {ua.achievement.xp_reward}")
        print()
    
    # Test 4: Social tracking
    print("\n4️⃣ SOCIAL TRACKING TEST")
    print("-" * 23)
    
    social_data = {
        'vote_value': 1,
        'pin_id': 12345,
        'reactor_user_id': 999
    }
    
    print(f"Simulating social interaction: {social_data}")
    
    # Get achievements before social action
    social_achievements_before = UserAchievement.objects.filter(
        user=user,
        completed_at__isnull=False
    ).count()
    
    # Update social progress
    ProgressTracker.update_social_progress(user, 'reaction_received', social_data)
    
    # Check achievements after social action
    social_achievements_after = UserAchievement.objects.filter(
        user=user,
        completed_at__isnull=False
    ).count()
    
    final_xp = XPCalculator.get_user_total_xp(user)
    final_level_info = XPCalculator.get_user_level_info(user)
    
    print(f"Social achievements before: {social_achievements_before}")
    print(f"Social achievements after: {social_achievements_after}")
    print(f"New social achievements: {social_achievements_after - social_achievements_before}")
    print(f"Total XP gained: {final_xp - initial_xp}")
    print(f"Final Level: {final_level_info['current_level']['name']} (Level {final_level_info['current_level']['level']})")
    
    # Test 5: Check WebSocket notification system
    print("\n5️⃣ WEBSOCKET NOTIFICATION TEST")
    print("-" * 30)
    
    try:
        from gamification.consumers import AchievementConsumer
        from asgiref.sync import async_to_sync
        
        # Test WebSocket notification
        test_notification = {
            'type': 'achievement_completed',
            'achievement': {
                'name': 'Test Achievement',
                'xp_earned': 100
            }
        }
        
        print("Testing WebSocket notification...")
        # This would normally send to the user's WebSocket channel
        # but we'll just test the function exists
        print("✅ WebSocket notification system is available")
        
    except Exception as e:
        print(f"❌ WebSocket notification error: {e}")
    
    # Test 6: Achievement completion check
    print("\n6️⃣ ACHIEVEMENT COMPLETION CHECK")
    print("-" * 33)
    
    # Find achievements that are close to completion
    close_achievements = UserAchievement.objects.filter(
        user=user,
        completed_at__isnull=True
    ).select_related('achievement')
    
    print(f"Total in-progress achievements: {close_achievements.count()}")
    
    for ua in close_achievements[:3]:  # Show first 3
        progress = ua.progress or {}
        criteria = ua.achievement.criteria
        
        print(f"🎯 {ua.achievement.name}")
        print(f"   Criteria: {criteria}")
        print(f"   Progress: {progress}")
        
        # Check if close to completion
        if 'total_pins' in criteria and 'total_pins' in progress:
            required = criteria['total_pins']
            current = progress['total_pins']
            percentage = (current / required) * 100 if required > 0 else 0
            print(f"   Completion: {current}/{required} ({percentage:.1f}%)")
        
        print()
    
    # Test 7: Check signal system
    print("\n7️⃣ SIGNAL SYSTEM TEST")
    print("-" * 22)
    
    # Check if signals are properly connected
    from django.db.models.signals import post_save
    from gamification.signals import handle_achievement_completion
    
    # Check if our signal handlers are connected
    signal_handlers = [str(handler) for handler in post_save._live_receivers(sender=UserAchievement)]
    print(f"UserAchievement post_save handlers: {len(signal_handlers)}")
    
    for handler in signal_handlers:
        if 'handle_achievement_completion' in handler:
            print("✅ Achievement completion signal handler is connected")
            break
    else:
        print("❌ Achievement completion signal handler is NOT connected")
    
    print("\n🎉 TRACKING SYSTEM TEST COMPLETE")
    print("=" * 50)
    
    return {
        'user': user,
        'initial_xp': initial_xp,
        'final_xp': final_xp,
        'xp_gained': final_xp - initial_xp,
        'achievements_gained': achievements_after - achievements_before,
        'level_info': final_level_info
    }

if __name__ == '__main__':
    try:
        results = test_tracking_system()
        if results:
            print(f"\n📊 SUMMARY:")
            print(f"   User: {results['user'].username}")
            print(f"   XP Gained: {results['xp_gained']}")
            print(f"   Achievements Completed: {results['achievements_gained']}")
            print(f"   Final Level: {results['level_info']['current_level']['name']}")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc() 