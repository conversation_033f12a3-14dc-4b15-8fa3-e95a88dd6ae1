import os
import subprocess
import tempfile
import shutil
import boto3
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
import logging


logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Download OpenMapTiles planet data, process it, and upload to B2'

    def add_arguments(self, parser):
        parser.add_argument(
            '--area',
            type=str,
            default='planet',
            help='Area to process (default: planet, or specify country like "albania")'
        )
        parser.add_argument(
            '--skip-download',
            action='store_true',
            help='Skip downloading and use existing data'
        )
        parser.add_argument(
            '--skip-upload',
            action='store_true',
            help='Skip uploading to B2'
        )
        parser.add_argument(
            '--zoom-levels',
            type=str,
            default='0-14',
            help='Zoom levels to generate (default: 0-14)'
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Starting OpenMapTiles planet processing...')
        )
        
        area = options['area']
        skip_download = options['skip_download']
        skip_upload = options['skip_upload']
        zoom_levels = options['zoom_levels']
        
        # Create temporary working directory
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            openmaptiles_dir = temp_path / 'openmaptiles'
            
            try:
                if not skip_download:
                    self._clone_openmaptiles(openmaptiles_dir)
                    self._download_data(openmaptiles_dir, area)
                
                tiles_file = self._process_tiles(openmaptiles_dir, area, zoom_levels)
                
                if not skip_upload:
                    self._upload_to_b2(tiles_file, area)
                
                self.stdout.write(
                    self.style.SUCCESS(f'Successfully processed {area} tiles!')
                )
                
            except Exception as e:
                logger.error(f"Error processing tiles: {e}")
                raise CommandError(f'Error processing tiles: {e}')

    def _clone_openmaptiles(self, target_dir):
        """Clone the OpenMapTiles repository"""
        self.stdout.write('Cloning OpenMapTiles repository...')
        
        cmd = [
            'git', 'clone', 
            'https://github.com/openmaptiles/openmaptiles.git',
            str(target_dir)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise CommandError(f'Failed to clone repository: {result.stderr}')
        
        self.stdout.write(self.style.SUCCESS('Repository cloned successfully'))

    def _download_data(self, openmaptiles_dir, area):
        """Download OSM data for the specified area"""
        self.stdout.write(f'Downloading OSM data for {area}...')
        
        os.chdir(openmaptiles_dir)
        
        # Run the download command
        cmd = ['make', 'download', f'area={area}']
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise CommandError(f'Failed to download data: {result.stderr}')
        
        self.stdout.write(self.style.SUCCESS(f'Data for {area} downloaded successfully'))

    def _process_tiles(self, openmaptiles_dir, area, zoom_levels):
        """Process the downloaded data into vector tiles"""
        self.stdout.write(f'Processing tiles for {area} (zoom levels: {zoom_levels})...')
        
        os.chdir(openmaptiles_dir)
        
        # Set environment variables for processing
        env = os.environ.copy()
        env['ZOOM_LEVELS'] = zoom_levels
        
        # Run the full processing pipeline
        commands = [
            ['make', 'clean'],
            ['make'],
            ['make', 'start-db'],
            ['make', 'import-data'],
            ['make', 'import-osm'],
            ['make', 'import-wikidata'],
            ['make', 'import-sql'],
            ['make', 'generate-bbox-file'],
            ['make', 'generate-tiles-pg']
        ]
        
        for cmd in commands:
            self.stdout.write(f'Running: {" ".join(cmd)}')
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode != 0:
                self.stdout.write(self.style.WARNING(f'Command output: {result.stdout}'))
                raise CommandError(f'Failed at step {" ".join(cmd)}: {result.stderr}')
        
        # Find the generated mbtiles file
        mbtiles_pattern = f"{area}*.mbtiles"
        mbtiles_files = list(openmaptiles_dir.glob(mbtiles_pattern))
        
        if not mbtiles_files:
            # Try default filename
            mbtiles_files = list(openmaptiles_dir.glob("tiles.mbtiles"))
        
        if not mbtiles_files:
            raise CommandError(f'No mbtiles file found matching {mbtiles_pattern}')
        
        tiles_file = mbtiles_files[0]
        self.stdout.write(self.style.SUCCESS(f'Tiles generated: {tiles_file}'))
        
        return tiles_file

    def _upload_to_b2(self, tiles_file, area):
        """Upload the generated tiles to Backblaze B2"""
        self.stdout.write(f'Uploading {tiles_file} to B2...')
        
        # Configure B2 client with S3-compatible interface
        b2_client = boto3.client(
            's3',
            endpoint_url=settings.B2_ENDPOINT_URL,
            aws_access_key_id=settings.B2_APPLICATION_KEY_ID,
            aws_secret_access_key=settings.B2_APPLICATION_KEY,
            region_name=settings.B2_REGION,
        )
        
        # Upload the mbtiles file
        bucket_name = settings.B2_TILES_BUCKET_NAME
        key = f'planet/{area}.mbtiles'
        
        try:
            file_size = os.path.getsize(tiles_file)
            self.stdout.write(f'Uploading {file_size / (1024**3):.2f} GB file...')
            
            # Use multipart upload for large files
            with open(tiles_file, 'rb') as f:
                b2_client.upload_fileobj(
                    f, 
                    bucket_name, 
                    key,
                    ExtraArgs={
                        'ContentType': 'application/x-sqlite3',
                        'Metadata': {
                            'area': area,
                            'generated': str(os.path.getctime(tiles_file))
                        }
                    }
                )
            
            self.stdout.write(
                self.style.SUCCESS(f'Successfully uploaded to B2: {bucket_name}/{key}')
            )
            
            # Also extract and upload individual tiles for web serving
            self._extract_and_upload_tiles(tiles_file, area, b2_client, bucket_name)
            
        except Exception as e:
            raise CommandError(f'Failed to upload to B2: {e}')

    def _extract_and_upload_tiles(self, mbtiles_file, area, b2_client, bucket_name):
        """Extract individual tiles from mbtiles and upload to B2 for web serving"""
        self.stdout.write('Extracting individual tiles for web serving...')
        
        try:
            # Use mb-util or similar tool to extract tiles
            # For now, we'll upload the mbtiles file and handle extraction server-side
            # This can be enhanced with proper tile extraction
            
            # Upload styles and fonts if they exist
            styles_dir = Path(mbtiles_file).parent / 'style'
            if styles_dir.exists():
                self._upload_directory(b2_client, bucket_name, styles_dir, f'{area}/styles/')
            
            self.stdout.write(self.style.SUCCESS('Tile extraction completed'))
            
        except Exception as e:
            self.stdout.write(self.style.WARNING(f'Tile extraction failed: {e}'))

    def _upload_directory(self, client, bucket, local_dir, prefix):
        """Upload a directory to B2"""
        for file_path in local_dir.rglob('*'):
            if file_path.is_file():
                key = prefix + str(file_path.relative_to(local_dir))
                with open(file_path, 'rb') as f:
                    client.upload_fileobj(f, bucket, key)
                self.stdout.write(f'Uploaded: {key}') 