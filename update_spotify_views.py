#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update Spotify views to handle rate limit exceptions properly.
This script will update all service calls to use the handle_spotify_service_call helper
and add proper exception handling for SpotifyRateLimitExceeded.
"""

import re

def update_service_calls():
    """Update all service calls in spotify/views.py to use the helper function"""
    
    with open('spotify/views.py', 'r') as f:
        content = f.read()
    
    # Pattern to match service calls that need to be updated
    # This will match patterns like:
    # data = service.get_user_top_tracks(
    # data = service.get_user_top_artists(
    # data = service.get_track_details(
    # etc.
    
    # List of service methods that need to be wrapped
    service_methods = [
        'get_user_top_tracks',
        'get_user_top_artists', 
        'get_track_details',
        'get_artist_details',
        'get_multiple_artists',
        'get_playlist_tracks',
        'get_user_top_artists',
        'get_cache_stats',
        'cleanup_expired_cache'
    ]
    
    # Update each service method call
    for method in service_methods:
        # Pattern to match: data = service.method_name(
        pattern = rf'data = service\.{method}\('
        replacement = f'data = handle_spotify_service_call(\n                service.{method},'
        
        content = re.sub(pattern, replacement, content)
    
    # Add exception handling for SpotifyRateLimitExceeded
    # Find all try-except blocks that don't already handle SpotifyRateLimitExceeded
    # and add the exception handling
    
    # Pattern to match try-except blocks that need rate limit handling
    try_except_pattern = r'(try:\s*.*?service.*?except Exception as e:\s*.*?logger\.error.*?return Response.*?status=status\.HTTP_500_INTERNAL_SERVER_ERROR\s*\))'
    
    def add_rate_limit_handling(match):
        try_block = match.group(1)
        
        # Check if SpotifyRateLimitExceeded is already handled
        if 'SpotifyRateLimitExceeded' in try_block:
            return try_block
        
        # Add the rate limit exception handling before the general exception
        rate_limit_handling = '''        except SpotifyRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        '''
        
        # Insert the rate limit handling before the general exception
        updated_block = try_block.replace(
            'except Exception as e:',
            f'{rate_limit_handling}        except Exception as e:'
        )
        
        return updated_block
    
    content = re.sub(try_except_pattern, add_rate_limit_handling, content, flags=re.DOTALL)
    
    # Write the updated content back to the file
    with open('spotify/views.py', 'w') as f:
        f.write(content)
    
    print("Updated Spotify views with rate limit exception handling")

if __name__ == '__main__':
    update_service_calls() 