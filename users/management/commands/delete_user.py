#!/usr/bin/env python
"""
Django management command to delete a user by username

This command safely deletes a user and all their related data by using raw SQL
to bypass Django signals and handle foreign key constraints properly.

Usage:
    python manage.py delete_user --username <username>
    python manage.py delete_user --username <username> --confirm

Examples:
    python manage.py delete_user --username testuser
    python manage.py delete_user --username testuser --confirm
"""

import os
import django
from django.core.management.base import BaseCommand, CommandError
from django.db import connection
from users.models import User


class Command(BaseCommand):
    help = 'Delete a user by username and all their related data'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            required=True,
            help='Username of the user to delete'
        )
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm deletion without prompting'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting'
        )
    
    def handle(self, *args, **options):
        username = options['username']
        confirm = options['confirm']
        dry_run = options['dry_run']
        
        # Check if user exists
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            raise CommandError(f'User "{username}" does not exist')
        
        # Display user information
        self.stdout.write(f'\nUser found:')
        self.stdout.write(f'  Username: {user.username}')
        self.stdout.write(f'  Email: {user.email}')
        self.stdout.write(f'  ID: {user.id}')
        self.stdout.write(f'  Date joined: {user.date_joined}')
        self.stdout.write(f'  Is staff: {user.is_staff}')
        self.stdout.write(f'  Is superuser: {user.is_superuser}')
        
        # Check related data
        related_data_count = self.check_related_data(user.id)
        
        if dry_run:
            self.stdout.write(f'\n🔍 DRY RUN: Would delete user "{username}" and {related_data_count} related records')
            return
        
        # Confirm deletion
        if not confirm:
            response = input(f'\nAre you sure you want to delete user "{username}" and all related data? (yes/no): ')
            if response.lower() != 'yes':
                self.stdout.write('Deletion cancelled.')
                return
        
        # Delete the user using SQL
        success = self.delete_user_by_sql(username, user.id)
        
        if success:
            self.stdout.write(
                self.style.SUCCESS(f'✅ User "{username}" deleted successfully!')
            )
        else:
            raise CommandError(f'Failed to delete user "{username}"')
    
    def check_related_data(self, user_id):
        """Check and display related data that will be affected"""
        self.stdout.write(f'\nChecking related data...')
        
        total_count = 0
        
        with connection.cursor() as cursor:
            # Define tables to check
            tables_to_check = [
                ('rankings_rankinghistory', 'user_id', 'Ranking history'),
                ('rankings_userranking', 'user_id', 'User ranking'),
                ('gamification_userachievement', 'user_id', 'Achievements'),
                ('challenges_challengeparticipation', 'user_id', 'Challenge participations'),
                ('pins_pininteraction', 'user_id', 'Pin interactions'),
                ('pins_virtualpininteraction', 'user_id', 'Virtual pin interactions'),
                ('pins_collection', 'owner_id', 'Collections'),
                ('pins_pin', 'owner_id', 'Pins'),
                ('pins_virtualpin', 'owner_id', 'Virtual pins'),
                ('pins_userskin', 'user_id', 'User skins'),
                ('votes_vote', 'user_id', 'Votes'),
                ('friends_friend', 'requester_id', 'Friend requests (as requester)'),
                ('friends_friend', 'addressee_id', 'Friend requests (as addressee)'),
                ('seeding_userseedingstate', 'user_id', 'Seeding states'),
                ('token_blacklist_outstandingtoken', 'user_id', 'Auth tokens'),
                ('user_notification_settings', 'user_id', 'Notification settings'),
                ('notifications', 'recipient_id', 'Notifications'),
            ]
            
            for table, column, description in tables_to_check:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE {column} = %s", [user_id])
                    count = cursor.fetchone()[0]
                    if count > 0:
                        self.stdout.write(f'  - {description}: {count}')
                        total_count += count
                except Exception:
                    # Table might not exist or column name might be wrong
                    pass
            
            # Check collection pins (special case)
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM pins_collectionpin 
                    WHERE collection_id IN (
                        SELECT id FROM pins_collection WHERE owner_id = %s
                    )
                """, [user_id])
                count = cursor.fetchone()[0]
                if count > 0:
                    self.stdout.write(f'  - Collection pins: {count}')
                    total_count += count
            except Exception:
                pass
        
        self.stdout.write(f'\n⚠️  Total related records to be deleted: {total_count}')
        return total_count
    
    def delete_user_by_sql(self, username, user_id):
        """Delete user using raw SQL to bypass signals and constraints"""
        with connection.cursor() as cursor:
            self.stdout.write(f'\nDeleting user "{username}" (ID: {user_id}) and related data...')
            
            # Delete related data in the correct order to avoid foreign key constraints
            tables_to_clean = [
                # Rankings and history
                ('rankings_rankinghistory', 'user_id'),
                ('rankings_userranking', 'user_id'),

                # BOP Drops
                ('bop_drops_bopdroplike', 'user_id'),
                ('bop_drops_bopdropview', 'user_id'),
                ('bop_drops_bopdrop', 'user_id'),

                # Challenge votes and participations
                ('challenges_challengeentryvote', 'user_id'),
                ('challenges_challengeparticipation', 'user_id'),
                ('pins_challengeparticipation', 'user_id'),

                # Gamification
                ('gamification_userachievement', 'user_id'),

                # Comments
                ('comments_comment', 'user_id'),

                # Pin interactions (delete these first before pins)
                ('pins_pininteraction', 'user_id'),
                ('pins_virtualpininteraction', 'user_id'),
                ('pins_pininteraction_by_pin', None),  # Special handling
                ('pins_collectionpin', None),  # Special handling

                # Votes
                ('votes_vote', 'user_id'),

                # Music services and chat
                ('music_musicchatreaction', 'user_id'),
                ('music_musicchatmessage', 'sender_id'),
                ('music_musicchatconversation_participants', 'user_id'),
                ('music_recenttrack', 'user_id'),
                ('music_musicservice', 'user_id'),

                # Pins and collections
                ('pins_collection', 'owner_id'),
                ('pins_pin', 'owner_id'),
                ('pins_virtualpin', 'owner_id'),
                ('pins_userskin', 'user_id'),

                # Friends and groups
                ('friends_friend', 'requester_id'),
                ('friends_friend_recipient', None),  # Special handling
                ('friends_friendgroup', 'owner_id'),

                # Geo and location
                ('geo_userlocation', 'user_id'),
                ('geo_usermapsettings', 'user_id'),

                # Moderation
                ('moderation_userblock', 'blocked_id'),
                ('moderation_userblock', 'blocker_id'),
                ('moderation_report', 'reporter_id'),
                ('moderation_report', 'reported_user_id'),
                ('moderation_report', 'assigned_moderator_id'),
                ('moderation_moderationaction', 'target_user_id'),
                ('moderation_moderationaction', 'moderator_id'),
                ('moderation_appealrequest', 'user_id'),
                ('moderation_appealrequest', 'reviewed_by_id'),

                # Seeding
                ('seeding_curatoraccount', 'user_id'),
                ('seeding_userseedingstate', 'user_id'),

                # Verification
                ('verification_verificationattempt', 'user_id'),
                ('verification_verificationrequest', 'user_id'),
                ('verification_verificationrequest', 'reviewed_by_id'),
                ('verification_approveddomain', 'added_by_id'),

                # Token blacklist
                ('token_blacklist_blacklistedtoken', None),  # Special handling
                ('token_blacklist_outstandingtoken', 'user_id'),

                # User settings and notifications
                ('user_notification_settings', 'user_id'),
                ('notification_batches', 'recipient_id'),
                ('notifications', 'recipient_id'),
                ('onesignal_player_ids', 'user_id'),

                # Django admin and permissions
                ('django_admin_log', 'user_id'),
                ('users_user_groups', 'user_id'),
                ('users_user_user_permissions', 'user_id'),
            ]
            
            # Delete from related tables
            total_deleted = 0
            for table, column in tables_to_clean:
                try:
                    if table == 'pins_collectionpin':
                        # Delete collection pins for user's collections
                        cursor.execute("""
                            DELETE FROM pins_collectionpin 
                            WHERE collection_id IN (
                                SELECT id FROM pins_collection WHERE owner_id = %s
                            )
                        """, [user_id])
                    elif table == 'pins_pininteraction_by_pin':
                        # Delete interactions on pins owned by this user
                        cursor.execute("""
                            DELETE FROM pins_pininteraction 
                            WHERE pin_id IN (
                                SELECT id FROM pins_pin WHERE owner_id = %s
                            )
                        """, [user_id])
                    elif table == 'friends_friend_recipient':
                        # Delete friend relationships where user is the recipient
                        cursor.execute("""
                            DELETE FROM friends_friend WHERE recipient_id = %s
                        """, [user_id])
                    elif table == 'token_blacklist_blacklistedtoken':
                        # Delete blacklisted tokens for this user
                        cursor.execute("""
                            DELETE FROM token_blacklist_blacklistedtoken
                            WHERE token_id IN (
                                SELECT id FROM token_blacklist_outstandingtoken WHERE user_id = %s
                            )
                        """, [user_id])
                    else:
                        cursor.execute(f"DELETE FROM {table} WHERE {column} = %s", [user_id])
                    
                    deleted_count = cursor.rowcount
                    if deleted_count > 0:
                        self.stdout.write(f'  - Deleted {deleted_count} records from {table}')
                        total_deleted += deleted_count
                        
                except Exception as e:
                    # Some tables might not exist or have different column names
                    # This is expected and we continue
                    pass
            
            # Finally, delete the user
            try:
                cursor.execute("DELETE FROM users_user WHERE id = %s", [user_id])
                if cursor.rowcount > 0:
                    self.stdout.write(f'  - Deleted user record')
                    total_deleted += 1
                    self.stdout.write(f'\nTotal records deleted: {total_deleted}')
                    return True
                else:
                    self.stdout.write(f'❌ Failed to delete user record')
                    return False
            except Exception as e:
                self.stdout.write(f'❌ Error deleting user: {e}')
                return False
