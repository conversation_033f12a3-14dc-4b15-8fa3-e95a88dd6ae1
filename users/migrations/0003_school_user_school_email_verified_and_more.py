# Generated by Django 4.2.7 on 2025-06-14 02:33

import django.contrib.gis.db.models.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0002_user_current_pin_skin"),
    ]

    operations = [
        migrations.CreateModel(
            name="School",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("address", models.TextField()),
                (
                    "location",
                    django.contrib.gis.db.models.fields.PointField(
                        geography=True, srid=4326
                    ),
                ),
                ("website", models.URLField(blank=True, null=True)),
                (
                    "email_domain",
                    models.CharField(
                        help_text="Email domain for school verification (e.g., 'harvard.edu')",
                        max_length=255,
                    ),
                ),
                ("verified", models.<PERSON>olean<PERSON>ield(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AddField(
            model_name="user",
            name="school",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="students",
                to="users.school",
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="school_email_verified",
            field=models.BooleanField(default=False),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["school"], name="users_user_school__bbce21_idx"),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(
                fields=["school_email_verified"], name="users_user_school__160961_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="school",
            index=models.Index(
                fields=["email_domain"], name="users_schoo_email_d_bbe8a2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="school",
            index=models.Index(
                fields=["verified"], name="users_schoo_verifie_6fbbf1_idx"
            ),
        ),
    ]
