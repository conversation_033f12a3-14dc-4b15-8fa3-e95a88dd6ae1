# Generated by Django 4.2.7 on 2025-07-01 17:45

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0008_alter_school_location"),
    ]

    operations = [
        migrations.CreateModel(
            name="EmailVerificationCode",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(max_length=254)),
                ("code", models.CharField(max_length=6)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("is_used", models.<PERSON>olean<PERSON>ield(default=False)),
                ("attempts", models.IntegerField(default=0)),
            ],
            options={
                "db_table": "email_verification_codes",
                "indexes": [
                    models.Index(
                        fields=["email", "is_used"], name="email_verif_email_287deb_idx"
                    ),
                    models.Index(
                        fields=["expires_at"], name="email_verif_expires_42d424_idx"
                    ),
                ],
            },
        ),
    ]
