# Generated by Django 4.2.7 on 2025-07-07 19:40

import bopmaps.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0009_emailverificationcode"),
    ]

    operations = [
        migrations.AlterField(
            model_name="user",
            name="profile_pic",
            field=models.ImageField(
                blank=True,
                help_text="Profile picture (max 5MB, 100x100 to 2000x2000 pixels, JPEG/PNG/WebP only)",
                null=True,
                upload_to="profile_pics/",
                validators=[
                    bopmaps.validators.FileSizeValidator(max_size_mb=5),
                    bopmaps.validators.SecureImageTypeValidator(),
                    bopmaps.validators.ImageContentValidator(),
                    bopmaps.validators.ImageDimensionsValidator(
                        max_height=2000, max_width=2000, min_height=100, min_width=100
                    ),
                ],
            ),
        ),
    ]
