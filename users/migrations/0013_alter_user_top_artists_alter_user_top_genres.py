# Generated by Django 4.2.7 on 2025-07-11 17:29

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0012_user_top_artists_user_top_genres"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="user",
            name="top_artists",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=100),
                blank=True,
                help_text="User's top artists",
                null=True,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="top_genres",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=100),
                blank=True,
                help_text="User's top music genres",
                null=True,
                size=None,
            ),
        ),
    ]
