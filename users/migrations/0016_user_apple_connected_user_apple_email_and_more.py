# Generated by Django 4.2.7 on 2025-07-27 02:09

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0015_auto_20250716_2232"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="apple_connected",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="user",
            name="apple_email",
            field=models.EmailField(
                blank=True,
                help_text="Apple private relay email",
                max_length=254,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="apple_user_id",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True, unique=True),
        ),
        migrations.AddField(
            model_name="user",
            name="google_connected",
            field=models.BooleanField(default=False),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(
                fields=["google_connected"], name="users_user_google__aad1cc_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(
                fields=["apple_connected"], name="users_user_apple_c_1af9db_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(
                fields=["apple_user_id"], name="users_user_apple_u_b24bfc_idx"
            ),
        ),
    ]
