# Generated by Django 4.2.7 on 2025-07-27 12:56

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0017_add_artist_image_urls"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="user",
            name="artist_image_urls",
        ),
        migrations.AddField(
            model_name="user",
            name="artist_spotify_ids",
            field=models.JSONField(
                blank=True,
                help_text="Mapping of artist names to their Spotify IDs (e.g., {'artist_name': 'spotify_id'})",
                null=True,
            ),
        ),
    ]
