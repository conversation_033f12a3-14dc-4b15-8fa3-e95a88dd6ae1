from django.contrib.auth.models import AbstractUser
from django.db import models
from django.contrib.gis.db import models as gis_models
from django.utils import timezone
from django.core.validators import RegexValidator
from django.conf import settings
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from bopmaps.models import TimeStampedModel, ValidationModelMixin
from bopmaps.validators import username_validator, ImageDimensionsValidator, FileSizeValidator, SecureImageTypeValidator, ImageContentValidator
import logging
import random
import string

# Forward declaration for PinSkin, actual import later to avoid circular dependency if any
# from gamification.models import PinSkin # This line might cause issues if gamification.models imports User

logger = logging.getLogger('bopmaps')

class EmailVerificationCode(models.Model):
    """
    Model for storing email verification codes
    """
    email = models.EmailField()
    code = models.CharField(max_length=6)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)
    attempts = models.IntegerField(default=0)
    
    class Meta:
        db_table = 'email_verification_codes'
        indexes = [
            models.Index(fields=['email', 'is_used']),
            models.Index(fields=['expires_at']),
        ]
        
    def save(self, *args, **kwargs):
        if not self.pk:  # Only set expiry on creation
            self.expires_at = timezone.now() + timezone.timedelta(minutes=10)  # 10 minute expiry
        super().save(*args, **kwargs)
    
    @property
    def is_expired(self):
        return timezone.now() > self.expires_at
    
    @property
    def is_valid(self):
        return not self.is_used and not self.is_expired and self.attempts < 5
    
    @classmethod
    def generate_code(cls):
        return ''.join(random.choices(string.digits, k=6))
    
    @classmethod
    def create_for_email(cls, email):
        # Deactivate existing codes for this email
        cls.objects.filter(email=email, is_used=False).update(is_used=True)
        
        # Create new code
        code = cls.generate_code()
        return cls.objects.create(email=email, code=code)

class School(models.Model):
    """
    Model representing educational institutions
    """
    name = models.CharField(max_length=255)
    address = models.TextField()
    location = gis_models.PointField(geography=True, blank=True, null=True)
    website = models.URLField(blank=True, null=True)
    email_domain = models.CharField(
        max_length=255,
        help_text="Email domain for school verification (e.g., 'harvard.edu')"
    )
    verified = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['email_domain']),
            models.Index(fields=['verified']),
        ]
    
    def __str__(self):
        return self.name

class User(AbstractUser, ValidationModelMixin):
    """
    Custom User model that extends Django's AbstractUser
    with additional fields for BOPMaps functionality.
    """
    # Use our custom username validator
    username = models.CharField(
        max_length=150,
        unique=True,
        validators=[
            RegexValidator(
                regex=r'^[\w.@+-]+$',
                message='Enter a valid username. This value may contain only letters, numbers, and @/./+/-/_ characters.'
            )
        ],
        error_messages={
            'unique': 'A user with that username already exists.'
        }
    )
    
    # Email is unique and case-insensitive
    email = models.EmailField(
        unique=True,
        error_messages={
            'unique': 'A user with that email already exists.'
        }
    )
    
    # Profile picture URL field
    profile_pic = models.TextField(
        blank=True, 
        null=True,
        help_text="Profile picture URL"
    )
    
    bio = models.TextField(blank=True, null=True)
    location = gis_models.PointField(geography=True, blank=True, null=True)
    last_active = models.DateTimeField(auto_now=True)
    last_location_update = models.DateTimeField(null=True, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    
    # School affiliation
    school = models.ForeignKey(
        School,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='students'
    )
    school_email_verified = models.BooleanField(default=False)
    
    # Music service connections
    spotify_connected = models.BooleanField(default=False)
    apple_music_connected = models.BooleanField(default=False)
    soundcloud_connected = models.BooleanField(default=False)

    # OAuth connections
    google_connected = models.BooleanField(default=False)
    apple_connected = models.BooleanField(default=False)

    # Apple Sign-In specific fields
    apple_user_id = models.CharField(max_length=255, unique=True, null=True, blank=True)
    apple_email = models.EmailField(null=True, blank=True, help_text="Apple private relay email")
    
    # User preferences
    notification_enabled = models.BooleanField(default=True)
    location_tracking_enabled = models.BooleanField(default=True)
    email_notifications_enabled = models.BooleanField(default=True)
    
    # Device information
    fcm_token = models.CharField(max_length=512, blank=True, null=True)
    device_os = models.CharField(max_length=100, blank=True, null=True)
    app_version = models.CharField(max_length=20, blank=True, null=True)
    
    # Account status
    is_banned = models.BooleanField(default=False)
    ban_reason = models.TextField(null=True, blank=True)
    banned_until = models.DateTimeField(null=True, blank=True)
    
    # Music preferences
    top_genres = ArrayField(
        models.CharField(max_length=100),
        null=True,
        blank=True,
        help_text="User's top music genres"
    )
    top_artists = ArrayField(
        models.CharField(max_length=100),
        null=True,
        blank=True,
        help_text="User's top artists"
    )
    
    # Add artist_genres field to the User model
    artist_genres = models.JSONField(
        null=True,
        blank=True,
        help_text="Mapping of artist names to their genres (e.g., {'artist_name': ['genre1', 'genre2']})"
    )

    # Add artist image URLs field
    artist_image_urls = models.JSONField(
        null=True,
        blank=True,
        help_text="Mapping of artist names to their image URLs (e.g., {'artist_name': 'https://image.url'})"
    )

    # Add artist Spotify IDs field
    artist_spotify_ids = models.JSONField(
        null=True,
        blank=True,
        help_text="Mapping of artist names to their Spotify IDs (e.g., {'artist_name': 'spotify_id'})"
    )
    
    # Stats
    pins_created = models.PositiveIntegerField(default=0)
    pins_collected = models.PositiveIntegerField(default=0)
    # XP and Level System
    total_xp = models.PositiveIntegerField(default=0)
    current_level = models.PositiveIntegerField(default=1)
    current_pin_skin = models.ForeignKey(
        'pins.PinSkin', 
        null=True, 
        blank=True, 
        on_delete=models.SET_NULL, 
        related_name='users_equipped'
    )
    
    class Meta:
        verbose_name = 'User'
        verbose_name_plural = 'Users'
        indexes = [
            models.Index(fields=['username']),
            models.Index(fields=['email']),
            models.Index(fields=['spotify_connected']),
            models.Index(fields=['apple_music_connected']),
            models.Index(fields=['soundcloud_connected']),
            models.Index(fields=['google_connected']),
            models.Index(fields=['apple_connected']),
            models.Index(fields=['apple_user_id']),
            models.Index(fields=['is_banned']),
            models.Index(fields=['school']),
            models.Index(fields=['school_email_verified']),
        ]
    
    def __str__(self):
        return self.username
    
    def save(self, *args, **kwargs):
        """Override save to normalize and validate email"""
        # Convert email to lowercase
        if self.email:
            self.email = self.email.lower()
        
        # Flag to track if this is a new user
        is_new_user = self.pk is None
        
        super().save(*args, **kwargs)
        
        # Assign default pin skin for new users
        if is_new_user:
            self.assign_default_pin_skin()
    
    def assign_default_pin_skin(self):
        """Assign the default pin skin to the user and create default collections"""
        from pins.models import PinSkin, UserSkin, Collection
        
        try:
            # Get or create the default pin skin
            default_skin, _ = PinSkin.objects.get_or_create(
                slug='default-pin',
                defaults={
                    'name': 'Default',
                    'description': 'The standard pin for all BOPMaps users',
                    'image': 'https://bopmaps-prod-media-67ba6151.s3.us-east-005.backblazeb2.com/pin_skins/default.png',
                    'skin_type': PinSkin.HOUSE,
                    'is_premium': False,
                    'metadata': {
                        'unlock_type': 'DEFAULT',
                        'category': 'base',
                        'theme_color': '#4A90E2',
                        'color_theme': 'blue',
                        'rarity': 'common'
                    }
                }
            )
            
            # Create UserSkin entry if not already exists
            UserSkin.objects.get_or_create(
                user=self,
                skin=default_skin
            )
            
            # Set as current pin skin if not already set
            if not self.current_pin_skin:
                self.current_pin_skin = default_skin
                self.save(update_fields=['current_pin_skin'])
            
            # Create default "Favourites" collection
            Collection.objects.get_or_create(
                owner=self,
                name="Favourites ⭐",
                defaults={
                    'description': 'Your favourite pins and tracks',
                    'is_public': False,
                    'primary_color': '#FFD700'
                }
            )
        
        except Exception as e:
            logger.error(f"Error assigning default pin skin to user {self.username}: {str(e)}")
        
    def update_last_active(self):
        """Update last active timestamp"""
        self.last_active = timezone.now()
        self.save(update_fields=['last_active'])
        
    def update_location(self, point):
        """Update user location"""
        self.location = point
        self.last_location_update = timezone.now()
        self.save(update_fields=['location', 'last_location_update'])
        
    def get_profile_pic_url(self):
        """Get the URL for the user's profile picture"""
        if self.profile_pic:
            return self.profile_pic
        return f"{settings.STATIC_URL}default_profile.png"
    
    def is_connected_to_music_service(self):
        """Check if user is connected to any music service"""
        return self.spotify_connected or self.apple_music_connected or self.soundcloud_connected
    
    def increment_pins_created(self):
        """Increment pins created count"""
        self.pins_created += 1
        self.save(update_fields=['pins_created'])
    
    def increment_pins_collected(self):
        """Increment pins collected count"""
        self.pins_collected += 1
        self.save(update_fields=['pins_collected'])
        
    def check_ban_status(self):
        """
        Check if the user is currently banned.
        If the ban has expired, unban the user.
        """
        if not self.is_banned:
            return False
            
        if self.banned_until and self.banned_until < timezone.now():
            self.is_banned = False
            self.banned_until = None
            self.save(update_fields=['is_banned', 'banned_until'])
            logger.info(f"User {self.username} ban has expired and been lifted.")
            return False
            
        return True
        
    def ban_user(self, reason, days=None):
        """
        Ban a user for the specified reason and duration.
        
        Args:
            reason: The reason for the ban
            days: Number of days the ban should last (None for permanent)
        """
        self.is_banned = True
        self.ban_reason = reason
        
        if days:
            self.banned_until = timezone.now() + timezone.timedelta(days=days)
        else:
            self.banned_until = None  # Permanent ban
            
        self.save(update_fields=['is_banned', 'ban_reason', 'banned_until'])
        logger.info(f"User {self.username} has been banned. Reason: {reason}, Duration: {days if days else 'permanent'} days")
        
    def unban_user(self):
        """Unban a user"""
        if self.is_banned:
            self.is_banned = False
            self.banned_until = None
            self.save(update_fields=['is_banned', 'banned_until'])
            logger.info(f"User {self.username} has been unbanned.")
            
    @property
    def full_name(self):
        """Get the user's full name or username if not available"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        return self.username
        
    @property
    def age(self):
        """Calculate user's age from date of birth"""
        if not self.date_of_birth:
            return None
            
        today = timezone.now().date()
        return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))
    
    def is_blocked_by(self, user):
        """Check if this user is blocked by another user"""
        from moderation.models import UserBlock
        return UserBlock.objects.filter(blocker=user, blocked=self).exists()
    
    def has_blocked(self, user):
        """Check if this user has blocked another user"""
        from moderation.models import UserBlock
        return UserBlock.objects.filter(blocker=self, blocked=user).exists()
    
    def can_interact_with(self, user):
        """Check if this user can interact with another user (not blocked by either)"""
        from moderation.models import UserBlock
        return not UserBlock.objects.filter(
            models.Q(blocker=self, blocked=user) |
            models.Q(blocker=user, blocked=self)
        ).exists()
