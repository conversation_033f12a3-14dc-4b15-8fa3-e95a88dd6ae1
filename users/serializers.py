from rest_framework import serializers
from django.contrib.auth import get_user_model
from rest_framework_gis.serializers import GeoFeatureModelSerializer
from django.core.exceptions import ValidationError
from django.contrib.auth.password_validation import validate_password
from bopmaps.serializers import BaseSerializer, BaseReadOnlySerializer
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
import logging

logger = logging.getLogger('bopmaps')
User = get_user_model()

class UserSerializer(BaseSerializer):
    """
    Serializer for the User model
    """
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'profile_pic', 'bio',
            'location', 'last_active', 'spotify_connected',
            'apple_music_connected', 'soundcloud_connected',
            'top_genres', 'top_artists', 'artist_genres', 'artist_image_urls', 'artist_spotify_ids'
        ]
        read_only_fields = ['id', 'last_active']
        
    def validate_username(self, value):
        """
        Validate that the username is unique (case insensitive).
        """
        if User.objects.filter(username__iexact=value).exclude(id=getattr(self.instance, 'id', None)).exists():
            raise ValidationError("A user with this username already exists.")
        return value


class UserGeoSerializer(GeoFeatureModelSerializer):
    """
    GeoJSON serializer for User model
    """
    class Meta:
        model = User
        geo_field = 'location'
        fields = ['id', 'username', 'profile_pic', 'last_active']


class UserMiniSerializer(serializers.ModelSerializer):
    """
    Minimal serializer for User model, used for nested relationships
    """
    class Meta:
        model = User
        fields = ['id', 'username', 'profile_pic']


class UserRegistrationSerializer(BaseSerializer):
    """
    Serializer for registering new users
    """
    password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})
    password_confirm = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})

    class Meta:
        model = User
        fields = [
            'username', 'email', 'password', 'password_confirm', 
            'profile_pic', 'bio'
        ]
    
    def validate_email(self, value):
        """
        Validate that the email is unique (case insensitive).
        """
        if User.objects.filter(email__iexact=value).exists():
            raise ValidationError("A user with this email already exists.")
        return value.lower()  # Normalize to lowercase
    
    def validate_password(self, value):
        """
        Validate password using Django's password validators.
        """
        try:
            validate_password(value)
        except ValidationError as e:
            logger.warning(f"Password validation failed: {e}")
            raise serializers.ValidationError(e.messages)
        return value
    
    def validate(self, data):
        if data['password'] != data['password_confirm']:
            raise serializers.ValidationError({"password": "Password fields don't match."})
        return data
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        
        try:
            user = User(**validated_data)
            user.set_password(password)
            user.save()
            logger.info(f"User created: {user.username}")
            return user
        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")
            raise


class UserUpdateSerializer(BaseSerializer):
    """
    Serializer for updating user profile
    """
    current_password = serializers.CharField(
        write_only=True, required=False, style={'input_type': 'password'}
    )
    new_password = serializers.CharField(
        write_only=True, required=False, style={'input_type': 'password'}
    )
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'profile_pic', 'bio', 'location',
            'current_password', 'new_password'
        ]
        read_only_fields = ['email']  # Email can't be updated
        
    def validate(self, data):
        # If updating password, current_password is required
        if 'new_password' in data and not data.get('current_password'):
            raise serializers.ValidationError(
                {"current_password": "Current password is required to set a new password."}
            )
            
        # If both password fields are provided, validate current password
        if 'current_password' in data and 'new_password' in data:
            if not self.instance.check_password(data['current_password']):
                raise serializers.ValidationError(
                    {"current_password": "Current password is not correct."}
                )
                
            # Validate new password with Django's built-in validators
            try:
                validate_password(data['new_password'], self.instance)
            except ValidationError as e:
                raise serializers.ValidationError({"new_password": e.messages})
                
        return data
    
    def update(self, instance, validated_data):
        # Handle password update separately
        current_password = validated_data.pop('current_password', None)
        new_password = validated_data.pop('new_password', None)
        
        # Handle profile picture cleanup
        if 'profile_pic' in validated_data:
            old_profile_pic = instance.profile_pic
            new_profile_pic = validated_data['profile_pic']
            
            # If there's a new profile picture and an existing one, clean up the old one
            if new_profile_pic and old_profile_pic:
                try:
                    # Store the old file path for deletion
                    old_file_path = old_profile_pic.path if hasattr(old_profile_pic, 'path') else None
                    old_file_name = old_profile_pic.name
                    
                    logger.info(f"Replacing profile picture for user {instance.username}: {old_file_name} -> {new_profile_pic.name}")
                except Exception as e:
                    logger.warning(f"Could not get old profile picture info for user {instance.username}: {str(e)}")
        
        # Update the instance with the validated data
        instance = super().update(instance, validated_data)
        
        # Clean up old profile picture after successful update
        if 'profile_pic' in validated_data and old_profile_pic and validated_data['profile_pic']:
            try:
                # Delete the old file from storage
                old_profile_pic.delete(save=False)
                logger.info(f"Successfully deleted old profile picture for user {instance.username}")
            except Exception as e:
                logger.error(f"Failed to delete old profile picture for user {instance.username}: {str(e)}")
                # Don't raise exception here - the update was successful, just cleanup failed
        
        # Update password if provided
        if current_password and new_password:
            instance.set_password(new_password)
            instance.save()
            logger.info(f"Password updated for user: {instance.username}")
            
        return instance


class UserPublicProfileSerializer(BaseReadOnlySerializer):
    """
    Serializer for viewing other users' public profile information
    """
    pin_count = serializers.SerializerMethodField()
    public_collection_count = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'profile_pic', 'bio', 
            'last_active', 'pin_count', 'public_collection_count'
        ]
        read_only_fields = ['id', 'username', 'profile_pic', 'bio', 'last_active']
    
    def get_pin_count(self, obj):
        """Get count of user's public pins"""
        from pins.models import Pin
        from django.db import models
        from django.utils import timezone
        
        return Pin.objects.filter(
            owner=obj,
            is_private=False
        ).filter(
            # Filter out expired pins
            models.Q(expiration_date__isnull=True) | 
            models.Q(expiration_date__gt=timezone.now())
        ).count()
    
    def get_public_collection_count(self, obj):
        """Get count of user's public collections"""
        from pins.models import Collection
        
        return Collection.objects.filter(
            owner=obj,
            is_public=True
        ).count()


class PasswordResetRequestSerializer(serializers.Serializer):
    """
    Serializer for password reset request
    """
    email = serializers.EmailField()
    
    def validate_email(self, value):
        # Convert to lowercase for normalization
        return value.lower()


class PasswordResetConfirmSerializer(serializers.Serializer):
    """
    Serializer for password reset confirmation
    """
    token = serializers.CharField()
    password = serializers.CharField(style={'input_type': 'password'})
    
    def validate_password(self, value):
        try:
            validate_password(value)
        except ValidationError as e:
            raise serializers.ValidationError(e.messages)
        return value


class CheckEmailSerializer(serializers.Serializer):
    """
    Serializer for checking if email exists
    """
    email = serializers.EmailField()
    
    def validate_email(self, value):
        return value.lower()


class LoginSerializer(serializers.Serializer):
    """
    Serializer for email/password authentication
    """
    email = serializers.EmailField()
    password = serializers.CharField(style={'input_type': 'password'})
    
    def validate_email(self, value):
        return value.lower()
    
    def validate(self, data):
        email = data.get('email')
        password = data.get('password')
        
        if email and password:
            # Try to find user by email
            try:
                user = User.objects.get(email=email)
                # Authenticate using username (since Django's authenticate expects username)
                user = authenticate(username=user.username, password=password)
                if not user:
                    raise serializers.ValidationError({
                        'error': 'Invalid credentials',
                        'message': 'The password you entered is incorrect'
                    })
                if not user.is_active:
                    raise serializers.ValidationError({
                        'error': 'Account disabled',
                        'message': 'This account has been disabled'
                    })
                data['user'] = user
            except User.DoesNotExist:
                raise serializers.ValidationError({
                    'error': 'Invalid credentials',
                    'message': 'No account found with this email address'
                })
        else:
            raise serializers.ValidationError({
                'error': 'Missing credentials',
                'message': 'Email and password are required'
            })
        
        return data


class RegisterSerializer(serializers.Serializer):
    """
    Serializer for user registration
    """
    username = serializers.CharField(
        max_length=30,
        min_length=2,
        help_text="2-30 characters, letters, numbers, underscore, and hyphen only"
    )
    email = serializers.EmailField()
    password = serializers.CharField(
        style={'input_type': 'password'},
        min_length=8,
        help_text="Minimum 8 characters with uppercase, lowercase, and number"
    )
    
    def validate_username(self, value):
        # Check character requirements
        import re
        if not re.match(r'^[a-zA-Z0-9_-]+$', value):
            raise serializers.ValidationError(
                "Username can only contain letters, numbers, underscores, and hyphens"
            )
        
        # Check uniqueness (case insensitive)
        if User.objects.filter(username__iexact=value).exists():
            raise serializers.ValidationError("This username is already taken")
        
        return value
    
    def validate_email(self, value):
        email = value.lower()
        # Check uniqueness
        if User.objects.filter(email=email).exists():
            raise serializers.ValidationError("An account with this email already exists")
        
        return email
    
    def validate_password(self, value):
        # Custom password validation
        if len(value) < 8:
            raise serializers.ValidationError("Password must be at least 8 characters long")
        
        if not any(c.isupper() for c in value):
            raise serializers.ValidationError("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in value):
            raise serializers.ValidationError("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in value):
            raise serializers.ValidationError("Password must contain at least one number")
        
        # Also use Django's built-in validation
        try:
            validate_password(value)
        except ValidationError as e:
            raise serializers.ValidationError(e.messages)
        
        return value
    
    def create(self, validated_data):
        try:
            user = User.objects.create_user(
                username=validated_data['username'],
                email=validated_data['email'],
                password=validated_data['password']
            )
            logger.info(f"New user registered: {user.username}")
            return user
        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")
            raise serializers.ValidationError("Failed to create user account")


class UserAuthSerializer(serializers.ModelSerializer):
    """
    Serializer for user data in authentication responses
    """
    profile_pic = serializers.SerializerMethodField()
    is_verified = serializers.SerializerMethodField()
    favorite_genres = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(source='date_joined', read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'profile_pic', 'bio',
            'is_verified', 'favorite_genres', 'created_at',
            'top_genres', 'top_artists', 'artist_genres', 'artist_image_urls', 'artist_spotify_ids',
            'google_connected', 'apple_connected'
        ]
        read_only_fields = ['id', 'created_at', 'google_connected', 'apple_connected']
    
    def get_profile_pic(self, obj):
        if obj.profile_pic:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.profile_pic)
            return obj.profile_pic
        return None
    
    def get_is_verified(self, obj):
        # Use school_email_verified as verification status
        return getattr(obj, 'school_email_verified', False)
    
    def get_favorite_genres(self, obj):
        # Return empty list for now - this could be implemented later
        # when a favorite genres feature is added to the User model
        return []


class SendVerificationCodeSerializer(serializers.Serializer):
    """
    Serializer for sending email verification codes
    """
    email = serializers.EmailField()
    
    def validate_email(self, value):
        return value.lower()
    
    def save(self):
        from .models import EmailVerificationCode
        email = self.validated_data['email']
        
        # Create verification code
        verification_code = EmailVerificationCode.create_for_email(email)
        
        # Send email
        self.send_verification_email(email, verification_code.code)
        
        return verification_code
    
    def send_verification_email(self, email, code):
        # Import SES service
        from bopmaps.aws_ses_service import ses_service
        
        # Extract username from email for personalization
        username = email.split('@')[0]
        
        try:
            # Send email using SES templated email
            success = ses_service.send_user_verification_email(
                recipient_email=email,
                username=username,
                verification_code=code
            )
            
            if not success:
                # Fallback to raw email if template fails
                logger.warning(f"SES templated email failed for {email}, trying raw email")
                
                subject = '🎵 Verify Your Email - BOP Maps'
                html_message = f'''
                <html>
                <body style="font-family: Arial, sans-serif; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h2 style="color: #2196F3;">🎵 Welcome to BOP Maps!</h2>
                        <p>Hi <strong>{username}</strong>,</p>
                        <p>Your verification code is:</p>
                        <div style="background-color: #f5f5f5; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px;">
                            <h1 style="color: #2196F3; font-size: 32px; margin: 0; letter-spacing: 8px;">{code}</h1>
                        </div>
                        <p style="color: #666;">This code will expire in 10 minutes.</p>
                        <p style="color: #666;">If you didn't request this code, please ignore this email.</p>
                        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                        <p style="color: #999; font-size: 14px;">Best regards,<br>The BOP Maps Team</p>
                    </div>
                </body>
                </html>
                '''
                
                text_message = f'''
                BOP Maps - Verify Your Email
                
                Hi {username},
                
                Welcome to BOP Maps! Your verification code is: {code}
                
                This code will expire in 10 minutes.
                
                If you didn't request this code, please ignore this email.
                
                Best regards,
                The BOP Maps Team
                '''
                
                success = ses_service.send_raw_email(
                    recipient_email=email,
                    subject=subject,
                    html_body=html_message,
                    text_body=text_message
                )
            
            if not success:
                raise Exception("SES email sending failed")
                
        except Exception as e:
            logger.error(f"Failed to send verification email via SES: {e}")
            raise serializers.ValidationError("Failed to send verification email")


class VerifyEmailCodeSerializer(serializers.Serializer):
    """
    Serializer for verifying email codes
    """
    email = serializers.EmailField()
    code = serializers.CharField(max_length=6, min_length=6)
    
    def validate_email(self, value):
        return value.lower()
    
    def validate_code(self, value):
        if not value.isdigit():
            raise serializers.ValidationError("Code must contain only numbers")
        return value
    
    def validate(self, attrs):
        from .models import EmailVerificationCode
        email = attrs['email']
        code = attrs['code']
        
        # Find the verification code
        try:
            verification_code = EmailVerificationCode.objects.get(
                email=email,
                code=code,
                is_used=False
            )
        except EmailVerificationCode.DoesNotExist:
            raise serializers.ValidationError("Invalid verification code")
        
        # Check if expired
        if verification_code.is_expired:
            raise serializers.ValidationError("Verification code has expired")
        
        # Check attempts
        if verification_code.attempts >= 5:
            raise serializers.ValidationError("Too many attempts. Please request a new code.")
        
        # Increment attempts
        verification_code.attempts += 1
        verification_code.save()
        
        # Mark as used if valid
        verification_code.is_used = True
        verification_code.save()
        
        attrs['verification_code'] = verification_code
        return attrs


class EmailVerifiedRegisterSerializer(serializers.Serializer):
    """
    Serializer for registering users with email verification (passwordless)
    """
    username = serializers.CharField(
        max_length=30,
        min_length=2,
        help_text="2-30 characters, letters, numbers, underscore, and hyphen only"
    )
    email = serializers.EmailField()
    email_verified = serializers.BooleanField()
    
    def validate_email(self, value):
        return value.lower()
    
    def validate_username(self, value):
        # Check character requirements
        import re
        if not re.match(r'^[a-zA-Z0-9_-]+$', value):
            raise serializers.ValidationError(
                "Username can only contain letters, numbers, underscores, and hyphens"
            )
        
        # Check uniqueness (case insensitive)
        if User.objects.filter(username__iexact=value).exists():
            raise serializers.ValidationError("This username is already taken")
        
        return value
    
    def validate_email_verified(self, value):
        if not value:
            raise serializers.ValidationError("Email must be verified before registration")
        return value
    
    def validate(self, attrs):
        from .models import EmailVerificationCode
        email = attrs['email']
        
        # Check if email is already registered
        if User.objects.filter(email=email).exists():
            raise serializers.ValidationError("Email already registered")
        
        # Verify that email was actually verified recently (within last hour)
        recent_verification = EmailVerificationCode.objects.filter(
            email=email,
            is_used=True,
            created_at__gte=timezone.now() - timezone.timedelta(hours=1)
        ).exists()
        
        if not recent_verification:
            raise serializers.ValidationError("Email verification required")
        
        return attrs
    
    def create(self, validated_data):
        try:
            # Create user without password (passwordless authentication)
            user = User.objects.create_user(
                username=validated_data['username'],
                email=validated_data['email']
            )
            # Set unusable password for security
            user.set_unusable_password()
            user.save()
            
            logger.info(f"New user registered (passwordless): {user.username}")
            return user
        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")
            raise serializers.ValidationError("Failed to create user account")


class EmailVerifiedLoginSerializer(serializers.Serializer):
    """
    Serializer for logging in with email verification (passwordless)
    """
    email = serializers.EmailField()
    email_verified = serializers.BooleanField()
    
    def validate_email(self, value):
        return value.lower()
    
    def validate_email_verified(self, value):
        if not value:
            raise serializers.ValidationError("Email must be verified to login")
        return value
    
    def validate(self, attrs):
        from .models import EmailVerificationCode
        email = attrs['email']
        
        # Check if user exists
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found")
        
        # Verify that email was actually verified recently (within last hour)
        recent_verification = EmailVerificationCode.objects.filter(
            email=email,
            is_used=True,
            created_at__gte=timezone.now() - timezone.timedelta(hours=1)
        ).exists()
        
        if not recent_verification:
            raise serializers.ValidationError("Recent email verification required")
        
        attrs['user'] = user
        return attrs 


class MusicPreferencesSerializer(serializers.ModelSerializer):
    """
    Serializer for updating user's music preferences
    """
    class Meta:
        model = User
        fields = ['top_genres', 'top_artists', 'artist_genres', 'artist_image_urls', 'artist_spotify_ids']