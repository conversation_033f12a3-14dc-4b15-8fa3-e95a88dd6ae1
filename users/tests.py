from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from django.contrib.auth import get_user_model
from unittest.mock import patch, MagicMock

User = get_user_model()

class UserAuthTests(APITestCase):
    def setUp(self):
        self.register_url = reverse('users:register')
        self.login_url = reverse('users:token_obtain_pair')
        self.user_data = {
            'username': 'testuser_auth',
            'email': '<EMAIL>',
            'password': 'ComplexP@ssw0rd!',
            'password_confirm': 'ComplexP@ssw0rd!'
        }
        self.login_data = {
            'username': 'testuser_auth',
            'password': 'ComplexP@ssw0rd!'
        }

    def test_user_registration_success(self):
        """
        Ensure new user can be registered.
        """
        response = self.client.post(self.register_url, self.user_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(User.objects.count(), 1)
        self.assertEqual(User.objects.get().username, 'testuser_auth')
        self.assertIn('access', response.data['tokens'])
        self.assertIn('refresh', response.data['tokens'])
        self.assertEqual(response.data['user']['username'], self.user_data['username'])

    def test_user_registration_passwords_do_not_match(self):
        """
        Ensure registration fails if passwords do not match.
        """
        invalid_data = self.user_data.copy()
        invalid_data['email'] = '<EMAIL>'
        invalid_data['password_confirm'] = 'wrongpassword'
        response = self.client.post(self.register_url, invalid_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('password', response.data)

    def test_user_registration_existing_username(self):
        """
        Ensure registration fails if username already exists.
        """
        User.objects.create_user(username='testuser_auth', email='<EMAIL>', password='ComplexP@ssw0rd!')
        new_user_data_same_username = self.user_data.copy()
        new_user_data_same_username['email'] = '<EMAIL>'
        response = self.client.post(self.register_url, new_user_data_same_username, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('username', response.data)

    def test_user_registration_existing_email(self):
        """
        Ensure registration fails if email already exists.
        """
        User.objects.create_user(username='anotheruser_auth', email='<EMAIL>', password='ComplexP@ssw0rd!')
        new_user_data_same_email = self.user_data.copy()
        new_user_data_same_email['username'] = 'unique_username_for_this_test'
        response = self.client.post(self.register_url, new_user_data_same_email, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('email', response.data)

    def test_user_login_success(self):
        """
        Ensure registered user can log in.
        """
        self.client.post(self.register_url, self.user_data, format='json')
        response = self.client.post(self.login_url, self.login_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
        self.assertEqual(response.data['user']['username'], self.login_data['username'])

    def test_user_login_invalid_credentials(self):
        """
        Ensure login fails with invalid credentials.
        """
        self.client.post(self.register_url, self.user_data, format='json')
        invalid_login_data = {'username': 'testuser_auth', 'password': 'wrongpassword'}
        response = self.client.post(self.login_url, invalid_login_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertIn('detail', response.data)

    def test_user_login_nonexistent_user(self):
        """
        Ensure login fails if user does not exist.
        """
        response = self.client.post(self.login_url, self.login_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertIn('detail', response.data)

class UserProfileTests(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(username='profileuser', email='<EMAIL>', password='ComplexP@ssw0rd!')
        self.client.force_authenticate(user=self.user)
        
        self.login_url = reverse('users:token_obtain_pair')
        self.me_url = reverse('users:user-me')
        self.update_profile_url = reverse('users:user-update-profile')
        self.update_location_url = reverse('users:user-update-location')
        self.update_fcm_token_url = reverse('users:user-update-fcm-token')
        # Assume a detail URL for UserViewSet for other users, if applicable (e.g., 'user-detail')
        # self.other_user_url = reverse('user-detail', kwargs={'pk': self.other_user.pk}) 


    def test_get_current_user_profile_me(self):
        """
        Ensure authenticated user can retrieve their own profile.
        """
        response = self.client.get(self.me_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['username'], self.user.username)
        self.assertEqual(response.data['email'], self.user.email)

    def test_update_user_profile_success(self):
        """
        Ensure user can update their profile (bio, username).
        """
        update_data = {
            'username': 'newusername',
            'bio': 'This is my new bio.'
        }
        response = self.client.put(self.update_profile_url, update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.username, 'newusername')
        self.assertEqual(self.user.bio, 'This is my new bio.')
        self.assertEqual(response.data['username'], 'newusername')

    def test_update_user_profile_change_password(self):
        """
        Ensure user can change their password.
        """
        password_data = {
            'current_password': 'ComplexP@ssw0rd!',
            'new_password': 'newComplexP@ssw0rd!'
        }
        response = self.client.put(self.update_profile_url, password_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify new password works for login
        self.client.logout()
        login_response = self.client.post(self.login_url, {'username': self.user.username, 'password': 'newComplexP@ssw0rd!'})
        self.assertEqual(login_response.status_code, status.HTTP_200_OK)


    def test_update_user_profile_change_password_incorrect_current(self):
        """
        Ensure changing password fails with incorrect current password.
        """
        password_data = {
            'current_password': 'wrongoldpassword',
            'new_password': 'newComplexP@ssw0rd!'
        }
        response = self.client.put(self.update_profile_url, password_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('current_password', response.data)


    def test_update_user_location(self):
        """
        Ensure user can update their location.
        """
        location_data = {'latitude': 40.7128, 'longitude': -74.0060}
        response = self.client.post(self.update_location_url, location_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertIsNotNone(self.user.location)
        self.assertEqual(self.user.location.x, -74.0060)
        self.assertEqual(self.user.location.y, 40.7128)
        self.assertIn('location', response.data)

    def test_update_user_location_invalid_coordinates(self):
        """
        Ensure updating location fails with invalid coordinates.
        """
        location_data = {'latitude': 'invalid', 'longitude': -74.0060}
        response = self.client.post(self.update_location_url, location_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data) # Or specific field errors

    def test_update_fcm_token(self):
        """
        Ensure user can update their FCM token.
        """
        fcm_data = {'fcm_token': 'new_fcm_token_123'}
        response = self.client.post(self.update_fcm_token_url, fcm_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.fcm_token, 'new_fcm_token_123')
        self.assertTrue(response.data['success'])

    def test_update_fcm_token_missing_token(self):
        """
        Ensure updating FCM token fails if token is missing.
        """
        response = self.client.post(self.update_fcm_token_url, {}, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)


    def test_unauthenticated_access_to_me_fails(self):
        """
        Ensure unauthenticated users cannot access /me/ endpoint.
        """
        self.client.logout()
        response = self.client.get(self.me_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    @patch('users.models.User.save') # Mock save to avoid actual DB write during this specific check if needed
    def test_user_model_methods(self, mock_save):
        """ Test various model methods for User """
        user = User(username='modeltest', email='<EMAIL>')
        user.save() # This will call the mock

        user.update_last_active()
        self.assertTrue(mock_save.called)
        
        # Reset mock for next call
        mock_save.reset_mock() 
        from django.contrib.gis.geos import Point
        user.update_location(Point(-70, 40))
        self.assertTrue(mock_save.called)
        
        mock_save.reset_mock()
        user.increment_pins_created()
        self.assertEqual(user.pins_created, 1)
        self.assertTrue(mock_save.called)

        mock_save.reset_mock()
        user.increment_pins_collected()
        self.assertEqual(user.pins_collected, 1)
        self.assertTrue(mock_save.called)

        self.assertEqual(str(user), 'modeltest')
        self.assertEqual(user.full_name, 'modeltest')
        user.first_name = "Test"
        user.last_name = "User"
        self.assertEqual(user.full_name, 'Test User')

        self.assertFalse(user.is_connected_to_music_service())
        user.spotify_connected = True
        self.assertTrue(user.is_connected_to_music_service())

        self.assertIsNone(user.age)
        from datetime import date, timedelta
        user.date_of_birth = date.today() - timedelta(days=365*20) # Approx 20 years old
        self.assertEqual(user.age, 19)

    def test_ban_unban_user(self):
        """ Test banning and unbanning a user. """
        self.assertFalse(self.user.is_banned)
        self.assertFalse(self.user.check_ban_status())

        self.user.ban_user(reason="Test ban", days=1)
        self.user.refresh_from_db()
        self.assertTrue(self.user.is_banned)
        self.assertTrue(self.user.check_ban_status())
        self.assertIsNotNone(self.user.banned_until)

        self.user.unban_user()
        self.user.refresh_from_db()
        self.assertFalse(self.user.is_banned)
        self.assertFalse(self.user.check_ban_status())
        self.assertIsNone(self.user.banned_until)

    def test_expired_ban(self):
        """ Test that an expired ban is automatically lifted. """
        from django.utils import timezone
        from datetime import timedelta
        self.user.ban_user(reason="Expired ban", days=-1) # Ban ended yesterday
        self.user.refresh_from_db()
        
        self.assertTrue(self.user.is_banned) # Initially still marked as banned
        self.assertFalse(self.user.check_ban_status()) # check_ban_status should lift it
        
        self.user.refresh_from_db() # Refresh again to see the effect of check_ban_status
        self.assertFalse(self.user.is_banned)
        self.assertIsNone(self.user.banned_until)

    def test_permanent_ban(self):
        """ Test permanent ban. """
        self.user.ban_user(reason="Permanent ban")
        self.user.refresh_from_db()
        self.assertTrue(self.user.is_banned)
        self.assertTrue(self.user.check_ban_status())
        self.assertIsNone(self.user.banned_until)
        
# It would be good to also test PasswordResetRequestView and PasswordResetConfirmView
# However, these often involve email sending which is typically mocked.
# For now, focusing on core auth and profile.

# Example of how you might test PasswordResetRequestView with mocking:
# class PasswordResetTests(APITestCase):
#     def setUp(self):
#         self.user = User.objects.create_user(username='resetuser', email='<EMAIL>', password='password123')
#         self.request_reset_url = reverse('password_reset_request') # Ensure this URL name is correct

#     @patch('users.views.send_mail') # Mock the send_mail function
#     def test_password_reset_request_success(self, mock_send_mail):
#         response = self.client.post(self.request_reset_url, {'email': '<EMAIL>'}, format='json')
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(response.data['message'], 'Password reset email sent')
#         mock_send_mail.assert_called_once() # Check that an email was attempted

#     def test_password_reset_request_nonexistent_email(self):
#         response = self.client.post(self.request_reset_url, {'email': '<EMAIL>'}, format='json')
#         self.assertEqual(response.status_code, status.HTTP_200_OK) # Should not reveal email non-existence
#         self.assertEqual(response.data['message'], 'Password reset email sent if the email exists')

class UserPublicProfileTests(APITestCase):
    """Test cases for public profile viewing functionality"""
    
    def setUp(self):
        """Set up test data"""
        # Create test users
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123',
            bio='User 1 bio'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123',
            bio='User 2 bio'
        )
        self.user3 = User.objects.create_user(
            username='searchuser',
            email='<EMAIL>',
            password='testpass123',
            bio='Searchable user'
        )
        
        # Get access tokens
        self.access_token1 = self._get_access_token('user1', 'testpass123')
        self.access_token2 = self._get_access_token('user2', 'testpass123')
        
        # Create test pins and collections
        self._create_test_data()
    
    def _get_access_token(self, username, password):
        """Helper method to get access token"""
        response = self.client.post('/api/users/auth/token/', {
            'username': username,
            'password': password
        })
        return response.data['access']
    
    def _create_test_data(self):
        """Create test pins and collections"""
        from pins.models import Pin, Collection
        from gamification.models import PinSkin
        from django.contrib.gis.geos import Point
        
        # Create a default pin skin for testing
        self.default_skin = PinSkin.objects.create(
            name='Default Test Skin',
            image='test_skin.png',
            description='A test skin'
        )
        
        # Create public pins for user2
        self.public_pin1 = Pin.objects.create(
            owner=self.user2,
            location=Point(-74.0060, 40.7128),
            title='Public Pin 1',
            description='A public pin',
            track_title='Test Song',
            track_artist='Test Artist',
            track_url='https://open.spotify.com/track/test',
            service='spotify',
            skin=self.default_skin,
            is_private=False
        )
        
        self.public_pin2 = Pin.objects.create(
            owner=self.user2,
            location=Point(-74.0070, 40.7138),
            title='Public Pin 2',
            description='Another public pin',
            track_title='Test Song 2',
            track_artist='Test Artist 2',
            track_url='https://open.spotify.com/track/test2',
            service='spotify',
            skin=self.default_skin,
            is_private=False
        )
        
        # Create private pin for user2 (should not be visible)
        self.private_pin = Pin.objects.create(
            owner=self.user2,
            location=Point(-74.0080, 40.7148),
            title='Private Pin',
            description='A private pin',
            track_title='Private Song',
            track_artist='Private Artist',
            track_url='https://open.spotify.com/track/private',
            service='spotify',
            skin=self.default_skin,
            is_private=True
        )
        
        # Create public collection for user2
        self.public_collection = Collection.objects.create(
            owner=self.user2,
            name='Public Collection',
            description='A public collection',
            is_public=True
        )
        
        # Create private collection for user2 (should not be visible)
        self.private_collection = Collection.objects.create(
            owner=self.user2,
            name='Private Collection',
            description='A private collection',
            is_public=False
        )
    
    def test_search_users_success(self):
        """Test successful user search"""
        response = self.client.get(
            '/api/users/search/?q=user',
            HTTP_AUTHORIZATION=f'Bearer {self.access_token1}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should return user2 and searchuser, but not user1 (current user)
        usernames = [user['username'] for user in response.data]
        self.assertIn('user2', usernames)
        self.assertIn('searchuser', usernames)
        self.assertNotIn('user1', usernames)
    
    def test_search_users_specific_query(self):
        """Test user search with specific query"""
        response = self.client.get(
            '/api/users/search/?q=search',
            HTTP_AUTHORIZATION=f'Bearer {self.access_token1}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['username'], 'searchuser')
    
    def test_search_users_minimum_characters(self):
        """Test user search with insufficient characters"""
        response = self.client.get(
            '/api/users/search/?q=u',
            HTTP_AUTHORIZATION=f'Bearer {self.access_token1}'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        # The error response format from create_error_response is {'error': True, 'message': '...'}
        self.assertTrue(response.data['error'])
        self.assertIn('at least 2 characters', response.data['message'])
    
    def test_search_users_empty_query(self):
        """Test user search with empty query"""
        response = self.client.get(
            '/api/users/search/?q=',
            HTTP_AUTHORIZATION=f'Bearer {self.access_token1}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, [])
    
    def test_search_users_unauthenticated(self):
        """Test user search without authentication"""
        response = self.client.get('/api/users/search/?q=user')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_get_public_profile_success(self):
        """Test getting another user's public profile"""
        response = self.client.get(
            f'/api/users/{self.user2.id}/public_profile/',
            HTTP_AUTHORIZATION=f'Bearer {self.access_token1}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.data
        self.assertEqual(data['username'], 'user2')
        self.assertEqual(data['bio'], 'User 2 bio')
        self.assertEqual(data['pin_count'], 2)  # Only public pins
        self.assertEqual(data['public_collection_count'], 1)  # Only public collections
        
        # Should not include private information
        self.assertNotIn('email', data)
    
    def test_get_public_profile_nonexistent_user(self):
        """Test getting profile for non-existent user"""
        response = self.client.get(
            '/api/users/99999/public_profile/',
            HTTP_AUTHORIZATION=f'Bearer {self.access_token1}'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_get_public_pins_success(self):
        """Test getting another user's public pins"""
        response = self.client.get(
            f'/api/users/{self.user2.id}/public_pins/',
            HTTP_AUTHORIZATION=f'Bearer {self.access_token1}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should be paginated response
        self.assertIn('results', response.data)
        pins = response.data['results']
        
        # Should only return public pins
        self.assertEqual(len(pins), 2)
        pin_titles = [pin['title'] for pin in pins]
        self.assertIn('Public Pin 1', pin_titles)
        self.assertIn('Public Pin 2', pin_titles)
        self.assertNotIn('Private Pin', pin_titles)
    
    def test_get_public_pins_nonexistent_user(self):
        """Test getting pins for non-existent user"""
        response = self.client.get(
            '/api/users/99999/public_pins/',
            HTTP_AUTHORIZATION=f'Bearer {self.access_token1}'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_get_public_collections_success(self):
        """Test getting another user's public collections"""
        response = self.client.get(
            f'/api/users/{self.user2.id}/public_collections/',
            HTTP_AUTHORIZATION=f'Bearer {self.access_token1}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should be paginated response
        self.assertIn('results', response.data)
        collections = response.data['results']
        
        # Should only return public collections
        self.assertEqual(len(collections), 1)
        self.assertEqual(collections[0]['name'], 'Public Collection')
    
    def test_get_public_collections_nonexistent_user(self):
        """Test getting collections for non-existent user"""
        response = self.client.get(
            '/api/users/99999/public_collections/',
            HTTP_AUTHORIZATION=f'Bearer {self.access_token1}'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_public_endpoints_require_authentication(self):
        """Test that all public profile endpoints require authentication"""
        endpoints = [
            f'/api/users/{self.user2.id}/public_profile/',
            f'/api/users/{self.user2.id}/public_pins/',
            f'/api/users/{self.user2.id}/public_collections/',
            '/api/users/search/?q=user'
        ]
        
        for endpoint in endpoints:
            response = self.client.get(endpoint)
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_user_can_view_own_profile_via_public_endpoint(self):
        """Test that users can view their own profile via public endpoints"""
        response = self.client.get(
            f'/api/users/{self.user1.id}/public_profile/',
            HTTP_AUTHORIZATION=f'Bearer {self.access_token1}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['username'], 'user1')


class AppleSignInTests(APITestCase):
    """Test cases for Apple Sign-In functionality"""

    def setUp(self):
        """Set up test data"""
        self.apple_signin_url = reverse('users:apple-signin')
        self.mock_apple_token = "mock.apple.token"
        self.mock_user_identifier = "000123.abc456def789.1234"

    @patch('users.views.AppleSignInVerifier.verify_apple_token')
    def test_apple_signin_new_user_success(self, mock_verify):
        """Test successful Apple Sign-In for new user"""
        # Mock the Apple token verification
        mock_verify.return_value = {
            'sub': self.mock_user_identifier,
            'email': '<EMAIL>',
            'aud': 'com.bopmaps.app'
        }

        apple_data = {
            'credential': self.mock_apple_token,
            'user_identifier': self.mock_user_identifier,
            'email': '<EMAIL>',
            'full_name': 'Test User',
            'given_name': 'Test',
            'family_name': 'User'
        }

        response = self.client.post(self.apple_signin_url, apple_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('auth_token', response.data)
        self.assertIn('refresh_token', response.data)
        self.assertIn('user', response.data)
        self.assertTrue(response.data['is_new_user'])

        # Verify user was created
        user = User.objects.get(apple_user_id=self.mock_user_identifier)
        self.assertEqual(user.first_name, 'Test')
        self.assertEqual(user.last_name, 'User')
        self.assertTrue(user.apple_connected)
        self.assertTrue(user.is_active)

    @patch('users.views.AppleSignInVerifier.verify_apple_token')
    def test_apple_signin_existing_user_success(self, mock_verify):
        """Test successful Apple Sign-In for existing user"""
        # Create existing user
        existing_user = User.objects.create_user(
            username='existinguser',
            email='<EMAIL>',
            apple_user_id=self.mock_user_identifier,
            apple_connected=True
        )

        # Mock the Apple token verification
        mock_verify.return_value = {
            'sub': self.mock_user_identifier,
            'email': '<EMAIL>',
            'aud': 'com.bopmaps.app'
        }

        apple_data = {
            'credential': self.mock_apple_token,
            'user_identifier': self.mock_user_identifier,
            'email': '<EMAIL>',
            'given_name': 'Updated',
            'family_name': 'Name'
        }

        response = self.client.post(self.apple_signin_url, apple_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('auth_token', response.data)
        self.assertIn('refresh_token', response.data)
        self.assertIn('user', response.data)
        self.assertFalse(response.data['is_new_user'])

        # Verify user was updated
        existing_user.refresh_from_db()
        self.assertTrue(existing_user.apple_connected)

    def test_apple_signin_missing_credential(self):
        """Test Apple Sign-In fails with missing credential"""
        apple_data = {
            'user_identifier': self.mock_user_identifier,
            'email': '<EMAIL>'
        }

        response = self.client.post(self.apple_signin_url, apple_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)

    def test_apple_signin_missing_user_identifier(self):
        """Test Apple Sign-In fails with missing user identifier"""
        apple_data = {
            'credential': self.mock_apple_token,
            'email': '<EMAIL>'
        }

        response = self.client.post(self.apple_signin_url, apple_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)

    @patch('users.views.AppleSignInVerifier.verify_apple_token')
    def test_apple_signin_invalid_token(self, mock_verify):
        """Test Apple Sign-In fails with invalid token"""
        # Mock token verification to raise an error
        mock_verify.side_effect = ValueError("Invalid Apple ID token")

        apple_data = {
            'credential': 'invalid.token',
            'user_identifier': self.mock_user_identifier,
            'email': '<EMAIL>'
        }

        response = self.client.post(self.apple_signin_url, apple_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
        self.assertIn('Apple token verification failed', response.data['error'])

    @patch('users.views.AppleSignInVerifier.verify_apple_token')
    def test_apple_signin_no_email_provided(self, mock_verify):
        """Test Apple Sign-In works without email (private relay case)"""
        # Mock the Apple token verification
        mock_verify.return_value = {
            'sub': self.mock_user_identifier,
            'aud': 'com.bopmaps.app'
        }

        apple_data = {
            'credential': self.mock_apple_token,
            'user_identifier': self.mock_user_identifier,
            'full_name': 'Test User',
            'given_name': 'Test',
            'family_name': 'User'
        }

        response = self.client.post(self.apple_signin_url, apple_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['is_new_user'])

        # Verify user was created with placeholder email
        user = User.objects.get(apple_user_id=self.mock_user_identifier)
        self.assertTrue(user.email.endswith('@apple.privaterelay.appleid.com'))
        self.assertTrue(user.apple_connected)


class OneSignalPlayerTests(APITestCase):
    """
    Test OneSignal player registration and unregistration endpoints
    """

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser_onesignal',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

        self.register_url = reverse('users:user-register-onesignal-player')
        self.unregister_url = reverse('users:user-unregister-onesignal-player')

        self.player_data = {
            'player_id': 'test-player-id-123',
            'platform': 'android'
        }

    @patch('users.views.onesignal_service')
    def test_register_onesignal_player_success(self, mock_service):
        """
        Test successful OneSignal player registration
        """
        mock_service.register_player.return_value = True

        response = self.client.post(self.register_url, self.player_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['message'], 'OneSignal player ID registered successfully')

        # Verify service was called with correct parameters
        mock_service.register_player.assert_called_once_with(
            user=self.user,
            player_id='test-player-id-123',
            platform='android'
        )

    @patch('users.views.onesignal_service')
    def test_register_onesignal_player_failure(self, mock_service):
        """
        Test OneSignal player registration failure
        """
        mock_service.register_player.return_value = False

        response = self.client.post(self.register_url, self.player_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)

    def test_register_onesignal_player_invalid_data(self):
        """
        Test OneSignal player registration with invalid data
        """
        invalid_data = {
            'player_id': '',  # Empty player ID
            'platform': 'invalid_platform'
        }

        response = self.client.post(self.register_url, invalid_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    @patch('users.views.onesignal_service')
    def test_unregister_onesignal_player_success(self, mock_service):
        """
        Test successful OneSignal player unregistration
        """
        mock_service.unregister_player.return_value = True

        unregister_data = {'player_id': 'test-player-id-123'}
        response = self.client.post(self.unregister_url, unregister_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['message'], 'OneSignal player ID unregistered successfully')

        # Verify service was called with correct parameters
        mock_service.unregister_player.assert_called_once_with(
            user=self.user,
            player_id='test-player-id-123'
        )

    @patch('users.views.onesignal_service')
    def test_unregister_onesignal_player_failure(self, mock_service):
        """
        Test OneSignal player unregistration failure
        """
        mock_service.unregister_player.return_value = False

        unregister_data = {'player_id': 'test-player-id-123'}
        response = self.client.post(self.unregister_url, unregister_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)

    def test_unregister_onesignal_player_missing_player_id(self):
        """
        Test OneSignal player unregistration without player ID
        """
        response = self.client.post(self.unregister_url, {}, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
        self.assertEqual(response.data['error'], 'Player ID is required')

    def test_onesignal_endpoints_require_authentication(self):
        """
        Test that OneSignal endpoints require authentication
        """
        self.client.force_authenticate(user=None)

        # Test register endpoint
        response = self.client.post(self.register_url, self.player_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        # Test unregister endpoint
        unregister_data = {'player_id': 'test-player-id-123'}
        response = self.client.post(self.unregister_url, unregister_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
