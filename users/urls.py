from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView
from .views import (
    UserViewSet,
    PasswordResetRequestView,
    PasswordResetConfirmView,
    verify_token,
    auth_status,
    MusicPreferencesUpdateView,
    GoogleSignInView,
    AppleSignInView
)

router = DefaultRouter()
router.register(r'', UserViewSet)

app_name = 'users'

urlpatterns = [
    # Music preferences endpoint (must come before router)
    path('music-preferences/', MusicPreferencesUpdateView.as_view(), name='music-preferences'),
    
    # Auth routes
    path('auth/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('auth/password-reset/', PasswordResetRequestView.as_view(), name='password_reset_request'),
    path('auth/password-reset/confirm/', PasswordResetConfirmView.as_view(), name='password_reset_confirm'),
    path('auth/google/', GoogleSignInView.as_view(), name='google-signin'),
    path('auth/apple/', AppleSignInView.as_view(), name='apple-signin'),
    
    # Authentication debug endpoints
    path('auth/verify-token/', verify_token, name='verify_token'),
    path('auth/status/', auth_status, name='auth_status'),
    
    # User profile endpoint
    path('me/', UserViewSet.as_view({'get': 'me'}), name='user-me'),
    
    # ViewSet routes (must come last to avoid catching specific patterns)
    path('', include(router.urls)),
] 