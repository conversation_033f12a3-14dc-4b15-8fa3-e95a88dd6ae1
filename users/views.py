from django.shortcuts import render, redirect
from rest_framework import status, permissions, viewsets, generics
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.throttling import AnonRateThrottle, UserRateThrottle
from django.contrib.auth import get_user_model, authenticate, login
from django.contrib.gis.geos import Point
from django.db import transaction, models
from django.utils import timezone
from django.conf import settings
from django.shortcuts import get_object_or_404
from django.http import Http404
from django.core.exceptions import ValidationError
from google.oauth2 import id_token
from google.auth.transport import requests as google_requests
import os
import jwt
import requests
from cryptography.hazmat.primitives import serialization
from .serializers import (
    UserSerializer, UserUpdateSerializer, UserRegistrationSerializer,
    PasswordResetRequestSerializer, PasswordResetConfirmSerializer,
    UserPublicProfileSerializer, CheckEmailSerializer, UserAuthSerializer,
    SendVerificationCodeSerializer, VerifyEmailCodeSerializer, 
    EmailVerifiedRegisterSerializer, EmailVerifiedLoginSerializer,
    MusicPreferencesSerializer
)
from bopmaps.views import BaseModelViewSet
from bopmaps.permissions import IsOwnerOrReadOnly, IsOwner
from bopmaps.utils import create_error_response, create_success_response
import logging
from django.db.models import Q, Count, Sum
from datetime import datetime, timedelta
from friends.models import Friend
from pins.models import Pin, PinInteraction, Collection
from bop_drops.models import BopDrop
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.contrib.sites.shortcuts import get_current_site

User = get_user_model()
logger = logging.getLogger('bopmaps')

# Import OneSignal service for player registration
try:
    from notifications.services import OneSignalService
    from notifications.serializers import OneSignalPlayerRegistrationSerializer
    onesignal_service = OneSignalService()
except ImportError:
    logger.warning("OneSignal service not available")
    onesignal_service = None
    OneSignalPlayerRegistrationSerializer = None

class UploadRateThrottle(UserRateThrottle):
    """
    Custom rate throttle specifically for file upload endpoints
    """
    scope = 'upload'

class UserViewSet(BaseModelViewSet):
    """
    API viewset for user management.
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReadOnly]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        # Additional filters can be added here
        return queryset
    
    def get_serializer_class(self):
        if self.action == 'create':
            return UserRegistrationSerializer
        elif self.action in ['update', 'partial_update']:
            return UserUpdateSerializer
        elif self.action in ['public_profile', 'public_pins', 'public_collections', 'search']:
            return UserPublicProfileSerializer
        return UserSerializer
    
    def get_permissions(self):
        if self.action == 'create':
            return [permissions.AllowAny()]
        elif self.action in ['public_profile', 'public_pins', 'public_collections', 'search']:
            return [permissions.IsAuthenticated()]
        return super().get_permissions()
    
    @action(detail=False, methods=['get'])
    def me(self, request):
        """
        Get the current user's profile
        """
        try:
            serializer = self.get_serializer(request.user)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error retrieving user profile: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['get'])
    def public_profile(self, request, pk=None):
        """
        Get another user's public profile information
        """
        try:
            user = get_object_or_404(User, pk=pk)
            serializer = UserPublicProfileSerializer(user)
            return Response(serializer.data)
        except Http404:
            return Response({"detail": "Not found."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error retrieving public profile: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['get'])
    def public_pins(self, request, pk=None):
        """
        Get another user's public pins
        """
        try:
            user = get_object_or_404(User, pk=pk)
            
            # Import here to avoid circular imports
            from pins.models import Pin
            from pins.serializers import PinSerializer
            
            # Get only public pins from this user
            pins = Pin.objects.filter(
                owner=user,
                is_private=False
            ).filter(
                # Filter out expired pins
                models.Q(expiration_date__isnull=True) | 
                models.Q(expiration_date__gt=timezone.now())
            ).order_by('-created_at')
            
            # Add pagination
            page = self.paginate_queryset(pins)
            if page is not None:
                serializer = PinSerializer(page, many=True, context={'request': request})
                return self.get_paginated_response(serializer.data)
            
            serializer = PinSerializer(pins, many=True, context={'request': request})
            return Response(serializer.data)
            
        except Http404:
            return Response({"detail": "Not found."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error retrieving user's public pins: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['get'])
    def public_collections(self, request, pk=None):
        """
        Get another user's public collections
        """
        try:
            user = get_object_or_404(User, pk=pk)
            
            # Import here to avoid circular imports
            from pins.models import Collection
            from pins.serializers import CollectionSerializer
            
            # Get only public collections from this user
            collections = Collection.objects.filter(
                owner=user,
                is_public=True
            ).order_by('-updated_at')
            
            # Add pagination
            page = self.paginate_queryset(collections)
            if page is not None:
                serializer = CollectionSerializer(page, many=True, context={'request': request})
                return self.get_paginated_response(serializer.data)
            
            serializer = CollectionSerializer(collections, many=True, context={'request': request})
            return Response(serializer.data)
            
        except Http404:
            return Response({"detail": "Not found."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error retrieving user's public collections: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['put', 'patch'])
    def update_profile(self, request):
        """
        Update the current user's profile with enhanced validation and security
        """
        try:
            # Check if it's a file upload by looking for multipart content
            is_file_upload = (
                hasattr(request, 'content_type') and 
                'multipart/form-data' in request.content_type
            ) or 'profile_pic' in request.FILES
            
            # Apply upload throttling for file uploads
            if is_file_upload:
                throttle = UploadRateThrottle()
                if not throttle.allow_request(request, self):
                    return Response(
                        {"error": "Upload rate limit exceeded. Please try again later."},
                        status=status.HTTP_429_TOO_MANY_REQUESTS
                    )
            
            # Log the update attempt
            update_fields = list(request.data.keys()) if hasattr(request.data, 'keys') else []
            if request.FILES:
                update_fields.extend(list(request.FILES.keys()))
            
            logger.info(f"Profile update attempt by user {request.user.username}. Fields: {update_fields}")
            
            # Validate and update profile
            serializer = UserUpdateSerializer(request.user, data=request.data, partial=True, context={'request': request})
            
            if serializer.is_valid():
                updated_user = serializer.save()
                
                # Log successful update
                logger.info(f"Profile successfully updated for user {request.user.username}")
                
                # Return the updated user data with absolute URLs
                response_serializer = UserSerializer(updated_user, context={'request': request})
                return Response(response_serializer.data)
            else:
                # Log validation errors
                logger.warning(f"Profile update validation failed for user {request.user.username}: {serializer.errors}")
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
                
        except ValidationError as e:
            logger.error(f"Validation error in profile update for user {request.user.username}: {str(e)}")
            return create_error_response(str(e), status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Unexpected error updating profile for user {request.user.username}: {str(e)}")
            return create_error_response("An unexpected error occurred while updating your profile.", status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def update_location(self, request):
        """
        Update the current user's location
        """
        try:
            lat = request.data.get('latitude')
            lng = request.data.get('longitude')
            
            if not lat or not lng:
                return create_error_response("Latitude and longitude are required", status.HTTP_400_BAD_REQUEST)
            
            try:
                point = Point(float(lng), float(lat), srid=4326)
            except (ValueError, TypeError):
                return create_error_response("Invalid coordinates", status.HTTP_400_BAD_REQUEST)
            
            with transaction.atomic():
                request.user.location = point
                request.user.last_location_update = timezone.now()
                request.user.save(update_fields=['location', 'last_location_update'])
                
            return Response({
                "success": True,
                "message": "Location updated successfully",
                "location": {
                    "latitude": lat,
                    "longitude": lng,
                    "updated_at": request.user.last_location_update
                }
            })
        except Exception as e:
            logger.error(f"Error updating user location: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def update_fcm_token(self, request):
        """
        Update the user's FCM token for push notifications
        """
        try:
            fcm_token = request.data.get('fcm_token')
            if not fcm_token:
                return create_error_response("FCM token is required", status.HTTP_400_BAD_REQUEST)
            
            with transaction.atomic():
                request.user.fcm_token = fcm_token
                request.user.save(update_fields=['fcm_token'])
                
            return Response({
                "success": True,
                "message": "FCM token updated successfully"
            })
        except Exception as e:
            logger.error(f"Error updating FCM token: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """
        Search for users by username
        """
        try:
            query = request.query_params.get('q', '').strip()
            if not query:
                return Response([], status=status.HTTP_200_OK)
            
            if len(query) < 2:
                return create_error_response("Search query must be at least 2 characters", status.HTTP_400_BAD_REQUEST)
            
            # Search for users by username (case insensitive, partial match)
            users = User.objects.filter(
                username__icontains=query
            ).exclude(
                id=request.user.id  # Exclude current user from search results
            )[:20]  # Limit to 20 results
            
            serializer = UserPublicProfileSerializer(users, many=True)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error searching users: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['GET'])
    def profile_stats(self, request):
        """
        Get profile statistics for the current user
        Returns: pins, streak (bop streak), following, followers (pin likes)
        """
        user = request.user
        
        # 1. Total pins count
        pins_count = Pin.objects.filter(owner=user).count()
        
        # 2. Calculate bop streak (consecutive days of activity)
        today = timezone.now().date()
        streak = 0
        current_date = today
        
        # Look back up to 365 days to find the streak
        for i in range(365):
            # Check if user was active on this date (either dropped pin or bop drop)
            pin_activity = Pin.objects.filter(
                owner=user,
                created_at__date=current_date
            ).exists()
            
            bop_drop_activity = BopDrop.objects.filter(
                user=user,
                created_at__date=current_date
            ).exists()
            
            if pin_activity or bop_drop_activity:
                streak += 1
                current_date -= timedelta(days=1)
            else:
                # Streak broken
                break
        
        # 3. Following count (number of accepted friendships)
        following_count = Friend.objects.filter(
            Q(requester=user) | Q(recipient=user),
            status='accepted'
        ).count()
        
        # 4. Total likes on user's pins (upvotes on their pin songs)
        pin_likes_count = PinInteraction.objects.filter(
            pin__owner=user,
            interaction_type='like'
        ).count()
        
        # Also get from upvote_count field as backup
        total_upvotes = Pin.objects.filter(owner=user).aggregate(
            total=Sum('upvote_count')
        )['total'] or 0
        
        # Use the higher of the two counts (in case data is inconsistent)
        followers_count = max(pin_likes_count, total_upvotes)
        
        stats = {
            'pins': str(pins_count),
            'streak': str(streak),
            'following': str(following_count),
            'followers': str(followers_count)
        }
        
        return Response(stats)

    @action(detail=False, methods=['get'])
    def profile_header(self, request):
        """
        Get minimal user info for profile headers/avatars
        Returns just username and profile_pic for lightweight requests
        """
        try:
            user = request.user
            
            # Get profile pic URL with absolute path
            profile_pic_url = None
            if user.profile_pic:
                profile_pic_url = request.build_absolute_uri(user.profile_pic)
            
            data = {
                'id': user.id,
                'username': user.username,
                'profile_pic': profile_pic_url
            }
            
            return Response(data)
        except Exception as e:
            logger.error(f"Error retrieving profile header: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['patch'])
    def update_username(self, request):
        """
        Update the current user's username with validation
        Max 30 characters, must be unique (case insensitive)
        """
        try:
            new_username = request.data.get('username', '').strip()
            
            # Validation checks
            if not new_username:
                return create_error_response("Username is required", status.HTTP_400_BAD_REQUEST)
            
            # Character limit check (30 characters max)
            if len(new_username) > 30:
                return create_error_response("Username must be 30 characters or less", status.HTTP_400_BAD_REQUEST)
            
            # Minimum length check (2 characters min)
            if len(new_username) < 2:
                return create_error_response("Username must be at least 2 characters", status.HTTP_400_BAD_REQUEST)
            
            # Check for valid characters (alphanumeric, underscore, hyphen)
            import re
            if not re.match(r'^[a-zA-Z0-9_-]+$', new_username):
                return create_error_response("Username can only contain letters, numbers, underscores, and hyphens", status.HTTP_400_BAD_REQUEST)
            
            # Check if username already exists (case insensitive)
            if User.objects.filter(username__iexact=new_username).exclude(id=request.user.id).exists():
                return create_error_response("This username is already taken", status.HTTP_400_BAD_REQUEST)
            
            # Update username
            old_username = request.user.username
            with transaction.atomic():
                request.user.username = new_username
                request.user.save(update_fields=['username'])
                
            logger.info(f"Username updated: {old_username} -> {new_username}")
            
            return Response({
                "success": True,
                "message": "Username updated successfully",
                "username": new_username
            })
            
        except Exception as e:
            logger.error(f"Error updating username: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['patch'])
    def update_bio(self, request):
        """
        Update the current user's bio
        Max 150 characters
        """
        try:
            new_bio = request.data.get('bio', '').strip()
            
            # Character limit check (150 characters max)
            if len(new_bio) > 150:
                return create_error_response("Bio must be 150 characters or less", status.HTTP_400_BAD_REQUEST)
            
            # Update bio (empty string is allowed)
            old_bio = request.user.bio or ""
            with transaction.atomic():
                request.user.bio = new_bio
                request.user.save(update_fields=['bio'])
                
            logger.info(f"Bio updated for user: {request.user.username}")
            
            return Response({
                "success": True,
                "message": "Bio updated successfully",
                "bio": new_bio
            })
            
        except Exception as e:
            logger.error(f"Error updating bio: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def comprehensive_profile(self, request, pk=None):
        """
        Get comprehensive profile data for another user in a single API call
        Returns: profile info, pins, collections, friendship status, block status
        """
        try:
            user = get_object_or_404(User, pk=pk)
            current_user = request.user
            
            # Get basic profile info
            profile_serializer = UserPublicProfileSerializer(user)
            profile_data = profile_serializer.data
            
            # Get friendship status
            from friends.models import Friend
            friendship_status = None
            friendship_id = None
            is_friend = False
            has_pending_request = False
            sent_request = False
            received_request = False
            
            if current_user != user:
                # Check existing friendship
                friendship = Friend.objects.filter(
                    (Q(requester=current_user, recipient=user) | 
                     Q(requester=user, recipient=current_user)),
                ).first()
                
                if friendship:
                    friendship_status = friendship.status
                    friendship_id = friendship.id
                    
                    if friendship.status == 'accepted':
                        is_friend = True
                    elif friendship.status == 'pending':
                        has_pending_request = True
                        if friendship.requester == current_user:
                            sent_request = True
                        else:
                            received_request = True
            
            # Get blocking status
            is_blocked = False
            has_blocked = False
            if current_user != user:
                # Check if current user has blocked this user or vice versa
                if hasattr(current_user, 'is_blocked_by'):
                    is_blocked = current_user.is_blocked_by(user)
                    has_blocked = current_user.has_blocked(user)
            
            # Get public pins (limited to first 20)
            from pins.models import Pin
            from pins.serializers import PinSerializer
            
            pins = Pin.objects.filter(
                owner=user,
                is_private=False
            ).filter(
                # Filter out expired pins
                models.Q(expiration_date__isnull=True) | 
                models.Q(expiration_date__gt=timezone.now())
            ).order_by('-created_at')[:20]
            
            pins_data = PinSerializer(pins, many=True, context={'request': request}).data
            
            # Get public collections (limited to first 20)
            from pins.models import Collection
            from pins.serializers import CollectionSerializer
            
            collections = Collection.objects.filter(
                owner=user,
                is_public=True
            ).order_by('-updated_at')[:20]
            
            collections_data = CollectionSerializer(collections, many=True, context={'request': request}).data
            
            # Compile comprehensive response
            response_data = {
                'profile': profile_data,
                'friendship': {
                    'status': friendship_status,
                    'friendship_id': friendship_id,
                    'is_friend': is_friend,
                    'has_pending_request': has_pending_request,
                    'sent_request': sent_request,
                    'received_request': received_request,
                },
                'blocking': {
                    'is_blocked': is_blocked,  # Current user is blocked by this user
                    'has_blocked': has_blocked,  # Current user has blocked this user
                },
                'pins': {
                    'data': pins_data,
                    'count': len(pins_data),
                    'total_count': profile_data.get('pin_count', 0),
                    'has_more': len(pins_data) == 20
                },
                'collections': {
                    'data': collections_data,
                    'count': len(collections_data),
                    'total_count': profile_data.get('public_collection_count', 0),
                    'has_more': len(collections_data) == 20
                }
            }
            
            return Response(response_data)
            
        except Http404:
            return Response({"detail": "Not found."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error retrieving comprehensive profile: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['patch'])
    def update_profile_picture(self, request):
        """
        Update profile picture using Cloudinary URL
        """
        try:
            # Apply upload throttling
            throttle = UploadRateThrottle()
            if not throttle.allow_request(request, self):
                return Response(
                    {"error": "Upload rate limit exceeded. Please try again later."},
                    status=status.HTTP_429_TOO_MANY_REQUESTS
                )
            
            # Check if profile_pic_url is provided
            profile_pic = request.data.get('profile_pic')
            if not profile_pic:
                return create_error_response("No profile picture URL provided.", status.HTTP_400_BAD_REQUEST)
            
            # Log update attempt
            logger.info(f"Profile picture update attempt by user {request.user.username}. URL: {profile_pic}")
            
            # Update user with new profile picture URL
            data = {'profile_pic': profile_pic}
            serializer = UserUpdateSerializer(request.user, data=data, partial=True, context={'request': request})
            
            if serializer.is_valid():
                updated_user = serializer.save()
                
                logger.info(f"Profile picture successfully updated for user {request.user.username}")
                
                return Response({
                    "success": True,
                    "message": "Profile picture updated successfully",
                    "profile_pic_url": updated_user.profile_pic,
                    "user": {
                        "id": updated_user.id,
                        "username": updated_user.username,
                        "profile_pic_url": updated_user.profile_pic
                    }
                })
            else:
                logger.warning(f"Profile picture update validation failed for user {request.user.username}: {serializer.errors}")
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
                
        except ValidationError as e:
            logger.error(f"Validation error in profile picture update for user {request.user.username}: {str(e)}")
            return create_error_response(str(e), status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Unexpected error updating profile picture for user {request.user.username}: {str(e)}")
            return create_error_response("An unexpected error occurred while updating your profile picture.", status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['delete'])
    def remove_profile_picture(self, request):
        """
        Remove the current user's profile picture
        """
        try:
            if not request.user.profile_pic:
                return create_error_response("No profile picture to remove.", status.HTTP_400_BAD_REQUEST)
            
            # Store reference to the file before removing
            old_profile_pic = request.user.profile_pic
            old_file_name = old_profile_pic.name
            
            # Remove the profile picture
            request.user.profile_pic = None
            request.user.save(update_fields=['profile_pic'])
            
            # Clean up the file
            try:
                old_profile_pic.delete(save=False)
                logger.info(f"Profile picture removed and file deleted for user {request.user.username}: {old_file_name}")
            except Exception as e:
                logger.warning(f"Profile picture removed but file deletion failed for user {request.user.username}: {str(e)}")
            
            return Response({
                "success": True,
                "message": "Profile picture removed successfully",
                "profile_pic_url": None
            })
            
        except Exception as e:
            logger.error(f"Error removing profile picture for user {request.user.username}: {str(e)}")
            return create_error_response("An error occurred while removing your profile picture.", status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def register_onesignal_player(self, request):
        """
        Register a OneSignal player ID for the current user
        """
        if not onesignal_service:
            return create_error_response("OneSignal service not available", status.HTTP_503_SERVICE_UNAVAILABLE)

        serializer = OneSignalPlayerRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            try:
                success = onesignal_service.register_player(
                    user=request.user,
                    player_id=serializer.validated_data['player_id'],
                    platform=serializer.validated_data['platform']
                )

                if success:
                    logger.info(f"OneSignal player registered for user {request.user.username}: {serializer.validated_data['player_id']}")
                    return Response({
                        "success": True,
                        "message": "OneSignal player ID registered successfully"
                    })
                else:
                    return create_error_response("Failed to register player ID", status.HTTP_400_BAD_REQUEST)

            except Exception as e:
                logger.error(f"Failed to register OneSignal player for user {request.user.username}: {str(e)}")
                return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def unregister_onesignal_player(self, request):
        """
        Unregister a OneSignal player ID for the current user
        """
        if not onesignal_service:
            return create_error_response("OneSignal service not available", status.HTTP_503_SERVICE_UNAVAILABLE)

        player_id = request.data.get('player_id')
        if not player_id:
            return create_error_response("Player ID is required", status.HTTP_400_BAD_REQUEST)

        try:
            success = onesignal_service.unregister_player(
                user=request.user,
                player_id=player_id
            )

            if success:
                logger.info(f"OneSignal player unregistered for user {request.user.username}: {player_id}")
                return Response({
                    "success": True,
                    "message": "OneSignal player ID unregistered successfully"
                })
            else:
                return create_error_response("Failed to unregister player ID", status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Failed to unregister OneSignal player for user {request.user.username}: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)


class AuthRateThrottle(AnonRateThrottle):
    """
    Custom rate throttle for authentication endpoints
    """
    scope = 'auth'


class CheckEmailView(generics.GenericAPIView):
    """
    API view to check if an email address already has an account
    """
    serializer_class = CheckEmailSerializer
    permission_classes = [AllowAny]
    throttle_classes = [AuthRateThrottle]
    
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            
            try:
                user = User.objects.get(email=email)
                # User exists, return user data
                user_serializer = UserAuthSerializer(user, context={'request': request})
                return Response({
                    'exists': True,
                    'user': user_serializer.data
                }, status=status.HTTP_200_OK)
            except User.DoesNotExist:
                # User doesn't exist
                return Response({
                    'exists': False,
                    'user': None
                }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SendVerificationCodeView(generics.GenericAPIView):
    """
    API view to send email verification code
    """
    serializer_class = SendVerificationCodeSerializer
    permission_classes = [AllowAny]
    throttle_classes = [AuthRateThrottle]
    
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            try:
                verification_code = serializer.save()
                return Response({
                    'success': True,
                    'message': f'Verification code sent to {serializer.validated_data["email"]}',
                    'expires_in': 600  # 10 minutes in seconds
                }, status=status.HTTP_200_OK)
            except Exception as e:
                logger.error(f"Error sending verification code: {str(e)}")
                return Response({
                    'success': False,
                    'message': 'Failed to send verification code',
                    'error': str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'success': False,
            'message': 'Invalid email address',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class VerifyEmailCodeView(generics.GenericAPIView):
    """
    API view to verify email verification code
    """
    serializer_class = VerifyEmailCodeSerializer
    permission_classes = [AllowAny]
    throttle_classes = [AuthRateThrottle]
    
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            return Response({
                'success': True,
                'message': 'Email verified successfully'
            }, status=status.HTTP_200_OK)
        
        return Response({
            'success': False,
            'message': 'Verification failed',
            'errors': serializer.errors if serializer.errors else ['Invalid verification code']
        }, status=status.HTTP_400_BAD_REQUEST)


class LoginView(generics.GenericAPIView):
    """
    API view for email verification based login (passwordless)
    """
    serializer_class = EmailVerifiedLoginSerializer
    permission_classes = [AllowAny]
    throttle_classes = [AuthRateThrottle]
    
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            user = serializer.validated_data['user']
            
            # Generate JWT tokens
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            refresh_token = str(refresh)
            
            # Serialize user data
            user_serializer = UserAuthSerializer(user, context={'request': request})
            
            return Response({
                'auth_token': access_token,
                'refresh_token': refresh_token,
                'user': user_serializer.data
            }, status=status.HTTP_200_OK)
        
        return Response({
            'error': 'Login failed',
            'message': 'Please verify your email to continue',
            'errors': serializer.errors if serializer.errors else ['Authentication failed']
        }, status=status.HTTP_400_BAD_REQUEST)


class RegisterView(generics.GenericAPIView):
    """
    API view for email verification based registration (passwordless)
    Uses email as unique identifier - creates new user or updates existing user
    """
    serializer_class = EmailVerifiedRegisterSerializer
    permission_classes = [AllowAny]
    throttle_classes = [AuthRateThrottle]
    
    def post(self, request, *args, **kwargs):
        email = request.data.get('email')
        username = request.data.get('username')
        source = request.data.get('source', 'email')
        email_verified = request.data.get('email_verified', False)
        
        if not email:
            return Response({
                'error': 'Validation error',
                'errors': {'email': ['Email is required']}
            }, status=status.HTTP_400_BAD_REQUEST)

        is_new_user = not User.objects.filter(email=email.lower()).exists()
        
        try:
            # Check if user with this email already exists
            try:
                user = User.objects.get(email=email.lower())
                # User exists - update relevant info
                if username and username != user.username:
                    # Check if username is already taken by another user
                    if User.objects.filter(username=username).exclude(id=user.id).exists():
                        return Response({
                            'error': 'Validation error',
                            'errors': {'username': ['Username already taken']}
                        }, status=status.HTTP_400_BAD_REQUEST)
                    user.username = username
                
                # Update music service connections based on source
                if source == 'spotify':
                    user.spotify_connected = True
                elif source == 'apple_music':
                    user.apple_music_connected = True
                
                # Mark email as verified if indicated
                if email_verified:
                    user.is_active = True
                
                user.save()
                
            except User.DoesNotExist:
                # User doesn't exist - create new user
                serializer = self.get_serializer(data=request.data)
                
                if serializer.is_valid():
                    user = serializer.save()
                    
                    # Set music service connection based on source
                    if source == 'spotify':
                        user.spotify_connected = True
                    elif source == 'apple_music':
                        user.apple_music_connected = True
                    
                    # Mark email as verified if indicated
                    if email_verified:
                        user.is_active = True
                    
                    user.save()
                else:
                    # Format validation errors
                    errors = {}
                    for field, field_errors in serializer.errors.items():
                        if isinstance(field_errors, list):
                            errors[field] = field_errors
                        else:
                            errors[field] = [str(field_errors)]
                    
                    return Response({
                        'error': 'Validation error',
                        'errors': errors
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            # Generate JWT tokens for both new and existing users
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            refresh_token = str(refresh)
            
            # Serialize user data
            user_serializer = UserAuthSerializer(user, context={'request': request})
            
            return Response({
                'auth_token': access_token,
                'refresh_token': refresh_token,
                'user': user_serializer.data,
                'is_new_user': is_new_user
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Registration error: {str(e)}")
            return Response({
                'error': 'Registration failed',
                'message': 'Unable to create or update account. Please try again.'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AuthTokenObtainPairView(TokenObtainPairView):
    """
    Custom token obtain pair view with extra data
    """
    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        
        if response.status_code == 200:
            try:
                # Add user data to response
                username = request.data.get('username')
                user = User.objects.get(username=username)
                user_data = UserSerializer(user).data
                
                # Update response with user data
                data = response.data
                data['user'] = user_data
                
                # Update last active
                user.update_last_active()
                
                return Response(data)
            except User.DoesNotExist:
                logger.error(f"User not found during token obtain: {username}")
            except Exception as e:
                logger.error(f"Error adding user data to token response: {str(e)}")
                
        return response


class RegistrationView(generics.CreateAPIView):
    """
    API view for user registration
    """
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [AllowAny]
    
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            try:
                with transaction.atomic():
                    user = serializer.save()
                    
                    # Generate JWT tokens
                    refresh = RefreshToken.for_user(user)
                    
                    return Response({
                        'message': 'User registered successfully',
                        'user': UserSerializer(user).data,
                        'tokens': {
                            'refresh': str(refresh),
                            'access': str(refresh.access_token),
                        }
                    }, status=status.HTTP_201_CREATED)
            except Exception as e:
                logger.error(f"Error during user registration: {str(e)}")
                return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetRequestView(generics.GenericAPIView):
    """
    API view to request a password reset
    """
    serializer_class = PasswordResetRequestSerializer
    permission_classes = [AllowAny]
    
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            email = serializer.validated_data['email']
            
            try:
                user = User.objects.get(email=email)
                
                # Generate token here and send email with reset link
                # In a real implementation, you'd use a dedicated service for this
                
                return Response({
                    'message': 'Password reset email sent'
                })
            except User.DoesNotExist:
                # Don't reveal that the user doesn't exist for security
                logger.info(f"Password reset requested for non-existent email: {email}")
                return Response({
                    'message': 'Password reset email sent if the email exists'
                })
            except Exception as e:
                logger.error(f"Error sending password reset: {str(e)}")
                return create_error_response("Error processing request", status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetConfirmView(generics.GenericAPIView):
    """
    API view to confirm a password reset
    """
    serializer_class = PasswordResetConfirmSerializer
    permission_classes = [AllowAny]
    
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            token = serializer.validated_data['token']
            password = serializer.validated_data['password']
            
            # Validate token and update password
            # This is a simplified example, actual implementation would verify the token
            
            try:
                # In a real implementation, decode the token and find the user
                # user = User.objects.get(pk=user_id_from_token)
                # user.set_password(password)
                # user.save()
                
                return Response({
                    'message': 'Password reset successful'
                })
            except Exception as e:
                logger.error(f"Error confirming password reset: {str(e)}")
                return create_error_response("Invalid or expired token", status.HTTP_400_BAD_REQUEST)
                
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MusicPreferencesUpdateView(generics.UpdateAPIView):
    """
    View for updating user's music preferences (top genres and artists)
    """
    serializer_class = MusicPreferencesSerializer
    permission_classes = [IsAuthenticated]
    
    def get_object(self):
        return self.request.user
    
    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        return Response(serializer.data)


class AppleSignInVerifier:
    """
    Apple Sign-In token verification utility
    """
    def __init__(self):
        self.apple_keys_url = "https://appleid.apple.com/auth/keys"
        self.apple_issuer = "https://appleid.apple.com"

    def get_apple_public_keys(self):
        """Fetch Apple's public keys for token verification"""
        response = requests.get(self.apple_keys_url)
        return response.json()

    def verify_apple_token(self, identity_token, client_id):
        """Verify Apple ID token"""
        try:
            # Get Apple's public keys
            apple_keys = self.get_apple_public_keys()

            # Decode token header to get key ID
            unverified_header = jwt.get_unverified_header(identity_token)
            key_id = unverified_header['kid']

            # Find the matching public key
            public_key = None
            for key in apple_keys['keys']:
                if key['kid'] == key_id:
                    public_key = jwt.algorithms.RSAAlgorithm.from_jwk(key)
                    break

            if not public_key:
                raise ValueError("Unable to find matching public key")

            # Verify and decode the token
            decoded_token = jwt.decode(
                identity_token,
                public_key,
                algorithms=['RS256'],
                audience=client_id,
                issuer=self.apple_issuer
            )

            return decoded_token

        except Exception as e:
            raise ValueError(f"Invalid Apple ID token: {str(e)}")


class AppleSignInView(generics.GenericAPIView):
    """
    API view for Apple Sign-In authentication
    Handles both sign-in and registration using Apple credentials
    """
    permission_classes = [AllowAny]
    throttle_classes = [AuthRateThrottle]

    def post(self, request, *args, **kwargs):
        try:
            # Extract data from request
            identity_token = request.data.get('credential')
            authorization_code = request.data.get('authorization_code')
            user_identifier = request.data.get('user_identifier')
            email = request.data.get('email', '')
            full_name = request.data.get('full_name', '')
            given_name = request.data.get('given_name', '')
            family_name = request.data.get('family_name', '')

            if not identity_token or not user_identifier:
                logger.warning("Apple sign-in failed: Missing required data.")
                return Response({
                    'error': 'Missing required Apple Sign-In data'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Verify Apple ID token
            verifier = AppleSignInVerifier()
            try:
                # Use your app's bundle ID as client_id
                client_id = os.environ.get('APPLE_BUNDLE_ID', 'com.example.bopmaps')
                decoded_token = verifier.verify_apple_token(identity_token, client_id)
            except ValueError as e:
                logger.error(f"Apple token verification failed: {e}")
                return Response({
                    'error': f'Apple token verification failed: {str(e)}'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check if user exists by Apple user identifier
            try:
                user = User.objects.get(apple_user_id=user_identifier)

                # Update user info if needed
                user.apple_connected = True
                if not user.first_name and given_name:
                    user.first_name = given_name
                if not user.last_name and family_name:
                    user.last_name = family_name
                if email and not user.apple_email:
                    user.apple_email = email
                user.is_active = True  # Apple users are pre-verified
                user.save()

                created = False

            except User.DoesNotExist:
                # Create new user
                with transaction.atomic():
                    # Generate username from full name or email or user identifier
                    if full_name:
                        base_username = full_name.replace(' ', '').lower()
                    elif email:
                        base_username = email.split('@')[0]
                    else:
                        base_username = f"appleuser{user_identifier[:8]}"

                    username = base_username
                    counter = 1

                    # Ensure unique username
                    while User.objects.filter(username=username).exists():
                        username = f"{base_username}{counter}"
                        counter += 1

                    # Use email if provided, otherwise create a placeholder
                    user_email = email if email else f"{user_identifier}@apple.privaterelay.appleid.com"

                    user = User.objects.create(
                        apple_user_id=user_identifier,
                        email=user_email.lower(),
                        username=username,
                        first_name=given_name or '',
                        last_name=family_name or '',
                        apple_email=email if email else None,
                        is_active=True,  # Apple users are pre-verified
                        apple_connected=True
                    )
                    created = True

            # Generate JWT tokens
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            refresh_token = str(refresh)

            # Serialize user data
            user_serializer = UserAuthSerializer(user, context={'request': request})

            if created:
                logger.info(f"New user created via Apple sign-in: {user.email}")
                response_status = status.HTTP_201_CREATED
            else:
                logger.info(f"Existing user signed in via Apple: {user.email}")
                response_status = status.HTTP_200_OK

            return Response({
                'auth_token': access_token,
                'refresh_token': refresh_token,
                'user': user_serializer.data,
                'is_new_user': created
            }, status=response_status)

        except Exception as e:
            logger.exception("Unexpected error during Apple sign-in.")
            return Response({
                'error': 'An unexpected error occurred during Apple sign-in'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GoogleSignInView(generics.GenericAPIView):
    """
    API view for Google OAuth2 authentication
    Handles both sign-in and registration using Google credentials
    """
    permission_classes = [AllowAny]
    throttle_classes = [AuthRateThrottle]
    
    def post(self, request, *args, **kwargs):
        try:
            google_token = request.data.get('credential')
            if not google_token:
                logger.warning("Google sign-in failed: Credential not provided.")
                return Response({"error": "Credential is required"}, status=status.HTTP_400_BAD_REQUEST)

            # Verify the Google token
            try:
                user_data = id_token.verify_oauth2_token(
                    google_token, 
                    google_requests.Request(), 
                    os.environ.get('GOOGLE_OAUTH_CLIENT_ID')
                )
            except ValueError as e:
                logger.error(f"Google token verification failed: {e}")
                return Response({"error": "Invalid Google token"}, status=status.HTTP_403_FORBIDDEN)

            email = user_data.get("email")
            if not email:
                logger.warning("Google sign-in failed: Email not found in token.")
                return Response({"error": "Email not found in Google token"}, status=status.HTTP_400_BAD_REQUEST)

            try:
                # Check if user exists
                user = User.objects.get(email=email.lower())
                
                # Update user info if needed
                user.google_connected = True
                if not user.first_name and user_data.get("given_name"):
                    user.first_name = user_data.get("given_name")
                if not user.last_name and user_data.get("family_name"):
                    user.last_name = user_data.get("family_name")
                if not user.profile_pic and user_data.get("picture"):
                    user.profile_pic = user_data.get("picture")
                user.is_active = True  # Ensure user is active since Google verified the email
                user.save()
                
                created = False
                
            except User.DoesNotExist:
                # Create new user
                with transaction.atomic():
                    username = email.split('@')[0]
                    base_username = username
                    counter = 1
                    
                    # Ensure unique username
                    while User.objects.filter(username=username).exists():
                        username = f"{base_username}{counter}"
                        counter += 1
                    
                    user = User.objects.create(
                        email=email.lower(),
                        username=username,
                        first_name=user_data.get("given_name", ""),
                        last_name=user_data.get("family_name", ""),
                        profile_pic=user_data.get("picture", ""),
                        is_active=True,  # Google verified the email
                        google_connected=True
                    )
                    created = True

            # Generate JWT tokens
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            refresh_token = str(refresh)
            
            # Serialize user data
            user_serializer = UserAuthSerializer(user, context={'request': request})
            
            if created:
                logger.info(f"New user created via Google sign-in: {user.email}")
                response_status = status.HTTP_201_CREATED
            else:
                logger.info(f"Existing user signed in via Google: {user.email}")
                response_status = status.HTTP_200_OK

            return Response({
                'auth_token': access_token,
                'refresh_token': refresh_token,
                'user': user_serializer.data,
                'is_new_user': created
            }, status=response_status)

        except Exception as e:
            logger.exception("Unexpected error during Google sign-in.")
            return Response(
                {"error": "An unexpected error occurred during sign in"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_view(request):
    """
    Industry-standard logout API with comprehensive security features
    
    Features:
    - Works with or without refresh token
    - Idempotent (safe to call multiple times)
    - Rate limited for security
    - Comprehensive logging
    - Multiple logout modes (current device, all devices)
    - Graceful error handling
    """
    try:
        # Apply rate limiting
        throttle = AuthRateThrottle()
        if not throttle.allow_request(request, None):
            return Response(
                {"error": "Too many logout attempts. Please try again later."},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        
        user = request.user
        logout_mode = request.data.get('mode', 'current')  # 'current' or 'all_devices'
        refresh_token = request.data.get('refresh_token') or request.data.get('refresh')
        
        # Log logout attempt for security monitoring
        logger.info(f"Logout attempt by user {user.username} (mode: {logout_mode})")
        
        tokens_invalidated = 0
        
        # Handle different logout modes
        if logout_mode == 'all_devices':
            # Logout from all devices - invalidate all refresh tokens for this user
            try:
                from rest_framework_simplejwt.token_blacklist.models import OutstandingToken
                outstanding_tokens = OutstandingToken.objects.filter(user=user)
                
                for token in outstanding_tokens:
                    try:
                        refresh_token_obj = RefreshToken(token.token)
                        refresh_token_obj.blacklist()
                        tokens_invalidated += 1
                    except Exception as e:
                        logger.warning(f"Failed to blacklist token for user {user.username}: {str(e)}")
                
                logger.info(f"User {user.username} logged out from all devices ({tokens_invalidated} tokens invalidated)")
                
                return Response({
                    'success': True,
                    'message': f'Successfully logged out from all devices ({tokens_invalidated} sessions ended)',
                    'logout_mode': 'all_devices',
                    'tokens_invalidated': tokens_invalidated
                })
                
            except Exception as e:
                logger.error(f"Error during all-devices logout for user {user.username}: {str(e)}")
                # Still return success - logout shouldn't fail due to token cleanup issues
                return Response({
                    'success': True,
                    'message': 'Logout successful (some sessions may remain active)',
                    'logout_mode': 'all_devices',
                    'warning': 'Some tokens could not be invalidated'
                })
        
        else:
            # Current device logout
            if refresh_token:
                try:
                    # Invalidate the specific refresh token
                    token = RefreshToken(refresh_token)
                    token.blacklist()
                    tokens_invalidated = 1
                    
                    logger.info(f"User {user.username} logged out successfully (token invalidated)")
                    
                    return Response({
                        'success': True,
                        'message': 'Logout successful',
                        'logout_mode': 'current_device',
                        'tokens_invalidated': 1
                    })
                    
                except Exception as e:
                    logger.warning(f"Failed to blacklist specific token for user {user.username}: {str(e)}")
                    # Even if token blacklisting fails, consider logout successful
                    # The access token will expire naturally
                    return Response({
                        'success': True,
                        'message': 'Logout successful (token will expire naturally)',
                        'logout_mode': 'current_device',
                        'warning': 'Token could not be immediately invalidated'
                    })
            else:
                # No refresh token provided - still successful logout
                # The access token will expire naturally
                logger.info(f"User {user.username} logged out without refresh token")
                
                return Response({
                    'success': True,
                    'message': 'Logout successful (access token will expire naturally)',
                    'logout_mode': 'current_device',
                    'note': 'Refresh token not provided - session will expire naturally'
                })
    
    except Exception as e:
        logger.error(f"Unexpected error during logout for user {getattr(request.user, 'username', 'unknown')}: {str(e)}")
        
        # Even on error, return success - logout should be idempotent
        # The worst case is that tokens remain active until natural expiration
        return Response({
            'success': True,
            'message': 'Logout completed (some sessions may remain active)',
            'warning': 'Logout process encountered issues but was completed'
        })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_all_devices(request):
    """
    Dedicated endpoint for logging out from all devices
    """
    # Redirect to main logout with all_devices mode
    request.data['mode'] = 'all_devices'
    return logout_view(request)


@api_view(['POST'])
@permission_classes([AllowAny])
def verify_token(request):
    """
    Verify JWT token and return user info
    This endpoint helps diagnose authentication issues
    """
    from rest_framework_simplejwt.tokens import AccessToken
    from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
    
    token = request.data.get('token')
    if not token:
        # Try to extract from Authorization header
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if auth_header.startswith('Bearer '):
            token = auth_header[7:]
    
    if not token:
        return Response({
            'valid': False,
            'error': 'No token provided',
            'help': 'Provide token in request body or Authorization header'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        # Validate the token
        access_token = AccessToken(token)
        user_id = access_token.get('user_id')
        
        if user_id:
            try:
                user = User.objects.get(id=user_id)
                return Response({
                    'valid': True,
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'is_active': user.is_active,
                        'last_active': user.last_active
                    },
                    'token_info': {
                        'expires_at': access_token.get('exp'),
                        'issued_at': access_token.get('iat'),
                        'token_type': access_token.get('token_type', 'access')
                    }
                })
            except User.DoesNotExist:
                return Response({
                    'valid': False,
                    'error': 'User not found',
                    'user_id': user_id
                }, status=status.HTTP_404_NOT_FOUND)
        else:
            return Response({
                'valid': False,
                'error': 'Invalid token: no user_id claim'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except TokenError as e:
        return Response({
            'valid': False,
            'error': f'Token error: {str(e)}',
            'token_debug': {
                'length': len(token),
                'starts_with': token[:10] + '...' if len(token) > 10 else token
            }
        }, status=status.HTTP_401_UNAUTHORIZED)
    except Exception as e:
        logger.error(f"Unexpected error in token verification: {str(e)}")
        return Response({
            'valid': False,
            'error': f'Unexpected error: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def auth_status(request):
    """
    Get current authentication status
    This endpoint helps debug authentication issues
    """
    try:
        user = request.user
        return Response({
            'authenticated': True,
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'is_active': user.is_active,
                'last_active': user.last_active,
                'date_joined': user.date_joined
            },
            'session_info': {
                'last_activity_middleware': hasattr(user, 'last_active'),
                'request_method': request.method,
                'request_path': request.path
            }
        })
    except Exception as e:
        logger.error(f"Error in auth_status: {str(e)}")
        return Response({
            'authenticated': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
