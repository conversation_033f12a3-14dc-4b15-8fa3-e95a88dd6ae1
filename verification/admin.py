"""
Django admin configuration for verification app
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import VerificationRequest, ApprovedDomain, VerificationAttempt


@admin.register(VerificationRequest)
class VerificationRequestAdmin(admin.ModelAdmin):
    """
    Admin interface for VerificationRequest model
    """
    list_display = [
        'user_link', 'email', 'school_name', 'status', 
        'verification_method', 'created_at', 'reviewed_at'
    ]
    list_filter = [
        'status', 'verification_method', 'created_at', 'reviewed_at'
    ]
    search_fields = [
        'user__username', 'email', 'school_name'
    ]
    readonly_fields = [
        'user', 'email', 'email_domain', 'verification_method', 
        'created_at', 'updated_at'
    ]
    fieldsets = (
        ('Request Information', {
            'fields': ('user', 'email', 'email_domain', 'school_name', 'verification_method')
        }),
        ('Status', {
            'fields': ('status', 'admin_notes')
        }),
        ('Review Information', {
            'fields': ('reviewed_by', 'reviewed_at')
        }),
        ('Documents', {
            'fields': ('documents',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    actions = ['approve_requests', 'reject_requests']
    
    def user_link(self, obj):
        """Link to user in admin"""
        url = reverse('admin:users_user_change', args=[obj.user.pk])
        return format_html('<a href="{}">{}</a>', url, obj.user.username)
    user_link.short_description = 'User'
    
    def email_domain(self, obj):
        """Display email domain"""
        return obj.email_domain
    email_domain.short_description = 'Domain'
    
    def approve_requests(self, request, queryset):
        """Bulk approve selected requests"""
        count = 0
        for req in queryset.filter(status='pending'):
            req.approve(admin_user=request.user, notes="Bulk approved via admin")
            count += 1
        
        self.message_user(request, f'Approved {count} verification requests.')
    approve_requests.short_description = 'Approve selected requests'
    
    def reject_requests(self, request, queryset):
        """Bulk reject selected requests"""
        count = 0
        for req in queryset.filter(status='pending'):
            req.reject(admin_user=request.user, notes="Bulk rejected via admin")
            count += 1
        
        self.message_user(request, f'Rejected {count} verification requests.')
    reject_requests.short_description = 'Reject selected requests'


@admin.register(ApprovedDomain)
class ApprovedDomainAdmin(admin.ModelAdmin):
    """
    Admin interface for ApprovedDomain model
    """
    list_display = [
        'domain', 'school_name', 'is_active', 'school_link', 
        'added_by_link', 'created_at'
    ]
    list_filter = ['is_active', 'created_at']
    search_fields = ['domain', 'school_name']
    readonly_fields = ['added_by', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Domain Information', {
            'fields': ('domain', 'school_name', 'is_active')
        }),
        ('School Link', {
            'fields': ('school',),
            'description': 'Link to existing School record if available'
        }),
        ('Metadata', {
            'fields': ('added_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def school_link(self, obj):
        """Link to school in admin if available"""
        if obj.school:
            url = reverse('admin:users_school_change', args=[obj.school.pk])
            return format_html('<a href="{}">{}</a>', url, obj.school.name)
        return '-'
    school_link.short_description = 'School Record'
    
    def added_by_link(self, obj):
        """Link to user who added the domain"""
        if obj.added_by:
            url = reverse('admin:users_user_change', args=[obj.added_by.pk])
            return format_html('<a href="{}">{}</a>', url, obj.added_by.username)
        return '-'
    added_by_link.short_description = 'Added By'
    
    def save_model(self, request, obj, form, change):
        """Set added_by when creating new domain"""
        if not change:  # Creating new object
            obj.added_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(VerificationAttempt)
class VerificationAttemptAdmin(admin.ModelAdmin):
    """
    Admin interface for VerificationAttempt model (read-only for analytics)
    """
    list_display = [
        'user_link', 'domain', 'success', 'ip_address', 'created_at'
    ]
    list_filter = ['success', 'domain', 'created_at']
    search_fields = ['user__username', 'email', 'domain', 'ip_address']
    readonly_fields = ['user', 'email', 'domain', 'ip_address', 'user_agent', 'success', 'created_at']
    
    def user_link(self, obj):
        """Link to user in admin"""
        url = reverse('admin:users_user_change', args=[obj.user.pk])
        return format_html('<a href="{}">{}</a>', url, obj.user.username)
    user_link.short_description = 'User'
    
    def has_add_permission(self, request):
        """Prevent manual creation of attempts"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Read-only model"""
        return False
    
    def has_delete_permission(self, request, obj=None):
        """Allow deletion for cleanup"""
        return True


# Customize admin site header
admin.site.site_header = "BOPMaps Administration"
admin.site.site_title = "BOPMaps Admin"
