"""
Email service for sending verification codes to students
"""
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils.html import strip_tags
import logging

logger = logging.getLogger('bopmaps')


class VerificationEmailService:
    """Service for sending verification emails"""
    
    @staticmethod
    def send_verification_code(verification_request, code):
        """
        Send verification code to the student's school email
        
        Args:
            verification_request: VerificationRequest instance
            code: 6-digit verification code
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            # Import SES service
            from bopmaps.aws_ses_service import ses_service
            
            # Get username
            username = verification_request.user.first_name or verification_request.user.username
            
            # Send email using SES templated email
            success = ses_service.send_school_verification_email(
                recipient_email=verification_request.email,
                username=username,
                school_name=verification_request.school_name,
                verification_code=code
            )
            
            if not success:
                # Fallback to raw email if template fails
                logger.warning(f"SES templated email failed for {verification_request.email}, trying raw email")
                
                subject = f'🎓 Verify your {verification_request.school_name} email - BOP Maps'
                
                # Create email content
                html_message = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <title>School Email Verification</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
                        .container {{ max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                        .header {{ text-align: center; margin-bottom: 30px; }}
                        .logo {{ font-size: 28px; font-weight: bold; color: #1DB954; margin-bottom: 10px; }}
                        .title {{ font-size: 24px; color: #333; margin-bottom: 20px; }}
                        .school-badge {{ background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%); color: white; padding: 8px 20px; border-radius: 20px; font-size: 14px; font-weight: 600; display: inline-block; margin-bottom: 20px; }}
                        .code-container {{ background: #f8f9fa; border: 2px dashed #1DB954; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0; }}
                        .verification-code {{ font-size: 36px; font-weight: bold; color: #1DB954; letter-spacing: 3px; font-family: 'Courier New', monospace; }}
                        .info {{ color: #666; margin: 20px 0; line-height: 1.6; }}
                        .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                        .footer {{ text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #888; font-size: 14px; }}
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <div class="logo">🎓 BOP Maps</div>
                            <div class="title">Verify Your School Email</div>
                            <div class="school-badge">🏫 {verification_request.school_name}</div>
                        </div>
                        
                        <p class="info">
                            Hi <strong>{username}</strong>,
                        </p>
                        
                        <p class="info">
                            Welcome to BOP Maps! To complete your verification for <strong>{verification_request.school_name}</strong>, 
                            please enter the verification code below in the app:
                        </p>
                        
                        <div class="code-container">
                            <div class="verification-code">{code}</div>
                        </div>
                        
                        <div class="warning">
                            <strong>⏰ This code expires in 30 minutes</strong><br>
                            If you didn't request this verification, please ignore this email.
                        </div>
                        
                        <p class="info">
                            Once verified, you'll be able to:
                            <br>• Connect with fellow students at {verification_request.school_name}
                            <br>• Join campus music challenges
                            <br>• Access school-specific leaderboards
                            <br>• Get your verified student badge
                        </p>
                        
                        <div class="footer">
                            <p>Questions? Contact <NAME_EMAIL></p>
                            <p>BOP Maps - Discover Music Together 🎶</p>
                        </div>
                    </div>
                </body>
                </html>
                """
                
                # Plain text version
                text_message = f"""
                BOP Maps - Verify Your School Email
                
                Hi {username},
                
                Welcome to BOP Maps! To complete your verification for {verification_request.school_name}, 
                please enter this verification code in the app:
                
                VERIFICATION CODE: {code}
                
                ⏰ This code expires in 30 minutes.
                
                Once verified, you'll be able to connect with fellow students, join campus challenges, 
                and access school-specific features!
                
                If you didn't request this verification, please ignore this email.
                
                Questions? Contact <NAME_EMAIL>
                BOP Maps - Discover Music Together 🎶
                """
                
                success = ses_service.send_raw_email(
                    recipient_email=verification_request.email,
                    subject=subject,
                    html_body=html_message,
                    text_body=text_message
                )
            
            if success:
                logger.info(f"Verification code sent to {verification_request.email} for user {verification_request.user.username}")
                return True
            else:
                logger.error(f"Failed to send verification code to {verification_request.email}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending verification email to {verification_request.email}: {str(e)}")
            return False
    
    @staticmethod
    def send_verification_success(verification_request):
        """
        Send confirmation email when verification is successful
        
        Args:
            verification_request: VerificationRequest instance
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            # Import SES service
            from bopmaps.aws_ses_service import ses_service
            
            # Get username
            username = verification_request.user.first_name or verification_request.user.username
            
            # Send email using SES templated email
            success = ses_service.send_school_verification_success_email(
                recipient_email=verification_request.email,
                username=username,
                school_name=verification_request.school_name
            )
            
            if not success:
                # Fallback to raw email if template fails
                logger.warning(f"SES templated success email failed for {verification_request.email}, trying raw email")
                
                subject = f'🎉 Welcome to BOP Maps, {verification_request.school_name} student!'
                
                html_message = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <title>Verification Complete</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
                        .container {{ max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                        .header {{ text-align: center; margin-bottom: 30px; }}
                        .logo {{ font-size: 28px; font-weight: bold; color: #1DB954; margin-bottom: 10px; }}
                        .title {{ font-size: 24px; color: #333; margin-bottom: 20px; }}
                        .success {{ background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center; }}
                        .features {{ background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }}
                        .feature {{ margin: 10px 0; padding: 10px; border-left: 3px solid #1DB954; }}
                        .footer {{ text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #888; font-size: 14px; }}
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <div class="logo">🎓 BOP Maps</div>
                            <div class="title">Verification Complete! 🎉</div>
                        </div>
                        
                        <div class="success">
                            <h3>🎓 You're now verified as a {verification_request.school_name} student!</h3>
                            <p>Your school email has been successfully verified.</p>
                        </div>
                        
                        <p>Hi <strong>{username}</strong>,</p>
                        
                        <p>Congratulations! You now have access to exclusive features for {verification_request.school_name} students:</p>
                        
                        <div class="features">
                            <div class="feature">🏆 <strong>Campus Leaderboards</strong> - Compete with fellow students</div>
                            <div class="feature">🎵 <strong>School Challenges</strong> - Join weekly music challenges</div>
                            <div class="feature">👥 <strong>Student Network</strong> - Connect with classmates</div>
                            <div class="feature">✅ <strong>Verified Badge</strong> - Show your school pride</div>
                            <div class="feature">📍 <strong>Campus Pins</strong> - Discover music around campus</div>
                        </div>
                        
                        <p>Start exploring and connecting with your campus music community!</p>
                        
                        <div class="footer">
                            <p>Questions? Contact <NAME_EMAIL></p>
                            <p>BOP Maps - Discover Music Together 🎶</p>
                        </div>
                    </div>
                </body>
                </html>
                """
                
                text_message = f"""
                BOP Maps - Verification Complete! 🎉
                
                Hi {username},
                
                Congratulations! You're now verified as a {verification_request.school_name} student!
                
                You now have access to:
                🏆 Campus Leaderboards - Compete with fellow students
                🎵 School Challenges - Join weekly music challenges  
                👥 Student Network - Connect with classmates
                ✅ Verified Badge - Show your school pride
                📍 Campus Pins - Discover music around campus
                
                Start exploring and connecting with your campus music community!
                
                Questions? Contact <NAME_EMAIL>
                BOP Maps - Discover Music Together 🎶
                """
                
                success = ses_service.send_raw_email(
                    recipient_email=verification_request.email,
                    subject=subject,
                    html_body=html_message,
                    text_body=text_message
                )
            
            if success:
                logger.info(f"Success email sent to {verification_request.email} for user {verification_request.user.username}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending success email to {verification_request.email}: {str(e)}")
            return False 