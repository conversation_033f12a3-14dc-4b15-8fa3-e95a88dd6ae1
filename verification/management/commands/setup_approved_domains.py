"""
Django management command to set up initial approved domains for school verification
"""

from django.core.management.base import BaseCommand
from verification.models import ApprovedDomain


class Command(BaseCommand):
    help = 'Set up initial approved domains for automatic school verification'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing approved domains before creating new ones',
        )

    def handle(self, *args, **options):
        if options['clear_existing']:
            self.stdout.write('🧹 Clearing existing approved domains...')
            ApprovedDomain.objects.all().delete()
            self.stdout.write('   ✓ Cleared existing domains')

        self.stdout.write('🎓 Setting up approved educational domains...')
        
        # Common US Universities - EXPANDED LIST
        us_universities = [
            # Ivy League
            ('harvard.edu', 'Harvard University'),
            ('mit.edu', 'Massachusetts Institute of Technology'),
            ('stanford.edu', 'Stanford University'),
            ('yale.edu', 'Yale University'),
            ('princeton.edu', 'Princeton University'),
            ('columbia.edu', 'Columbia University'),
            ('cornell.edu', 'Cornell University'),
            ('upenn.edu', 'University of Pennsylvania'),
            ('dartmouth.edu', 'Dartmouth College'),
            ('brown.edu', 'Brown University'),
            
            # Top Private Universities
            ('uchicago.edu', 'University of Chicago'),
            ('caltech.edu', 'California Institute of Technology'),
            ('duke.edu', 'Duke University'),
            ('jhu.edu', 'Johns Hopkins University'),
            ('northwestern.edu', 'Northwestern University'),
            ('nyu.edu', 'New York University'),
            ('usc.edu', 'University of Southern California'),
            ('georgetown.edu', 'Georgetown University'),
            ('vanderbilt.edu', 'Vanderbilt University'),
            ('emory.edu', 'Emory University'),
            ('rice.edu', 'Rice University'),
            ('wustl.edu', 'Washington University in St. Louis'),
            ('nd.edu', 'University of Notre Dame'),
            ('cmu.edu', 'Carnegie Mellon University'),
            ('tufts.edu', 'Tufts University'),
            ('bc.edu', 'Boston College'),
            ('bu.edu', 'Boston University'),
            ('northeastern.edu', 'Northeastern University'),
            ('fordham.edu', 'Fordham University'),
            ('wake.edu', 'Wake Forest University'),
            ('richmond.edu', 'University of Richmond'),
            ('tulane.edu', 'Tulane University'),
            ('smu.edu', 'Southern Methodist University'),
            ('tcu.edu', 'Texas Christian University'),
            ('baylor.edu', 'Baylor University'),
            ('colgate.edu', 'Colgate University'),
            ('hamilton.edu', 'Hamilton College'),
            ('middlebury.edu', 'Middlebury College'),
            ('wesleyan.edu', 'Wesleyan University'),
            ('carleton.edu', 'Carleton College'),
            ('grinnell.edu', 'Grinnell College'),
            ('macalester.edu', 'Macalester College'),
            ('bowdoin.edu', 'Bowdoin College'),
            ('bates.edu', 'Bates College'),
            ('colby.edu', 'Colby College'),
            ('oberlin.edu', 'Oberlin College'),
            ('kenyon.edu', 'Kenyon College'),
            ('denison.edu', 'Denison University'),
            ('wooster.edu', 'College of Wooster'),
            ('whitman.edu', 'Whitman College'),
            ('reed.edu', 'Reed College'),
            ('pomona.edu', 'Pomona College'),
            ('cmc.edu', 'Claremont McKenna College'),
            ('hmc.edu', 'Harvey Mudd College'),
            ('swarthmore.edu', 'Swarthmore College'),
            ('haverford.edu', 'Haverford College'),
            ('brynmawr.edu', 'Bryn Mawr College'),
            ('vassar.edu', 'Vassar College'),
            ('smith.edu', 'Smith College'),
            ('wellesley.edu', 'Wellesley College'),
            ('mtholyoke.edu', 'Mount Holyoke College'),
            ('barnard.edu', 'Barnard College'),
            
            # Major State Universities - UC System
            ('berkeley.edu', 'University of California, Berkeley'),
            ('ucla.edu', 'University of California, Los Angeles'),
            ('ucdavis.edu', 'University of California, Davis'),
            ('ucsd.edu', 'University of California, San Diego'),
            ('uci.edu', 'University of California, Irvine'),
            ('ucsb.edu', 'University of California, Santa Barbara'),
            ('ucsc.edu', 'University of California, Santa Cruz'),
            ('ucr.edu', 'University of California, Riverside'),
            ('ucmerced.edu', 'University of California, Merced'),
            ('ucsf.edu', 'University of California, San Francisco'),
            
            # Major State Universities - Other States
            ('umich.edu', 'University of Michigan'),
            ('gatech.edu', 'Georgia Institute of Technology'),
            ('utexas.edu', 'University of Texas at Austin'),
            ('uw.edu', 'University of Washington'),
            ('wisc.edu', 'University of Wisconsin-Madison'),
            ('illinois.edu', 'University of Illinois'),
            ('osu.edu', 'Ohio State University'),
            ('psu.edu', 'Pennsylvania State University'),
            ('asu.edu', 'Arizona State University'),
            ('fsu.edu', 'Florida State University'),
            ('uf.edu', 'University of Florida'),
            ('uga.edu', 'University of Georgia'),
            ('vt.edu', 'Virginia Tech'),
            ('virginia.edu', 'University of Virginia'),
            ('unc.edu', 'University of North Carolina'),
            ('ncsu.edu', 'North Carolina State University'),
            ('purdue.edu', 'Purdue University'),
            ('indiana.edu', 'Indiana University'),
            ('iastate.edu', 'Iowa State University'),
            ('uiowa.edu', 'University of Iowa'),
            ('ku.edu', 'University of Kansas'),
            ('k-state.edu', 'Kansas State University'),
            ('uky.edu', 'University of Kentucky'),
            ('louisville.edu', 'University of Louisville'),
            ('lsu.edu', 'Louisiana State University'),
            ('maine.edu', 'University of Maine'),
            ('umd.edu', 'University of Maryland'),
            ('umass.edu', 'University of Massachusetts'),
            ('msu.edu', 'Michigan State University'),
            ('umn.edu', 'University of Minnesota'),
            ('olemiss.edu', 'University of Mississippi'),
            ('msstate.edu', 'Mississippi State University'),
            ('missouri.edu', 'University of Missouri'),
            ('montana.edu', 'University of Montana'),
            ('unl.edu', 'University of Nebraska-Lincoln'),
            ('unr.edu', 'University of Nevada, Reno'),
            ('unlv.edu', 'University of Nevada, Las Vegas'),
            ('unh.edu', 'University of New Hampshire'),
            ('rutgers.edu', 'Rutgers University'),
            ('unm.edu', 'University of New Mexico'),
            ('albany.edu', 'University at Albany'),
            ('buffalo.edu', 'University at Buffalo'),
            ('binghamton.edu', 'Binghamton University'),
            ('stonybrook.edu', 'Stony Brook University'),
            ('und.edu', 'University of North Dakota'),
            ('ndsu.edu', 'North Dakota State University'),
            ('ohio.edu', 'Ohio University'),
            ('okstate.edu', 'Oklahoma State University'),
            ('ou.edu', 'University of Oklahoma'),
            ('oregonstate.edu', 'Oregon State University'),
            ('uoregon.edu', 'University of Oregon'),
            ('temple.edu', 'Temple University'),
            ('pitt.edu', 'University of Pittsburgh'),
            ('uri.edu', 'University of Rhode Island'),
            ('sc.edu', 'University of South Carolina'),
            ('clemson.edu', 'Clemson University'),
            ('sdstate.edu', 'South Dakota State University'),
            ('usd.edu', 'University of South Dakota'),
            ('utk.edu', 'University of Tennessee'),
            ('ttu.edu', 'Texas Tech University'),
            ('uta.edu', 'University of Texas at Arlington'),
            ('utd.edu', 'University of Texas at Dallas'),
            ('uth.edu', 'University of Texas Health Science Center'),
            ('utah.edu', 'University of Utah'),
            ('usu.edu', 'Utah State University'),
            ('uvm.edu', 'University of Vermont'),
            ('vcu.edu', 'Virginia Commonwealth University'),
            ('wsu.edu', 'Washington State University'),
            ('wwu.edu', 'Western Washington University'),
            ('wvu.edu', 'West Virginia University'),
            ('uwyo.edu', 'University of Wyoming'),
            
            # Additional Major Universities
            ('american.edu', 'American University'),
            ('gwu.edu', 'George Washington University'),
            ('howard.edu', 'Howard University'),
            ('catholic.edu', 'Catholic University of America'),
            ('gallaudet.edu', 'Gallaudet University'),
            ('miami.edu', 'University of Miami'),
            ('fiu.edu', 'Florida International University'),
            ('ucf.edu', 'University of Central Florida'),
            ('usf.edu', 'University of South Florida'),
            ('fit.edu', 'Florida Institute of Technology'),
            ('nova.edu', 'Nova Southeastern University'),
            ('rollins.edu', 'Rollins College'),
            ('stetson.edu', 'Stetson University'),
            ('mercer.edu', 'Mercer University'),
            ('gsu.edu', 'Georgia State University'),
            ('georgia.edu', 'University of Georgia'),
            ('uidaho.edu', 'University of Idaho'),
            ('bsu.edu', 'Boise State University'),
            ('depaul.edu', 'DePaul University'),
            ('loyola.edu', 'Loyola University Chicago'),
            ('niu.edu', 'Northern Illinois University'),
            ('siu.edu', 'Southern Illinois University'),
            ('butler.edu', 'Butler University'),
            ('valpo.edu', 'Valparaiso University'),
            ('drake.edu', 'Drake University'),
            ('wichita.edu', 'Wichita State University'),
            ('wku.edu', 'Western Kentucky University'),
            ('eku.edu', 'Eastern Kentucky University'),
            ('nku.edu', 'Northern Kentucky University'),
            ('loyno.edu', 'Loyola University New Orleans'),
            ('colby-sawyer.edu', 'Colby-Sawyer College'),
            ('snhu.edu', 'Southern New Hampshire University'),
            ('unh.edu', 'University of New Hampshire'),
            ('kean.edu', 'Kean University'),
            ('rowan.edu', 'Rowan University'),
            ('tcnj.edu', 'The College of New Jersey'),
            ('montclair.edu', 'Montclair State University'),
            ('njit.edu', 'New Jersey Institute of Technology'),
            ('stevens.edu', 'Stevens Institute of Technology'),
            ('drew.edu', 'Drew University'),
            ('rider.edu', 'Rider University'),
            ('fairleigh.edu', 'Fairleigh Dickinson University'),
            ('rpi.edu', 'Rensselaer Polytechnic Institute'),
            ('union.edu', 'Union College'),
            ('syracuse.edu', 'Syracuse University'),
            ('rochester.edu', 'University of Rochester'),
            ('rit.edu', 'Rochester Institute of Technology'),
            ('clarkson.edu', 'Clarkson University'),
            ('skidmore.edu', 'Skidmore College'),
            ('siena.edu', 'Siena College'),
            ('manhattan.edu', 'Manhattan College'),
            ('iona.edu', 'Iona College'),
            ('hofstra.edu', 'Hofstra University'),
            ('adelphi.edu', 'Adelphi University'),
            ('stjohns.edu', 'St. John\'s University'),
            ('pace.edu', 'Pace University'),
            ('newschool.edu', 'The New School'),
            ('cooper.edu', 'Cooper Union'),
            ('pratt.edu', 'Pratt Institute'),
            ('parsons.edu', 'Parsons School of Design'),
            ('fIT.edu', 'Fashion Institute of Technology'),
            ('sva.edu', 'School of Visual Arts'),
            ('nec.edu', 'New England Conservatory'),
            ('berklee.edu', 'Berklee College of Music'),
            ('emerson.edu', 'Emerson College'),
            ('suffolk.edu', 'Suffolk University'),
            ('simmons.edu', 'Simmons University'),
            ('babson.edu', 'Babson College'),
            ('olin.edu', 'Olin College of Engineering'),
            ('bentley.edu', 'Bentley University'),
            ('brandeis.edu', 'Brandeis University'),
            ('clark.edu', 'Clark University'),
            ('wpi.edu', 'Worcester Polytechnic Institute'),
            ('assumption.edu', 'Assumption University'),
            ('fairfield.edu', 'Fairfield University'),
            ('quinnipiac.edu', 'Quinnipiac University'),
            ('uconn.edu', 'University of Connecticut'),
            ('ccsu.edu', 'Central Connecticut State University'),
            ('easternct.edu', 'Eastern Connecticut State University'),
            ('southernct.edu', 'Southern Connecticut State University'),
            ('wcsu.edu', 'Western Connecticut State University'),
            ('trincoll.edu', 'Trinity College'),
            ('hartford.edu', 'University of Hartford'),
            ('udel.edu', 'University of Delaware'),
            ('wilmu.edu', 'Wilmington University'),
            
            # Community Colleges and Others
            ('student.cccd.edu', 'Coast Community College District'),
            ('student.laccd.edu', 'Los Angeles Community College District'),
            ('student.ccccd.edu', 'Contra Costa Community College District'),
            ('student.peralta.edu', 'Peralta Community College District'),
            ('student.smc.edu', 'Santa Monica College'),
            ('student.pasadena.edu', 'Pasadena City College'),
            ('student.glendale.edu', 'Glendale Community College'),
            ('student.pierce.edu', 'Pierce College'),
            ('student.lavc.edu', 'Los Angeles Valley College'),
            ('student.elac.edu', 'East Los Angeles College'),
            ('student.lattc.edu', 'Los Angeles Trade-Technical College'),
            ('student.lacitycollege.edu', 'Los Angeles City College'),
            ('student.lahc.edu', 'Los Angeles Harbor College'),
            ('student.lamission.edu', 'Los Angeles Mission College'),
            ('student.wlac.edu', 'West Los Angeles College'),
            ('student.swc.edu', 'Southwestern College'),
            ('student.grossmont.edu', 'Grossmont College'),
            ('student.cuyamaca.edu', 'Cuyamaca College'),
            ('student.miracosta.edu', 'MiraCosta College'),
            ('student.palomar.edu', 'Palomar College'),
            ('student.sdccd.edu', 'San Diego Community College District'),
            ('student.ccsf.edu', 'City College of San Francisco'),
            ('student.skylinecollege.edu', 'Skyline College'),
            ('student.canadacollege.edu', 'Canada College'),
            ('student.ohlone.edu', 'Ohlone College'),
            ('student.fhda.edu', 'Foothill-De Anza Community College District'),
            ('student.deanza.edu', 'De Anza College'),
            ('student.foothill.edu', 'Foothill College'),
            ('student.westvalley.edu', 'West Valley College'),
            ('student.missioncollege.edu', 'Mission College'),
            ('student.sjeccd.edu', 'San Jose Evergreen Community College District'),
            ('student.gavilan.edu', 'Gavilan College'),
            ('student.hartnell.edu', 'Hartnell College'),
            ('student.cabrillo.edu', 'Cabrillo College'),
            ('student.monterey.edu', 'Monterey Peninsula College'),
        ]
        
        # UK Universities
        uk_universities = [
            ('cam.ac.uk', 'University of Cambridge'),
            ('ox.ac.uk', 'University of Oxford'),
            ('imperial.ac.uk', 'Imperial College London'),
            ('ucl.ac.uk', 'University College London'),
            ('kcl.ac.uk', "King's College London"),
            ('ed.ac.uk', 'University of Edinburgh'),
            ('manchester.ac.uk', 'University of Manchester'),
            ('warwick.ac.uk', 'University of Warwick'),
            ('bristol.ac.uk', 'University of Bristol'),
            ('glasgow.ac.uk', 'University of Glasgow'),
        ]
        
        # Canadian Universities
        canadian_universities = [
            ('utoronto.ca', 'University of Toronto'),
            ('ubc.ca', 'University of British Columbia'),
            ('mcgill.ca', 'McGill University'),
            ('uwaterloo.ca', 'University of Waterloo'),
            ('ualberta.ca', 'University of Alberta'),
            ('queensu.ca', "Queen's University"),
            ('yorku.ca', 'York University'),
            ('sfu.ca', 'Simon Fraser University'),
        ]
        
        # Australian Universities
        australian_universities = [
            ('sydney.edu.au', 'University of Sydney'),
            ('unsw.edu.au', 'University of New South Wales'),
            ('unimelb.edu.au', 'University of Melbourne'),
            ('anu.edu.au', 'Australian National University'),
            ('uq.edu.au', 'University of Queensland'),
            ('monash.edu', 'Monash University'),
            ('adelaide.edu.au', 'University of Adelaide'),
            ('uts.edu.au', 'University of Technology Sydney'),
        ]

        all_universities = us_universities + uk_universities + canadian_universities + australian_universities
        
        created_count = 0
        skipped_count = 0
        
        for domain, school_name in all_universities:
            approved_domain, created = ApprovedDomain.objects.get_or_create(
                domain=domain,
                defaults={
                    'school_name': school_name,
                    'is_active': True
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(f'   ✓ Added: {school_name} ({domain})')
            else:
                skipped_count += 1

        self.stdout.write(f'\n✅ Setup completed!')
        self.stdout.write(f'   📦 Domains added: {created_count}')
        self.stdout.write(f'   ⏩ Domains skipped (already exist): {skipped_count}')
        self.stdout.write(f'   🎯 Total approved domains: {ApprovedDomain.objects.count()}')
        
        self.stdout.write('\n💡 These domains will automatically verify students:')
        self.stdout.write('   • All .edu domains (US universities)')
        self.stdout.write('   • All .ac.uk domains (UK universities)')  
        self.stdout.write('   • All .edu.au domains (Australian universities)')
        self.stdout.write('   • All .edu.ca domains (Canadian universities)')
        self.stdout.write('   • Specific university domains listed above')
        
        self.stdout.write('\n🔧 To add more domains, use the admin interface:')
        self.stdout.write('   /admin/verification/approveddomain/') 