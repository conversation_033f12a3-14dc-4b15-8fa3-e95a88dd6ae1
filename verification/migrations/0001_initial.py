# Generated by Django 4.2.7 on 2025-06-17 21:43

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ('users', '0004_merge_20250617_0052'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ApprovedDomain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain', models.CharField(help_text="Email domain (e.g., 'harvard.edu')", max_length=255, unique=True)),
                ('school_name', models.CharField(help_text='Official school name', max_length=255)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this domain is currently approved for auto-verification')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('added_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('school', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_domains', to='users.school')),
            ],
            options={
                'ordering': ['school_name'],
            },
        ),
        migrations.CreateModel(
            name='VerificationRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(help_text='School email address to verify', max_length=254, validators=[django.core.validators.EmailValidator()])),
                ('school_name', models.CharField(help_text='Name of the school/university', max_length=255)),
                ('verification_method', models.CharField(choices=[('email_domain', 'Email Domain'), ('document', 'Document Upload'), ('manual', 'Manual Review')], default='email_domain', max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('auto_approved', 'Auto Approved')], default='pending', max_length=20)),
                ('documents', models.JSONField(blank=True, default=list, help_text='URLs to uploaded verification documents')),
                ('admin_notes', models.TextField(blank=True, help_text='Admin notes about the verification')),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_verifications', to=settings.AUTH_USER_MODEL)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='verification_request', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VerificationAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254)),
                ('domain', models.CharField(max_length=255)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('success', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='verificationrequest',
            index=models.Index(fields=['status'], name='verificatio_status_77ae72_idx'),
        ),
        migrations.AddIndex(
            model_name='verificationrequest',
            index=models.Index(fields=['verification_method'], name='verificatio_verific_3db7b7_idx'),
        ),
        migrations.AddIndex(
            model_name='verificationrequest',
            index=models.Index(fields=['created_at'], name='verificatio_created_8c65e3_idx'),
        ),
        migrations.AddIndex(
            model_name='verificationattempt',
            index=models.Index(fields=['user', 'created_at'], name='verificatio_user_id_b7b02c_idx'),
        ),
        migrations.AddIndex(
            model_name='verificationattempt',
            index=models.Index(fields=['domain'], name='verificatio_domain_61b3e5_idx'),
        ),
        migrations.AddIndex(
            model_name='verificationattempt',
            index=models.Index(fields=['success'], name='verificatio_success_c4feff_idx'),
        ),
        migrations.AddIndex(
            model_name='approveddomain',
            index=models.Index(fields=['domain'], name='verificatio_domain_b5b421_idx'),
        ),
        migrations.AddIndex(
            model_name='approveddomain',
            index=models.Index(fields=['is_active'], name='verificatio_is_acti_e3ebdb_idx'),
        ),
    ]
