from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import EmailValidator

User = get_user_model()

class VerificationRequest(models.Model):
    """
    Model to handle school verification requests using email domains
    """
    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('email_sent', 'Email Verification Sent'),
        ('email_verified', 'Email Verified'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('auto_approved', 'Auto Approved'),
    ]
    
    METHOD_CHOICES = [
        ('email_domain', 'Email Domain'),
        ('document', 'Document Upload'),
        ('manual', 'Manual Review'),
    ]
    
    user = models.OneToOneField(
        User, 
        on_delete=models.CASCADE,
        related_name='verification_request'
    )
    email = models.EmailField(
        validators=[EmailValidator()],
        help_text="School email address to verify"
    )
    school_name = models.CharField(
        max_length=255,
        help_text="Name of the school/university"
    )
    verification_method = models.CharField(
        max_length=20,
        choices=METHOD_CHOICES,
        default='email_domain'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending'
    )
    
    # Optional fields for document verification
    documents = models.JSONField(
        default=list,
        blank=True,
        help_text="URLs to uploaded verification documents"
    )
    
    # Admin fields
    admin_notes = models.TextField(
        blank=True,
        help_text="Admin notes about the verification"
    )
    
    # Email verification fields
    email_verification_token = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        help_text="Token for email verification"
    )
    email_verification_code = models.CharField(
        max_length=6,
        blank=True,
        null=True,
        help_text="6-digit verification code sent to email"
    )
    email_verified = models.BooleanField(
        default=False,
        help_text="Whether the email has been verified"
    )
    email_verification_sent_at = models.DateTimeField(
        null=True, 
        blank=True,
        help_text="When the verification email was sent"
    )
    email_verified_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the email was verified"
    )
    
    reviewed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_verifications'
    )
    reviewed_at = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['verification_method']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.school_name} ({self.status})"
    
    def generate_verification_token(self):
        """Generate a unique verification token for email verification"""
        import secrets
        self.email_verification_token = secrets.token_urlsafe(32)
        self.email_verification_sent_at = timezone.now()
        self.status = 'email_sent'
        self.save()
        return self.email_verification_token
    
    def generate_verification_code(self):
        """Generate a 6-digit verification code for email verification"""
        import random
        code = f"{random.randint(100000, 999999)}"
        self.email_verification_code = code
        self.email_verification_sent_at = timezone.now()
        self.status = 'email_sent'
        self.save()
        return code
    
    def verify_email(self, token):
        """Verify email with the provided token"""
        if not self.email_verification_token:
            return False, "No verification token found"
        
        if self.email_verification_token != token:
            return False, "Invalid verification token"
        
        # Check if token is expired (24 hours)
        if self.email_verification_sent_at:
            elapsed = timezone.now() - self.email_verification_sent_at
            if elapsed.total_seconds() > 86400:  # 24 hours
                return False, "Verification token has expired"
        
        # Mark email as verified
        self.email_verified = True
        self.email_verified_at = timezone.now()
        self.status = 'email_verified'
        self.save()
        
        return True, "Email verified successfully"
    
    def verify_email_with_code(self, code):
        """Verify email with the provided 6-digit code"""
        if not self.email_verification_code:
            return False, "No verification code found"
        
        if self.email_verification_code != code:
            return False, "Invalid verification code"
        
        # Check if code is expired (30 minutes for codes)
        if self.email_verification_sent_at:
            elapsed = timezone.now() - self.email_verification_sent_at
            if elapsed.total_seconds() > 1800:  # 30 minutes
                return False, "Verification code has expired"
        
        # Mark email as verified
        self.email_verified = True
        self.email_verified_at = timezone.now()
        
        # Auto-approve if possible
        if self.can_auto_approve():
            self.approve()
        else:
            self.status = 'email_verified'
        
        self.save()
        return True, "Email verified successfully"
    
    def can_auto_approve(self):
        """Check if this request can be auto-approved (domain + email verified)"""
        if not self.email_verified:
            return False
        
        domain = self.email_domain
        
        # Check if domain is pre-approved
        if ApprovedDomain.is_domain_approved(domain):
            return True
            
        # Check if it's a general educational domain
        from .services import VerificationService
        return VerificationService.is_educational_domain(domain)
    
    @property
    def email_domain(self):
        """Extract domain from email"""
        return self.email.split('@')[1].lower() if '@' in self.email else ''
    
    def approve(self, admin_user=None, notes=""):
        """Approve the verification request"""
        self.status = 'approved'
        self.admin_notes = notes
        self.reviewed_by = admin_user
        self.reviewed_at = timezone.now()
        self.save()
        
        # Update user's school verification status
        self.user.school_email_verified = True
        
        # Try to link to existing school or create new one
        from users.models import School
        school, created = School.objects.get_or_create(
            email_domain=self.email_domain,
            defaults={
                'name': self.school_name,
                'address': f"{self.school_name} Campus",
                'location': None,  # Will be set later if needed
                'verified': True
            }
        )
        
        self.user.school = school
        self.user.save()
    
    def reject(self, admin_user=None, notes=""):
        """Reject the verification request"""
        self.status = 'rejected'
        self.admin_notes = notes
        self.reviewed_by = admin_user
        self.reviewed_at = timezone.now()
        self.save()


class ApprovedDomain(models.Model):
    """
    Model to store pre-approved email domains for automatic verification
    """
    domain = models.CharField(
        max_length=255,
        unique=True,
        help_text="Email domain (e.g., 'harvard.edu')"
    )
    school_name = models.CharField(
        max_length=255,
        help_text="Official school name"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this domain is currently approved for auto-verification"
    )
    
    # Link to School model if it exists
    school = models.ForeignKey(
        'users.School',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_domains'
    )
    
    # Metadata
    added_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['school_name']
        indexes = [
            models.Index(fields=['domain']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"{self.domain} - {self.school_name}"
    
    @classmethod
    def is_domain_approved(cls, domain):
        """Check if a domain is pre-approved for auto-verification"""
        return cls.objects.filter(domain=domain.lower(), is_active=True).exists()
    
    @classmethod
    def get_school_for_domain(cls, domain):
        """Get school information for an approved domain"""
        try:
            approved_domain = cls.objects.get(domain=domain.lower(), is_active=True)
            return approved_domain.school_name, approved_domain.school
        except cls.DoesNotExist:
            return None, None


class VerificationAttempt(models.Model):
    """
    Model to track verification attempts for analytics and abuse prevention
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    email = models.EmailField()
    domain = models.CharField(max_length=255)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    success = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['domain']),
            models.Index(fields=['success']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.domain} ({'Success' if self.success else 'Failed'})"
