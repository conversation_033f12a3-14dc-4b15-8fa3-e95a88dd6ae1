"""
Serializers for school verification API
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import VerificationRequest, ApprovedDomain, VerificationAttempt
from .services import VerificationService

User = get_user_model()


class VerificationRequestSerializer(serializers.ModelSerializer):
    """
    Serializer for VerificationRequest model
    """
    email_domain = serializers.CharField(read_only=True)
    user_display = serializers.CharField(source='user.username', read_only=True)
    reviewed_by_display = serializers.CharField(source='reviewed_by.username', read_only=True)
    
    class Meta:
        model = VerificationRequest
        fields = [
            'id', 'user', 'user_display', 'email', 'email_domain', 'school_name',
            'verification_method', 'status', 'documents', 'admin_notes',
            'reviewed_by', 'reviewed_by_display', 'reviewed_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'user', 'user_display', 'email_domain', 'status',
            'reviewed_by', 'reviewed_by_display', 'reviewed_at',
            'created_at', 'updated_at'
        ]


class CreateVerificationRequestSerializer(serializers.Serializer):
    """
    Serializer for creating a new verification request
    """
    email = serializers.EmailField(
        help_text="Your school email address (e.g., <EMAIL>)"
    )
    school_name = serializers.CharField(
        max_length=255,
        required=False,
        help_text="Name of your school/university (will be auto-detected if possible)"
    )
    
    def validate_email(self, value):
        """
        Validate email and check if it's educational
        """
        if not VerificationService.validate_email_format(value):
            raise serializers.ValidationError("Invalid email format")
        
        domain = VerificationService.extract_domain(value)
        if not domain:
            raise serializers.ValidationError("Could not extract domain from email")
        
        return value.lower()
    
    def validate(self, data):
        """
        Cross-field validation
        """
        email = data.get('email')
        school_name = data.get('school_name', '')
        
        if email:
            domain = VerificationService.extract_domain(email)
            
            # If no school name provided, try to suggest one
            if not school_name:
                data['school_name'] = VerificationService.suggest_school_name(domain)
        
        return data


class VerificationStatusSerializer(serializers.Serializer):
    """
    Serializer for user's verification status
    """
    has_request = serializers.BooleanField()
    status = serializers.CharField(allow_null=True)
    school_name = serializers.CharField(allow_null=True)
    email = serializers.EmailField(allow_null=True)
    created_at = serializers.DateTimeField(allow_null=True)
    updated_at = serializers.DateTimeField(allow_null=True)
    is_verified = serializers.BooleanField()
    school = serializers.CharField(allow_null=True)
    
    # Additional helpful fields
    can_verify = serializers.SerializerMethodField()
    verification_reason = serializers.SerializerMethodField()
    recent_attempts = serializers.SerializerMethodField()
    
    def get_can_verify(self, obj):
        """Check if user can start verification"""
        user = self.context.get('user')
        if user:
            can_verify, _ = VerificationService.can_user_verify(user)
            return can_verify
        return False
    
    def get_verification_reason(self, obj):
        """Get reason why user can or cannot verify"""
        user = self.context.get('user')
        if user:
            _, reason = VerificationService.can_user_verify(user)
            return reason
        return None
    
    def get_recent_attempts(self, obj):
        """Get number of recent attempts"""
        user = self.context.get('user')
        if user:
            return VerificationService.get_recent_attempts(user)
        return 0


class DomainCheckSerializer(serializers.Serializer):
    """
    Serializer for checking if a domain is pre-approved
    """
    domain = serializers.CharField(max_length=255)
    
    def validate_domain(self, value):
        """
        Validate domain format
        """
        # Basic domain validation
        if not value or '.' not in value:
            raise serializers.ValidationError("Invalid domain format")
        return value.lower()


class DomainCheckResponseSerializer(serializers.Serializer):
    """
    Serializer for domain check response
    """
    domain = serializers.CharField()
    is_approved = serializers.BooleanField()
    is_educational = serializers.BooleanField()
    school_name = serializers.CharField(allow_null=True)
    suggested_name = serializers.CharField()
    auto_approval = serializers.BooleanField()


class ApprovedDomainSerializer(serializers.ModelSerializer):
    """
    Serializer for ApprovedDomain model (admin use)
    """
    school_display = serializers.CharField(source='school.name', read_only=True)
    added_by_display = serializers.CharField(source='added_by.username', read_only=True)
    
    class Meta:
        model = ApprovedDomain
        fields = [
            'id', 'domain', 'school_name', 'is_active',
            'school', 'school_display', 'added_by', 'added_by_display',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'added_by', 'created_at', 'updated_at']


class VerificationAttemptSerializer(serializers.ModelSerializer):
    """
    Serializer for VerificationAttempt model (analytics)
    """
    user_display = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = VerificationAttempt
        fields = [
            'id', 'user', 'user_display', 'email', 'domain',
            'ip_address', 'success', 'created_at'
        ]
        read_only_fields = ['id', 'user', 'created_at']


class AdminVerificationActionSerializer(serializers.Serializer):
    """
    Serializer for admin actions on verification requests
    """
    action = serializers.ChoiceField(choices=['approve', 'reject'])
    notes = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    
    def validate_action(self, value):
        """Validate action"""
        if value not in ['approve', 'reject']:
            raise serializers.ValidationError("Action must be 'approve' or 'reject'")
        return value 