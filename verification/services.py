"""
School verification services for email domain verification
"""
import re
from typing import <PERSON><PERSON>, Optional
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import VerificationRequest, ApprovedDomain, VerificationAttempt

User = get_user_model()


class VerificationService:
    """
    Service class to handle school email verification logic
    """
    
    # Common educational domains that are automatically approved
    EDU_DOMAINS = {
        'edu',          # US educational institutions
        'ac.uk',        # UK academic institutions  
        'edu.au',       # Australian educational institutions
        'ac.nz',        # New Zealand academic institutions
        'edu.ca',       # Canadian educational institutions
        'ac.za',        # South African academic institutions
    }
    
    @classmethod
    def is_educational_domain(cls, domain: str) -> bool:
        """
        Check if a domain is a known educational domain
        """
        domain = domain.lower()
        
        # Check direct matches
        if domain in cls.EDU_DOMAINS:
            return True
            
        # Check if domain ends with educational suffixes
        for edu_domain in cls.EDU_DOMAINS:
            if domain.endswith(f'.{edu_domain}'):
                return True
                
        return False
    
    @classmethod
    def extract_domain(cls, email: str) -> str:
        """
        Extract domain from email address
        """
        if '@' not in email:
            return ''
        return email.split('@')[1].lower()
    
    @classmethod
    def validate_email_format(cls, email: str) -> bool:
        """
        Validate email format using regex
        """
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @classmethod
    def check_domain_approval_status(cls, domain: str) -> Tuple[bool, Optional[str], Optional['School']]:
        """
        Check if a domain is approved for automatic verification
        
        Returns:
            (is_approved, school_name, school_object)
        """
        # Check if domain is in our pre-approved list
        if ApprovedDomain.is_domain_approved(domain):
            school_name, school_obj = ApprovedDomain.get_school_for_domain(domain)
            return True, school_name, school_obj
        
        # Check if it's a general educational domain
        if cls.is_educational_domain(domain):
            return True, None, None
            
        return False, None, None
    
    @classmethod
    def create_verification_request(
        cls, 
        user: User, 
        email: str, 
        school_name: str,
        ip_address: str = None,
        user_agent: str = None
    ) -> Tuple[VerificationRequest, bool]:
        """
        Create a verification request for a user and send verification code
        
        Returns:
            (verification_request, auto_approved)
        """
        if not cls.validate_email_format(email):
            raise ValueError("Invalid email format")
        
        # Check if user already has a verification request
        if hasattr(user, 'verification_request'):
            raise ValueError("User already has a pending verification request")
        
        domain = cls.extract_domain(email)
        is_approved, approved_school_name, school_obj = cls.check_domain_approval_status(domain)
        
        # Use approved school name if available, otherwise use provided name
        final_school_name = approved_school_name or school_name
        
        # Create verification request
        verification_request = VerificationRequest.objects.create(
            user=user,
            email=email,
            school_name=final_school_name,
            verification_method='email_domain'
        )
        
        # Log the attempt
        VerificationAttempt.objects.create(
            user=user,
            email=email,
            domain=domain,
            ip_address=ip_address,
            user_agent=user_agent,
            success=False  # Will be True when code is verified
        )
        
        # Generate and send verification code
        from .email_service import VerificationEmailService
        
        code = verification_request.generate_verification_code()
        email_sent = VerificationEmailService.send_verification_code(verification_request, code)
        
        if not email_sent:
            # If email fails, still allow the process but log it
            import logging
            logger = logging.getLogger('bopmaps')
            logger.error(f"Failed to send verification email to {email}")
            # Don't raise error, let user try again
        
        return verification_request, False  # Never auto-approved without code verification
    
    @classmethod
    def verify_email_code(cls, user: User, code: str) -> Tuple[bool, str, bool]:
        """
        Verify email with code and auto-approve if possible
        
        Returns:
            (success, message, auto_approved)
        """
        try:
            verification_request = user.verification_request
        except VerificationRequest.DoesNotExist:
            return False, "No verification request found", False
        
        success, message = verification_request.verify_email_with_code(code)
        
        if success:
            # Update attempt to success
            VerificationAttempt.objects.filter(
                user=user,
                email=verification_request.email
            ).update(success=True)
            
            # Send success email
            from .email_service import VerificationEmailService
            VerificationEmailService.send_verification_success(verification_request)
            
            # Check if auto-approved
            auto_approved = verification_request.status == 'approved'
            return True, message, auto_approved
        
        return False, message, False
    
    @classmethod
    def resend_verification_code(cls, user: User) -> Tuple[bool, str]:
        """
        Resend verification code to user's email
        
        Returns:
            (success, message)
        """
        try:
            verification_request = user.verification_request
        except VerificationRequest.DoesNotExist:
            return False, "No verification request found"
        
        if verification_request.email_verified:
            return False, "Email already verified"
        
        # Check rate limiting (max 3 codes per hour)
        recent_attempts = cls.get_recent_attempts(user, hours=1)
        if recent_attempts >= 3:
            return False, "Too many verification attempts. Please try again later."
        
        # Generate new code and send
        from .email_service import VerificationEmailService
        
        code = verification_request.generate_verification_code()
        email_sent = VerificationEmailService.send_verification_code(verification_request, code)
        
        if email_sent:
            return True, "Verification code sent successfully"
        else:
            return False, "Failed to send verification code"
    
    @classmethod
    def get_verification_status(cls, user: User) -> dict:
        """
        Get user's current verification status
        """
        try:
            request = user.verification_request
            return {
                'has_request': True,
                'status': request.status,
                'school_name': request.school_name,
                'email': request.email,
                'created_at': request.created_at,
                'updated_at': request.updated_at,
                'is_verified': user.school_email_verified,
                'school': user.school.name if user.school else None
            }
        except VerificationRequest.DoesNotExist:
            return {
                'has_request': False,
                'status': None,
                'school_name': None,
                'email': None,
                'created_at': None,
                'updated_at': None,
                'is_verified': user.school_email_verified,
                'school': user.school.name if user.school else None
            }
    
    @classmethod
    def can_user_verify(cls, user: User) -> Tuple[bool, str]:
        """
        Check if user can start verification process
        
        Returns:
            (can_verify, reason)
        """
        # Check if already verified
        if user.school_email_verified:
            return False, "User is already verified"
        
        # Check if has pending request
        if hasattr(user, 'verification_request'):
            request = user.verification_request
            if request.status == 'pending':
                return False, "User has a pending verification request"
            elif request.status == 'rejected':
                # Allow retry after 24 hours
                if request.updated_at and (timezone.now() - request.updated_at).days < 1:
                    return False, "Please wait 24 hours before trying again"
        
        return True, "User can start verification"
    
    @classmethod
    def get_recent_attempts(cls, user: User, hours: int = 24) -> int:
        """
        Get number of recent verification attempts for rate limiting
        """
        since = timezone.now() - timezone.timedelta(hours=hours)
        return VerificationAttempt.objects.filter(
            user=user,
            created_at__gte=since
        ).count()
    
    @classmethod
    def suggest_school_name(cls, domain: str) -> str:
        """
        Suggest a school name based on domain
        """
        # Check if we have this domain in our approved list
        school_name, _ = ApprovedDomain.get_school_for_domain(domain)
        if school_name:
            return school_name
        
        # Basic domain-to-name conversion
        # Remove common educational suffixes and convert to title case
        domain = domain.lower()
        
        # Remove educational suffixes
        for suffix in ['.edu', '.ac.uk', '.edu.au', '.ac.nz', '.edu.ca', '.ac.za']:
            if domain.endswith(suffix):
                domain = domain[:-len(suffix)]
                break
        
        # Convert to readable name
        parts = domain.split('.')
        if parts:
            # Take the main part (usually the first part)
            main_part = parts[0]
            # Convert to title case and add "University"
            return f"{main_part.title()} University"
        
        return "Unknown Institution" 