"""
URL configuration for verification app
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    VerificationViewSet,
    AdminVerificationViewSet,
    ApprovedDomainViewSet,
    check_domain,
    verification_info
)

app_name = 'verification'

# Create routers
router = DefaultRouter()
router.register(r'requests', VerificationViewSet, basename='verification-requests')

# Admin router
admin_router = DefaultRouter()
admin_router.register(r'admin/requests', AdminVerificationViewSet, basename='admin-verification-requests')
admin_router.register(r'admin/domains', ApprovedDomainViewSet, basename='admin-approved-domains')

urlpatterns = [
    # Public endpoints
    path('check-domain/', check_domain, name='check-domain'),
    path('info/', verification_info, name='verification-info'),
    
    # User endpoints
    path('', include(router.urls)),
    
    # Admin endpoints
    path('', include(admin_router.urls)),
] 