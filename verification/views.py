"""
API views for school verification
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django_ratelimit.decorators import ratelimit
import logging

from .models import VerificationRequest, ApprovedDomain, VerificationAttempt
from .serializers import (
    VerificationRequestSerializer,
    CreateVerificationRequestSerializer,
    VerificationStatusSerializer,
    DomainCheckSerializer,
    DomainCheckResponseSerializer,
    ApprovedDomainSerializer,
    AdminVerificationActionSerializer
)
from .services import VerificationService

User = get_user_model()
logger = logging.getLogger('bopmaps')


def get_client_ip(request):
    """Get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


class VerificationViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for verification requests - user can only see their own
    """
    serializer_class = VerificationRequestSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Users can only see their own verification requests"""
        return VerificationRequest.objects.filter(user=self.request.user)
    
    @action(detail=False, methods=['get'])
    def status(self, request):
        """
        Get user's current verification status
        """
        status_data = VerificationService.get_verification_status(request.user)
        serializer = VerificationStatusSerializer(
            status_data, 
            context={'user': request.user}
        )
        return Response(serializer.data)
    
    @method_decorator(ratelimit(key='user', rate='5/h', method='POST'))
    @action(detail=False, methods=['post'])
    def submit(self, request):
        """
        Submit a new verification request
        """
        # Check if user can verify
        can_verify, reason = VerificationService.can_user_verify(request.user)
        if not can_verify:
            return Response(
                {'error': reason},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check rate limiting
        recent_attempts = VerificationService.get_recent_attempts(request.user, hours=1)
        if recent_attempts >= 3:
            return Response(
                {'error': 'Too many verification attempts. Please try again later.'},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        
        # Validate request data
        serializer = CreateVerificationRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Create verification request
            verification_request, auto_approved = VerificationService.create_verification_request(
                user=request.user,
                email=serializer.validated_data['email'],
                school_name=serializer.validated_data['school_name'],
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
            
            # Prepare response
            response_data = {
                'id': verification_request.id,
                'status': verification_request.status,
                'school_name': verification_request.school_name,
                'email': verification_request.email,
                'auto_approved': auto_approved,
                'message': 'Verification request submitted successfully'
            }
            
            if auto_approved:
                response_data['message'] = 'Your school email has been automatically verified!'
                logger.info(f"Auto-approved verification for {request.user.username} at {verification_request.school_name}")
            else:
                response_data['message'] = 'Your verification request is pending review'
                logger.info(f"Verification request pending for {request.user.username} at {verification_request.school_name}")
            
            return Response(response_data, status=status.HTTP_201_CREATED)
            
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Error creating verification request for {request.user.username}: {str(e)}")
            return Response(
                {'error': 'An error occurred while processing your request'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['delete'])
    def cancel(self, request, pk=None):
        """
        Cancel a pending verification request
        """
        try:
            verification_request = self.get_object()
            
            if verification_request.status not in ['pending']:
                return Response(
                    {'error': 'Can only cancel pending requests'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            verification_request.delete()
            logger.info(f"Verification request cancelled by {request.user.username}")
            
            return Response({'message': 'Verification request cancelled'})
            
        except VerificationRequest.DoesNotExist:
            return Response(
                {'error': 'Verification request not found'},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['post'])
    def verify_code(self, request):
        """
        Verify email with 6-digit code
        """
        code = request.data.get('code', '').strip()
        
        if not code:
            return Response(
                {'error': 'Verification code is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if len(code) != 6 or not code.isdigit():
            return Response(
                {'error': 'Verification code must be exactly 6 digits'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        success, message, auto_approved = VerificationService.verify_email_code(request.user, code)
        
        if success:
            status_code = status.HTTP_200_OK
            response_data = {
                'message': message,
                'verified': True,
                'auto_approved': auto_approved
            }
            
            if auto_approved:
                response_data['message'] = 'Email verified and school verification complete!'
            
            logger.info(f"Email verified for {request.user.username} with code")
        else:
            status_code = status.HTTP_400_BAD_REQUEST
            response_data = {
                'error': message,
                'verified': False
            }
        
        return Response(response_data, status=status_code)
    
    @action(detail=False, methods=['post'])
    def resend_code(self, request):
        """
        Resend verification code to email
        """
        success, message = VerificationService.resend_verification_code(request.user)
        
        if success:
            logger.info(f"Verification code resent for {request.user.username}")
            return Response({'message': message})
        else:
            return Response(
                {'error': message},
                status=status.HTTP_400_BAD_REQUEST
            )


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
@method_decorator(ratelimit(key='ip', rate='10/m', method='POST'))
def check_domain(request):
    """
    Check if an email domain is pre-approved for verification
    Public endpoint for frontend to provide instant feedback
    """
    serializer = DomainCheckSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    domain = serializer.validated_data['domain']
    
    # Check domain status
    is_approved, school_name, school_obj = VerificationService.check_domain_approval_status(domain)
    is_educational = VerificationService.is_educational_domain(domain)
    suggested_name = VerificationService.suggest_school_name(domain)
    
    response_data = {
        'domain': domain,
        'is_approved': is_approved,
        'is_educational': is_educational,
        'school_name': school_name,
        'suggested_name': suggested_name,
        'auto_approval': is_approved
    }
    
    response_serializer = DomainCheckResponseSerializer(response_data)
    return Response(response_serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
@method_decorator(cache_page(60 * 15))  # Cache for 15 minutes
def verification_info(request):
    """
    Get general information about the verification process
    """
    return Response({
        'supported_domains': list(VerificationService.EDU_DOMAINS),
        'auto_approval_info': {
            'edu_domains': 'Automatically approved',
            'custom_domains': 'May require manual review',
            'processing_time': 'Instant for .edu, up to 24 hours for others'
        },
        'requirements': [
            'Must be a valid school email address',
            'School must be an accredited educational institution',
            'One verification per user account'
        ],
        'benefits': [
            'Access to school-specific leaderboards',
            'Participate in campus challenges',
            'Connect with fellow students',
            'School verification badge'
        ]
    })


# Admin-only views
class AdminVerificationViewSet(viewsets.ModelViewSet):
    """
    Admin viewset for managing verification requests
    """
    serializer_class = VerificationRequestSerializer
    permission_classes = [permissions.IsAdminUser]
    
    def get_queryset(self):
        """Get all verification requests for admin"""
        return VerificationRequest.objects.all().select_related(
            'user', 'reviewed_by'
        ).order_by('-created_at')
    
    @action(detail=False, methods=['get'])
    def pending(self, request):
        """Get pending verification requests"""
        pending_requests = self.get_queryset().filter(status='pending')
        serializer = self.get_serializer(pending_requests, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def review(self, request, pk=None):
        """
        Approve or reject a verification request
        """
        verification_request = self.get_object()
        
        if verification_request.status != 'pending':
            return Response(
                {'error': 'Request has already been reviewed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = AdminVerificationActionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        action = serializer.validated_data['action']
        notes = serializer.validated_data.get('notes', '')
        
        if action == 'approve':
            verification_request.approve(admin_user=request.user, notes=notes)
            logger.info(f"Verification approved by {request.user.username} for {verification_request.user.username}")
            message = 'Verification request approved'
        else:
            verification_request.reject(admin_user=request.user, notes=notes)
            logger.info(f"Verification rejected by {request.user.username} for {verification_request.user.username}")
            message = 'Verification request rejected'
        
        return Response({
            'message': message,
            'status': verification_request.status,
            'notes': verification_request.admin_notes
        })


class ApprovedDomainViewSet(viewsets.ModelViewSet):
    """
    Admin viewset for managing approved domains
    """
    queryset = ApprovedDomain.objects.all()
    serializer_class = ApprovedDomainSerializer
    permission_classes = [permissions.IsAdminUser]
    
    def perform_create(self, serializer):
        """Set the admin user who added the domain"""
        serializer.save(added_by=self.request.user)
