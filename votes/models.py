from django.db import models
from django.conf import settings
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.db.models import Sum, Count
from django.utils import timezone
import logging

logger = logging.getLogger('bopmaps')

class Vote(models.Model):
    """
    Model for handling pin votes (upvotes/downvotes)
    """
    VOTE_CHOICES = [
        (1, 'Upvote'),
        (-1, 'Downvote')
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='pin_votes'
    )
    pin = models.ForeignKey(
        'pins.Pin',
        on_delete=models.CASCADE,
        related_name='votes'
    )
    value = models.SmallIntegerField(
        choices=VOTE_CHOICES,
        validators=[MinValueValidator(-1), MaxValueValidator(1)]
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('user', 'pin')
        indexes = [
            models.Index(fields=['created_at']),
            models.Index(fields=['value']),
        ]
        
    def __str__(self):
        return f"{self.user.username} {'upvoted' if self.value == 1 else 'downvoted'} {self.pin.title}"
    
    def save(self, *args, **kwargs):
        """Override save to update pin's vote counts"""
        is_new = self._state.adding
        
        if not is_new:
            # If this is an update, get the old value
            old_vote = Vote.objects.get(pk=self.pk)
            if old_vote.value == self.value:
                # No change in vote value, just return
                return super().save(*args, **kwargs)
        
        # Save the vote
        super().save(*args, **kwargs)
        
        try:
            # Update pin's vote counts
            self.update_pin_vote_counts()
        except Exception as e:
            logger.error(f"Error updating pin vote counts: {str(e)}")
            
    def update_pin_vote_counts(self):
        """Update the pin's vote counts"""
        pin = self.pin
        
        # Calculate vote counts
        vote_counts = Vote.objects.filter(pin=pin).aggregate(
            upvotes=Count('id', filter=models.Q(value=1)),
            downvotes=Count('id', filter=models.Q(value=-1))
        )
        
        # Update pin's vote counts
        pin.upvote_count = vote_counts['upvotes']
        pin.downvote_count = vote_counts['downvotes']
        pin.vote_score = pin.upvote_count - pin.downvote_count
        pin.save(update_fields=['upvote_count', 'downvote_count', 'vote_score'])
        
    @classmethod
    def get_pin_vote_stats(cls, pin):
        """Get voting statistics for a pin"""
        stats = cls.objects.filter(pin=pin).aggregate(
            total_votes=Count('id'),
            upvotes=Count('id', filter=models.Q(value=1)),
            downvotes=Count('id', filter=models.Q(value=-1))
        )
        
        stats['score'] = stats['upvotes'] - stats['downvotes']
        if stats['total_votes'] > 0:
            stats['upvote_ratio'] = stats['upvotes'] / stats['total_votes']
        else:
            stats['upvote_ratio'] = 0
            
        return stats
            
    @classmethod
    def get_user_upvoted_pins(cls, user, ordering='recent'):
        """
        Get all pins that a user has upvoted
        
        Args:
            user: The user to get upvoted pins for
            ordering: 'recent' (default) or 'oldest'
            
        Returns:
            A queryset of Vote objects for the user's upvotes, ordered appropriately
        """
        votes = cls.objects.filter(
            user=user,
            value=1  # Only upvotes
        ).select_related(
            'pin',
            'pin__owner',
            'pin__skin'
        )
        
        # Apply ordering
        if ordering == 'oldest':
            votes = votes.order_by('created_at')
        else:  # default to recent
            votes = votes.order_by('-created_at')
            
        return votes