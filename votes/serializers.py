from rest_framework import serializers
from .models import Vote
from django.contrib.auth import get_user_model

User = get_user_model()

class VoteSerializer(serializers.ModelSerializer):
    """
    Serializer for Vote model
    """
    user = serializers.PrimaryKeyRelatedField(read_only=True)
    
    class Meta:
        model = Vote
        fields = ['id', 'user', 'pin', 'value', 'created_at', 'updated_at']
        read_only_fields = ['id', 'user', 'created_at', 'updated_at']
        
    def validate(self, data):
        """
        Check that users cannot vote on their own pins
        """
        pin = data.get('pin')
        user = self.context['request'].user
        
        if pin.owner == user:
            raise serializers.ValidationError('You cannot vote on your own pins.')
            
        return data
        
    def create(self, validated_data):
        """
        Create or update vote
        """
        user = self.context['request'].user
        pin = validated_data['pin']
        value = validated_data['value']
        
        # Try to get existing vote
        vote, created = Vote.objects.get_or_create(
            user=user,
            pin=pin,
            defaults={'value': value}
        )
        
        if not created and vote.value != value:
            # Update existing vote if value changed
            vote.value = value
            vote.save()
            
        return vote 