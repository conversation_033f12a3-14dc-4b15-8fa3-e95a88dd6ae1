from django.test import TestCase, TransactionTestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, APIClient
from django.contrib.auth import get_user_model
from django.contrib.gis.geos import Point
from django.db import transaction, connection
from django.test.utils import CaptureQueriesContext
from .models import Vote
from pins.models import Pin
from gamification.models import PinSkin

User = get_user_model()

class VoteTests(TransactionTestCase):
    reset_sequences = True  # This ensures primary keys start at 1 in each test
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Any class-level setup if needed
    
    @classmethod
    def tearDownClass(cls):
        super().tearDownClass()
        # Any class-level cleanup if needed
    
    def setUp(self):
        # Clean up any existing data first
        with transaction.atomic():
            Vote.objects.all().delete()
            Pin.objects.all().delete()
            PinSkin.objects.all().delete()
            User.objects.all().delete()
        
        # Create test users
        self.user1 = User.objects.create_user(username='user1', email='<EMAIL>', password='password123')
        self.user2 = User.objects.create_user(username='user2', email='<EMAIL>', password='password123')
        
        # Create a default PinSkin
        self.default_skin = PinSkin.objects.create(
            id=1, 
            name='Default Skin',
            image='skins/default.png'
        )
        
        # Create test pins
        self.pin_user1 = Pin.objects.create(
            owner=self.user1,
            location=Point(-73.985130, 40.758896),
            title='User1 Pin',
            track_title='Test Track 1',
            track_artist='Test Artist 1',
            track_url='http://example.com/track1',
            service='spotify',
            skin=self.default_skin
        )
        
        self.pin_user2 = Pin.objects.create(
            owner=self.user2,
            location=Point(-73.985130, 40.758896),
            title='User2 Pin',
            track_title='Test Track 2',
            track_artist='Test Artist 2',
            track_url='http://example.com/track2',
            service='spotify',
            skin=self.default_skin
        )
        
        # Set up client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user1)
        
        # URLs
        self.list_create_url = reverse('vote-list')
        self.stats_url = reverse('vote-stats')

    def tearDown(self):
        # Clean up all test data
        with transaction.atomic():
            Vote.objects.all().delete()
            Pin.objects.all().delete()
            PinSkin.objects.all().delete()
            User.objects.all().delete()
        
        # Reset sequences for PostgreSQL
        with connection.cursor() as cursor:
            try:
                cursor.execute("ALTER SEQUENCE votes_vote_id_seq RESTART WITH 1;")
                cursor.execute("ALTER SEQUENCE pins_pin_id_seq RESTART WITH 1;")
                cursor.execute("ALTER SEQUENCE gamification_pinskin_id_seq RESTART WITH 1;")
                cursor.execute("ALTER SEQUENCE auth_user_id_seq RESTART WITH 1;")
            except Exception:
                # If not PostgreSQL or sequence doesn't exist, ignore the error
                pass

    def test_create_upvote(self):
        """Test creating an upvote"""
        with transaction.atomic():
            data = {
                'pin': self.pin_user2.id,
                'value': 1
            }
            response = self.client.post(self.list_create_url, data)
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            self.assertEqual(response.data['value'], 1)
            self.assertEqual(response.data['user'], self.user1.id)
            
    def test_create_downvote(self):
        """Test creating a downvote"""
        with transaction.atomic():
            data = {
                'pin': self.pin_user2.id,
                'value': -1
            }
            response = self.client.post(self.list_create_url, data)
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            self.assertEqual(response.data['value'], -1)
            
    def test_update_vote(self):
        """Test updating a vote"""
        with transaction.atomic():
            data = {
                'pin': self.pin_user2.id,
                'value': 1
            }
            response = self.client.post(self.list_create_url, data)
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            
    def test_invalid_vote_value(self):
        """Test creating a vote with invalid value"""
        with transaction.atomic():
            data = {
                'pin': self.pin_user2.id,
                'value': 2  # Only -1 and 1 are valid
            }
            response = self.client.post(self.list_create_url, data)
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            
    def test_vote_own_pin(self):
        """Test attempting to vote on own pin"""
        with transaction.atomic():
            data = {
                'pin': self.pin_user1.id,  # User1's own pin
                'value': 1
            }
            response = self.client.post(self.list_create_url, data)
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            
    def test_list_user_votes(self):
        """Test listing user's votes"""
        # Clean up any existing votes
        Vote.objects.all().delete()
        
        # Create a test vote
        vote = Vote.objects.create(
            user=self.user1,
            pin=self.pin_user2,
            value=1
        )
        
        # Create another vote for user2
        Vote.objects.create(
            user=self.user2,
            pin=self.pin_user1,
            value=1
        )
        
        # Print all votes for debugging
        print("\nAll votes in database:")
        for vote in Vote.objects.all():
            print(f"Vote {vote.id}: {vote.value} by {vote.user.username} on pin {vote.pin.id}")
        
        response = self.client.get(self.list_create_url)
        
        # Print response data for debugging
        print("\nResponse data:")
        if isinstance(response.data, dict) and 'results' in response.data:
            for item in response.data['results']:
                print(f"Vote {item.get('id')}: {item.get('value')} by user {item.get('user')} on pin {item.get('pin')}")
        else:
            print(f"Response data is not paginated: {response.data}")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)  # Total count should be 1
        self.assertEqual(len(response.data['results']), 1)  # Should only see user1's vote
        self.assertEqual(response.data['results'][0]['pin'], self.pin_user2.id)
        self.assertEqual(response.data['results'][0]['value'], 1)
            
    def test_retrieve_vote(self):
        """Test retrieving a single vote"""
        # Create a test vote
        vote = Vote.objects.create(
            user=self.user1,
            pin=self.pin_user2,
            value=1
        )
        detail_url = reverse('vote-detail', kwargs={'pk': vote.pk})
        
        response = self.client.get(detail_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['value'], 1)

    def test_vote_stats(self):
        """Test getting vote statistics for a pin"""
        # Create a test vote
        vote = Vote.objects.create(
            user=self.user1,
            pin=self.pin_user2,
            value=1
        )
        
        # Create another vote
        Vote.objects.create(
            user=self.user2,
            pin=self.pin_user1,
            value=1
        )
        
        # Get stats for pin_user2
        response = self.client.get(f"{self.stats_url}?pin_id={self.pin_user2.id}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['upvotes'], 1)
        self.assertEqual(response.data['downvotes'], 0)
        self.assertEqual(response.data['score'], 1)
        self.assertEqual(response.data['upvote_ratio'], 1.0)
        self.assertEqual(response.data['user_vote'], 1)

    def test_vote_stats_no_pin(self):
        """Test getting vote stats without pin_id"""
        with transaction.atomic():
            response = self.client.get(self.stats_url)
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            
    def test_vote_stats_invalid_pin(self):
        """Test getting vote stats for non-existent pin"""
        with transaction.atomic():
            response = self.client.get(f"{self.stats_url}?pin_id=999")
            self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_pin_vote_counts_update(self):
        """Test that pin's vote counts are updated when votes change"""
        # Clean up any existing votes
        Vote.objects.all().delete()
        
        # Create initial vote
        vote = Vote.objects.create(
            user=self.user1,
            pin=self.pin_user2,
            value=1
        )
        
        # Add another vote
        Vote.objects.create(
            user=self.user2,
            pin=self.pin_user1,
            value=1
        )
        
        # Change vote from upvote to downvote
        data = {
            'pin': self.pin_user2.id,
            'value': -1
        }
        response = self.client.post(self.list_create_url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Refresh pin from database
        self.pin_user2.refresh_from_db()
        
        # Check vote counts
        self.assertEqual(self.pin_user2.upvote_count, 0)
        self.assertEqual(self.pin_user2.downvote_count, 1)
        self.assertEqual(self.pin_user2.vote_score, -1)
