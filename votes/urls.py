from django.urls import path, include
from rest_framework.routers import <PERSON>faultRouter
from .views import VoteViewSet

# Create a router and register our viewset
router = DefaultRouter()
router.register(r'', VoteViewSet, basename='votes')

urlpatterns = [
    # ViewSet routes
    path('', include(router.urls)),
    
    # The upvoted_pins endpoint will be available at:
    # /api/votes/upvoted_pins/
] 