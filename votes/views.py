from django.shortcuts import render
from rest_framework import viewsets, status, mixins
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db import transaction
from django.utils import timezone
from django.db.models import Q
import logging

from .models import Vote
from .serializers import VoteSerializer
from pins.models import Pin
from pins.serializers import PinSerializer
from bopmaps.utils import create_error_response

logger = logging.getLogger('bopmaps')

class VoteViewSet(mixins.CreateModelMixin,
                 mixins.ListModelMixin,
                 mixins.RetrieveModelMixin,
                 viewsets.GenericViewSet):
    """
    API viewset for Vote operations
    Limited to create, list, and retrieve operations
    """
    serializer_class = VoteSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Filter queryset to only show user's votes"""
        # Start with base queryset
        queryset = Vote.objects.all()
        
        # Filter to only show current user's votes
        queryset = queryset.filter(user=self.request.user)
        
        # Order by created_at
        return queryset.order_by('-created_at')
    
    def perform_create(self, serializer):
        """Create or update vote"""
        try:
            with transaction.atomic():
                vote = serializer.save(user=self.request.user)
                logger.info(f"Vote created/updated: {vote}")
        except Exception as e:
            logger.error(f"Error creating/updating vote: {str(e)}")
            raise
            
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get voting statistics for a pin"""
        pin_id = request.query_params.get('pin_id')
        if not pin_id:
            return Response(
                {"error": "pin_id parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            pin = Pin.objects.get(id=pin_id)
            stats = Vote.get_pin_vote_stats(pin)
            
            # Add user's vote if exists
            user_vote = Vote.objects.filter(user=request.user, pin=pin).first()
            stats['user_vote'] = user_vote.value if user_vote else None
            
            return Response(stats)
        except Pin.DoesNotExist:
            return Response(
                {"error": "Pin not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error getting vote stats: {str(e)}")
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class VoteViewSet(viewsets.ModelViewSet):
    """
    API viewset for handling votes and retrieving voted pins
    """
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['GET'])
    def upvoted_pins(self, request):
        """
        Get all pins that the current user has upvoted
        
        Query Parameters:
        - ordering: recent (default), oldest
        - page, page_size: for pagination
        """
        try:
            # Get pins the user has upvoted
            upvoted_pins = request.user.pin_votes.filter(
                value=1  # Only upvotes
            ).select_related(
                'pin',
                'pin__owner',
                'pin__skin'
            ).order_by(
                '-created_at'  # Most recent first by default
            )
            
            # Apply ordering if specified
            ordering = request.query_params.get('ordering', 'recent').lower()
            if ordering == 'oldest':
                upvoted_pins = upvoted_pins.order_by('created_at')
            elif ordering != 'recent':
                logger.warning(f"Invalid ordering parameter '{ordering}' provided to upvoted_pins. Using default 'recent'.")
            
            # Get the pins from the votes
            pins = [vote.pin for vote in upvoted_pins]
            
            # Serialize the pins
            serializer = PinSerializer(pins, many=True, context={'request': request})
            
            # Add vote timestamps to the response
            response_data = serializer.data
            for i, pin_data in enumerate(response_data):
                pin_data['upvoted_at'] = upvoted_pins[i].created_at
            
            return Response({
                'results': response_data,
                'sorting': {
                    'current_ordering': ordering,
                    'available_options': ['recent', 'oldest']
                }
            })
            
        except Exception as e:
            logger.error(f"Error retrieving upvoted pins: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
