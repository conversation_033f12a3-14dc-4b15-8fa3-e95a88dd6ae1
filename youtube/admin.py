from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import YouTubeCacheEntry, YouTubeApiUsage, YouTubeRateLimit


@admin.register(YouTubeCacheEntry)
class YouTubeCacheEntryAdmin(admin.ModelAdmin):
    """Admin interface for YouTube cache entries"""
    
    list_display = [
        'cache_key_short', 'endpoint', 'created_at', 'expires_at', 
        'hit_count', 'is_expired_display', 'size_estimate'
    ]
    list_filter = ['endpoint', 'created_at', 'expires_at']
    search_fields = ['cache_key', 'endpoint', 'query_hash']
    readonly_fields = [
        'cache_key', 'query_hash', 'created_at', 'updated_at', 
        'last_accessed', 'response_data_preview'
    ]
    ordering = ['-last_accessed']
    
    fieldsets = (
        ('Cache Information', {
            'fields': ('cache_key', 'endpoint', 'query_hash')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'expires_at', 'last_accessed')
        }),
        ('Usage Statistics', {
            'fields': ('hit_count',)
        }),
        ('Response Data', {
            'fields': ('response_data_preview',),
            'classes': ('collapse',)
        }),
    )
    
    def cache_key_short(self, obj):
        """Display shortened cache key"""
        return f"{obj.cache_key[:30]}..." if len(obj.cache_key) > 30 else obj.cache_key
    cache_key_short.short_description = 'Cache Key'
    
    def is_expired_display(self, obj):
        """Display expiration status with color"""
        if obj.is_expired:
            return format_html('<span style="color: red;">Expired</span>')
        else:
            return format_html('<span style="color: green;">Active</span>')
    is_expired_display.short_description = 'Status'
    
    def size_estimate(self, obj):
        """Estimate size of cached data"""
        import sys
        size_bytes = sys.getsizeof(str(obj.response_data))
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
    size_estimate.short_description = 'Size'
    
    def response_data_preview(self, obj):
        """Show preview of response data"""
        import json
        try:
            formatted_json = json.dumps(obj.response_data, indent=2)[:1000]
            if len(formatted_json) >= 1000:
                formatted_json += "..."
            return format_html('<pre>{}</pre>', formatted_json)
        except:
            return str(obj.response_data)[:1000]
    response_data_preview.short_description = 'Response Data Preview'
    
    actions = ['delete_expired_entries', 'clear_hit_counts']
    
    def delete_expired_entries(self, request, queryset):
        """Delete expired cache entries"""
        expired_count = queryset.filter(expires_at__lt=timezone.now()).delete()[0]
        self.message_user(request, f"Deleted {expired_count} expired cache entries.")
    delete_expired_entries.short_description = "Delete expired entries"
    
    def clear_hit_counts(self, request, queryset):
        """Reset hit counts to zero"""
        updated_count = queryset.update(hit_count=0)
        self.message_user(request, f"Reset hit counts for {updated_count} entries.")
    clear_hit_counts.short_description = "Clear hit counts"


@admin.register(YouTubeApiUsage)
class YouTubeApiUsageAdmin(admin.ModelAdmin):
    """Admin interface for YouTube API usage statistics"""
    
    list_display = [
        'date', 'endpoint', 'total_requests', 'cache_hits', 
        'cache_hit_rate_display', 'api_calls_made', 'average_response_time'
    ]
    list_filter = ['date', 'endpoint']
    search_fields = ['endpoint']
    readonly_fields = ['cache_hit_rate']
    ordering = ['-date', 'endpoint']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('date', 'endpoint')
        }),
        ('Request Statistics', {
            'fields': ('total_requests', 'cache_hits', 'cache_misses', 'cache_hit_rate')
        }),
        ('API Statistics', {
            'fields': ('api_calls_made', 'average_response_time', 'unique_users')
        }),
    )
    
    def cache_hit_rate_display(self, obj):
        """Display cache hit rate with color coding"""
        rate = obj.cache_hit_rate
        if rate >= 80:
            color = 'green'
        elif rate >= 60:
            color = 'orange'
        else:
            color = 'red'
        return format_html('<span style="color: {};">{:.1f}%</span>', color, rate)
    cache_hit_rate_display.short_description = 'Hit Rate'
    
    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related()


@admin.register(YouTubeRateLimit)
class YouTubeRateLimitAdmin(admin.ModelAdmin):
    """Admin interface for YouTube rate limit tracking"""
    
    list_display = [
        'window_start', 'endpoint_display', 'user_ip', 'requests_made', 'time_ago'
    ]
    list_filter = ['window_start', 'endpoint', 'user_ip']
    search_fields = ['endpoint', 'user_ip']
    ordering = ['-window_start']
    
    fieldsets = (
        ('Rate Limit Information', {
            'fields': ('window_start', 'endpoint', 'user_ip', 'requests_made')
        }),
    )
    
    def endpoint_display(self, obj):
        """Display endpoint or global indicator"""
        return obj.endpoint or '(Global)'
    endpoint_display.short_description = 'Endpoint'
    
    def time_ago(self, obj):
        """Show how long ago this window was"""
        now = timezone.now()
        diff = now - obj.window_start
        
        if diff.total_seconds() < 3600:  # Less than 1 hour
            minutes = int(diff.total_seconds() / 60)
            return f"{minutes}m ago"
        elif diff.total_seconds() < 86400:  # Less than 1 day
            hours = int(diff.total_seconds() / 3600)
            return f"{hours}h ago"
        else:
            days = diff.days
            return f"{days}d ago"
    time_ago.short_description = 'Age'
    
    actions = ['delete_old_records']
    
    def delete_old_records(self, request, queryset):
        """Delete rate limit records older than 24 hours"""
        cutoff = timezone.now() - timezone.timedelta(hours=24)
        old_count = queryset.filter(window_start__lt=cutoff).delete()[0]
        self.message_user(request, f"Deleted {old_count} old rate limit records.")
    delete_old_records.short_description = "Delete records older than 24h"
