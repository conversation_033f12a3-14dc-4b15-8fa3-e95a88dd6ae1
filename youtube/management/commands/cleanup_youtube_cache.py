from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.db.models import Count, Sum
from youtube.models import YouTubeCacheEntry
import sys


class Command(BaseCommand):
    help = 'Clean up expired YouTube cache entries and manage cache size'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Delete all cache entries (not just expired ones)',
        )
        parser.add_argument(
            '--endpoint',
            type=str,
            help='Only clean cache entries for specific endpoint',
        )
        parser.add_argument(
            '--older-than-days',
            type=int,
            default=0,
            help='Delete entries older than specified days (0 = only expired)',
        )
        parser.add_argument(
            '--keep-top',
            type=int,
            help='Keep top N most accessed entries',
        )
    
    def handle(self, *args, **options):
        dry_run = options['dry_run']
        delete_all = options['all']
        endpoint_filter = options['endpoint']
        older_than_days = options['older_than_days']
        keep_top = options['keep_top']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No entries will be deleted')
            )
        
        # Build queryset
        queryset = YouTubeCacheEntry.objects.all()
        
        if endpoint_filter:
            queryset = queryset.filter(endpoint=endpoint_filter)
            self.stdout.write(f'Filtering by endpoint: {endpoint_filter}')
        
        if delete_all:
            entries_to_delete = queryset
            self.stdout.write('Selecting ALL cache entries for deletion')
        elif older_than_days > 0:
            cutoff_date = timezone.now() - timezone.timedelta(days=older_than_days)
            entries_to_delete = queryset.filter(created_at__lt=cutoff_date)
            self.stdout.write(f'Selecting entries older than {older_than_days} days')
        else:
            entries_to_delete = queryset.filter(expires_at__lt=timezone.now())
            self.stdout.write('Selecting expired cache entries')
        
        # Apply keep-top filter
        if keep_top and not delete_all:
            # Get IDs of top entries to keep
            top_entries = queryset.order_by('-hit_count')[:keep_top].values_list('id', flat=True)
            entries_to_delete = entries_to_delete.exclude(id__in=top_entries)
            self.stdout.write(f'Keeping top {keep_top} most accessed entries')
        
        # Get statistics before deletion
        total_count = entries_to_delete.count()
        
        if total_count == 0:
            self.stdout.write(
                self.style.SUCCESS('No cache entries found matching criteria')
            )
            return
        
        # Show statistics
        stats = entries_to_delete.aggregate(
            total_hits=Sum('hit_count'),
            total_entries=Count('id')
        )
        
        # Estimate size
        sample_entries = list(entries_to_delete[:10])
        if sample_entries:
            avg_size = sum(sys.getsizeof(str(entry.response_data)) for entry in sample_entries) / len(sample_entries)
            estimated_size_mb = (avg_size * total_count) / (1024 * 1024)
        else:
            estimated_size_mb = 0
        
        self.stdout.write(f'Found {total_count} entries to delete')
        self.stdout.write(f'Total hits: {stats["total_hits"] or 0}')
        self.stdout.write(f'Estimated size: {estimated_size_mb:.2f} MB')
        
        # Show breakdown by endpoint
        endpoint_stats = entries_to_delete.values('endpoint').annotate(
            count=Count('id'),
            hits=Sum('hit_count')
        ).order_by('-count')
        
        if endpoint_stats:
            self.stdout.write('\nBreakdown by endpoint:')
            for stat in endpoint_stats:
                self.stdout.write(
                    f"  {stat['endpoint']}: {stat['count']} entries, {stat['hits'] or 0} hits"
                )
        
        if not dry_run:
            # Confirm deletion unless --all flag is used
            if not delete_all and total_count > 100:
                confirm = input(f'\nDelete {total_count} cache entries? [y/N]: ')
                if confirm.lower() != 'y':
                    self.stdout.write('Deletion cancelled')
                    return
            
            # Perform deletion
            deleted_count, deleted_details = entries_to_delete.delete()
            
            self.stdout.write(
                self.style.SUCCESS(f'Successfully deleted {deleted_count} cache entries')
            )
            
            # Show what was deleted
            if deleted_details:
                for model, count in deleted_details.items():
                    if count > 0:
                        self.stdout.write(f'  {model}: {count}')
        else:
            self.stdout.write('\nDRY RUN - No entries were deleted')
        
        # Show remaining cache statistics
        remaining_count = YouTubeCacheEntry.objects.count()
        active_count = YouTubeCacheEntry.objects.filter(
            expires_at__gt=timezone.now()
        ).count()
        
        self.stdout.write(f'\nRemaining cache entries: {remaining_count}')
        self.stdout.write(f'Active (non-expired): {active_count}')
        self.stdout.write(f'Expired: {remaining_count - active_count}')