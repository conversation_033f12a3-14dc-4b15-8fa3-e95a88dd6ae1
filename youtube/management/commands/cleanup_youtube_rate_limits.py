from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Count
from youtube.models import YouTubeRateLimit
from datetime import timedelta


class Command(BaseCommand):
    help = 'Clean up old YouTube rate limit tracking records'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )
        parser.add_argument(
            '--older-than-hours',
            type=int,
            default=24,
            help='Delete rate limit records older than specified hours (default: 24)',
        )
        parser.add_argument(
            '--keep-recent',
            type=int,
            default=100,
            help='Keep at least this many recent records (default: 100)',
        )
    
    def handle(self, *args, **options):
        dry_run = options['dry_run']
        older_than_hours = options['older_than_hours']
        keep_recent = options['keep_recent']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No records will be deleted')
            )
        
        # Calculate cutoff time
        cutoff_time = timezone.now() - timedelta(hours=older_than_hours)
        
        # Get records to delete (older than cutoff)
        old_records = YouTubeRateLimit.objects.filter(
            window_start__lt=cutoff_time
        )
        
        # Get total count before applying keep_recent filter
        total_old_count = old_records.count()
        
        # If we have keep_recent specified, make sure we don't delete too many
        if keep_recent > 0:
            total_records = YouTubeRateLimit.objects.count()
            if total_records - total_old_count < keep_recent:
                # Calculate how many we can actually delete
                can_delete = max(0, total_records - keep_recent)
                if can_delete < total_old_count:
                    # Get the oldest records we can delete
                    old_records = YouTubeRateLimit.objects.order_by('window_start')[:can_delete]
                    self.stdout.write(
                        f'Limiting deletion to {can_delete} records to keep {keep_recent} recent records'
                    )
        
        records_to_delete_count = old_records.count()
        
        if records_to_delete_count == 0:
            self.stdout.write(
                self.style.SUCCESS('No old rate limit records found to delete')
            )
            return
        
        # Show statistics
        self.stdout.write(f'Found {records_to_delete_count} rate limit records to delete')
        self.stdout.write(f'Records older than {older_than_hours} hours')
        
        # Show breakdown by endpoint and IP
        endpoint_stats = old_records.values('endpoint').annotate(
            count=Count('id')
        ).order_by('-count')
        
        ip_stats = old_records.exclude(user_ip__isnull=True).values('user_ip').annotate(
            count=Count('id')
        ).order_by('-count')[:10]  # Top 10 IPs
        
        if endpoint_stats:
            self.stdout.write('\nBreakdown by endpoint:')
            for stat in endpoint_stats:
                endpoint_name = stat['endpoint'] or '(global)'
                self.stdout.write(f"  {endpoint_name}: {stat['count']} records")
        
        if ip_stats:
            self.stdout.write('\nTop IPs by record count:')
            for stat in ip_stats:
                self.stdout.write(f"  {stat['user_ip']}: {stat['count']} records")
        
        # Show time range
        if records_to_delete_count > 0:
            oldest_record = old_records.order_by('window_start').first()
            newest_record = old_records.order_by('-window_start').first()
            
            if oldest_record and newest_record:
                self.stdout.write(f'\nTime range: {oldest_record.window_start} to {newest_record.window_start}')
        
        if not dry_run:
            # Confirm deletion if deleting many records
            if records_to_delete_count > 1000:
                confirm = input(f'\nDelete {records_to_delete_count} rate limit records? [y/N]: ')
                if confirm.lower() != 'y':
                    self.stdout.write('Deletion cancelled')
                    return
            
            # Perform deletion
            deleted_count, deleted_details = old_records.delete()
            
            self.stdout.write(
                self.style.SUCCESS(f'Successfully deleted {deleted_count} rate limit records')
            )
            
            # Show what was deleted
            if deleted_details:
                for model, count in deleted_details.items():
                    if count > 0:
                        self.stdout.write(f'  {model}: {count}')
        else:
            self.stdout.write('\nDRY RUN - No records were deleted')
        
        # Show remaining statistics
        remaining_count = YouTubeRateLimit.objects.count()
        recent_count = YouTubeRateLimit.objects.filter(
            window_start__gte=timezone.now() - timedelta(hours=1)
        ).count()
        
        self.stdout.write(f'\nRemaining rate limit records: {remaining_count}')
        self.stdout.write(f'Records from last hour: {recent_count}')
        
        # Show current rate limit status
        current_window = timezone.now().replace(second=0, microsecond=0)
        current_limits = YouTubeRateLimit.objects.filter(
            window_start=current_window
        )
        
        if current_limits.exists():
            self.stdout.write(f'\nCurrent window ({current_window}):')
            for limit in current_limits:
                endpoint_name = limit.endpoint or '(global)'
                ip_info = f' - IP: {limit.user_ip}' if limit.user_ip else ''
                self.stdout.write(f'  {endpoint_name}: {limit.requests_made} requests{ip_info}')
        else:
            self.stdout.write(f'\nNo rate limit records for current window ({current_window})')