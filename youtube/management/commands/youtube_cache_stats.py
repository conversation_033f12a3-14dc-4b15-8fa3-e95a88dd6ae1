from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Count, Sum, Avg, Max, Min
from youtube.models import YouTubeCacheEntry, YouTubeApiUsage, YouTubeRateLimit
from datetime import timedelta
import sys


class Command(BaseCommand):
    help = 'Display comprehensive YouTube cache statistics'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='Show detailed statistics including per-endpoint breakdown',
        )
        parser.add_argument(
            '--usage-days',
            type=int,
            default=7,
            help='Number of days to include in usage statistics (default: 7)',
        )
        parser.add_argument(
            '--export-csv',
            type=str,
            help='Export statistics to CSV file',
        )
    
    def handle(self, *args, **options):
        detailed = options['detailed']
        usage_days = options['usage_days']
        export_csv = options['export_csv']
        
        self.stdout.write(
            self.style.SUCCESS('YouTube Cache Statistics')
        )
        self.stdout.write('=' * 50)
        
        # Cache Entry Statistics
        self.show_cache_statistics(detailed)
        
        # API Usage Statistics
        self.show_usage_statistics(usage_days, detailed)
        
        # Rate Limit Statistics
        self.show_rate_limit_statistics(detailed)
        
        # Export to CSV if requested
        if export_csv:
            self.export_to_csv(export_csv, usage_days)
    
    def show_cache_statistics(self, detailed=False):
        """Show cache entry statistics"""
        self.stdout.write('\n📦 Cache Entry Statistics')
        self.stdout.write('-' * 30)
        
        # Basic counts
        total_entries = YouTubeCacheEntry.objects.count()
        active_entries = YouTubeCacheEntry.objects.filter(
            expires_at__gt=timezone.now()
        ).count()
        expired_entries = total_entries - active_entries
        
        # Hit statistics
        hit_stats = YouTubeCacheEntry.objects.aggregate(
            total_hits=Sum('hit_count'),
            avg_hits=Avg('hit_count'),
            max_hits=Max('hit_count')
        )
        
        # Size estimation
        cache_size_mb = self.estimate_cache_size()
        
        self.stdout.write(f'Total entries: {total_entries:,}')
        self.stdout.write(f'Active entries: {active_entries:,}')
        self.stdout.write(f'Expired entries: {expired_entries:,}')
        self.stdout.write(f'Total hits: {hit_stats["total_hits"] or 0:,}')
        self.stdout.write(f'Average hits per entry: {hit_stats["avg_hits"] or 0:.1f}')
        self.stdout.write(f'Max hits for single entry: {hit_stats["max_hits"] or 0:,}')
        self.stdout.write(f'Estimated cache size: {cache_size_mb:.2f} MB')
        
        if detailed and total_entries > 0:
            self.stdout.write('\n📊 Detailed Cache Breakdown:')
            
            # By endpoint
            endpoint_stats = YouTubeCacheEntry.objects.values('endpoint').annotate(
                count=Count('id'),
                hits=Sum('hit_count'),
                avg_hits=Avg('hit_count')
            ).order_by('-count')
            
            self.stdout.write('\nBy Endpoint:')
            for stat in endpoint_stats:
                self.stdout.write(
                    f"  {stat['endpoint']}: {stat['count']:,} entries, "
                    f"{stat['hits'] or 0:,} hits (avg: {stat['avg_hits'] or 0:.1f})"
                )
            
            # Age distribution
            now = timezone.now()
            age_ranges = [
                ('< 1 hour', now - timedelta(hours=1)),
                ('< 1 day', now - timedelta(days=1)),
                ('< 1 week', now - timedelta(weeks=1)),
                ('< 1 month', now - timedelta(days=30)),
            ]
            
            self.stdout.write('\nBy Age:')
            prev_time = None
            for label, time_threshold in age_ranges:
                if prev_time:
                    count = YouTubeCacheEntry.objects.filter(
                        created_at__gte=time_threshold,
                        created_at__lt=prev_time
                    ).count()
                else:
                    count = YouTubeCacheEntry.objects.filter(
                        created_at__gte=time_threshold
                    ).count()
                self.stdout.write(f"  {label}: {count:,} entries")
                prev_time = time_threshold
            
            # Older than 1 month
            old_count = YouTubeCacheEntry.objects.filter(
                created_at__lt=now - timedelta(days=30)
            ).count()
            self.stdout.write(f"  > 1 month: {old_count:,} entries")
    
    def show_usage_statistics(self, days=7, detailed=False):
        """Show API usage statistics"""
        self.stdout.write(f'\n📈 API Usage Statistics (Last {days} days)')
        self.stdout.write('-' * 40)
        
        # Date range
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        usage_queryset = YouTubeApiUsage.objects.filter(
            date__gte=start_date,
            date__lte=end_date
        )
        
        if not usage_queryset.exists():
            self.stdout.write('No usage data available for the specified period')
            return
        
        # Aggregate statistics
        totals = usage_queryset.aggregate(
            total_requests=Sum('total_requests'),
            total_cache_hits=Sum('cache_hits'),
            total_cache_misses=Sum('cache_misses'),
            total_api_calls=Sum('api_calls_made'),
            avg_response_time=Avg('average_response_time')
        )
        
        total_requests = totals['total_requests'] or 0
        cache_hits = totals['total_cache_hits'] or 0
        cache_hit_rate = (cache_hits / total_requests * 100) if total_requests > 0 else 0
        
        self.stdout.write(f'Total requests: {total_requests:,}')
        self.stdout.write(f'Cache hits: {cache_hits:,}')
        self.stdout.write(f'Cache misses: {totals["total_cache_misses"] or 0:,}')
        self.stdout.write(f'API calls made: {totals["total_api_calls"] or 0:,}')
        self.stdout.write(f'Cache hit rate: {cache_hit_rate:.1f}%')
        self.stdout.write(f'Average response time: {totals["avg_response_time"] or 0:.3f}s')
        
        if detailed:
            self.stdout.write('\n📊 Daily Breakdown:')
            daily_usage = usage_queryset.order_by('-date')
            
            for usage in daily_usage:
                hit_rate = usage.cache_hit_rate
                self.stdout.write(
                    f"  {usage.date}: {usage.total_requests:,} requests, "
                    f"{hit_rate:.1f}% hit rate, {usage.api_calls_made:,} API calls"
                )
            
            # By endpoint
            endpoint_totals = usage_queryset.values('endpoint').annotate(
                total_requests=Sum('total_requests'),
                total_hits=Sum('cache_hits'),
                total_api_calls=Sum('api_calls_made')
            ).order_by('-total_requests')
            
            if endpoint_totals:
                self.stdout.write('\nBy Endpoint:')
                for stat in endpoint_totals:
                    hit_rate = (stat['total_hits'] / stat['total_requests'] * 100) if stat['total_requests'] > 0 else 0
                    self.stdout.write(
                        f"  {stat['endpoint']}: {stat['total_requests']:,} requests, "
                        f"{hit_rate:.1f}% hit rate, {stat['total_api_calls']:,} API calls"
                    )
    
    def show_rate_limit_statistics(self, detailed=False):
        """Show rate limit statistics"""
        self.stdout.write('\n🚦 Rate Limit Statistics')
        self.stdout.write('-' * 30)
        
        # Current window
        current_window = timezone.now().replace(second=0, microsecond=0)
        current_limits = YouTubeRateLimit.objects.filter(
            window_start=current_window
        )
        
        if current_limits.exists():
            self.stdout.write(f'Current window ({current_window}):')
            total_current = 0
            for limit in current_limits:
                endpoint_name = limit.endpoint or '(global)'
                ip_info = f' - {limit.user_ip}' if limit.user_ip else ''
                self.stdout.write(f'  {endpoint_name}{ip_info}: {limit.requests_made} requests')
                total_current += limit.requests_made
            self.stdout.write(f'Total current requests: {total_current}')
        else:
            self.stdout.write(f'No requests in current window ({current_window})')
        
        # Recent activity (last hour)
        recent_window = timezone.now() - timedelta(hours=1)
        recent_limits = YouTubeRateLimit.objects.filter(
            window_start__gte=recent_window
        )
        
        if recent_limits.exists():
            recent_total = recent_limits.aggregate(
                total=Sum('requests_made')
            )['total'] or 0
            self.stdout.write(f'Requests in last hour: {recent_total:,}')
        
        if detailed:
            # Historical rate limit data
            self.stdout.write('\n📊 Rate Limit History (Last 24 hours):')
            
            last_24h = timezone.now() - timedelta(hours=24)
            historical_limits = YouTubeRateLimit.objects.filter(
                window_start__gte=last_24h
            ).order_by('-window_start')[:20]  # Last 20 windows
            
            if historical_limits:
                for limit in historical_limits:
                    endpoint_name = limit.endpoint or '(global)'
                    ip_info = f' - {limit.user_ip}' if limit.user_ip else ''
                    self.stdout.write(
                        f"  {limit.window_start}: {endpoint_name}{ip_info} - {limit.requests_made} requests"
                    )
            else:
                self.stdout.write('  No recent rate limit data')
    
    def estimate_cache_size(self):
        """Estimate cache size in MB"""
        sample_size = min(100, YouTubeCacheEntry.objects.count())
        if sample_size == 0:
            return 0.0
        
        sample_entries = YouTubeCacheEntry.objects.all()[:sample_size]
        total_size = sum(sys.getsizeof(str(entry.response_data)) for entry in sample_entries)
        avg_size = total_size / sample_size
        
        total_entries = YouTubeCacheEntry.objects.count()
        estimated_total_size = avg_size * total_entries
        
        return estimated_total_size / (1024 * 1024)  # Convert to MB
    
    def export_to_csv(self, filename, usage_days):
        """Export statistics to CSV file"""
        import csv
        
        try:
            with open(filename, 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)
                
                # Cache statistics
                writer.writerow(['YouTube Cache Statistics'])
                writer.writerow(['Metric', 'Value'])
                
                total_entries = YouTubeCacheEntry.objects.count()
                active_entries = YouTubeCacheEntry.objects.filter(
                    expires_at__gt=timezone.now()
                ).count()
                
                writer.writerow(['Total Entries', total_entries])
                writer.writerow(['Active Entries', active_entries])
                writer.writerow(['Expired Entries', total_entries - active_entries])
                
                # Usage statistics
                writer.writerow([])
                writer.writerow(['API Usage Statistics'])
                writer.writerow(['Date', 'Endpoint', 'Total Requests', 'Cache Hits', 'Cache Hit Rate', 'API Calls'])
                
                end_date = timezone.now().date()
                start_date = end_date - timedelta(days=usage_days)
                
                usage_data = YouTubeApiUsage.objects.filter(
                    date__gte=start_date,
                    date__lte=end_date
                ).order_by('-date')
                
                for usage in usage_data:
                    writer.writerow([
                        usage.date,
                        usage.endpoint,
                        usage.total_requests,
                        usage.cache_hits,
                        f'{usage.cache_hit_rate:.1f}%',
                        usage.api_calls_made
                    ])
            
            self.stdout.write(
                self.style.SUCCESS(f'Statistics exported to {filename}')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error exporting to CSV: {e}')
            )