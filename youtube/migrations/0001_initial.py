# Generated by Django 4.2.7 on 2025-07-17 21:31

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="YouTubeRateLimit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("window_start", models.DateTimeField(unique=True)),
                ("request_count", models.PositiveIntegerField(default=0)),
                ("user_limit", models.PositiveIntegerField(default=10000)),
                ("global_limit", models.PositiveIntegerField(default=1000000)),
            ],
            options={
                "verbose_name": "YouTube Rate Limit",
                "verbose_name_plural": "YouTube Rate Limits",
            },
        ),
        migrations.CreateModel(
            name="YouTubeUserCache",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "cache_key",
                    models.CharField(db_index=True, max_length=255, unique=True),
                ),
                ("endpoint", models.CharField(db_index=True, max_length=100)),
                ("query_hash", models.CharField(db_index=True, max_length=64)),
                ("user_hash", models.CharField(db_index=True, max_length=64)),
                ("response_data", models.JSONField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("expires_at", models.DateTimeField(db_index=True)),
                ("hit_count", models.PositiveIntegerField(default=0)),
                ("last_accessed", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "YouTube User Cache",
                "verbose_name_plural": "YouTube User Caches",
                "indexes": [
                    models.Index(
                        fields=["endpoint", "user_hash", "query_hash"],
                        name="youtube_you_endpoin_82bc36_idx",
                    ),
                    models.Index(
                        fields=["expires_at"], name="youtube_you_expires_5405e4_idx"
                    ),
                    models.Index(
                        fields=["user_hash", "query_hash"],
                        name="youtube_you_user_ha_6e5bd4_idx",
                    ),
                ],
                "unique_together": {("user_hash", "cache_key")},
            },
        ),
        migrations.CreateModel(
            name="YouTubeCacheEntry",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "cache_key",
                    models.CharField(db_index=True, max_length=255, unique=True),
                ),
                ("endpoint", models.CharField(db_index=True, max_length=100)),
                ("query_hash", models.CharField(db_index=True, max_length=64)),
                ("response_data", models.JSONField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("expires_at", models.DateTimeField(db_index=True)),
                ("hit_count", models.PositiveIntegerField(default=0)),
                ("last_accessed", models.DateTimeField(auto_now=True)),
                ("user_specific", models.BooleanField(db_index=True, default=False)),
            ],
            options={
                "verbose_name": "YouTube Cache Entry",
                "verbose_name_plural": "YouTube Cache Entries",
                "indexes": [
                    models.Index(
                        fields=["endpoint", "query_hash"],
                        name="youtube_you_endpoin_0a4d7e_idx",
                    ),
                    models.Index(
                        fields=["expires_at"], name="youtube_you_expires_8e8bc5_idx"
                    ),
                    models.Index(
                        fields=["user_specific", "query_hash"],
                        name="youtube_you_user_sp_312f11_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="YouTubeApiUsage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(db_index=True)),
                ("endpoint", models.CharField(db_index=True, max_length=100)),
                ("total_requests", models.PositiveIntegerField(default=0)),
                ("cache_hits", models.PositiveIntegerField(default=0)),
                ("cache_misses", models.PositiveIntegerField(default=0)),
                ("api_calls_made", models.PositiveIntegerField(default=0)),
                ("average_response_time", models.FloatField(default=0.0)),
                ("unique_users", models.PositiveIntegerField(default=0)),
            ],
            options={
                "verbose_name": "YouTube API Usage",
                "verbose_name_plural": "YouTube API Usage",
                "indexes": [
                    models.Index(
                        fields=["date", "endpoint"], name="youtube_you_date_5fc043_idx"
                    )
                ],
                "unique_together": {("date", "endpoint")},
            },
        ),
    ]
