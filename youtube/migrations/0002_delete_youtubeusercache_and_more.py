# Generated by Django 4.2.7 on 2025-07-17 22:09

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("youtube", "0001_initial"),
    ]

    operations = [
        migrations.DeleteModel(
            name="YouTubeUserCache",
        ),
        migrations.AlterModelOptions(
            name="youtubeapiusage",
            options={"ordering": ["-date", "endpoint"]},
        ),
        migrations.AlterModelOptions(
            name="youtubecacheentry",
            options={"ordering": ["-last_accessed"]},
        ),
        migrations.AlterModelOptions(
            name="youtuberatelimit",
            options={},
        ),
        migrations.RemoveIndex(
            model_name="youtubeapiusage",
            name="youtube_you_date_5fc043_idx",
        ),
        migrations.RemoveIndex(
            model_name="youtubecacheentry",
            name="youtube_you_endpoin_0a4d7e_idx",
        ),
        migrations.RemoveIndex(
            model_name="youtubecacheentry",
            name="youtube_you_expires_8e8bc5_idx",
        ),
        migrations.RemoveIndex(
            model_name="youtubecacheentry",
            name="youtube_you_user_sp_312f11_idx",
        ),
        migrations.RenameField(
            model_name="youtuberatelimit",
            old_name="request_count",
            new_name="requests_made",
        ),
        migrations.RemoveField(
            model_name="youtubecacheentry",
            name="user_specific",
        ),
        migrations.AddField(
            model_name="youtuberatelimit",
            name="endpoint",
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name="youtuberatelimit",
            name="user_ip",
            field=models.GenericIPAddressField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="youtubeapiusage",
            name="date",
            field=models.DateField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="youtubeapiusage",
            name="endpoint",
            field=models.CharField(db_index=True, max_length=200),
        ),
        migrations.AlterField(
            model_name="youtubecacheentry",
            name="endpoint",
            field=models.CharField(db_index=True, max_length=200),
        ),
        migrations.AlterField(
            model_name="youtuberatelimit",
            name="window_start",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterUniqueTogether(
            name="youtuberatelimit",
            unique_together={("window_start", "endpoint", "user_ip")},
        ),
        migrations.AddIndex(
            model_name="youtubecacheentry",
            index=models.Index(
                fields=["endpoint", "expires_at"], name="youtube_you_endpoin_230215_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="youtubecacheentry",
            index=models.Index(
                fields=["created_at"], name="youtube_you_created_1db996_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="youtubecacheentry",
            index=models.Index(
                fields=["last_accessed"], name="youtube_you_last_ac_1610b6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="youtuberatelimit",
            index=models.Index(
                fields=["window_start", "endpoint"],
                name="youtube_you_window__60f9e8_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="youtuberatelimit",
            index=models.Index(
                fields=["window_start", "user_ip"],
                name="youtube_you_window__f8782a_idx",
            ),
        ),
        migrations.RemoveField(
            model_name="youtuberatelimit",
            name="global_limit",
        ),
        migrations.RemoveField(
            model_name="youtuberatelimit",
            name="user_limit",
        ),
    ]
