from rest_framework import serializers
from .models import YouTubeCacheEntry, YouTubeApiUsage, YouTubeRateLimit


class YouTubeSearchRequestSerializer(serializers.Serializer):
    """Serializer for YouTube search request parameters"""
    
    q = serializers.CharField(
        max_length=500,
        help_text="Search query"
    )
    maxResults = serializers.IntegerField(
        default=10,
        min_value=1,
        max_value=50,
        help_text="Maximum number of results to return (1-50)"
    )
    regionCode = serializers.CharField(
        max_length=2,
        default='US',
        help_text="Region code (e.g., US, GB)"
    )
    videoEmbeddable = serializers.BooleanField(
        default=True,
        help_text="Only return embeddable videos"
    )
    safeSearch = serializers.ChoiceField(
        choices=['none', 'moderate', 'strict'],
        default='none',
        help_text="Safe search setting"
    )
    videoSyndicated = serializers.BooleanField(
        default=True,
        help_text="Only return syndicated videos"
    )
    videoCategoryId = serializers.Char<PERSON><PERSON>(
        max_length=10,
        default='10',
        help_text="Video category ID (10 = Music)"
    )


class YouTubeSearchPostSerializer(serializers.Serializer):
    """Serializer for YouTube search POST requests"""
    
    q = serializers.CharField(
        max_length=500,
        help_text="Search query"
    )
    maxResults = serializers.IntegerField(
        default=10,
        min_value=1,
        max_value=50,
        help_text="Maximum number of results to return (1-50)"
    )
    regionCode = serializers.CharField(
        max_length=2,
        default='US',
        help_text="Region code (e.g., US, GB)"
    )
    videoEmbeddable = serializers.BooleanField(
        default=True,
        help_text="Only return embeddable videos"
    )
    safeSearch = serializers.ChoiceField(
        choices=['none', 'moderate', 'strict'],
        default='none',
        help_text="Safe search setting"
    )
    videoSyndicated = serializers.BooleanField(
        default=True,
        help_text="Only return syndicated videos"
    )
    videoCategoryId = serializers.CharField(
        max_length=10,
        default='10',
        help_text="Video category ID (10 = Music)"
    )


class YouTubeVideoDetailsRequestSerializer(serializers.Serializer):
    """Serializer for YouTube video details request parameters"""
    
    id = serializers.CharField(
        max_length=500,
        help_text="Comma-separated list of video IDs"
    )
    part = serializers.CharField(
        max_length=200,
        default='contentDetails,statistics',
        help_text="Comma-separated list of parts to include"
    )


class YouTubeVideoDetailsPostSerializer(serializers.Serializer):
    """Serializer for YouTube video details POST requests"""
    
    id = serializers.CharField(
        max_length=500,
        help_text="Comma-separated list of video IDs"
    )
    part = serializers.CharField(
        max_length=200,
        default='contentDetails,statistics',
        help_text="Comma-separated list of parts to include"
    )


class YouTubeErrorResponseSerializer(serializers.Serializer):
    """Serializer for YouTube API error responses"""
    
    error = serializers.CharField(help_text="Error type")
    message = serializers.CharField(help_text="Error message")
    retry_after = serializers.IntegerField(
        required=False,
        help_text="Seconds to wait before retrying (for rate limit errors)"
    )


class YouTubeSearchResponseSerializer(serializers.Serializer):
    """Serializer for YouTube search response"""
    
    kind = serializers.CharField(help_text="Response type")
    etag = serializers.CharField(help_text="ETag")
    nextPageToken = serializers.CharField(
        required=False,
        help_text="Token for next page of results"
    )
    prevPageToken = serializers.CharField(
        required=False,
        help_text="Token for previous page of results"
    )
    regionCode = serializers.CharField(help_text="Region code")
    pageInfo = serializers.DictField(help_text="Page information")
    items = serializers.ListField(
        child=serializers.DictField(),
        help_text="List of video items"
    )


class YouTubeVideoDetailsResponseSerializer(serializers.Serializer):
    """Serializer for YouTube video details response"""
    
    kind = serializers.CharField(help_text="Response type")
    etag = serializers.CharField(help_text="ETag")
    items = serializers.ListField(
        child=serializers.DictField(),
        help_text="List of video detail items"
    )
    pageInfo = serializers.DictField(help_text="Page information")


class YouTubeCacheEntrySerializer(serializers.ModelSerializer):
    """Serializer for YouTube cache entries"""
    
    class Meta:
        model = YouTubeCacheEntry
        fields = [
            'id', 'cache_key', 'endpoint', 'query_hash',
            'created_at', 'updated_at', 'expires_at',
            'hit_count', 'last_accessed', 'is_expired'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'last_accessed', 'is_expired']


class YouTubeApiUsageSerializer(serializers.ModelSerializer):
    """Serializer for YouTube API usage statistics"""
    
    cache_hit_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = YouTubeApiUsage
        fields = [
            'id', 'date', 'endpoint', 'total_requests',
            'cache_hits', 'cache_misses', 'api_calls_made',
            'average_response_time', 'unique_users', 'cache_hit_rate'
        ]
        read_only_fields = ['id']


class YouTubeRateLimitSerializer(serializers.ModelSerializer):
    """Serializer for YouTube rate limit tracking"""
    
    class Meta:
        model = YouTubeRateLimit
        fields = [
            'id', 'window_start', 'requests_made',
            'endpoint', 'user_ip'
        ]
        read_only_fields = ['id']


class YouTubeCacheStatsSerializer(serializers.Serializer):
    """Serializer for YouTube cache statistics"""
    
    total_entries = serializers.IntegerField(help_text="Total cache entries")
    active_entries = serializers.IntegerField(help_text="Non-expired cache entries")
    expired_entries = serializers.IntegerField(help_text="Expired cache entries")
    total_hits = serializers.IntegerField(help_text="Total cache hits")
    cache_size_mb = serializers.FloatField(help_text="Approximate cache size in MB")
    endpoints = serializers.DictField(help_text="Statistics by endpoint")
    
    
class YouTubeCacheManagementSerializer(serializers.Serializer):
    """Serializer for YouTube cache management operations"""
    
    action = serializers.ChoiceField(
        choices=['cleanup_expired', 'clear_all', 'clear_endpoint'],
        help_text="Cache management action to perform"
    )
    endpoint = serializers.CharField(
        required=False,
        help_text="Endpoint to clear (required for clear_endpoint action)"
    )
    
    def validate(self, data):
        if data.get('action') == 'clear_endpoint' and not data.get('endpoint'):
            raise serializers.ValidationError(
                "endpoint is required when action is 'clear_endpoint'"
            )
        return data