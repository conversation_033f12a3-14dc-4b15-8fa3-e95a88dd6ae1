import hashlib
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from urllib.parse import urlencode, urlparse

import requests
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from django.db import transaction
from django.db import models
from django.db.utils import IntegrityError

from .models import (
    YouTubeCacheEntry, 
    YouTubeApiUsage, 
    YouTubeRateLimit,
)

logger = logging.getLogger(__name__)


class YouTubeRateLimitExceeded(Exception):
    """Exception raised when YouTube API rate limits are exceeded"""
    def __init__(self, message: str = "Rate limit exceeded", retry_after: int = 60):
        self.message = message
        self.retry_after = retry_after
        super().__init__(self.message)


class YouTubeService:
    """Service for handling YouTube API requests with caching and rate limiting"""
    
    BASE_URL = 'https://www.googleapis.com/youtube/v3'
    
    # Cache TTL settings - 30 days as specified
    CACHE_TTL_YOUTUBE = 60 * 60 * 24 * 30  # 30 days
    
    RATE_LIMIT_WINDOW = 60  # 1 minute
    MAX_REQUESTS_PER_WINDOW = 100  # Conservative limit for YouTube API
    REQUEST_TIMEOUT = 15
    
    # Endpoint configurations
    ENDPOINT_CONFIGS = {
        '/search': {'ttl': CACHE_TTL_YOUTUBE, 'user_specific': False},
        '/videos': {'ttl': CACHE_TTL_YOUTUBE, 'user_specific': False},
    }
    
    def __init__(self):
        self.api_key = getattr(settings, 'YOUTUBE_API_KEY', '')
        if not self.api_key:
            logger.warning("YOUTUBE_API_KEY not configured in settings")
    
    def _get_endpoint_config(self, endpoint: str) -> Dict[str, Any]:
        """Get configuration for a specific endpoint"""
        # Find the most specific match
        for pattern, config in self.ENDPOINT_CONFIGS.items():
            if endpoint.startswith(pattern):
                return config
        
        # Default configuration
        return {
            'ttl': self.CACHE_TTL_YOUTUBE,
            'user_specific': False
        }
    
    def _generate_cache_key(self, endpoint: str, params: Dict[str, Any]) -> str:
        """Generate a cache key for the request"""
        # Sort params for consistent hashing
        sorted_params = sorted(params.items())
        query_string = urlencode(sorted_params)
        cache_data = f"{endpoint}?{query_string}"
        cache_hash = hashlib.sha1(cache_data.encode()).hexdigest()
        return f"youtube:{cache_hash}"
    
    def _generate_query_hash(self, params: Dict[str, Any]) -> str:
        """Generate a query hash for database storage"""
        sorted_params = sorted(params.items())
        query_string = urlencode(sorted_params)
        return hashlib.sha1(query_string.encode()).hexdigest()
    
    def _check_rate_limit(self, endpoint: str = '', user_ip: str = None) -> bool:
        """Check if we're within rate limits"""
        now = timezone.now()
        window_start = now.replace(second=0, microsecond=0)
        
        # Check global rate limit
        try:
            global_limit, created = YouTubeRateLimit.objects.get_or_create(
                window_start=window_start,
                endpoint=endpoint,
                user_ip=None,
                defaults={'requests_made': 0}
            )
        except YouTubeRateLimit.MultipleObjectsReturned:
            # Handle the edge case where duplicates still exist
            logger.warning(f"Multiple rate limit records found for {endpoint}, cleaning up...")
            records = YouTubeRateLimit.objects.filter(
                window_start=window_start,
                endpoint=endpoint,
                user_ip=None
            ).order_by('id')
            global_limit = records.first()
            # Sum all requests and delete duplicates
            total_requests = sum(r.requests_made for r in records)
            global_limit.requests_made = total_requests
            global_limit.save()
            records.exclude(id=global_limit.id).delete()
        except IntegrityError:
            # Race condition - another request already created the record
            global_limit = YouTubeRateLimit.objects.get(
                window_start=window_start,
                endpoint=endpoint,
                user_ip=None
            )
        
        if global_limit.requests_made >= self.MAX_REQUESTS_PER_WINDOW:
            logger.warning(f"Global rate limit exceeded for endpoint {endpoint}")
            return False
        
        # Check per-IP rate limit if IP provided
        if user_ip:
            try:
                ip_limit, created = YouTubeRateLimit.objects.get_or_create(
                    window_start=window_start,
                    endpoint='',
                    user_ip=user_ip,
                    defaults={'requests_made': 0}
                )
            except YouTubeRateLimit.MultipleObjectsReturned:
                # Handle the edge case where duplicates still exist
                logger.warning(f"Multiple rate limit records found for IP {user_ip}, cleaning up...")
                records = YouTubeRateLimit.objects.filter(
                    window_start=window_start,
                    endpoint='',
                    user_ip=user_ip
                ).order_by('id')
                ip_limit = records.first()
                # Sum all requests and delete duplicates
                total_requests = sum(r.requests_made for r in records)
                ip_limit.requests_made = total_requests
                ip_limit.save()
                records.exclude(id=ip_limit.id).delete()
            except IntegrityError:
                # Race condition - another request already created the record
                ip_limit = YouTubeRateLimit.objects.get(
                    window_start=window_start,
                    endpoint='',
                    user_ip=user_ip
                )
            
            if ip_limit.requests_made >= 60:  # 1 request per second per IP
                logger.warning(f"IP rate limit exceeded for {user_ip}")
                return False
        
        return True
    
    def _increment_rate_limit(self, endpoint: str = '', user_ip: str = None):
        """Increment rate limit counters"""
        now = timezone.now()
        window_start = now.replace(second=0, microsecond=0)
        
        # Increment global rate limit
        try:
            global_limit, created = YouTubeRateLimit.objects.get_or_create(
                window_start=window_start,
                endpoint=endpoint,
                user_ip=None,
                defaults={'requests_made': 1}
            )
            
            if not created:
                global_limit.requests_made = models.F('requests_made') + 1
                global_limit.save(update_fields=['requests_made'])
                
        except YouTubeRateLimit.MultipleObjectsReturned:
            # Handle the edge case where duplicates still exist
            logger.warning(f"Multiple rate limit records found while incrementing for {endpoint}")
            records = YouTubeRateLimit.objects.filter(
                window_start=window_start,
                endpoint=endpoint,
                user_ip=None
            ).order_by('id')
            global_limit = records.first()
            # Increment and clean up
            global_limit.requests_made += 1
            global_limit.save()
            records.exclude(id=global_limit.id).delete()
        except IntegrityError:
            # Race condition - use update instead
            YouTubeRateLimit.objects.filter(
                window_start=window_start,
                endpoint=endpoint,
                user_ip=None
            ).update(requests_made=models.F('requests_made') + 1)
        
        # Increment per-IP rate limit if IP provided
        if user_ip:
            try:
                ip_limit, created = YouTubeRateLimit.objects.get_or_create(
                    window_start=window_start,
                    endpoint='',
                    user_ip=user_ip,
                    defaults={'requests_made': 1}
                )
                
                if not created:
                    ip_limit.requests_made = models.F('requests_made') + 1
                    ip_limit.save(update_fields=['requests_made'])
                    
            except YouTubeRateLimit.MultipleObjectsReturned:
                # Handle the edge case where duplicates still exist
                logger.warning(f"Multiple rate limit records found while incrementing for IP {user_ip}")
                records = YouTubeRateLimit.objects.filter(
                    window_start=window_start,
                    endpoint='',
                    user_ip=user_ip
                ).order_by('id')
                ip_limit = records.first()
                # Increment and clean up
                ip_limit.requests_made += 1
                ip_limit.save()
                records.exclude(id=ip_limit.id).delete()
            except IntegrityError:
                # Race condition - use update instead
                YouTubeRateLimit.objects.filter(
                    window_start=window_start,
                    endpoint='',
                    user_ip=user_ip
                ).update(requests_made=models.F('requests_made') + 1)
    
    def _get_from_cache(self, cache_key: str, endpoint: str) -> Optional[Dict[str, Any]]:
        """Get data from cache (Redis first, then database)"""
        config = self._get_endpoint_config(endpoint)
        
        # Try Redis first
        cached_data = cache.get(cache_key)
        if cached_data:
            self._update_usage_stats(endpoint, cache_hit=True)
            logger.info(f"Cache hit (Redis) for {cache_key}")
            return cached_data
        
        # Try database cache
        try:
            cache_entry = YouTubeCacheEntry.objects.get(
                cache_key=cache_key,
                expires_at__gt=timezone.now()
            )
            
            cache_entry.increment_hit_count()
            
            # Store back in Redis for faster access
            cache.set(cache_key, cache_entry.response_data, config['ttl'])
            
            self._update_usage_stats(endpoint, cache_hit=True)
            logger.info(f"Cache hit (Database) for {cache_key}")
            return cache_entry.response_data
            
        except YouTubeCacheEntry.DoesNotExist:
            return None
    
    def _store_in_cache(self, cache_key: str, endpoint: str, query_hash: str, data: Dict[str, Any]):
        """Store data in both Redis and database cache"""
        config = self._get_endpoint_config(endpoint)
        expires_at = timezone.now() + timedelta(seconds=config['ttl'])
        
        # Store in Redis
        cache.set(cache_key, data, config['ttl'])
        
        # Store in database cache
        try:
            with transaction.atomic():
                YouTubeCacheEntry.objects.update_or_create(
                    cache_key=cache_key,
                    defaults={
                        'endpoint': endpoint,
                        'query_hash': query_hash,
                        'response_data': data,
                        'expires_at': expires_at,
                        'hit_count': 0,
                    }
                )
            logger.info(f"Stored in cache: {cache_key}")
        except Exception as e:
            logger.error(f"Error storing cache entry: {e}")
    
    def _update_usage_stats(self, endpoint: str, cache_hit: bool = False, 
                           api_call: bool = False, response_time: float = 0.0):
        """Update usage statistics"""
        today = timezone.now().date()
        
        try:
            usage, created = YouTubeApiUsage.objects.get_or_create(
                date=today,
                endpoint=endpoint,
                defaults={
                    'total_requests': 0,
                    'cache_hits': 0,
                    'cache_misses': 0,
                    'api_calls_made': 0,
                    'average_response_time': 0.0,
                    'unique_users': 0,
                }
            )
            
            usage.total_requests += 1
            
            if cache_hit:
                usage.cache_hits += 1
            else:
                usage.cache_misses += 1
            
            if api_call:
                usage.api_calls_made += 1
                # Update average response time
                if usage.api_calls_made == 1:
                    usage.average_response_time = response_time
                else:
                    usage.average_response_time = (
                        (usage.average_response_time * (usage.api_calls_made - 1) + response_time) 
                        / usage.api_calls_made
                    )
            
            usage.save()
            
        except Exception as e:
            logger.error(f"Error updating usage stats: {e}")
    
    def _make_api_request(self, endpoint: str, params: Dict[str, Any] = None, user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Make request to YouTube API"""
        if not self.api_key:
            logger.error("YouTube API key not configured")
            return None
        
        if params is None:
            params = {}
        
        # Add API key to params
        params['key'] = self.api_key
        
        # Check rate limit
        if not self._check_rate_limit(endpoint, user_ip):
            logger.warning("Rate limit exceeded, skipping API request")
            raise YouTubeRateLimitExceeded("Rate limit exceeded", retry_after=60)
        
        # Prepare request
        url = f"{self.BASE_URL}{endpoint}"
        
        start_time = time.time()
        
        try:
            logger.debug(f"Making request to {url} with params: {params}")
            
            response = requests.get(
                url,
                params=params,
                timeout=self.REQUEST_TIMEOUT
            )
            
            response_time = time.time() - start_time
            
            # Increment rate limit counter
            self._increment_rate_limit(endpoint, user_ip)
            
            if response.status_code == 200:
                data = response.json()
                self._update_usage_stats(endpoint, api_call=True, response_time=response_time)
                logger.info(f"Successful API request for {endpoint}")
                return data
            
            elif response.status_code == 400:
                logger.warning(f"Bad request to {endpoint}: {response.text}")
                return {
                    'error': {
                        'code': 400,
                        'message': 'Bad request - invalid parameters'
                    }
                }
            
            elif response.status_code == 401:
                logger.warning(f"Unauthorized request to {endpoint} - invalid API key")
                return {
                    'error': {
                        'code': 401,
                        'message': 'Invalid API key'
                    }
                }
            
            elif response.status_code == 403:
                logger.warning(f"Forbidden request to {endpoint} - quota exceeded or API disabled")
                return {
                    'error': {
                        'code': 403,
                        'message': 'Quota exceeded or API access disabled'
                    }
                }
            
            elif response.status_code == 404:
                logger.warning(f"Not found request to {endpoint}")
                return {
                    'error': {
                        'code': 404,
                        'message': 'The requested resource was not found'
                    }
                }
            
            elif response.status_code == 429:
                logger.warning(f"Rate limited by YouTube API for {endpoint}")
                # Parse retry-after header if available
                retry_after = int(response.headers.get('Retry-After', 60))
                raise YouTubeRateLimitExceeded("Rate limited by YouTube API", retry_after=retry_after)
            
            else:
                logger.error(f"HTTP error {response.status_code} for {endpoint}: {response.text}")
                return {
                    'error': {
                        'code': response.status_code,
                        'message': f'HTTP error {response.status_code}'
                    }
                }
                
        except requests.exceptions.Timeout:
            logger.error(f"Timeout making request to YouTube API for endpoint {endpoint}")
            return {
                'error': {
                    'code': 408,
                    'message': 'Request timeout'
                }
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"Error making request to YouTube API for {endpoint}: {e}")
            return {
                'error': {
                    'code': 500,
                    'message': 'Network error'
                }
            }
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON response from {endpoint}: {e}")
            return {
                'error': {
                    'code': 500,
                    'message': 'Invalid JSON response'
                }
            }
    
    def get_data(self, endpoint: str, params: Dict[str, Any] = None, user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get data from YouTube API with caching"""
        if params is None:
            params = {}
        
        # Generate cache key
        cache_key = self._generate_cache_key(endpoint, params)
        query_hash = self._generate_query_hash(params)
        
        logger.info(f"Processing request for endpoint: {endpoint}")
        
        # Try to get from cache first
        cached_data = self._get_from_cache(cache_key, endpoint)
        if cached_data:
            return cached_data
        
        # Make API request
        try:
            data = self._make_api_request(endpoint, params, user_ip)
            if data and 'error' not in data:
                # Store in cache only if successful
                self._store_in_cache(cache_key, endpoint, query_hash, data)
                self._update_usage_stats(endpoint, cache_hit=False)
            return data
            
        except YouTubeRateLimitExceeded:
            # Re-raise rate limit exceptions
            raise
        except Exception as e:
            logger.error(f"Error in get_data for {endpoint}: {e}")
            return {
                'error': {
                    'code': 500,
                    'message': 'Internal server error'
                }
            }
    
    def search_videos(self, query: str, max_results: int = 10, region_code: str = 'US',
                     video_embeddable: bool = True, safe_search: str = 'none',
                     video_syndicated: bool = True, video_category_id: str = '10',
                     user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Search for videos on YouTube"""
        params = {
            'part': 'snippet',
            'q': query,
            'maxResults': max_results,
            'regionCode': region_code,
            'type': 'video',
            'videoEmbeddable': 'true' if video_embeddable else 'false',
            'safeSearch': safe_search,
            'videoSyndicated': 'true' if video_syndicated else 'false',
            'videoCategoryId': video_category_id,
        }
        
        return self.get_data('/search', params, user_ip)
    
    def get_video_details(self, video_ids: str, parts: str = 'contentDetails,statistics',
                         user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get details for specific video IDs"""
        params = {
            'part': parts,
            'id': video_ids,
        }
        
        return self.get_data('/videos', params, user_ip)