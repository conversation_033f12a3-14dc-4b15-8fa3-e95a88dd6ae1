from django.test import TestCase, override_settings
from django.utils import timezone
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch, Mock
from datetime import timedelta
import json

from .models import YouTubeCacheEntry, YouTubeApiUsage, YouTubeRateLimit
from .services import YouTubeService, YouTubeRateLimitExceeded


class YouTubeCacheEntryModelTest(TestCase):
    """Test YouTube cache entry model"""
    
    def setUp(self):
        self.cache_entry = YouTubeCacheEntry.objects.create(
            cache_key='test_key',
            endpoint='/search',
            query_hash='test_hash',
            response_data={'test': 'data'},
            expires_at=timezone.now() + timedelta(days=1)
        )
    
    def test_cache_entry_creation(self):
        """Test cache entry is created correctly"""
        self.assertEqual(self.cache_entry.cache_key, 'test_key')
        self.assertEqual(self.cache_entry.endpoint, '/search')
        self.assertEqual(self.cache_entry.response_data, {'test': 'data'})
        self.assertFalse(self.cache_entry.is_expired)
    
    def test_is_expired_property(self):
        """Test is_expired property"""
        # Create expired entry
        expired_entry = YouTubeCacheEntry.objects.create(
            cache_key='expired_key',
            endpoint='/search',
            query_hash='expired_hash',
            response_data={'test': 'data'},
            expires_at=timezone.now() - timedelta(days=1)
        )
        self.assertTrue(expired_entry.is_expired)
    
    def test_increment_hit_count(self):
        """Test hit count increment"""
        initial_count = self.cache_entry.hit_count
        self.cache_entry.increment_hit_count()
        self.cache_entry.refresh_from_db()
        self.assertEqual(self.cache_entry.hit_count, initial_count + 1)
    
    def test_string_representation(self):
        """Test string representation"""
        expected = f"YouTube Cache: /search - test_key"
        self.assertEqual(str(self.cache_entry), expected)


class YouTubeApiUsageModelTest(TestCase):
    """Test YouTube API usage model"""
    
    def setUp(self):
        self.usage = YouTubeApiUsage.objects.create(
            endpoint='/search',
            total_requests=100,
            cache_hits=80,
            cache_misses=20,
            api_calls_made=20
        )
    
    def test_cache_hit_rate_property(self):
        """Test cache hit rate calculation"""
        self.assertEqual(self.usage.cache_hit_rate, 80.0)
    
    def test_cache_hit_rate_zero_requests(self):
        """Test cache hit rate with zero requests"""
        usage = YouTubeApiUsage.objects.create(
            endpoint='/videos',
            total_requests=0,
            cache_hits=0,
            cache_misses=0
        )
        self.assertEqual(usage.cache_hit_rate, 0.0)
    
    def test_string_representation(self):
        """Test string representation"""
        expected = f"YouTube Usage: {self.usage.date} - /search"
        self.assertEqual(str(self.usage), expected)


class YouTubeRateLimitModelTest(TestCase):
    """Test YouTube rate limit model"""
    
    def setUp(self):
        self.rate_limit = YouTubeRateLimit.objects.create(
            window_start=timezone.now(),
            requests_made=10,
            endpoint='/search'
        )
    
    def test_rate_limit_creation(self):
        """Test rate limit is created correctly"""
        self.assertEqual(self.rate_limit.requests_made, 10)
        self.assertEqual(self.rate_limit.endpoint, '/search')
    
    def test_string_representation(self):
        """Test string representation"""
        expected = f"YouTube Rate Limit: {self.rate_limit.window_start} - 10 requests"
        self.assertEqual(str(self.rate_limit), expected)


class YouTubeServiceTest(TestCase):
    """Test YouTube service functionality"""
    
    def setUp(self):
        self.service = YouTubeService()
    
    @override_settings(YOUTUBE_API_KEY='test_key')
    def test_service_initialization(self):
        """Test service initializes with API key"""
        service = YouTubeService()
        self.assertEqual(service.api_key, 'test_key')
    
    def test_generate_cache_key(self):
        """Test cache key generation"""
        params = {'q': 'test query', 'maxResults': 10}
        cache_key = self.service._generate_cache_key('/search', params)
        self.assertTrue(cache_key.startswith('youtube:'))
        self.assertEqual(len(cache_key), 48)  # youtube: + 40 char hash
    
    def test_generate_query_hash(self):
        """Test query hash generation"""
        params = {'q': 'test query', 'maxResults': 10}
        query_hash = self.service._generate_query_hash(params)
        self.assertEqual(len(query_hash), 40)  # SHA1 hash length
    
    def test_get_endpoint_config(self):
        """Test endpoint configuration retrieval"""
        config = self.service._get_endpoint_config('/search')
        self.assertEqual(config['ttl'], self.service.CACHE_TTL_YOUTUBE)
        self.assertFalse(config['user_specific'])
    
    @patch('youtube.services.requests.get')
    @override_settings(YOUTUBE_API_KEY='test_key')
    def test_make_api_request_success(self, mock_get):
        """Test successful API request"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'items': []}
        mock_get.return_value = mock_response
        
        result = self.service._make_api_request('/search', {'q': 'test'})
        self.assertEqual(result, {'items': []})
    
    @patch('youtube.services.requests.get')
    @override_settings(YOUTUBE_API_KEY='test_key')
    def test_make_api_request_error(self, mock_get):
        """Test API request with error response"""
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.text = 'Unauthorized'
        mock_get.return_value = mock_response
        
        result = self.service._make_api_request('/search', {'q': 'test'})
        self.assertIn('error', result)
        self.assertEqual(result['error']['code'], 401)
    
    def test_check_rate_limit_within_limits(self):
        """Test rate limit check when within limits"""
        result = self.service._check_rate_limit('/search')
        self.assertTrue(result)
    
    def test_check_rate_limit_exceeded(self):
        """Test rate limit check when exceeded"""
        # Create rate limit entry that exceeds the limit
        now = timezone.now()
        window_start = now.replace(second=0, microsecond=0)
        YouTubeRateLimit.objects.create(
            window_start=window_start,
            endpoint='/search',
            requests_made=self.service.MAX_REQUESTS_PER_WINDOW
        )
        
        result = self.service._check_rate_limit('/search')
        self.assertFalse(result)


class YouTubeAPIViewTest(APITestCase):
    """Test YouTube API views"""
    
    def setUp(self):
        self.search_url = reverse('youtube:search')
        self.video_details_url = reverse('youtube:video-details')
        self.cache_stats_url = reverse('youtube:cache-stats')
    
    def test_search_view_invalid_params(self):
        """Test search view with invalid parameters"""
        response = self.client.get(self.search_url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    @patch('youtube.views.YouTubeService')
    def test_search_view_success(self, mock_service_class):
        """Test successful search view"""
        mock_service = Mock()
        mock_service.search_videos.return_value = {'items': []}
        mock_service_class.return_value = mock_service
        
        response = self.client.get(self.search_url, {'q': 'test query'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, {'items': []})
    
    @patch('youtube.views.YouTubeService')
    def test_search_view_post_success(self, mock_service_class):
        """Test successful search view via POST"""
        mock_service = Mock()
        mock_service.search_videos.return_value = {'items': []}
        mock_service_class.return_value = mock_service
        
        response = self.client.post(self.search_url, {'q': 'test query'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, {'items': []})
    
    @patch('youtube.views.YouTubeService')
    def test_search_view_rate_limit_exceeded(self, mock_service_class):
        """Test search view with rate limit exceeded"""
        mock_service = Mock()
        mock_service.search_videos.side_effect = YouTubeRateLimitExceeded()
        mock_service_class.return_value = mock_service
        
        response = self.client.get(self.search_url, {'q': 'test query'})
        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
        self.assertIn('rate_limit_exceeded', response.data['error'])
    
    def test_video_details_view_invalid_params(self):
        """Test video details view with invalid parameters"""
        response = self.client.get(self.video_details_url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    @patch('youtube.views.YouTubeService')
    def test_video_details_view_success(self, mock_service_class):
        """Test successful video details view"""
        mock_service = Mock()
        mock_service.get_video_details.return_value = {'items': []}
        mock_service_class.return_value = mock_service
        
        response = self.client.get(self.video_details_url, {'id': 'test_video_id'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, {'items': []})
    
    def test_cache_stats_view(self):
        """Test cache statistics view"""
        # Create some test cache entries
        YouTubeCacheEntry.objects.create(
            cache_key='test_key_1',
            endpoint='/search',
            query_hash='hash1',
            response_data={'test': 'data1'},
            expires_at=timezone.now() + timedelta(days=1),
            hit_count=5
        )
        YouTubeCacheEntry.objects.create(
            cache_key='test_key_2',
            endpoint='/videos',
            query_hash='hash2',
            response_data={'test': 'data2'},
            expires_at=timezone.now() - timedelta(days=1),
            hit_count=3
        )
        
        response = self.client.get(self.cache_stats_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_entries', response.data)
        self.assertIn('active_entries', response.data)
        self.assertIn('expired_entries', response.data)
        self.assertEqual(response.data['total_entries'], 2)
        self.assertEqual(response.data['active_entries'], 1)
        self.assertEqual(response.data['expired_entries'], 1)


class YouTubeCacheManagementTest(APITestCase):
    """Test YouTube cache management functionality"""
    
    def setUp(self):
        self.cleanup_url = reverse('youtube:cache-cleanup')
        
        # Create test cache entries
        self.active_entry = YouTubeCacheEntry.objects.create(
            cache_key='active_key',
            endpoint='/search',
            query_hash='active_hash',
            response_data={'test': 'active'},
            expires_at=timezone.now() + timedelta(days=1)
        )
        self.expired_entry = YouTubeCacheEntry.objects.create(
            cache_key='expired_key',
            endpoint='/search',
            query_hash='expired_hash',
            response_data={'test': 'expired'},
            expires_at=timezone.now() - timedelta(days=1)
        )
    
    def test_cleanup_expired_entries(self):
        """Test cleanup of expired cache entries"""
        response = self.client.post(self.cleanup_url, {'action': 'cleanup_expired'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('deleted_count', response.data)
        
        # Check that expired entry is deleted but active entry remains
        self.assertTrue(YouTubeCacheEntry.objects.filter(id=self.active_entry.id).exists())
        self.assertFalse(YouTubeCacheEntry.objects.filter(id=self.expired_entry.id).exists())
    
    def test_clear_all_entries(self):
        """Test clearing all cache entries"""
        response = self.client.post(self.cleanup_url, {'action': 'clear_all'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('deleted_count', response.data)
        
        # Check that all entries are deleted
        self.assertEqual(YouTubeCacheEntry.objects.count(), 0)
    
    def test_clear_endpoint_entries(self):
        """Test clearing entries for specific endpoint"""
        response = self.client.post(self.cleanup_url, {
            'action': 'clear_endpoint',
            'endpoint': '/search'
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('deleted_count', response.data)
        
        # Check that entries for the endpoint are deleted
        self.assertEqual(YouTubeCacheEntry.objects.filter(endpoint='/search').count(), 0)
    
    def test_invalid_action(self):
        """Test invalid cache management action"""
        response = self.client.post(self.cleanup_url, {'action': 'invalid_action'})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    def test_missing_endpoint_for_clear_endpoint(self):
        """Test clear_endpoint action without endpoint parameter"""
        response = self.client.post(self.cleanup_url, {'action': 'clear_endpoint'})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)


class YouTubeUsageStatsTest(APITestCase):
    """Test YouTube usage statistics functionality"""
    
    def setUp(self):
        self.usage_stats_url = reverse('youtube:usage-stats')
        
        # Create test usage data with unique date/endpoint combinations
        # Use specific dates to avoid conflicts between test runs
        today = timezone.now().date()
        yesterday = today - timedelta(days=1)
        
        # Use update_or_create to handle race conditions
        YouTubeApiUsage.objects.update_or_create(
            date=today,
            endpoint='/search',
            defaults={
                'total_requests': 100,
                'cache_hits': 80,
                'cache_misses': 20,
                'api_calls_made': 20,
                'average_response_time': 0.5
            }
        )
        YouTubeApiUsage.objects.update_or_create(
            date=yesterday,
            endpoint='/search',
            defaults={
                'total_requests': 50,
                'cache_hits': 30,
                'cache_misses': 20,
                'api_calls_made': 20,
                'average_response_time': 0.6
            }
        )
    
    def test_usage_stats_default(self):
        """Test usage statistics with default parameters"""
        response = self.client.get(self.usage_stats_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('period', response.data)
        self.assertIn('totals', response.data)
        self.assertIn('daily_usage', response.data)
        self.assertEqual(response.data['period']['days'], 7)
    
    def test_usage_stats_with_days_filter(self):
        """Test usage statistics with days filter"""
        response = self.client.get(self.usage_stats_url, {'days': 1})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['period']['days'], 1)
    
    def test_usage_stats_with_endpoint_filter(self):
        """Test usage statistics with endpoint filter"""
        response = self.client.get(self.usage_stats_url, {'endpoint': '/search'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('endpoint_filter', response.data)
        self.assertEqual(response.data['endpoint_filter'], '/search')
