from django.urls import path
from .views import (
    YouTubeSearchView,
    YouTubeVideoDetailsView,
    YouTubeCacheStatsView,
    YouTubeCacheManagementView,
    YouTubeUsageStatsView,
)

app_name = 'youtube'

urlpatterns = [
    # Main YouTube API endpoints
    path('search/', YouTubeSearchView.as_view(), name='search'),
    path('videos/', YouTubeVideoDetailsView.as_view(), name='video-details'),
    
    # Admin/monitoring endpoints
    path('admin/stats/', YouTubeCacheStatsView.as_view(), name='cache-stats'),
    path('admin/cleanup/', YouTubeCacheManagementView.as_view(), name='cache-cleanup'),
    path('admin/usage/', YouTubeUsageStatsView.as_view(), name='usage-stats'),
]