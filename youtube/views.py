import logging
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.throttling import AnonRateThrottle, UserRateThrottle
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes
from django.utils import timezone
from django.core.cache import cache as django_cache
import hashlib
from urllib.parse import urlencode

from .services import YouTubeService, YouTubeRateLimitExceeded
from .serializers import (
    YouTubeSearchRequestSerializer,
    YouTubeSearchPostSerializer,
    YouTubeVideoDetailsRequestSerializer,
    YouTubeVideoDetailsPostSerializer,
    YouTubeCacheEntrySerializer,
    YouTubeApiUsageSerializer,
    YouTubeRateLimitSerializer,
    YouTubeCacheStatsSerializer,
    YouTubeCacheManagementSerializer,
    YouTubeErrorResponseSerializer,
    YouTubeSearchResponseSerializer,
    YouTubeVideoDetailsResponseSerializer,
)
from .models import YouTubeCacheEntry, YouTubeApiUsage, YouTubeRateLimit

logger = logging.getLogger(__name__)


class YouTubeCacheAwareThrottle(AnonRateThrottle):
    """Custom rate throttle that checks cache before applying rate limiting"""
    scope = 'youtube'
    rate = '200/min'  # 200 requests per minute per IP
    
    def allow_request(self, request, view):
        """Check if request should be allowed - skip rate limiting for cached requests"""
        # For YouTube, we need to check the endpoint and params
        if request.method in ['GET', 'POST']:
            # Extract endpoint from path
            path_parts = request.path.strip('/').split('/')
            if len(path_parts) >= 3:  # api/youtube/endpoint
                endpoint = '/'.join(path_parts[2:])  # Everything after api/youtube/
                
                # Get params based on method
                if request.method == 'GET':
                    params = dict(request.query_params)
                else:  # POST
                    params = request.data if hasattr(request, 'data') else {}
                
                # For search endpoint
                if 'search' in endpoint and params:
                    sorted_params = sorted(params.items())
                    query_string = urlencode(sorted_params)
                    query_hash = hashlib.sha1(query_string.encode()).hexdigest()
                    cache_key = f"youtube:{query_hash}"
                    
                    # Check if data exists in cache
                    if django_cache.get(cache_key) is not None:
                        return True
                    
                    # Check database cache
                    try:
                        YouTubeCacheEntry.objects.get(
                            cache_key=cache_key,
                            expires_at__gt=timezone.now()
                        )
                        return True
                    except YouTubeCacheEntry.DoesNotExist:
                        pass
        
        # Not cached, apply normal rate limiting
        return super().allow_request(request, view)


class YouTubeRateThrottle(AnonRateThrottle):
    """Custom rate throttle for YouTube API"""
    scope = 'youtube'
    rate = '200/min'  # 200 requests per minute per IP


def get_client_ip(request):
    """Get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def handle_youtube_service_call(service_method, *args, **kwargs):
    """Helper function to handle YouTube service calls with consistent error handling"""
    try:
        return service_method(*args, **kwargs)
    except YouTubeRateLimitExceeded as e:
        logger.warning(f"Rate limit exceeded in YouTube service call: {e.message}")
        raise e
    except Exception as e:
        logger.error(f"Error in YouTube service call: {e}")
        return None


class YouTubeSearchView(APIView):
    """Search endpoint for YouTube videos"""
    
    permission_classes = [AllowAny]
    throttle_classes = [YouTubeCacheAwareThrottle]
    
    @extend_schema(
        summary="Search YouTube Videos",
        description="Search for videos on YouTube with caching (30 days).",
        parameters=[
            OpenApiParameter(
                name='q',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=True,
                description='Search query'
            ),
            OpenApiParameter(
                name='maxResults',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Number of results to return (default: 10, max: 50)'
            ),
            OpenApiParameter(
                name='regionCode',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Region code (e.g., US, GB)'
            ),
            OpenApiParameter(
                name='videoEmbeddable',
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Only return embeddable videos'
            ),
            OpenApiParameter(
                name='safeSearch',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Safe search setting (none, moderate, strict)'
            ),
            OpenApiParameter(
                name='videoSyndicated',
                type=OpenApiTypes.BOOL,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Only return syndicated videos'
            ),
            OpenApiParameter(
                name='videoCategoryId',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Video category ID (10 = Music)'
            ),
        ],
        responses={
            200: YouTubeSearchResponseSerializer,
            400: YouTubeErrorResponseSerializer,
            429: YouTubeErrorResponseSerializer,
            502: YouTubeErrorResponseSerializer,
        },
        tags=['YouTube Cache API']
    )
    def get(self, request):
        """Handle search requests"""
        
        # Validate request parameters
        serializer = YouTubeSearchRequestSerializer(data=request.query_params)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        user_ip = get_client_ip(request)
        
        try:
            service = YouTubeService()
            data = handle_youtube_service_call(
                service.search_videos,
                query=validated_data['q'],
                max_results=validated_data.get('maxResults', 10),
                region_code=validated_data.get('regionCode', 'US'),
                video_embeddable=validated_data.get('videoEmbeddable', True),
                safe_search=validated_data.get('safeSearch', 'none'),
                video_syndicated=validated_data.get('videoSyndicated', True),
                video_category_id=validated_data.get('videoCategoryId', '10'),
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'service_unavailable',
                        'message': 'YouTube service temporarily unavailable'
                    },
                    status=status.HTTP_502_BAD_GATEWAY
                )
            
            # Check if YouTube returned an error
            if 'error' in data:
                error_info = data['error']
                if isinstance(error_info, dict):
                    error_code = error_info.get('code', 500)
                    if error_code == 401:
                        return Response(
                            {
                                'error': 'unauthorized',
                                'message': error_info.get('message', 'Invalid API key')
                            },
                            status=status.HTTP_401_UNAUTHORIZED
                        )
                    elif error_code == 403:
                        return Response(
                            {
                                'error': 'forbidden',
                                'message': error_info.get('message', 'Quota exceeded or API access disabled')
                            },
                            status=status.HTTP_403_FORBIDDEN
                        )
                    elif error_code == 404:
                        return Response(
                            {
                                'error': 'not_found',
                                'message': error_info.get('message', 'Resource not found')
                            },
                            status=status.HTTP_404_NOT_FOUND
                        )
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except YouTubeRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error in YouTube search view: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @extend_schema(
        summary="Search YouTube Videos (POST)",
        description="Search for videos on YouTube with caching (30 days). Pass search parameters in request body.",
        request=YouTubeSearchPostSerializer,
        responses={
            200: YouTubeSearchResponseSerializer,
            400: YouTubeErrorResponseSerializer,
            429: YouTubeErrorResponseSerializer,
            502: YouTubeErrorResponseSerializer,
        },
        tags=['YouTube Cache API']
    )
    def post(self, request):
        """Handle search requests via POST"""
        
        serializer = YouTubeSearchPostSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        user_ip = get_client_ip(request)
        
        try:
            service = YouTubeService()
            data = handle_youtube_service_call(
                service.search_videos,
                query=validated_data['q'],
                max_results=validated_data.get('maxResults', 10),
                region_code=validated_data.get('regionCode', 'US'),
                video_embeddable=validated_data.get('videoEmbeddable', True),
                safe_search=validated_data.get('safeSearch', 'none'),
                video_syndicated=validated_data.get('videoSyndicated', True),
                video_category_id=validated_data.get('videoCategoryId', '10'),
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'service_unavailable',
                        'message': 'YouTube service temporarily unavailable'
                    },
                    status=status.HTTP_502_BAD_GATEWAY
                )
            
            # Check if YouTube returned an error
            if 'error' in data:
                error_info = data['error']
                if isinstance(error_info, dict):
                    error_code = error_info.get('code', 500)
                    if error_code == 401:
                        return Response(
                            {
                                'error': 'unauthorized',
                                'message': error_info.get('message', 'Invalid API key')
                            },
                            status=status.HTTP_401_UNAUTHORIZED
                        )
                    elif error_code == 403:
                        return Response(
                            {
                                'error': 'forbidden',
                                'message': error_info.get('message', 'Quota exceeded or API access disabled')
                            },
                            status=status.HTTP_403_FORBIDDEN
                        )
                    elif error_code == 404:
                        return Response(
                            {
                                'error': 'not_found',
                                'message': error_info.get('message', 'Resource not found')
                            },
                            status=status.HTTP_404_NOT_FOUND
                        )
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except YouTubeRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error in YouTube search view: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class YouTubeVideoDetailsView(APIView):
    """Video details endpoint for YouTube videos"""
    
    permission_classes = [AllowAny]
    throttle_classes = [YouTubeCacheAwareThrottle]
    
    @extend_schema(
        summary="Get YouTube Video Details",
        description="Get details for specific YouTube videos with caching (30 days).",
        parameters=[
            OpenApiParameter(
                name='id',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=True,
                description='Comma-separated list of video IDs'
            ),
            OpenApiParameter(
                name='part',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Comma-separated list of parts to include (default: contentDetails,statistics)'
            ),
        ],
        responses={
            200: YouTubeVideoDetailsResponseSerializer,
            400: YouTubeErrorResponseSerializer,
            429: YouTubeErrorResponseSerializer,
            502: YouTubeErrorResponseSerializer,
        },
        tags=['YouTube Cache API']
    )
    def get(self, request):
        """Handle video details requests"""
        
        # Validate request parameters
        serializer = YouTubeVideoDetailsRequestSerializer(data=request.query_params)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        user_ip = get_client_ip(request)
        
        try:
            service = YouTubeService()
            data = handle_youtube_service_call(
                service.get_video_details,
                video_ids=validated_data['id'],
                parts=validated_data.get('part', 'contentDetails,statistics'),
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'service_unavailable',
                        'message': 'YouTube service temporarily unavailable'
                    },
                    status=status.HTTP_502_BAD_GATEWAY
                )
            
            # Check if YouTube returned an error
            if 'error' in data:
                error_info = data['error']
                if isinstance(error_info, dict):
                    error_code = error_info.get('code', 500)
                    if error_code == 401:
                        return Response(
                            {
                                'error': 'unauthorized',
                                'message': error_info.get('message', 'Invalid API key')
                            },
                            status=status.HTTP_401_UNAUTHORIZED
                        )
                    elif error_code == 403:
                        return Response(
                            {
                                'error': 'forbidden',
                                'message': error_info.get('message', 'Quota exceeded or API access disabled')
                            },
                            status=status.HTTP_403_FORBIDDEN
                        )
                    elif error_code == 404:
                        return Response(
                            {
                                'error': 'not_found',
                                'message': error_info.get('message', 'Video not found')
                            },
                            status=status.HTTP_404_NOT_FOUND
                        )
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except YouTubeRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error in YouTube video details view: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @extend_schema(
        summary="Get YouTube Video Details (POST)",
        description="Get details for specific YouTube videos with caching (30 days). Pass video IDs in request body.",
        request=YouTubeVideoDetailsPostSerializer,
        responses={
            200: YouTubeVideoDetailsResponseSerializer,
            400: YouTubeErrorResponseSerializer,
            429: YouTubeErrorResponseSerializer,
            502: YouTubeErrorResponseSerializer,
        },
        tags=['YouTube Cache API']
    )
    def post(self, request):
        """Handle video details requests via POST"""
        
        serializer = YouTubeVideoDetailsPostSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        user_ip = get_client_ip(request)
        
        try:
            service = YouTubeService()
            data = handle_youtube_service_call(
                service.get_video_details,
                video_ids=validated_data['id'],
                parts=validated_data.get('part', 'contentDetails,statistics'),
                user_ip=user_ip
            )
            
            if data is None:
                return Response(
                    {
                        'error': 'service_unavailable',
                        'message': 'YouTube service temporarily unavailable'
                    },
                    status=status.HTTP_502_BAD_GATEWAY
                )
            
            # Check if YouTube returned an error
            if 'error' in data:
                error_info = data['error']
                if isinstance(error_info, dict):
                    error_code = error_info.get('code', 500)
                    if error_code == 401:
                        return Response(
                            {
                                'error': 'unauthorized',
                                'message': error_info.get('message', 'Invalid API key')
                            },
                            status=status.HTTP_401_UNAUTHORIZED
                        )
                    elif error_code == 403:
                        return Response(
                            {
                                'error': 'forbidden',
                                'message': error_info.get('message', 'Quota exceeded or API access disabled')
                            },
                            status=status.HTTP_403_FORBIDDEN
                        )
                    elif error_code == 404:
                        return Response(
                            {
                                'error': 'not_found',
                                'message': error_info.get('message', 'Video not found')
                            },
                            status=status.HTTP_404_NOT_FOUND
                        )
                return Response(data, status=status.HTTP_502_BAD_GATEWAY)
            
            return Response(data, status=status.HTTP_200_OK)
            
        except YouTubeRateLimitExceeded as e:
            return Response(
                {
                    'error': 'rate_limit_exceeded',
                    'message': e.message,
                    'retry_after': e.retry_after
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error in YouTube video details view: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Internal server error'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class YouTubeCacheStatsView(APIView):
    """Admin endpoint for YouTube cache statistics"""
    
    permission_classes = [AllowAny]  # In production, use proper admin permissions
    
    @extend_schema(
        summary="Get YouTube Cache Statistics",
        description="Get comprehensive statistics about YouTube cache usage.",
        responses={
            200: YouTubeCacheStatsSerializer,
            500: YouTubeErrorResponseSerializer,
        },
        tags=['YouTube Admin API']
    )
    def get(self, request):
        """Get cache statistics"""
        
        try:
            from django.db.models import Count, Sum
            
            # Get basic cache statistics
            total_entries = YouTubeCacheEntry.objects.count()
            active_entries = YouTubeCacheEntry.objects.filter(
                expires_at__gt=timezone.now()
            ).count()
            expired_entries = total_entries - active_entries
            
            # Get total hits
            total_hits = YouTubeCacheEntry.objects.aggregate(
                total=Sum('hit_count')
            )['total'] or 0
            
            # Estimate cache size (rough calculation)
            import sys
            cache_size_bytes = 0
            for entry in YouTubeCacheEntry.objects.all()[:100]:  # Sample first 100
                cache_size_bytes += sys.getsizeof(str(entry.response_data))
            
            # Extrapolate to all entries
            if total_entries > 0:
                cache_size_bytes = (cache_size_bytes / min(100, total_entries)) * total_entries
            
            cache_size_mb = cache_size_bytes / (1024 * 1024)
            
            # Get statistics by endpoint
            endpoint_stats = {}
            for endpoint_data in YouTubeCacheEntry.objects.values('endpoint').annotate(
                count=Count('id'),
                hits=Sum('hit_count')
            ):
                endpoint_stats[endpoint_data['endpoint']] = {
                    'entries': endpoint_data['count'],
                    'hits': endpoint_data['hits'] or 0
                }
            
            stats = {
                'total_entries': total_entries,
                'active_entries': active_entries,
                'expired_entries': expired_entries,
                'total_hits': total_hits,
                'cache_size_mb': round(cache_size_mb, 2),
                'endpoints': endpoint_stats
            }
            
            return Response(stats, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Error retrieving cache statistics'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class YouTubeCacheManagementView(APIView):
    """Admin endpoint for YouTube cache management"""
    
    permission_classes = [AllowAny]  # In production, use proper admin permissions
    
    @extend_schema(
        summary="Manage YouTube Cache",
        description="Perform cache management operations like cleanup and clearing.",
        request=YouTubeCacheManagementSerializer,
        responses={
            200: OpenApiTypes.OBJECT,
            400: YouTubeErrorResponseSerializer,
            500: YouTubeErrorResponseSerializer,
        },
        tags=['YouTube Admin API']
    )
    def post(self, request):
        """Perform cache management operations"""
        
        serializer = YouTubeCacheManagementSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'invalid_request',
                    'message': 'Invalid request parameters',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        action = validated_data['action']
        
        try:
            if action == 'cleanup_expired':
                # Remove expired cache entries
                deleted_count = YouTubeCacheEntry.objects.filter(
                    expires_at__lt=timezone.now()
                ).delete()[0]
                
                return Response(
                    {
                        'message': f'Cleaned up {deleted_count} expired cache entries',
                        'deleted_count': deleted_count
                    },
                    status=status.HTTP_200_OK
                )
            
            elif action == 'clear_all':
                # Clear all cache entries
                deleted_count = YouTubeCacheEntry.objects.all().delete()[0]
                
                # Also clear Redis cache
                try:
                    from django.core.cache import cache
                    cache.clear()
                except Exception as e:
                    logger.warning(f"Error clearing Redis cache: {e}")
                
                return Response(
                    {
                        'message': f'Cleared all cache entries ({deleted_count} entries)',
                        'deleted_count': deleted_count
                    },
                    status=status.HTTP_200_OK
                )
            
            elif action == 'clear_endpoint':
                endpoint = validated_data['endpoint']
                deleted_count = YouTubeCacheEntry.objects.filter(
                    endpoint=endpoint
                ).delete()[0]
                
                return Response(
                    {
                        'message': f'Cleared {deleted_count} cache entries for endpoint {endpoint}',
                        'deleted_count': deleted_count,
                        'endpoint': endpoint
                    },
                    status=status.HTTP_200_OK
                )
            
            else:
                return Response(
                    {
                        'error': 'invalid_action',
                        'message': f'Unknown action: {action}'
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            logger.error(f"Error in cache management: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Error performing cache management operation'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class YouTubeUsageStatsView(APIView):
    """Admin endpoint for YouTube usage analytics"""
    
    permission_classes = [AllowAny]  # In production, use proper admin permissions
    
    @extend_schema(
        summary="Get YouTube Usage Statistics",
        description="Get usage analytics for YouTube API calls and caching.",
        parameters=[
            OpenApiParameter(
                name='days',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Number of days to include (default: 7)'
            ),
            OpenApiParameter(
                name='endpoint',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description='Filter by specific endpoint'
            ),
        ],
        responses={
            200: OpenApiTypes.OBJECT,
            500: YouTubeErrorResponseSerializer,
        },
        tags=['YouTube Admin API']
    )
    def get(self, request):
        """Get usage statistics"""
        
        try:
            days = int(request.query_params.get('days', 7))
            endpoint_filter = request.query_params.get('endpoint')
            
            # Calculate date range
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=days)
            
            # Build query
            queryset = YouTubeApiUsage.objects.filter(
                date__gte=start_date,
                date__lte=end_date
            )
            
            if endpoint_filter:
                queryset = queryset.filter(endpoint=endpoint_filter)
            
            # Get usage data
            usage_data = []
            for usage in queryset.order_by('-date'):
                serializer = YouTubeApiUsageSerializer(usage)
                usage_data.append(serializer.data)
            
            # Calculate totals
            from django.db.models import Sum, Avg
            totals = queryset.aggregate(
                total_requests=Sum('total_requests'),
                total_cache_hits=Sum('cache_hits'),
                total_cache_misses=Sum('cache_misses'),
                total_api_calls=Sum('api_calls_made'),
                avg_response_time=Avg('average_response_time')
            )
            
            # Calculate overall cache hit rate
            total_requests = totals['total_requests'] or 0
            total_cache_hits = totals['total_cache_hits'] or 0
            overall_cache_hit_rate = (total_cache_hits / total_requests * 100) if total_requests > 0 else 0
            
            response_data = {
                'period': {
                    'start_date': start_date,
                    'end_date': end_date,
                    'days': days
                },
                'totals': {
                    'total_requests': total_requests,
                    'cache_hits': total_cache_hits,
                    'cache_misses': totals['total_cache_misses'] or 0,
                    'api_calls_made': totals['total_api_calls'] or 0,
                    'cache_hit_rate': round(overall_cache_hit_rate, 2),
                    'average_response_time': round(totals['avg_response_time'] or 0, 3)
                },
                'daily_usage': usage_data
            }
            
            if endpoint_filter:
                response_data['endpoint_filter'] = endpoint_filter
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error getting usage stats: {e}")
            return Response(
                {
                    'error': 'internal_error',
                    'message': 'Error retrieving usage statistics'
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )