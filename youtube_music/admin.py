from django.contrib import admin
from django.utils import timezone
from .models import YTMusicCacheEntry, YTMusicApiUsage, YTMusicRateLimit


@admin.register(YTMusicCacheEntry)
class YTMusicCacheEntryAdmin(admin.ModelAdmin):
    """Admin interface for YouTube Music cache entries"""
    
    list_display = [
        'endpoint', 'cache_key_short', 'hit_count', 'created_at', 'expires_at', 'is_expired'
    ]
    list_filter = ['endpoint', 'created_at', 'expires_at']
    search_fields = ['cache_key', 'endpoint']
    readonly_fields = ['created_at', 'updated_at', 'last_accessed']
    
    def cache_key_short(self, obj):
        """Display shortened cache key"""
        return obj.cache_key[:50] + '...' if len(obj.cache_key) > 50 else obj.cache_key
    cache_key_short.short_description = 'Cache Key'
    
    def is_expired(self, obj):
        """Check if entry is expired"""
        return obj.expires_at < timezone.now()
    is_expired.boolean = True
    is_expired.short_description = 'Expired'


@admin.register(YTMusicApiUsage)
class YTMusicApiUsageAdmin(admin.ModelAdmin):
    """Admin interface for YouTube Music API usage"""
    
    list_display = [
        'date', 'endpoint', 'total_requests', 'cache_hits', 'cache_misses',
        'api_calls_made', 'cache_hit_rate_display'
    ]
    list_filter = ['date', 'endpoint']
    search_fields = ['endpoint']
    
    def cache_hit_rate_display(self, obj):
        """Display cache hit rate as percentage"""
        return f"{obj.cache_hit_rate:.1f}%"
    cache_hit_rate_display.short_description = 'Cache Hit Rate'


@admin.register(YTMusicRateLimit)
class YTMusicRateLimitAdmin(admin.ModelAdmin):
    """Admin interface for YouTube Music rate limit tracking"""
    
    list_display = [
        'window_start', 'endpoint_display', 'user_ip', 'requests_made', 'time_ago'
    ]
    list_filter = ['window_start', 'endpoint', 'user_ip']
    search_fields = ['endpoint', 'user_ip']
    ordering = ['-window_start']
    
    fieldsets = (
        ('Rate Limit Information', {
            'fields': ('window_start', 'endpoint', 'user_ip', 'requests_made')
        }),
    )
    
    def endpoint_display(self, obj):
        """Display endpoint name"""
        return obj.endpoint or 'Global'
    endpoint_display.short_description = 'Endpoint'
    
    def time_ago(self, obj):
        """Display time since window start"""
        now = timezone.now()
        diff = now - obj.window_start
        minutes = diff.seconds // 60
        return f"{minutes} min ago"
    time_ago.short_description = 'Age'
    
    actions = ['delete_old_records']
    
    def delete_old_records(self, request, queryset):
        """Delete records older than 24 hours"""
        cutoff = timezone.now() - timezone.timedelta(hours=24)
        old_records = YTMusicRateLimit.objects.filter(window_start__lt=cutoff)
        count = old_records.count()
        old_records.delete()
        self.message_user(request, f"Deleted {count} records older than 24 hours")
    delete_old_records.short_description = "Delete records older than 24h"