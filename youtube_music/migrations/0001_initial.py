# Generated by Django 4.2.7 on 2025-07-21 05:23

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="YTMusicRateLimit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("window_start", models.DateTimeField(db_index=True)),
                ("requests_made", models.PositiveIntegerField(default=0)),
                ("endpoint", models.CharField(blank=True, max_length=200)),
                ("user_ip", models.GenericIPAddressField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "YouTube Music Rate Limit",
                "verbose_name_plural": "YouTube Music Rate Limits",
                "indexes": [
                    models.Index(
                        fields=["window_start", "endpoint"],
                        name="youtube_mus_window__f91a3c_idx",
                    ),
                    models.Index(
                        fields=["window_start", "user_ip"],
                        name="youtube_mus_window__7a4fc7_idx",
                    ),
                ],
                "unique_together": {("window_start", "endpoint", "user_ip")},
            },
        ),
        migrations.CreateModel(
            name="YTMusicCacheEntry",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "cache_key",
                    models.CharField(db_index=True, max_length=255, unique=True),
                ),
                ("endpoint", models.CharField(db_index=True, max_length=200)),
                ("query_hash", models.CharField(db_index=True, max_length=64)),
                ("response_data", models.JSONField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("expires_at", models.DateTimeField(db_index=True)),
                ("hit_count", models.PositiveIntegerField(default=0)),
                ("last_accessed", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "YouTube Music Cache Entry",
                "verbose_name_plural": "YouTube Music Cache Entries",
                "ordering": ["-last_accessed"],
                "indexes": [
                    models.Index(
                        fields=["endpoint", "expires_at"],
                        name="youtube_mus_endpoin_a722af_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="youtube_mus_created_71606f_idx"
                    ),
                    models.Index(
                        fields=["last_accessed"], name="youtube_mus_last_ac_ab98d6_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="YTMusicApiUsage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(auto_now_add=True, db_index=True)),
                ("endpoint", models.CharField(db_index=True, max_length=200)),
                ("total_requests", models.PositiveIntegerField(default=0)),
                ("cache_hits", models.PositiveIntegerField(default=0)),
                ("cache_misses", models.PositiveIntegerField(default=0)),
                ("api_calls_made", models.PositiveIntegerField(default=0)),
                ("average_response_time", models.FloatField(default=0.0)),
                ("unique_users", models.PositiveIntegerField(default=0)),
            ],
            options={
                "verbose_name": "YouTube Music API Usage",
                "verbose_name_plural": "YouTube Music API Usage",
                "ordering": ["-date", "endpoint"],
                "unique_together": {("date", "endpoint")},
            },
        ),
    ]
