from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('youtube_music', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='YTMusicApiUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(auto_now_add=True, db_index=True)),
                ('endpoint', models.CharField(db_index=True, max_length=200)),
                ('total_requests', models.PositiveIntegerField(default=0)),
                ('cache_hits', models.PositiveIntegerField(default=0)),
                ('cache_misses', models.PositiveIntegerField(default=0)),
                ('api_calls_made', models.PositiveIntegerField(default=0)),
                ('average_response_time', models.FloatField(default=0.0)),
                ('unique_users', models.PositiveIntegerField(default=0)),
            ],
            options={
                'verbose_name': 'YouTube Music API Usage',
                'verbose_name_plural': 'YouTube Music API Usage',
                'ordering': ['-date', 'endpoint'],
                'unique_together': {('date', 'endpoint')},
                'db_table': 'youtube_music_youtubemusicapiusage',
            },
        ),
        migrations.CreateModel(
            name='YTMusicRateLimit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('window_start', models.DateTimeField(db_index=True)),
                ('requests_made', models.PositiveIntegerField(default=0)),
                ('endpoint', models.CharField(blank=True, max_length=200)),
                ('user_ip', models.GenericIPAddressField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'YouTube Music Rate Limit',
                'verbose_name_plural': 'YouTube Music Rate Limits',
                'indexes': [
                    models.Index(fields=['window_start', 'endpoint'], name='youtube_mus_window__ab518b_idx'),
                    models.Index(fields=['window_start', 'user_ip'], name='youtube_mus_window__1758c9_idx'),
                ],
                'unique_together': {('window_start', 'endpoint', 'user_ip')},
                'db_table': 'youtube_music_youtubemusicratelimit',
            },
        ),
    ]