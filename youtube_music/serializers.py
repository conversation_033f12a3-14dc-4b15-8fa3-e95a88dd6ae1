from rest_framework import serializers
from .models import YTMusicApiUsage


class YTMusicSearchRequestSerializer(serializers.Serializer):
    """Serializer for YouTube Music search request parameters"""
    
    query = serializers.CharField(
        max_length=500,
        help_text="Search query"
    )
    filter = serializers.ChoiceField(
        choices=['songs', 'videos', 'albums', 'artists', 'playlists', 
                'community_playlists', 'featured_playlists'],
        required=False,
        help_text="Filter for item types"
    )
    limit = serializers.IntegerField(
        default=20,
        min_value=1,
        max_value=50,
        help_text="Number of search results to return (1-50)"
    )
    ignore_spelling = serializers.BooleanField(
        default=False,
        help_text="Whether to ignore YTM spelling suggestions"
    )


class YTMusicSearchSuggestionsSerializer(serializers.Serializer):
    """Serializer for YouTube Music search suggestions request"""
    
    query = serializers.CharField(
        max_length=500,
        help_text="Search query for suggestions"
    )
    detailed_runs = serializers.BooleanField(
        default=False,
        help_text="Whether to return detailed information"
    )


class YTMusicApiUsageSerializer(serializers.ModelSerializer):
    """Serializer for YouTube Music API usage statistics"""
    
    cache_hit_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = YTMusicApiUsage
        fields = [
            'id', 'date', 'endpoint', 'total_requests',
            'cache_hits', 'cache_misses', 'api_calls_made',
            'average_response_time', 'unique_users', 'cache_hit_rate'
        ]
        read_only_fields = ['id']


class YTMusicCacheStatsSerializer(serializers.Serializer):
    """Serializer for YouTube Music cache statistics"""
    
    total_entries = serializers.IntegerField(help_text="Total cache entries")
    active_entries = serializers.IntegerField(help_text="Non-expired cache entries")
    expired_entries = serializers.IntegerField(help_text="Expired cache entries")
    total_hits = serializers.IntegerField(help_text="Total cache hits")
    cache_size_mb = serializers.FloatField(help_text="Approximate cache size in MB")
    endpoints = serializers.DictField(help_text="Statistics by endpoint")