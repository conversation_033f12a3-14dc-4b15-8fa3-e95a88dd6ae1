import hashlib
import json
import logging
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List

from django.utils import timezone
from django.core.cache import cache as django_cache
from ytmusicapi import YTMusic

from .models import YTMusicCacheEntry, YTMusicApiUsage, YTMusicRateLimit

logger = logging.getLogger(__name__)


class YTMusicRateLimitExceeded(Exception):
    """Exception raised when rate limit is exceeded"""
    pass


class YTMusicService:
    """Service for handling YouTube Music API requests with caching and rate limiting"""
    
    # Cache TTL settings - 30 days as specified
    CACHE_TTL = 60 * 60 * 24 * 30  # 30 days
    
    RATE_LIMIT_WINDOW = 60  # 1 minute
    MAX_REQUESTS_PER_WINDOW = 100  # Conservative limit
    
    # Endpoint configurations
    ENDPOINT_CONFIGS = {
        'search': {'ttl': CACHE_TTL, 'user_specific': False},
        'get_search_suggestions': {'ttl': CACHE_TTL, 'user_specific': False},
    }
    
    def __init__(self):
        """Initialize the YouTube Music service"""
        self.ytmusic = YTMusic()
    
    def _get_endpoint_config(self, endpoint: str) -> Dict[str, Any]:
        """Get configuration for an endpoint"""
        return self.ENDPOINT_CONFIGS.get(endpoint, {'ttl': self.CACHE_TTL, 'user_specific': False})
    
    def _generate_cache_key(self, endpoint: str, params: Dict[str, Any]) -> str:
        """Generate a cache key for the request"""
        # Sort params to ensure consistent key generation
        param_str = json.dumps(params, sort_keys=True)
        key_base = f"ytmusic:{endpoint}:{hashlib.md5(param_str.encode()).hexdigest()}"
        return key_base
    
    def _generate_query_hash(self, params: Dict[str, Any]) -> str:
        """Generate a hash for the query parameters"""
        param_str = json.dumps(params, sort_keys=True)
        return hashlib.sha1(param_str.encode()).hexdigest()
    
    def _check_rate_limit(self, endpoint: str = '', user_ip: str = None) -> bool:
        """Check if the request is within rate limits"""
        now = timezone.now()
        window_start = now.replace(second=0, microsecond=0)  # Current minute
        
        # Get or create rate limit record
        rate_limit, created = YTMusicRateLimit.objects.get_or_create(
            window_start=window_start,
            endpoint=endpoint,
            user_ip=user_ip,
            defaults={'requests_made': 0}
        )
        
        # Check if limit exceeded
        if rate_limit.requests_made >= self.MAX_REQUESTS_PER_WINDOW:
            logger.warning(f"Rate limit exceeded for {endpoint} from {user_ip}")
            return False
        
        return True
    
    def _increment_rate_limit(self, endpoint: str = '', user_ip: str = None):
        """Increment the rate limit counter"""
        now = timezone.now()
        window_start = now.replace(second=0, microsecond=0)  # Current minute
        
        # Get or create rate limit record
        rate_limit, created = YTMusicRateLimit.objects.get_or_create(
            window_start=window_start,
            endpoint=endpoint,
            user_ip=user_ip,
            defaults={'requests_made': 0}
        )
        
        # Increment counter
        rate_limit.requests_made += 1
        rate_limit.save(update_fields=['requests_made'])
    
    def _get_from_cache(self, cache_key: str, endpoint: str) -> Optional[Dict[str, Any]]:
        """Get data from cache"""
        # Try Django's cache first (faster)
        cached_data = django_cache.get(cache_key)
        if cached_data:
            self._update_usage_stats(endpoint, cache_hit=True)
            return cached_data
        
        # Try database cache
        try:
            cache_entry = YTMusicCacheEntry.objects.get(
                cache_key=cache_key,
                expires_at__gt=timezone.now()
            )
            # Update hit count
            cache_entry.increment_hit_count()
            
            # Also store in Django's cache for faster subsequent access
            django_cache.set(cache_key, cache_entry.response_data, timeout=3600)  # 1 hour in Django cache
            
            self._update_usage_stats(endpoint, cache_hit=True)
            return cache_entry.response_data
            
        except YTMusicCacheEntry.DoesNotExist:
            return None
    
    def _store_in_cache(self, cache_key: str, endpoint: str, query_hash: str, data: Dict[str, Any]):
        """Store data in cache"""
        # Calculate TTL
        config = self._get_endpoint_config(endpoint)
        ttl = config.get('ttl', self.CACHE_TTL)
        expires_at = timezone.now() + timedelta(seconds=ttl)
        
        # Store in database cache
        YTMusicCacheEntry.objects.update_or_create(
            cache_key=cache_key,
            defaults={
                'endpoint': endpoint,
                'query_hash': query_hash,
                'response_data': data,
                'expires_at': expires_at
            }
        )
        
        # Also store in Django's cache for faster access
        django_cache.set(cache_key, data, timeout=3600)  # 1 hour in Django cache
    
    def _update_usage_stats(self, endpoint: str, cache_hit: bool = False):
        """Update API usage statistics"""
        today = timezone.now().date()
        
        # Get or create usage stats for today
        usage, created = YTMusicApiUsage.objects.get_or_create(
            date=today,
            endpoint=endpoint,
            defaults={
                'total_requests': 0,
                'cache_hits': 0,
                'cache_misses': 0,
                'api_calls_made': 0
            }
        )
        
        # Update counters
        usage.total_requests += 1
        if cache_hit:
            usage.cache_hits += 1
        else:
            usage.cache_misses += 1
            usage.api_calls_made += 1
        
        usage.save()
    
    def search(self, query: str, filter_type: str = None, limit: int = 20, 
               ignore_spelling: bool = False, user_ip: str = None) -> Dict[str, Any]:
        """Search YouTube Music"""
        endpoint = 'search'
        params = {
            'query': query,
            'filter': filter_type,
            'limit': limit,
            'ignore_spelling': ignore_spelling
        }
        
        # Generate cache key
        cache_key = self._generate_cache_key(endpoint, params)
        query_hash = self._generate_query_hash(params)
        
        # Try to get from cache first
        cached_data = self._get_from_cache(cache_key, endpoint)
        if cached_data:
            return cached_data
        
        # Check rate limit
        if not self._check_rate_limit(endpoint, user_ip):
            raise YTMusicRateLimitExceeded("YouTube Music API rate limit exceeded")
        
        # Make API request
        try:
            start_time = time.time()
            
            # Clean up params for the actual API call
            clean_params = {k: v for k, v in params.items() if v is not None}
            
            # Rename query to match ytmusicapi parameter name
            if 'query' in clean_params:
                clean_params['query'] = clean_params.pop('query')
            
            data = self.ytmusic.search(**clean_params)
            
            # Calculate response time
            response_time = time.time() - start_time
            
            # Increment rate limit counter
            self._increment_rate_limit(endpoint, user_ip)
            
            # Update usage stats with response time
            self._update_usage_stats(endpoint, cache_hit=False)
            
            # Store in cache
            result = {
                'results': data,
                'metadata': {
                    'query': query,
                    'filter': filter_type,
                    'timestamp': datetime.now().isoformat()
                }
            }
            self._store_in_cache(cache_key, endpoint, query_hash, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in search for {query}: {e}")
            return {
                'error': {
                    'code': 500,
                    'message': str(e)
                }
            }
    
    def get_search_suggestions(self, query: str, detailed_runs: bool = False, 
                              user_ip: str = None) -> Dict[str, Any]:
        """Get search suggestions from YouTube Music"""
        endpoint = 'get_search_suggestions'
        params = {
            'query': query,
            'detailed_runs': detailed_runs
        }
        
        # Generate cache key
        cache_key = self._generate_cache_key(endpoint, params)
        query_hash = self._generate_query_hash(params)
        
        # Try to get from cache first
        cached_data = self._get_from_cache(cache_key, endpoint)
        if cached_data:
            return cached_data
        
        # Check rate limit
        if not self._check_rate_limit(endpoint, user_ip):
            raise YTMusicRateLimitExceeded("YouTube Music API rate limit exceeded")
        
        # Make API request
        try:
            start_time = time.time()
            
            data = self.ytmusic.get_search_suggestions(query, detailed_runs)
            
            # Calculate response time
            response_time = time.time() - start_time
            
            # Increment rate limit counter
            self._increment_rate_limit(endpoint, user_ip)
            
            # Update usage stats with response time
            self._update_usage_stats(endpoint, cache_hit=False)
            
            # Store in cache
            result = {
                'suggestions': data,
                'metadata': {
                    'query': query,
                    'detailed': detailed_runs,
                    'timestamp': datetime.now().isoformat()
                }
            }
            self._store_in_cache(cache_key, endpoint, query_hash, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in get_search_suggestions for {query}: {e}")
            return {
                'error': {
                    'code': 500,
                    'message': str(e)
                }
            }
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        now = timezone.now()
        
        # Get all cache entries
        all_entries = YTMusicCacheEntry.objects.all()
        active_entries = all_entries.filter(expires_at__gt=now)
        expired_entries = all_entries.filter(expires_at__lte=now)
        
        # Calculate total hits
        total_hits = sum(entry.hit_count for entry in all_entries)
        
        # Group by endpoint
        endpoints = {}
        for entry in all_entries:
            if entry.endpoint not in endpoints:
                endpoints[entry.endpoint] = {
                    'total': 0,
                    'active': 0,
                    'expired': 0,
                    'hits': 0
                }
            
            endpoints[entry.endpoint]['total'] += 1
            endpoints[entry.endpoint]['hits'] += entry.hit_count
            
            if entry.expires_at > now:
                endpoints[entry.endpoint]['active'] += 1
            else:
                endpoints[entry.endpoint]['expired'] += 1
        
        # Estimate cache size
        cache_size_mb = self._estimate_cache_size()
        
        return {
            'total_entries': all_entries.count(),
            'active_entries': active_entries.count(),
            'expired_entries': expired_entries.count(),
            'total_hits': total_hits,
            'cache_size_mb': cache_size_mb,
            'endpoints': endpoints
        }
    
    def _estimate_cache_size(self) -> float:
        """Estimate cache size in MB"""
        sample_size = min(100, YTMusicCacheEntry.objects.count())
        if sample_size == 0:
            return 0.0
        
        sample_entries = YTMusicCacheEntry.objects.all()[:sample_size]
        total_size = sum(sys.getsizeof(str(entry.response_data)) for entry in sample_entries)
        avg_size = total_size / sample_size
        
        total_entries = YTMusicCacheEntry.objects.count()
        estimated_total_size = avg_size * total_entries
        
        return estimated_total_size / (1024 * 1024)  # Convert to MB
    
    def cleanup_cache(self, delete_expired: bool = True, max_age_days: int = None) -> Dict[str, int]:
        """Clean up cache entries"""
        now = timezone.now()
        deleted_count = 0
        
        # Delete expired entries
        if delete_expired:
            expired_entries = YTMusicCacheEntry.objects.filter(expires_at__lte=now)
            deleted_count = expired_entries.count()
            expired_entries.delete()
        
        # Delete entries older than max_age_days
        if max_age_days is not None:
            cutoff_date = now - timedelta(days=max_age_days)
            old_entries = YTMusicCacheEntry.objects.filter(created_at__lt=cutoff_date)
            old_count = old_entries.count()
            old_entries.delete()
            deleted_count += old_count
        
        return {
            'deleted_count': deleted_count
        }