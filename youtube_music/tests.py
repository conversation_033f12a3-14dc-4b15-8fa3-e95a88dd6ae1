import json
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch, MagicMock

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APITestCase

from .models import YTMusicCacheEntry, YTMusicApiUsage, YTMusicRateLimit
from .services import YTMusicService


class YTMusicApiUsageModelTest(TestCase):
    """Test YouTube Music API usage model"""
    
    def setUp(self):
        self.usage = YTMusicApiUsage.objects.create(
            endpoint='search',
            total_requests=100,
            cache_hits=80,
            cache_misses=20,
            api_calls_made=20
        )
    
    def test_cache_hit_rate_property(self):
        """Test cache hit rate calculation"""
        self.assertEqual(self.usage.cache_hit_rate, 80.0)
    
    def test_cache_hit_rate_zero_requests(self):
        """Test cache hit rate with zero requests"""
        usage = YTMusicApiUsage.objects.create(
            endpoint='get_search_suggestions',
            total_requests=0,
            cache_hits=0,
            cache_misses=0
        )
        self.assertEqual(usage.cache_hit_rate, 0.0)
    
    def test_string_representation(self):
        """Test string representation"""
        expected = f"YouTube Music Usage: {self.usage.date} - search"
        self.assertEqual(str(self.usage), expected)


class YTMusicCacheEntryModelTest(TestCase):
    """Test YouTube Music cache entry model"""
    
    def setUp(self):
        self.cache_entry = YTMusicCacheEntry.objects.create(
            cache_key='test_key',
            endpoint='search',
            query_hash='test_hash',
            response_data={'test': 'data'},
            expires_at=timezone.now() + timedelta(days=30)
        )
    
    def test_is_expired_property_not_expired(self):
        """Test is_expired property for non-expired entry"""
        self.assertFalse(self.cache_entry.is_expired)
    
    def test_is_expired_property_expired(self):
        """Test is_expired property for expired entry"""
        self.cache_entry.expires_at = timezone.now() - timedelta(days=1)
        self.cache_entry.save()
        self.assertTrue(self.cache_entry.is_expired)
    
    def test_increment_hit_count(self):
        """Test increment_hit_count method"""
        initial_count = self.cache_entry.hit_count
        self.cache_entry.increment_hit_count()
        self.assertEqual(self.cache_entry.hit_count, initial_count + 1)


class YTMusicSearchViewTest(APITestCase):
    """Test YouTube Music search view"""
    
    def setUp(self):
        self.search_url = reverse('youtube_music:search')
        self.suggestions_url = reverse('youtube_music:suggestions')
    
    @patch('youtube_music.services.YTMusic')
    def test_search_success(self, mock_ytmusic):
        """Test successful search"""
        # Mock the YTMusic.search method
        mock_instance = mock_ytmusic.return_value
        mock_instance.search.return_value = [
            {
                "category": "Songs",
                "resultType": "song",
                "videoId": "ZrOKjDZOtkA",
                "title": "Wonderwall",
                "artists": [
                    {
                        "name": "Oasis",
                        "id": "UCmMUZbaYdNH0bEd1PAlAqsA"
                    }
                ],
                "album": {
                    "name": "(What's The Story) Morning Glory? (Remastered)",
                    "id": "MPREb_9nqEki4ZDpp"
                },
                "duration": "4:19",
                "duration_seconds": 259,
                "isExplicit": False
            }
        ]
        
        # Make request
        data = {
            'query': 'Wonderwall',
            'filter': 'songs',
            'limit': 10
        }
        response = self.client.post(self.search_url, data, format='json')
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIn('results', response.data)
        self.assertIn('metadata', response.data)
        
        # Print response for debugging
        print(f"Search response for 'Wonderwall':")
        print(json.dumps(response.data, indent=2))
        
        # Check that the mock was called correctly
        mock_instance.search.assert_called_once_with(query='Wonderwall', filter='songs', limit=10, ignore_spelling=False)
    
    @patch('youtube_music.services.YTMusic')
    def test_search_suggestions(self, mock_ytmusic):
        """Test search suggestions"""
        # Mock the YTMusic.get_search_suggestions method
        mock_instance = mock_ytmusic.return_value
        mock_instance.get_search_suggestions.return_value = [
            "wonderwall",
            "wonderwall oasis",
            "wonderwall lyrics"
        ]
        
        # Make request
        data = {
            'query': 'wonder'
        }
        response = self.client.post(self.suggestions_url, data, format='json')
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIn('suggestions', response.data)
        
        # Print response for debugging
        print(f"Search suggestions for 'wonder':")
        print(json.dumps(response.data, indent=2))
        
        # Check that the mock was called correctly
        mock_instance.get_search_suggestions.assert_called_once_with('wonder', False)


class YTMusicCacheTest(APITestCase):
    """Test YouTube Music cache functionality"""
    
    def setUp(self):
        self.search_url = reverse('youtube_music:search')
        self.cache_stats_url = reverse('youtube_music:cache-stats')
        
        # Create a test cache entry
        self.cache_entry = YTMusicCacheEntry.objects.create(
            cache_key='ytmusic:search:test_hash',
            endpoint='search',
            query_hash='test_hash',
            response_data={
                'results': [{'title': 'Test Song'}],
                'metadata': {'query': 'test'}
            },
            expires_at=timezone.now() + timedelta(days=30)
        )
    
    @patch('youtube_music.services.YTMusic')
    def test_cache_hit(self, mock_ytmusic):
        """Test cache hit"""
        # Set up authentication for admin endpoints
        # This would normally require a user with proper permissions
        
        # Make a search request that should hit the cache
        with patch('youtube_music.services.YTMusicService._get_from_cache') as mock_get_cache:
            # Mock cache hit
            mock_get_cache.return_value = self.cache_entry.response_data
            
            data = {
                'query': 'test'
            }
            response = self.client.post(self.search_url, data, format='json')
            
            # Check response
            self.assertEqual(response.status_code, 200)
            
            # Verify the API wasn't called (cache hit)
            mock_ytmusic.return_value.search.assert_not_called()


class YTMusicPopularSongsTest(APITestCase):
    """Test searching for popular songs"""
    
    def setUp(self):
        self.search_url = reverse('youtube_music:search')
        self.popular_songs = [
            "Bohemian Rhapsody",
            "Hotel California",
            "Billie Jean",
            "Sweet Child O' Mine"
        ]
    
    @patch('youtube_music.services.YTMusic')
    def test_search_popular_songs(self, mock_ytmusic):
        """Test searching for popular songs"""
        # Mock the YTMusic.search method with different responses for each song
        mock_instance = mock_ytmusic.return_value
        
        # Set up mock responses for each song
        mock_responses = {
            "Bohemian Rhapsody": [
                {
                    "category": "Songs",
                    "resultType": "song",
                    "videoId": "fJ9rUzIMcZQ",
                    "title": "Bohemian Rhapsody",
                    "artists": [{"name": "Queen", "id": "UCiMhD4jzUqG-IgPzUmmytRQ"}],
                    "album": {"name": "A Night At The Opera", "id": "MPREb_BQZvfMxsqzu"},
                    "duration": "5:59",
                    "duration_seconds": 359
                }
            ],
            "Hotel California": [
                {
                    "category": "Songs",
                    "resultType": "song",
                    "videoId": "EqPtz5qN7HM",
                    "title": "Hotel California",
                    "artists": [{"name": "Eagles", "id": "UCLzbC0RKt9KVWvAT2Haj0Cg"}],
                    "album": {"name": "Hotel California", "id": "MPREb_ToSJVZFyqPy"},
                    "duration": "6:30",
                    "duration_seconds": 390
                }
            ],
            "Billie Jean": [
                {
                    "category": "Songs",
                    "resultType": "song",
                    "videoId": "Zi_XLOBDo_Y",
                    "title": "Billie Jean",
                    "artists": [{"name": "Michael Jackson", "id": "UCF6PEA4Y4xCZmVOgXnGgxKw"}],
                    "album": {"name": "Thriller", "id": "MPREb_eSYXzkJkU20"},
                    "duration": "4:54",
                    "duration_seconds": 294
                }
            ],
            "Sweet Child O' Mine": [
                {
                    "category": "Songs",
                    "resultType": "song",
                    "videoId": "1w7OgIMMRc4",
                    "title": "Sweet Child O' Mine",
                    "artists": [{"name": "Guns N' Roses", "id": "UCIaFw5VBEK8qaW6nRpx_qnw"}],
                    "album": {"name": "Appetite For Destruction", "id": "MPREb_LsJ0OEJVWaG"},
                    "duration": "5:56",
                    "duration_seconds": 356
                }
            ]
        }
        
        # Test each popular song
        for song in self.popular_songs:
            # Set the mock response for this song
            mock_instance.search.return_value = mock_responses[song]
            
            # Make request
            data = {
                'query': song,
                'filter': 'songs',
                'limit': 1
            }
            response = self.client.post(self.search_url, data, format='json')
            
            # Check response
            self.assertEqual(response.status_code, 200)
            self.assertIn('results', response.data)
            
            # Print response for debugging
            print(f"\nSearch response for '{song}':")
            print(json.dumps(response.data, indent=2))
            
            # Check that the mock was called correctly
            mock_instance.search.assert_called_with(query=song, filter='songs', limit=1, ignore_spelling=False)