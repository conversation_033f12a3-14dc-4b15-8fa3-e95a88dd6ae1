from django.urls import path
from .views import (
    YTMusicSearchView,
    YTMusicSearchSuggestionsView,
    YTMusicCacheStatsView,
    YTMusicCacheManagementView,
    YTMusicUsageStatsView,
)

app_name = 'youtube_music'

urlpatterns = [
    # Main YouTube Music API endpoints
    path('search/', YTMusicSearchView.as_view(), name='search'),
    path('suggestions/', YTMusicSearchSuggestionsView.as_view(), name='suggestions'),
    
    # Admin/monitoring endpoints
    path('admin/stats/', YTMusicCacheStatsView.as_view(), name='cache-stats'),
    path('admin/cleanup/', YTMusicCacheManagementView.as_view(), name='cache-cleanup'),
    path('admin/usage/', YTMusicUsageStatsView.as_view(), name='usage-stats'),
]