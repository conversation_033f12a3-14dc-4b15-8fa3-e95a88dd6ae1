import hashlib
import logging
from urllib.parse import urlencode

from django.utils import timezone
from django.core.cache import cache as django_cache
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IsAdmin<PERSON>ser, AllowAny
from rest_framework.throttling import AnonRateThrottle

from .models import YTMusicCacheEntry, YTMusicApiUsage
from .serializers import (
    YTMusicSearchRequestSerializer,
    YTMusicSearchSuggestionsSerializer,
    YTMusicApiUsageSerializer,
    YTMusicCacheStatsSerializer
)
from .services import YTMusicService, YTMusicRateLimitExceeded

logger = logging.getLogger(__name__)


class YTMusicCacheAwareThrottle(AnonRateThrottle):
    """Custom rate throttle that checks cache before applying rate limiting"""
    scope = 'ytmusic'
    rate = '200/min'  # 200 requests per minute per IP
    
    def allow_request(self, request, view):
        """Check if request should be allowed - skip rate limiting for cached requests"""
        if request.method in ['GET', 'POST']:
            # Extract endpoint from path
            path_parts = request.path.strip('/').split('/')
            if len(path_parts) >= 2:  # api/youtube_music/endpoint
                endpoint = path_parts[-1]  # Last part of the path
                
                # Get params based on method
                if request.method == 'GET':
                    params = dict(request.query_params)
                else:  # POST
                    params = request.data if hasattr(request, 'data') else {}
                
                # For search endpoint
                if endpoint in ['search', 'suggestions'] and params:
                    # Create a cache key
                    param_str = urlencode(sorted(params.items()))
                    query_hash = hashlib.sha1(param_str.encode()).hexdigest()
                    cache_key = f"ytmusic:{endpoint}:{query_hash}"
                    
                    # Check if data exists in cache
                    if django_cache.get(cache_key) is not None:
                        return True
                    
                    # Check database cache
                    try:
                        YTMusicCacheEntry.objects.get(
                            cache_key=cache_key,
                            expires_at__gt=timezone.now()
                        )
                        return True
                    except YTMusicCacheEntry.DoesNotExist:
                        pass
        
        # Not cached, apply normal rate limiting
        return super().allow_request(request, view)


class YTMusicSearchView(APIView):
    """View for searching YouTube Music"""
    permission_classes = [AllowAny]
    throttle_classes = [YTMusicCacheAwareThrottle]
    
    def post(self, request):
        """Handle POST requests for YouTube Music search"""
        serializer = YTMusicSearchRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        # Get client IP for rate limiting
        user_ip = self._get_client_ip(request)
        
        try:
            service = YTMusicService()
            result = service.search(
                query=serializer.validated_data['query'],
                filter_type=serializer.validated_data.get('filter'),
                limit=serializer.validated_data.get('limit', 20),
                ignore_spelling=serializer.validated_data.get('ignore_spelling', False),
                user_ip=user_ip
            )
            
            if 'error' in result:
                return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            return Response(result)
            
        except YTMusicRateLimitExceeded:
            return Response(
                {'error': 'Rate limit exceeded. Please try again later.'},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error in YTMusicSearchView: {e}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _get_client_ip(self, request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class YTMusicSearchSuggestionsView(APIView):
    """View for getting YouTube Music search suggestions"""
    permission_classes = [AllowAny]
    throttle_classes = [YTMusicCacheAwareThrottle]
    
    def post(self, request):
        """Handle POST requests for YouTube Music search suggestions"""
        serializer = YTMusicSearchSuggestionsSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        # Get client IP for rate limiting
        user_ip = self._get_client_ip(request)
        
        try:
            service = YTMusicService()
            result = service.get_search_suggestions(
                query=serializer.validated_data['query'],
                detailed_runs=serializer.validated_data.get('detailed_runs', False),
                user_ip=user_ip
            )
            
            if 'error' in result:
                return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            return Response(result)
            
        except YTMusicRateLimitExceeded:
            return Response(
                {'error': 'Rate limit exceeded. Please try again later.'},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )
        except Exception as e:
            logger.error(f"Error in YTMusicSearchSuggestionsView: {e}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _get_client_ip(self, request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class YTMusicCacheStatsView(APIView):
    """View for YouTube Music cache statistics"""
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def get(self, request):
        """Get cache statistics"""
        service = YTMusicService()
        stats = service.get_cache_stats()
        
        serializer = YTMusicCacheStatsSerializer(stats)
        return Response(serializer.data)


class YTMusicCacheManagementView(APIView):
    """View for managing YouTube Music cache"""
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def post(self, request):
        """Clean up cache entries"""
        delete_expired = request.data.get('delete_expired', True)
        max_age_days = request.data.get('max_age_days')
        
        service = YTMusicService()
        result = service.cleanup_cache(delete_expired, max_age_days)
        
        return Response(result)


class YTMusicUsageStatsView(APIView):
    """View for YouTube Music API usage statistics"""
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def get(self, request):
        """Get API usage statistics"""
        
        # Get filter parameters
        days = request.query_params.get('days')
        endpoint = request.query_params.get('endpoint')
        
        # Base queryset
        queryset = YTMusicApiUsage.objects.all()
        
        # Apply filters
        if days:
            try:
                days = int(days)
                cutoff_date = timezone.now().date() - timezone.timedelta(days=days)
                queryset = queryset.filter(date__gte=cutoff_date)
            except ValueError:
                return Response(
                    {'error': 'Invalid days parameter'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        if endpoint:
            queryset = queryset.filter(endpoint=endpoint)
        
        # Serialize and return
        serializer = YTMusicApiUsageSerializer(queryset, many=True)
        return Response(serializer.data)